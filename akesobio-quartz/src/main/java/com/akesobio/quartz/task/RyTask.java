package com.akesobio.quartz.task;

import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.autoacct.domain.AutoAccReceivedMaterials;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctInventory;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctPurchaseNotreturn;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctInventoryMapper;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctPurchaseNotreturnMapper;
import com.akesobio.report.autoacct.service.*;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import com.akesobio.common.utils.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component
@EnableScheduling
public class RyTask {

    @Resource
    private IAutoMaterialAcctInventoryService autoMaterialAcctInventoryService;

    @Resource
    private AutoMaterialAcctInventoryMapper autoMaterialAcctInventoryMapper;

    @Resource
    private AutoMaterialAcctPurchaseNotreturnMapper autoMaterialAcctPurchaseNotreturnMapper;

    @Resource
    private IAutoAccReceivedInventoryService autoAccReceivedInventoryService;

    @Resource
    private IAutoAccReceivedMaterialsService autoAccReceivedMaterialsService;


    public void ryMultipleParams(String s, Boolean b, Long l, Double d, Integer i)
    {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }


    public void ryNoParams()
    {
        System.out.println("执行无参方法");
    }

    public void refreshInventory() throws Exception {
//        refreshInventoryCopy1();
        try{
            //        //刷新物料库存
            List<String> factoryList = new ArrayList<>();
            factoryList.add("1000");
            factoryList.add("1050");
            factoryList.add("1030");
            factoryList.add("1060");
            for(String factory : factoryList){
                JSONObject jsonObject2 = JSONObject.parseObject("{\n" +
                        "    \"factory_code\": \"1000\"\n" +
                        "}");
                jsonObject2.put("factory_code", factory);
                String str =   HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZRP_MM_003",jsonObject2.toJSONString());
                JSONObject jsonObject = JSONObject.parseObject(str);
                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("ET_DATA");
                List<AutoMaterialAcctInventory> list = jsonArray.stream().filter(o -> {
                    JSONObject jsonObject1 = (JSONObject) o;
                    return jsonObject1.getString("MATNR").charAt(0) == '1' &&!jsonObject1.getString("LGOBE").contains("不合格");
                })
//                        .filter(o -> {
//                    JSONObject jsonObject1 = (JSONObject) o;
//                    return !jsonObject1.getString("LGOBE").contains("线边");})
                        .map(o -> {
                    JSONObject jsonObject1 = (JSONObject) o;
                    AutoMaterialAcctInventory autoMaterialAcctInventory = new AutoMaterialAcctInventory();
                    autoMaterialAcctInventory.sapInventoryDataConversion(jsonObject1);
                    return autoMaterialAcctInventory;
                }).collect(Collectors.toList());
//        autoMaterialAcctInventoryService.saveBatch(list);
                for (AutoMaterialAcctInventory autoMaterialAcctInventory : list){
                    LambdaQueryWrapper<AutoMaterialAcctInventory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(AutoMaterialAcctInventory::getFactory,autoMaterialAcctInventory.getFactory())
                            .eq(AutoMaterialAcctInventory::getMaterial,autoMaterialAcctInventory.getMaterial())
                            .eq(AutoMaterialAcctInventory::getBatch,autoMaterialAcctInventory.getBatch())
                            .eq(AutoMaterialAcctInventory::getStorageLocation,autoMaterialAcctInventory.getStorageLocation());
                    AutoMaterialAcctInventory old = autoMaterialAcctInventoryService.getOne(lambdaQueryWrapper);
                    if(old != null){
                        autoMaterialAcctInventory.setId(old.getId());
                        autoMaterialAcctInventoryService.updateById(autoMaterialAcctInventory);
                    }else {
                        autoMaterialAcctInventoryService.save(autoMaterialAcctInventory);
                    }
                }
            }
            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"更新成功: 物料库存（含线边仓）刷新完成!\"}\n" +
                            "}");
        }catch (Exception e){
            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"监控报警: 物料库存（含线边仓）刷新失败!\"}\n" +
                            "}");
        }

    }

    //采购未回
    public void refreshPurchaseNotReturn() throws Exception {
        try {
            JSONObject param = JSONObject.parseObject("{\n" +
                    "  \"werks\":\"\",\n" +
                    "  \"matrn\":\"\"\n" +
                    "}");
            String str = HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZFI_FM_OA_GET_001",param.toJSONString());
            autoMaterialAcctPurchaseNotreturnMapper.truncateTableAcctPurchaseNotreturn();
            JSONObject jsonObject = JSONObject.parseObject(str);
            if (jsonObject.getInteger("code") == 200) {
                if (jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("EO_TABLE")) {
                    List<Object> jsonArray = jsonObject.getJSONObject("data").getJSONArray("EO_TABLE")
                            .stream().filter(o -> {
                                JSONObject jsonObject1 = (JSONObject) o;
                                return jsonObject1.getString("MATNR").charAt(0) == '1';
                            }).collect(Collectors.toList());
                    for (Object i : jsonArray) {
                        JSONObject jsonObject1 = (JSONObject) i;
                        AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = new AutoMaterialAcctPurchaseNotreturn();
                        autoMaterialAcctPurchaseNotreturn.sapPurchaseNotreturnConversion(jsonObject1);
                        autoMaterialAcctPurchaseNotreturnMapper.insert(autoMaterialAcctPurchaseNotreturn);
                    }
                }
            }
            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"更新成功: 采购未回刷新完成!\"}\n" +
                            "}");
        }
        catch (Exception e){

            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"监控报警: 采购未回刷新失败!\"}\n" +
                            "}");
        }

    }

    //sap已经领料物料更新
    public void refreshReceivedInventory() throws Exception {
        try {
            List<AutoAccReceivedMaterials> list =  autoAccReceivedMaterialsService.list();
            List<String> orderNumbers = list.stream().map(AutoAccReceivedMaterials::getOrderNumber).collect(Collectors.toList());
            autoAccReceivedInventoryService.refreshAutoAccReceivedInventory(orderNumbers,null);
            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"更新成功: sap已经领料物料更新完成!\"}\n" +
                            "}");
        }
        catch (Exception e){
            HttpUtils.sendPostJson("https://oapi.dingtalk.com/robot/send?access_token=79a4d85d1f42089bdc915916381c7f32a29e659e6417bee41dfc915516f65650"
                    ,"{\n" +
                            "    \"msgtype\": \"text\",\n" +
                            "    \"text\": {\"content\":\"监控报警: sap已经领料物料更新失败!\"}\n" +
                            "}");

        }


    }



    //刷新auto_material_acct_purchase_notreturn_copy1 表 中的数据
    public void refreshPurchaseNotReturnCopy1() throws Exception {

        JSONObject param = JSONObject.parseObject("{\n" +
                "  \"werks\":\"\",\n" +
                "  \"matrn\":\"\"\n" +
                "}");
        String str = HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZFI_FM_OA_GET_001",param.toJSONString());
        autoMaterialAcctPurchaseNotreturnMapper.truncateTableAcctPurchaseNotreturn();
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject.getInteger("code") == 200) {
            if (jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("EO_TABLE")) {
                List<Object> jsonArray = jsonObject.getJSONObject("data").getJSONArray("EO_TABLE")
                        .stream().filter(o -> {
                             JSONObject jsonObject1 = (JSONObject) o;
                             return jsonObject1.getString("MATNR").charAt(0) == '1';
                        }).collect(Collectors.toList());
                for (Object i : jsonArray) {
                    JSONObject jsonObject1 = (JSONObject) i;
                    AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = new AutoMaterialAcctPurchaseNotreturn();
                    autoMaterialAcctPurchaseNotreturn.sapPurchaseNotreturnConversion(jsonObject1);
                    autoMaterialAcctPurchaseNotreturnMapper.insert(autoMaterialAcctPurchaseNotreturn);
                }
            }
        }

    }

    public void refreshInventoryCopy1() throws Exception {
        //刷新物料库存
        List<String> factoryList = new ArrayList<>();
        factoryList.add("1000");
        factoryList.add("1050");
        factoryList.add("1030");
        factoryList.add("1060");
        for(String factory : factoryList){
            JSONObject jsonObject2 = JSONObject.parseObject("{\n" +
                    "  \"factory_code\":\"1000\",\n" +
                    "  \"flag_zero\":\"\"\n" +
                    "}");
            jsonObject2.put("factory_code", factory);
            String str =   HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZRP_MM_003",jsonObject2.toJSONString());
            JSONObject jsonObject = JSONObject.parseObject(str);
            JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("ET_DATA");
            List<AutoMaterialAcctInventory> list = jsonArray.stream().filter(o -> {
                JSONObject jsonObject1 = (JSONObject) o;
                return jsonObject1.getString("MATNR").charAt(0) == '1';
            }).map(o -> {
                JSONObject jsonObject1 = (JSONObject) o;
                AutoMaterialAcctInventory autoMaterialAcctInventory = new AutoMaterialAcctInventory();
                autoMaterialAcctInventory.sapInventoryDataConversion(jsonObject1);
                return autoMaterialAcctInventory;
            }).collect(Collectors.toList());
            autoMaterialAcctInventoryMapper.truncateTableAcctInventoryCopy1();
            for (AutoMaterialAcctInventory autoMaterialAcctInventory : list){
                autoMaterialAcctInventoryMapper.insertAutoMaterialAcctInventoryCopy1(autoMaterialAcctInventory);
            }
        }
    }




}
