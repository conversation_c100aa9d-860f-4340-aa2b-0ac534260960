package com.akesobio.quartz.meeting;

import com.akesobio.common.utils.http.HttpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


public class MeetingApi {

//    获取权限 = "/t/akesobiouat/token"
     private static String token = "";
     @Value("${client.auth.IP}")
     private String IP;
     @Value("${auth.GetToken}")
     private String GetToken;
    /**
     * 获取访问凭证
     */
    private void  getToken(){
        Map<String,String> map =new HashMap<>();
        map.put("grant_type","password");
        map.put("username","demo");
        map.put("password","Jcyv2ori#");
        String post = HttpUtils.sendPost(IP + GetToken, String.valueOf(map));
        System.out.println(post);
    }

}
