package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessDayInventory2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessDayInventory2025;
import com.akesobio.report.ddi.service.IDdiBusinessDayInventory2025Service;

/**
 * 2025商业日库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Service
public class DdiBusinessDayInventory2025ServiceImpl implements IDdiBusinessDayInventory2025Service {
    @Autowired
    private DdiBusinessDayInventory2025Mapper ddiBusinessDayInventory2025Mapper;


    /**
     * 查询2025商业日库存列表
     *
     * @param ddiBusinessDayInventory2025 2025商业日库存
     * @return 2025商业日库存
     */
    @Override
    public List<DdiBusinessDayInventory2025> selectDdiBusinessDayInventory2025List(DdiBusinessDayInventory2025 ddiBusinessDayInventory2025) {
        String standardProductName = ddiBusinessDayInventory2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessDayInventory2025Mapper.selectKtnDdiBusinessDayInventory2025List(ddiBusinessDayInventory2025);
        } else {
            return ddiBusinessDayInventory2025Mapper.selectYdfDdiBusinessDayInventory2025List(ddiBusinessDayInventory2025);
        }

    }

}
