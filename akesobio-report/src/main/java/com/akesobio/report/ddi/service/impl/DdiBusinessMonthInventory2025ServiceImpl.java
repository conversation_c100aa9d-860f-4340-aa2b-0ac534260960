package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessMonthInventory2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessMonthInventory2025;
import com.akesobio.report.ddi.service.IDdiBusinessMonthInventory2025Service;

/**
 * 2025商业月库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiBusinessMonthInventory2025ServiceImpl implements IDdiBusinessMonthInventory2025Service {
    @Autowired
    private DdiBusinessMonthInventory2025Mapper ddiBusinessMonthInventory2025Mapper;

    /**
     * 查询2025商业月库存列表
     *
     * @param ddiBusinessMonthInventory2025 2025商业月库存
     * @return 2025商业月库存
     */
    @Override
    public List<DdiBusinessMonthInventory2025> selectDdiBusinessMonthInventory2025List(DdiBusinessMonthInventory2025 ddiBusinessMonthInventory2025) {
        String standardProductName = ddiBusinessMonthInventory2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessMonthInventory2025Mapper.selectKtnDdiBusinessMonthInventory2025List(ddiBusinessMonthInventory2025);
        } else {
            return ddiBusinessMonthInventory2025Mapper.selectYdfDdiBusinessMonthInventory2025List(ddiBusinessMonthInventory2025);
        }
    }
}
