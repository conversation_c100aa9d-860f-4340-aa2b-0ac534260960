package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.InventoryReport;

/**
 * 进销存报表Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IInventoryReportService {
    /**
     * 查询进销存报表
     *
     * @param id 进销存报表主键
     * @return 进销存报表
     */
    public InventoryReport selectInventoryReportById(Integer id);

    /**
     * 查询进销存报表列表
     *
     * @param inventoryReport 进销存报表
     * @return 进销存报表集合
     */
    public List<InventoryReport> selectInventoryReportList(InventoryReport inventoryReport);

    /**
     * 新增进销存报表
     *
     * @param inventoryReport 进销存报表
     * @return 结果
     */
    public int insertInventoryReport(InventoryReport inventoryReport);

    /**
     * 修改进销存报表
     *
     * @param inventoryReport 进销存报表
     * @return 结果
     */
    public int updateInventoryReport(InventoryReport inventoryReport);

    /**
     * 批量删除进销存报表
     *
     * @param ids 需要删除的进销存报表主键集合
     * @return 结果
     */
    public int deleteInventoryReportByIds(Integer[] ids);

    /**
     * 删除进销存报表信息
     *
     * @param id 进销存报表主键
     * @return 结果
     */
    public int deleteInventoryReportById(Integer id);
}
