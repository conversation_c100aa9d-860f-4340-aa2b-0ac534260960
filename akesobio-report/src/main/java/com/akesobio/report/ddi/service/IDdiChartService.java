package com.akesobio.report.ddi.service;

import com.akesobio.report.ddi.vo.*;
import com.akesobio.report.groupFinance.vo.Query;

/**
 * DDI图表Service接口
 */
public interface IDdiChartService {

    /**
     * 总监区
     * 总监区销售大区分布图表
     */
    DirectorAreaDistributionChartVo selectDirectorAreaDistributionChartVo(Query query);

    /**
     * 总监区
     * 总监区大区销量图表
     */
    DirectorAreaSalesVolumeChartVo selectDirectorAreaSalesVolumeChartVo(Query query);


    /**
     * 总监区
     * 总监区大区销售增长率图表
     */
    DirectorAreaSalesGrowthRateChartVo selectDirectorAreaSalesGrowthRateChartVo(Query query);

    /**
     * 总监区
     * 总监区月度销量走势图表
     */
    DirectorAreaMonthlySalesTrendChartVo selectDirectorAreaMonthlySalesTrendChartVo(Query query);


    /**
     * 省份
     * 省份销售分布图表
     */
    ProvinceDistributionChartVo selectProvinceDistributionChartVo(Query query);


    /**
     * 省份
     * 省份销量图表
     */
    ProvinceSalesVolumeChartVo selectProvinceSalesVolumeChartVo(Query query);

    /**
     * 省份
     * 省份销售增长率图表
     */
    ProvinceSalesGrowthRateChartVo selectProvinceSalesGrowthRateChartVo(Query query);

    /**
     * 省份
     * 省份月度销量走势图表
     */
    ProvinceMonthlySalesTrendChartVo selectProvinceMonthlySalesTrendChartVo(Query query);


}
