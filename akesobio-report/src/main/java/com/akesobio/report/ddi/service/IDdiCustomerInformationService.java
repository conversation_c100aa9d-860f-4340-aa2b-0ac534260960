package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.basicData.domain.RfExpenses;
import com.akesobio.report.ddi.domain.DdiCustomerInformation;

/**
 * DDI客户信息Service接口
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
public interface IDdiCustomerInformationService {
    /**
     * 查询DDI客户信息
     *
     * @param id DDI客户信息主键
     * @return DDI客户信息
     */
    public DdiCustomerInformation selectDdiCustomerInformationById(Integer id);

    /**
     * 查询DDI客户信息列表
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return DDI客户信息集合
     */
    public List<DdiCustomerInformation> selectDdiCustomerInformationList(DdiCustomerInformation ddiCustomerInformation);

    /**
     * 新增DDI客户信息
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return 结果
     */
    public int insertDdiCustomerInformation(DdiCustomerInformation ddiCustomerInformation);

    /**
     * 修改DDI客户信息
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return 结果
     */
    public int updateDdiCustomerInformation(DdiCustomerInformation ddiCustomerInformation);

    /**
     * 批量删除DDI客户信息
     *
     * @param ids 需要删除的DDI客户信息主键集合
     * @return 结果
     */
    public int deleteDdiCustomerInformationByIds(Integer[] ids);

    /**
     * 删除DDI客户信息信息
     *
     * @param id DDI客户信息主键
     * @return 结果
     */
    public int deleteDdiCustomerInformationById(Integer id);

    /**
     * 导入DDI客户信息数据
     *
     * @param list            DDI客户信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importDdiCustomerInformation(List<DdiCustomerInformation> list, Boolean isUpdateSupport);
}
