package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.DdiCustomerInformation;
import com.akesobio.report.ddi.domain.ProvincePerformance;

/**
 * 省份业绩Service接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IProvincePerformanceService {
    /**
     * 查询省份业绩
     *
     * @param id 省份业绩主键
     * @return 省份业绩
     */
    public ProvincePerformance selectProvincePerformanceById(Integer id);

    /**
     * 查询省份业绩列表
     *
     * @param provincePerformance 省份业绩
     * @return 省份业绩集合
     */
    public List<ProvincePerformance> selectProvincePerformanceList(ProvincePerformance provincePerformance);

    /**
     * 新增省份业绩
     *
     * @param provincePerformance 省份业绩
     * @return 结果
     */
    public int insertProvincePerformance(ProvincePerformance provincePerformance);

    /**
     * 修改省份业绩
     *
     * @param provincePerformance 省份业绩
     * @return 结果
     */
    public int updateProvincePerformance(ProvincePerformance provincePerformance);

    /**
     * 批量删除省份业绩
     *
     * @param ids 需要删除的省份业绩主键集合
     * @return 结果
     */
    public int deleteProvincePerformanceByIds(Integer[] ids);

    /**
     * 删除省份业绩信息
     *
     * @param id 省份业绩主键
     * @return 结果
     */
    public int deleteProvincePerformanceById(Integer id);

    /**
     * 导入省份业绩信息
     *
     * @param list           省份业绩信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importProvincePerformance(List<ProvincePerformance> list, Boolean isUpdateSupport);
}
