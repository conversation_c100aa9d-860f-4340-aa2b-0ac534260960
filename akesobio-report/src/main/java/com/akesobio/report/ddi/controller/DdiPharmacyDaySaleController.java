package com.akesobio.report.ddi.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.ddi.domain.DdiPharmacyDaySale;
import com.akesobio.report.ddi.service.IDdiPharmacyDaySaleService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 药房日销售Controller
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@RestController
@RequestMapping("/ddi/ddiPharmacyDaySale")
public class DdiPharmacyDaySaleController extends BaseController {
    @Autowired
    private IDdiPharmacyDaySaleService ddiPharmacyDaySaleService;

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 查询药房日销售列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiPharmacyDaySale:list')")
    @GetMapping("/list")
    public TableDataInfo list(DdiPharmacyDaySale ddiPharmacyDaySale) {
        List<String> provinceList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for(String a:roles){
            if(a.equals("3001")){
                provinceList.add("上海市");
            }
            if(a.equals("3002")){
                provinceList.add("江苏省");
            }
            if(a.equals("3003")){
                provinceList.add("安徽省");
            }
            if(a.equals("3004")){
                provinceList.add("浙江省");
            }
            if(a.equals("3005")){
                provinceList.add("江西省");
            }
            if(a.equals("3006")){
                provinceList.add("福建省");
            }
            if(a.equals("3007")){
                provinceList.add("广东省");
            }
            if(a.equals("3008")){
                provinceList.add("海南省");
            }
            if(a.equals("3009")){
                provinceList.add("贵州省");
            }
            if(a.equals("3010")){
                provinceList.add("云南省");
            }
            if(a.equals("3011")){
                provinceList.add("湖北省");
            }
            if(a.equals("3012")){
                provinceList.add("湖南省");
            }
            if(a.equals("3013")){
                provinceList.add("广西壮族自治区");
            }
            if(a.equals("3014")){
                provinceList.add("陕西省");
            }
            if(a.equals("3015")){
                provinceList.add("宁夏回族自治区");
            }
            if(a.equals("3016")){
                provinceList.add("河南省");
            }
            if(a.equals("3017")){
                provinceList.add("新疆维吾尔自治区");
            }
            if(a.equals("3018")){
                provinceList.add("青海省");
            }
            if(a.equals("3019")){
                provinceList.add("重庆市");
            }
            if(a.equals("3020")){
                provinceList.add("四川省");
            }
            if(a.equals("3021")){
                provinceList.add("甘肃省");
            }
            if(a.equals("3022")){
                provinceList.add("北京市");
            }
            if(a.equals("3023")){
                provinceList.add("内蒙古自治区");
            }
            if(a.equals("3024")){
                provinceList.add("天津市");
            }
            if(a.equals("3025")){
                provinceList.add("山西省");
            }
            if(a.equals("3026")){
                provinceList.add("河北省");
            }
            if(a.equals("3027")){
                provinceList.add("山东省");
            }
            if(a.equals("3028")){
                provinceList.add("黑龙江省");
            }
            if(a.equals("3029")){
                provinceList.add("吉林省");
            }
            if(a.equals("3030")){
                provinceList.add("辽宁省");
            }
        }
        if(provinceList.size()>0){
            ddiPharmacyDaySale.setProvinceList(provinceList);
        }
        startPage();
        List<DdiPharmacyDaySale> list = ddiPharmacyDaySaleService.selectDdiPharmacyDaySaleList(ddiPharmacyDaySale);
        return getDataTable(list);
    }

    /**
     * 导出药房日销售列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiPharmacyDaySale:export')")
    @Log(title = "药房日销售", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DdiPharmacyDaySale ddiPharmacyDaySale) {
        List<String> provinceList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for(String a:roles){
            if(a.equals("3001")){
                provinceList.add("上海市");
            }
            if(a.equals("3002")){
                provinceList.add("江苏省");
            }
            if(a.equals("3003")){
                provinceList.add("安徽省");
            }
            if(a.equals("3004")){
                provinceList.add("浙江省");
            }
            if(a.equals("3005")){
                provinceList.add("江西省");
            }
            if(a.equals("3006")){
                provinceList.add("福建省");
            }
            if(a.equals("3007")){
                provinceList.add("广东省");
            }
            if(a.equals("3008")){
                provinceList.add("海南省");
            }
            if(a.equals("3009")){
                provinceList.add("贵州省");
            }
            if(a.equals("3010")){
                provinceList.add("云南省");
            }
            if(a.equals("3011")){
                provinceList.add("湖北省");
            }
            if(a.equals("3012")){
                provinceList.add("湖南省");
            }
            if(a.equals("3013")){
                provinceList.add("广西壮族自治区");
            }
            if(a.equals("3014")){
                provinceList.add("陕西省");
            }
            if(a.equals("3015")){
                provinceList.add("宁夏回族自治区");
            }
            if(a.equals("3016")){
                provinceList.add("河南省");
            }
            if(a.equals("3017")){
                provinceList.add("新疆维吾尔自治区");
            }
            if(a.equals("3018")){
                provinceList.add("青海省");
            }
            if(a.equals("3019")){
                provinceList.add("重庆市");
            }
            if(a.equals("3020")){
                provinceList.add("四川省");
            }
            if(a.equals("3021")){
                provinceList.add("甘肃省");
            }
            if(a.equals("3022")){
                provinceList.add("北京市");
            }
            if(a.equals("3023")){
                provinceList.add("内蒙古自治区");
            }
            if(a.equals("3024")){
                provinceList.add("天津市");
            }
            if(a.equals("3025")){
                provinceList.add("山西省");
            }
            if(a.equals("3026")){
                provinceList.add("河北省");
            }
            if(a.equals("3027")){
                provinceList.add("山东省");
            }
            if(a.equals("3028")){
                provinceList.add("黑龙江省");
            }
            if(a.equals("3029")){
                provinceList.add("吉林省");
            }
            if(a.equals("3030")){
                provinceList.add("辽宁省");
            }
        }
        if(provinceList.size()>0){
            ddiPharmacyDaySale.setProvinceList(provinceList);
        }
        List<DdiPharmacyDaySale> list = ddiPharmacyDaySaleService.selectDdiPharmacyDaySaleList(ddiPharmacyDaySale);
        ExcelUtil<DdiPharmacyDaySale> util = new ExcelUtil<DdiPharmacyDaySale>(DdiPharmacyDaySale.class);
        util.exportExcel(response, list, "药房日销售数据");
    }
}
