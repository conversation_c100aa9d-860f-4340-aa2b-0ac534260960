package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyMonthProcurementMapper;
import com.akesobio.report.ddi.domain.DdiPharmacyMonthProcurement;
import com.akesobio.report.ddi.service.IDdiPharmacyMonthProcurementService;

/**
 * 药房月采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DdiPharmacyMonthProcurementServiceImpl implements IDdiPharmacyMonthProcurementService {
    @Autowired
    private DdiPharmacyMonthProcurementMapper ddiPharmacyMonthProcurementMapper;

    /**
     * 查询药房月采购列表
     *
     * @param ddiPharmacyMonthProcurement 药房月采购
     * @return 药房月采购
     */
    @Override
    public List<DdiPharmacyMonthProcurement> selectDdiPharmacyMonthProcurementList(DdiPharmacyMonthProcurement ddiPharmacyMonthProcurement) {
        return ddiPharmacyMonthProcurementMapper.selectDdiPharmacyMonthProcurementList(ddiPharmacyMonthProcurement);
    }
}
