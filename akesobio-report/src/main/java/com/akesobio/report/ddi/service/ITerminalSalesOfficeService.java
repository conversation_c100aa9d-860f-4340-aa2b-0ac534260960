package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.TerminalSalesOffice;

/**
 * 终端销售统计-办事处Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface ITerminalSalesOfficeService {
    /**
     * 查询终端销售统计-办事处
     *
     * @param id 终端销售统计-办事处主键
     * @return 终端销售统计-办事处
     */
    public TerminalSalesOffice selectTerminalSalesOfficeById(Integer id);

    /**
     * 查询终端销售统计-办事处列表
     *
     * @param terminalSalesOffice 终端销售统计-办事处
     * @return 终端销售统计-办事处集合
     */
    public List<TerminalSalesOffice> selectTerminalSalesOfficeList(TerminalSalesOffice terminalSalesOffice);

    /**
     * 新增终端销售统计-办事处
     *
     * @param terminalSalesOffice 终端销售统计-办事处
     * @return 结果
     */
    public int insertTerminalSalesOffice(TerminalSalesOffice terminalSalesOffice);

    /**
     * 修改终端销售统计-办事处
     *
     * @param terminalSalesOffice 终端销售统计-办事处
     * @return 结果
     */
    public int updateTerminalSalesOffice(TerminalSalesOffice terminalSalesOffice);

    /**
     * 批量删除终端销售统计-办事处
     *
     * @param ids 需要删除的终端销售统计-办事处主键集合
     * @return 结果
     */
    public int deleteTerminalSalesOfficeByIds(Integer[] ids);

    /**
     * 删除终端销售统计-办事处信息
     *
     * @param id 终端销售统计-办事处主键
     * @return 结果
     */
    public int deleteTerminalSalesOfficeById(Integer id);
}
