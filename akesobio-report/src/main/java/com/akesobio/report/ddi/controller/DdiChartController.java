package com.akesobio.report.ddi.controller;


import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import com.akesobio.report.ddi.service.IDdiChartService;
import com.akesobio.report.ddi.vo.*;
import com.akesobio.report.groupFinance.vo.ProfitabilityChartVo;
import com.akesobio.report.groupFinance.vo.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * DDI图表Controller
 */
@RestController
@RequestMapping("/ddi/ddiChart")
public class DdiChartController extends BaseController {

    @Autowired
    private IDdiChartService ddiChartService;

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 总监区
     * 总监区销售大区分布图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getDirectorAreaChart')")
    @GetMapping("/getDirectorAreaDistributionChart")
    public TableDataInfo getDirectorAreaDistributionChart(Query query) {
        List<DirectorAreaDistributionChartVo> list = new ArrayList<>();
        DirectorAreaDistributionChartVo vo = ddiChartService.selectDirectorAreaDistributionChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 总监区
     * 总监区大区销量图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getDirectorAreaChart')")
    @GetMapping("/getDirectorAreaSalesVolumeChart")
    public TableDataInfo getDirectorAreaSalesVolumeChart(Query query) {
        List<DirectorAreaSalesVolumeChartVo> list = new ArrayList<>();
        DirectorAreaSalesVolumeChartVo vo = ddiChartService.selectDirectorAreaSalesVolumeChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 总监区
     * 总监区大区销售增长率图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getDirectorAreaChart')")
    @GetMapping("/getDirectorAreaSalesGrowthRateChart")
    public TableDataInfo getDirectorAreaSalesGrowthRateChart(Query query) {
        List<DirectorAreaSalesGrowthRateChartVo> list = new ArrayList<>();
        DirectorAreaSalesGrowthRateChartVo vo = ddiChartService.selectDirectorAreaSalesGrowthRateChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 总监区
     * 总监区月度销量走势图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getDirectorAreaChart')")
    @GetMapping("/getDirectorAreaMonthlySalesTrendChart")
    public TableDataInfo getDirectorAreaMonthlySalesTrendChart(Query query) {
        List<DirectorAreaMonthlySalesTrendChartVo> list = new ArrayList<>();
        DirectorAreaMonthlySalesTrendChartVo vo = ddiChartService.selectDirectorAreaMonthlySalesTrendChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 省份
     * 省份销售分布图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getProvinceChart')")
    @GetMapping("/getProvinceDistributionChart")
    public TableDataInfo getProvinceDistributionChart(Query query) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**
         * 角色对应省份
         */
        for (String a : roles) {
            /** 省份 */
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            query.setProvinceList(provinceList);
        } else {
            provinceList.add("上海市");
            provinceList.add("江苏省");
            provinceList.add("安徽省");
            provinceList.add("浙江省");
            provinceList.add("江西省");
            provinceList.add("福建省");
            provinceList.add("广东省");
            provinceList.add("海南省");
            provinceList.add("贵州省");
            provinceList.add("云南省");
            provinceList.add("湖北省");
            provinceList.add("湖南省");
            provinceList.add("广西壮族自治区");
            provinceList.add("陕西省");
            provinceList.add("宁夏回族自治区");
            provinceList.add("河南省");
            provinceList.add("新疆维吾尔自治区");
            provinceList.add("青海省");
            provinceList.add("重庆市");
            provinceList.add("四川省");
            provinceList.add("甘肃省");
            provinceList.add("北京市");
            provinceList.add("内蒙古自治区");
            provinceList.add("天津市");
            provinceList.add("山西省");
            provinceList.add("河北省");
            provinceList.add("山东省");
            provinceList.add("黑龙江省");
            provinceList.add("吉林省");
            provinceList.add("辽宁省");
            query.setProvinceList(provinceList);
        }
        List<ProvinceDistributionChartVo> list = new ArrayList<>();
        ProvinceDistributionChartVo vo = ddiChartService.selectProvinceDistributionChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 省份
     * 省份销量图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getProvinceChart')")
    @GetMapping("/getProvinceSalesVolumeChart")
    public TableDataInfo getProvinceSalesVolumeChart(Query query) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            /** 省份 */
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            query.setProvinceList(provinceList);
        } else {
            provinceList.add("上海市");
            provinceList.add("江苏省");
            provinceList.add("安徽省");
            provinceList.add("浙江省");
            provinceList.add("江西省");
            provinceList.add("福建省");
            provinceList.add("广东省");
            provinceList.add("海南省");
            provinceList.add("贵州省");
            provinceList.add("云南省");
            provinceList.add("湖北省");
            provinceList.add("湖南省");
            provinceList.add("广西壮族自治区");
            provinceList.add("陕西省");
            provinceList.add("宁夏回族自治区");
            provinceList.add("河南省");
            provinceList.add("新疆维吾尔自治区");
            provinceList.add("青海省");
            provinceList.add("重庆市");
            provinceList.add("四川省");
            provinceList.add("甘肃省");
            provinceList.add("北京市");
            provinceList.add("内蒙古自治区");
            provinceList.add("天津市");
            provinceList.add("山西省");
            provinceList.add("河北省");
            provinceList.add("山东省");
            provinceList.add("黑龙江省");
            provinceList.add("吉林省");
            provinceList.add("辽宁省");
            query.setProvinceList(provinceList);
        }
        List<ProvinceSalesVolumeChartVo> list = new ArrayList<>();
        ProvinceSalesVolumeChartVo vo = ddiChartService.selectProvinceSalesVolumeChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }

    /**
     * 省份
     * 省份销售增长率图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getProvinceChart')")
    @GetMapping("/getProvinceSalesGrowthRateChart")
    public TableDataInfo getProvinceSalesGrowthRateChart(Query query) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            /** 省份 */
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            query.setProvinceList(provinceList);
        } else {
            provinceList.add("上海市");
            provinceList.add("江苏省");
            provinceList.add("安徽省");
            provinceList.add("浙江省");
            provinceList.add("江西省");
            provinceList.add("福建省");
            provinceList.add("广东省");
            provinceList.add("海南省");
            provinceList.add("贵州省");
            provinceList.add("云南省");
            provinceList.add("湖北省");
            provinceList.add("湖南省");
            provinceList.add("广西壮族自治区");
            provinceList.add("陕西省");
            provinceList.add("宁夏回族自治区");
            provinceList.add("河南省");
            provinceList.add("新疆维吾尔自治区");
            provinceList.add("青海省");
            provinceList.add("重庆市");
            provinceList.add("四川省");
            provinceList.add("甘肃省");
            provinceList.add("北京市");
            provinceList.add("内蒙古自治区");
            provinceList.add("天津市");
            provinceList.add("山西省");
            provinceList.add("河北省");
            provinceList.add("山东省");
            provinceList.add("黑龙江省");
            provinceList.add("吉林省");
            provinceList.add("辽宁省");
            query.setProvinceList(provinceList);
        }
        List<ProvinceSalesGrowthRateChartVo> list = new ArrayList<>();
        ProvinceSalesGrowthRateChartVo vo = ddiChartService.selectProvinceSalesGrowthRateChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }


    /**
     * 省份
     * 省份月度销量走势图表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiChart:getProvinceChart')")
    @GetMapping("/getProvinceMonthlySalesTrendChart")
    public TableDataInfo getProvinceMonthlySalesTrendChart(Query query) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            /** 省份 */
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            query.setProvinceList(provinceList);
        } else {
            provinceList.add("上海市");
            provinceList.add("江苏省");
            provinceList.add("安徽省");
            provinceList.add("浙江省");
            provinceList.add("江西省");
            provinceList.add("福建省");
            provinceList.add("广东省");
            provinceList.add("海南省");
            provinceList.add("贵州省");
            provinceList.add("云南省");
            provinceList.add("湖北省");
            provinceList.add("湖南省");
            provinceList.add("广西壮族自治区");
            provinceList.add("陕西省");
            provinceList.add("宁夏回族自治区");
            provinceList.add("河南省");
            provinceList.add("新疆维吾尔自治区");
            provinceList.add("青海省");
            provinceList.add("重庆市");
            provinceList.add("四川省");
            provinceList.add("甘肃省");
            provinceList.add("北京市");
            provinceList.add("内蒙古自治区");
            provinceList.add("天津市");
            provinceList.add("山西省");
            provinceList.add("河北省");
            provinceList.add("山东省");
            provinceList.add("黑龙江省");
            provinceList.add("吉林省");
            provinceList.add("辽宁省");
            query.setProvinceList(provinceList);
        }
        List<ProvinceMonthlySalesTrendChartVo> list = new ArrayList<>();
        ProvinceMonthlySalesTrendChartVo vo = ddiChartService.selectProvinceMonthlySalesTrendChartVo(query);
        list.add(vo);
        return getDataTable(list);
    }
}
