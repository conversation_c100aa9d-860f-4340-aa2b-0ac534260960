package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.BusinessInventoryReport2025Mapper;
import com.akesobio.report.ddi.domain.BusinessInventoryReport2025;
import com.akesobio.report.ddi.service.IBusinessInventoryReport2025Service;

/**
 * 2025商业进销存报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Service
public class BusinessInventoryReport2025ServiceImpl implements IBusinessInventoryReport2025Service {
    @Autowired
    private BusinessInventoryReport2025Mapper businessInventoryReport2025Mapper;

    /**
     * 查询2025商业进销存报表
     *
     * @param id 2025商业进销存报表主键
     * @return 2025商业进销存报表
     */
    @Override
    public BusinessInventoryReport2025 selectBusinessInventoryReport2025ById(Integer id) {
        return businessInventoryReport2025Mapper.selectBusinessInventoryReport2025ById(id);
    }

    /**
     * 查询2025商业进销存报表列表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 2025商业进销存报表
     */
    @Override
    public List<BusinessInventoryReport2025> selectBusinessInventoryReport2025List(BusinessInventoryReport2025 businessInventoryReport2025) {
        return businessInventoryReport2025Mapper.selectBusinessInventoryReport2025List(businessInventoryReport2025);
    }

    /**
     * 新增2025商业进销存报表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 结果
     */
    @Override
    public int insertBusinessInventoryReport2025(BusinessInventoryReport2025 businessInventoryReport2025) {
        return businessInventoryReport2025Mapper.insertBusinessInventoryReport2025(businessInventoryReport2025);
    }

    /**
     * 修改2025商业进销存报表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 结果
     */
    @Override
    public int updateBusinessInventoryReport2025(BusinessInventoryReport2025 businessInventoryReport2025) {
        return businessInventoryReport2025Mapper.updateBusinessInventoryReport2025(businessInventoryReport2025);
    }

    /**
     * 批量删除2025商业进销存报表
     *
     * @param ids 需要删除的2025商业进销存报表主键
     * @return 结果
     */
    @Override
    public int deleteBusinessInventoryReport2025ByIds(Integer[] ids) {
        return businessInventoryReport2025Mapper.deleteBusinessInventoryReport2025ByIds(ids);
    }

    /**
     * 删除2025商业进销存报表信息
     *
     * @param id 2025商业进销存报表主键
     * @return 结果
     */
    @Override
    public int deleteBusinessInventoryReport2025ById(Integer id) {
        return businessInventoryReport2025Mapper.deleteBusinessInventoryReport2025ById(id);
    }
}
