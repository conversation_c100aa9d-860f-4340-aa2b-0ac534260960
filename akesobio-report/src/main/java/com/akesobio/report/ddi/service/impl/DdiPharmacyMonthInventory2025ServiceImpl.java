package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyMonthInventory2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyMonthInventory2025;
import com.akesobio.report.ddi.service.IDdiPharmacyMonthInventory2025Service;

/**
 * 2025药房月库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiPharmacyMonthInventory2025ServiceImpl implements IDdiPharmacyMonthInventory2025Service {
    @Autowired
    private DdiPharmacyMonthInventory2025Mapper ddiPharmacyMonthInventory2025Mapper;


    /**
     * 查询2025药房月库存列表
     *
     * @param ddiPharmacyMonthInventory2025 2025药房月库存
     * @return 2025药房月库存
     */
    @Override
    public List<DdiPharmacyMonthInventory2025> selectDdiPharmacyMonthInventory2025List(DdiPharmacyMonthInventory2025 ddiPharmacyMonthInventory2025) {
        String standardProductName = ddiPharmacyMonthInventory2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyMonthInventory2025Mapper.selectKtnDdiPharmacyMonthInventory2025List(ddiPharmacyMonthInventory2025);
        } else {
            return ddiPharmacyMonthInventory2025Mapper.selectYdfDdiPharmacyMonthInventory2025List(ddiPharmacyMonthInventory2025);
        }
    }

}
