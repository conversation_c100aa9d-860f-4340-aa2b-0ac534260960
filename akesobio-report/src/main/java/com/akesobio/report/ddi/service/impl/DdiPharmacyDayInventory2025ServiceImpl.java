package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDayInventory2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDayInventory2025;
import com.akesobio.report.ddi.service.IDdiPharmacyDayInventory2025Service;

/**
 * 2025药房日库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiPharmacyDayInventory2025ServiceImpl implements IDdiPharmacyDayInventory2025Service {
    @Autowired
    private DdiPharmacyDayInventory2025Mapper ddiPharmacyDayInventory2025Mapper;


    /**
     * 查询2025药房日库存列表
     *
     * @param ddiPharmacyDayInventory2025 2025药房日库存
     * @return 2025药房日库存
     */
    @Override
    public List<DdiPharmacyDayInventory2025> selectDdiPharmacyDayInventory2025List(DdiPharmacyDayInventory2025 ddiPharmacyDayInventory2025) {
        String standardProductName = ddiPharmacyDayInventory2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyDayInventory2025Mapper.selectKtnDdiPharmacyDayInventory2025List(ddiPharmacyDayInventory2025);
        } else {
            return ddiPharmacyDayInventory2025Mapper.selectYdfDdiPharmacyDayInventory2025List(ddiPharmacyDayInventory2025);
        }
    }
}
