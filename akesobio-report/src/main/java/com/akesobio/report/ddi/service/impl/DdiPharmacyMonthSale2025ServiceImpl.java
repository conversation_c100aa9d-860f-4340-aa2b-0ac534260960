package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyMonthSale2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyMonthSale2025;
import com.akesobio.report.ddi.service.IDdiPharmacyMonthSale2025Service;

/**
 * 2025药房月销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiPharmacyMonthSale2025ServiceImpl implements IDdiPharmacyMonthSale2025Service {
    @Autowired
    private DdiPharmacyMonthSale2025Mapper ddiPharmacyMonthSale2025Mapper;


    /**
     * 查询2025药房月销售列表
     *
     * @param ddiPharmacyMonthSale2025 2025药房月销售
     * @return 2025药房月销售
     */
    @Override
    public List<DdiPharmacyMonthSale2025> selectDdiPharmacyMonthSale2025List(DdiPharmacyMonthSale2025 ddiPharmacyMonthSale2025) {
        String standardProductName = ddiPharmacyMonthSale2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyMonthSale2025Mapper.selectKtnDdiPharmacyMonthSale2025List(ddiPharmacyMonthSale2025);
        }else {
            return ddiPharmacyMonthSale2025Mapper.selectYdfDdiPharmacyMonthSale2025List(ddiPharmacyMonthSale2025);
        }
    }

}
