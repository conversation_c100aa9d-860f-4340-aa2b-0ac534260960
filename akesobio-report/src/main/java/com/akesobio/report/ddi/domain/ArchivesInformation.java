package com.akesobio.report.ddi.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 档案信息对象 archives_information
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public class ArchivesInformation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 机构编码
     */
    @Excel(name = "机构编码")
    private String institutionalCode;

    /**
     * 机构名称
     */
    @Excel(name = "机构名称")
    private String institutionalName;

    /**
     * 机构别名
     */
    @Excel(name = "机构别名")
    private String institutionalAlias;

    /**
     * 注册地址
     */
    @Excel(name = "注册地址")
    private String registeredAddress;

    /**
     * 注册省份
     */
    @Excel(name = "注册省份")
    private String registeredProvince;

    /**
     * 注册地市
     */
    @Excel(name = "注册地市")
    private String registeredCity;

    /**
     * 注册区县
     */
    @Excel(name = "注册区县")
    private String registeredDistrict;

    /**
     * 一级属性
     */
    @Excel(name = "一级属性")
    private String firstLevelAttribute;

    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码")
    private String unifiedSocialCreditCode;

    /**
     * 二级部门
     */
    @Excel(name = "二级部门")
    private String secondaryDepartment;

    /**
     * 三级部门
     */
    @Excel(name = "三级部门")
    private String thirdLevelDepartments;

    /**
     * 四级部门
     */
    @Excel(name = "四级部门")
    private String fourthLevelDepartment;

    /**
     * 五级部门
     */
    @Excel(name = "五级部门")
    private String fifthLevelDepartment;

    /**
     * 责任人
     */
    @Excel(name = "责任人")
    private String responsiblePerson;

    /**
     * 品种1
     */
    @Excel(name = "品种1")
    private String variety1;

    /**
     * 单价1
     */
    @Excel(name = "单价1")
    private BigDecimal unitPrice1;

    /**
     * 品种2
     */
    @Excel(name = "品种2")
    private String variety2;

    /**
     * 单价2
     */
    @Excel(name = "单价2")
    private BigDecimal unitPrice2;

    /**
     * 品种3
     */
    @Excel(name = "品种3")
    private String variety3;

    /**
     * 单价3
     */
    @Excel(name = "单价3")
    private BigDecimal unitPrice3;

    /**
     * 省份集合
     */
    @TableField(exist = false)
    private List<String> provinceList;

    public List<String> getProvinceList() {
        return provinceList;
    }

    public void setProvinceList(List<String> provinceList) {
        this.provinceList = provinceList;
    }

    public String getFirstLevelAttribute() {
        return firstLevelAttribute;
    }

    public void setFirstLevelAttribute(String firstLevelAttribute) {
        this.firstLevelAttribute = firstLevelAttribute;
    }

    public String getUnifiedSocialCreditCode() {
        return unifiedSocialCreditCode;
    }

    public void setUnifiedSocialCreditCode(String unifiedSocialCreditCode) {
        this.unifiedSocialCreditCode = unifiedSocialCreditCode;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setInstitutionalCode(String institutionalCode) {
        this.institutionalCode = institutionalCode;
    }

    public String getInstitutionalCode() {
        return institutionalCode;
    }

    public void setInstitutionalName(String institutionalName) {
        this.institutionalName = institutionalName;
    }

    public String getInstitutionalName() {
        return institutionalName;
    }

    public void setInstitutionalAlias(String institutionalAlias) {
        this.institutionalAlias = institutionalAlias;
    }

    public String getInstitutionalAlias() {
        return institutionalAlias;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredProvince(String registeredProvince) {
        this.registeredProvince = registeredProvince;
    }

    public String getRegisteredProvince() {
        return registeredProvince;
    }

    public void setRegisteredCity(String registeredCity) {
        this.registeredCity = registeredCity;
    }

    public String getRegisteredCity() {
        return registeredCity;
    }

    public void setRegisteredDistrict(String registeredDistrict) {
        this.registeredDistrict = registeredDistrict;
    }

    public String getRegisteredDistrict() {
        return registeredDistrict;
    }

    public void setSecondaryDepartment(String secondaryDepartment) {
        this.secondaryDepartment = secondaryDepartment;
    }

    public String getSecondaryDepartment() {
        return secondaryDepartment;
    }

    public void setThirdLevelDepartments(String thirdLevelDepartments) {
        this.thirdLevelDepartments = thirdLevelDepartments;
    }

    public String getThirdLevelDepartments() {
        return thirdLevelDepartments;
    }

    public void setFourthLevelDepartment(String fourthLevelDepartment) {
        this.fourthLevelDepartment = fourthLevelDepartment;
    }

    public String getFourthLevelDepartment() {
        return fourthLevelDepartment;
    }

    public void setFifthLevelDepartment(String fifthLevelDepartment) {
        this.fifthLevelDepartment = fifthLevelDepartment;
    }

    public String getFifthLevelDepartment() {
        return fifthLevelDepartment;
    }

    public String getResponsiblePerson() {
        return responsiblePerson;
    }

    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }

    public String getVariety1() {
        return variety1;
    }

    public void setVariety1(String variety1) {
        this.variety1 = variety1;
    }

    public BigDecimal getUnitPrice1() {
        return unitPrice1;
    }

    public void setUnitPrice1(BigDecimal unitPrice1) {
        this.unitPrice1 = unitPrice1;
    }

    public String getVariety2() {
        return variety2;
    }

    public void setVariety2(String variety2) {
        this.variety2 = variety2;
    }

    public BigDecimal getUnitPrice2() {
        return unitPrice2;
    }

    public void setUnitPrice2(BigDecimal unitPrice2) {
        this.unitPrice2 = unitPrice2;
    }

    public String getVariety3() {
        return variety3;
    }

    public void setVariety3(String variety3) {
        this.variety3 = variety3;
    }

    public BigDecimal getUnitPrice3() {
        return unitPrice3;
    }

    public void setUnitPrice3(BigDecimal unitPrice3) {
        this.unitPrice3 = unitPrice3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("institutionalCode", getInstitutionalCode())
                .append("institutionalName", getInstitutionalName())
                .append("institutionalAlias", getInstitutionalAlias())
                .append("registeredAddress", getRegisteredAddress())
                .append("registeredProvince", getRegisteredProvince())
                .append("registeredCity", getRegisteredCity())
                .append("registeredDistrict", getRegisteredDistrict())
                .append("secondaryDepartment", getSecondaryDepartment())
                .append("thirdLevelDepartments", getThirdLevelDepartments())
                .append("fourthLevelDepartment", getFourthLevelDepartment())
                .append("fifthLevelDepartment", getFifthLevelDepartment())
                .append("firstLevelAttribute", getFirstLevelAttribute())
                .append("unifiedSocialCreditCode", getUnifiedSocialCreditCode())
                .append("responsiblePerson", getResponsiblePerson())
                .append("variety1", getVariety1())
                .append("unitPrice1", getUnitPrice1())
                .append("variety2", getVariety2())
                .append("unitPrice2", getUnitPrice2())
                .append("variety3", getVariety3())
                .append("unitPrice3", getUnitPrice3())
                .toString();
    }
}
