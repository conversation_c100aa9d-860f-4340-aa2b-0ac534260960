package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessDayProcurement2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessDayProcurement2025;
import com.akesobio.report.ddi.service.IDdiBusinessDayProcurement2025Service;

/**
 * 2025商业日采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Service
public class DdiBusinessDayProcurement2025ServiceImpl implements IDdiBusinessDayProcurement2025Service {
    @Autowired
    private DdiBusinessDayProcurement2025Mapper ddiBusinessDayProcurement2025Mapper;


    /**
     * 查询2025商业日采购列表
     *
     * @param ddiBusinessDayProcurement2025 2025商业日采购
     * @return 2025商业日采购
     */
    @Override
    public List<DdiBusinessDayProcurement2025> selectDdiBusinessDayProcurement2025List(DdiBusinessDayProcurement2025 ddiBusinessDayProcurement2025) {
        String standardProductName = ddiBusinessDayProcurement2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessDayProcurement2025Mapper.selectKtnDdiBusinessDayProcurement2025List(ddiBusinessDayProcurement2025);
        } else {
            return ddiBusinessDayProcurement2025Mapper.selectYdfDdiBusinessDayProcurement2025List(ddiBusinessDayProcurement2025);
        }
    }

}
