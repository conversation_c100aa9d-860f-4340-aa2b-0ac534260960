package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDayInventoryMapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDayInventory;
import com.akesobio.report.ddi.service.IDdiPharmacyDayInventoryService;

/**
 * 药房日库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DdiPharmacyDayInventoryServiceImpl implements IDdiPharmacyDayInventoryService {
    @Autowired
    private DdiPharmacyDayInventoryMapper ddiPharmacyDayInventoryMapper;

    /**
     * 查询药房日库存列表
     *
     * @param ddiPharmacyDayInventory 药房日库存
     * @return 药房日库存
     */
    @Override
    public List<DdiPharmacyDayInventory> selectDdiPharmacyDayInventoryList(DdiPharmacyDayInventory ddiPharmacyDayInventory) {
        return ddiPharmacyDayInventoryMapper.selectDdiPharmacyDayInventoryList(ddiPharmacyDayInventory);
    }

}
