package com.akesobio.report.ddi.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.ddi.domain.ArchivesInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.ArchivesInformation2025Mapper;
import com.akesobio.report.ddi.domain.ArchivesInformation2025;
import com.akesobio.report.ddi.service.IArchivesInformation2025Service;

/**
 * 2025终端档案信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Service
public class ArchivesInformation2025ServiceImpl implements IArchivesInformation2025Service {

    private static final Logger log= LoggerFactory.getLogger(ArchivesInformation2025ServiceImpl.class);

    @Autowired
    private ArchivesInformation2025Mapper archivesInformation2025Mapper;

    /**
     * 查询2025终端档案信息
     *
     * @param id 2025终端档案信息主键
     * @return 2025终端档案信息
     */
    @Override
    public ArchivesInformation2025 selectArchivesInformation2025ById(Integer id) {
        return archivesInformation2025Mapper.selectArchivesInformation2025ById(id);
    }

    /**
     * 查询2025终端档案信息列表
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 2025终端档案信息
     */
    @Override
    public List<ArchivesInformation2025> selectArchivesInformation2025List(ArchivesInformation2025 archivesInformation2025) {
        return archivesInformation2025Mapper.selectArchivesInformation2025List(archivesInformation2025);
    }

    /**
     * 新增2025终端档案信息
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 结果
     */
    @Override
    public int insertArchivesInformation2025(ArchivesInformation2025 archivesInformation2025) {
        return archivesInformation2025Mapper.insertArchivesInformation2025(archivesInformation2025);
    }

    /**
     * 修改2025终端档案信息
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 结果
     */
    @Override
    public int updateArchivesInformation2025(ArchivesInformation2025 archivesInformation2025) {
        return archivesInformation2025Mapper.updateArchivesInformation2025(archivesInformation2025);
    }

    /**
     * 批量删除2025终端档案信息
     *
     * @param ids 需要删除的2025终端档案信息主键
     * @return 结果
     */
    @Override
    public int deleteArchivesInformation2025ByIds(Integer[] ids) {
        return archivesInformation2025Mapper.deleteArchivesInformation2025ByIds(ids);
    }

    /**
     * 删除2025终端档案信息信息
     *
     * @param id 2025终端档案信息主键
     * @return 结果
     */
    @Override
    public int deleteArchivesInformation2025ById(Integer id) {
        return archivesInformation2025Mapper.deleteArchivesInformation2025ById(id);
    }

    /**
     * 导入2025终端档案信息数据
     *
     * @param list            2025终端档案信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importArchivesInformation2025(List<ArchivesInformation2025> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ArchivesInformation2025 a : list) {
            try {
                archivesInformation2025Mapper.insertArchivesInformation2025(a);
                successNum++;
                successMsg.append("<br/>" + successNum + "、医院名称 " + a.getHospitalName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、医院名称 " + a.getHospitalName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
