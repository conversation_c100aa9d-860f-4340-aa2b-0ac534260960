package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.TerminalSalesPharmacy;

/**
 * 终端销售统计表-药房销售Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface ITerminalSalesPharmacyService {
    /**
     * 查询终端销售统计表-药房销售
     *
     * @param id 终端销售统计表-药房销售主键
     * @return 终端销售统计表-药房销售
     */
    public TerminalSalesPharmacy selectTerminalSalesPharmacyById(Integer id);

    /**
     * 查询终端销售统计表-药房销售列表
     *
     * @param terminalSalesPharmacy 终端销售统计表-药房销售
     * @return 终端销售统计表-药房销售集合
     */
    public List<TerminalSalesPharmacy> selectTerminalSalesPharmacyList(TerminalSalesPharmacy terminalSalesPharmacy);

    /**
     * 新增终端销售统计表-药房销售
     *
     * @param terminalSalesPharmacy 终端销售统计表-药房销售
     * @return 结果
     */
    public int insertTerminalSalesPharmacy(TerminalSalesPharmacy terminalSalesPharmacy);

    /**
     * 修改终端销售统计表-药房销售
     *
     * @param terminalSalesPharmacy 终端销售统计表-药房销售
     * @return 结果
     */
    public int updateTerminalSalesPharmacy(TerminalSalesPharmacy terminalSalesPharmacy);

    /**
     * 批量删除终端销售统计表-药房销售
     *
     * @param ids 需要删除的终端销售统计表-药房销售主键集合
     * @return 结果
     */
    public int deleteTerminalSalesPharmacyByIds(Integer[] ids);

    /**
     * 删除终端销售统计表-药房销售信息
     *
     * @param id 终端销售统计表-药房销售主键
     * @return 结果
     */
    public int deleteTerminalSalesPharmacyById(Integer id);
}
