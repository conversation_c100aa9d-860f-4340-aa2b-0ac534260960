package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.BusinessInventoryReport2025;

/**
 * 2025商业进销存报表Service接口
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface IBusinessInventoryReport2025Service {
    /**
     * 查询2025商业进销存报表
     *
     * @param id 2025商业进销存报表主键
     * @return 2025商业进销存报表
     */
    public BusinessInventoryReport2025 selectBusinessInventoryReport2025ById(Integer id);

    /**
     * 查询2025商业进销存报表列表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 2025商业进销存报表集合
     */
    public List<BusinessInventoryReport2025> selectBusinessInventoryReport2025List(BusinessInventoryReport2025 businessInventoryReport2025);

    /**
     * 新增2025商业进销存报表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 结果
     */
    public int insertBusinessInventoryReport2025(BusinessInventoryReport2025 businessInventoryReport2025);

    /**
     * 修改2025商业进销存报表
     *
     * @param businessInventoryReport2025 2025商业进销存报表
     * @return 结果
     */
    public int updateBusinessInventoryReport2025(BusinessInventoryReport2025 businessInventoryReport2025);

    /**
     * 批量删除2025商业进销存报表
     *
     * @param ids 需要删除的2025商业进销存报表主键集合
     * @return 结果
     */
    public int deleteBusinessInventoryReport2025ByIds(Integer[] ids);

    /**
     * 删除2025商业进销存报表信息
     *
     * @param id 2025商业进销存报表主键
     * @return 结果
     */
    public int deleteBusinessInventoryReport2025ById(Integer id);
}
