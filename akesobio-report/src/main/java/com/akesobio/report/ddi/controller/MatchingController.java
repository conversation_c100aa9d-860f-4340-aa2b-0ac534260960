package com.akesobio.report.ddi.controller;


import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.framework.web.service.SysPermissionService;
import com.akesobio.report.ddi.domain.*;
import com.akesobio.report.ddi.mapper.DdiMonthProcurementMapper;
import com.akesobio.report.ddi.mapper.DdiMonthSaleMapper;
import com.akesobio.report.ddi.service.IArchivesInformationService;
import com.akesobio.report.ddi.service.IDdiCustomerInformationService;
import com.akesobio.report.ddi.service.IDdiMonthProcurementService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/ddi/MatchingController")
public class MatchingController extends BaseController {
    @Autowired
    private SysPermissionService sysPermissionService;
    @Resource
    private IArchivesInformationService archivesInformationService;
    @Resource
    private IDdiCustomerInformationService ddiCustomerInformationService;
    @Resource
    private DdiMonthSaleMapper ddiMonthSaleMapper;
    @Resource
    private DdiMonthProcurementMapper ddiMonthProcurementMapper;
    @PostMapping("/saleBusinessMonth")
    public TableDataInfo saleBusinessMonth() {
        startPage();
        List<String> businessMonthList = ddiMonthSaleMapper.selectBusinessMonthList();
        JSONArray jsonArray = new JSONArray();
        for (String businessMonth : businessMonthList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("businessMonth", businessMonth);
            jsonObject.put("salesDirection", businessMonth+"_销售流向附件");
            jsonObject.put("traceabilityAssurance", businessMonth+"_码上放心附件");
            jsonArray.add(jsonObject);
        }
        return  getDataTable(jsonArray);
    }
    @PostMapping("/procurementBusinessMonth")
    public TableDataInfo procurementBusinessMonth() {
        startPage();
        List<String> businessMonthList = ddiMonthProcurementMapper.selectBusinessMonthList();
        JSONArray jsonArray = new JSONArray();
        for (String businessMonth : businessMonthList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("businessMonth", businessMonth);
            jsonObject.put("procurementDirection", businessMonth+"_采购流向附件");
            jsonObject.put("traceabilityAssurance", businessMonth+"_码上放心附件");
            jsonArray.add(jsonObject);
        }
        return  getDataTable(jsonArray);
    }
    @PostMapping("/encodesExport")
    @Log(title = "码上放心监管码导出", businessType = BusinessType.EXPORT)
    public void encodesExport(HttpServletResponse response, DdiMonthSale ddiMonthSale) throws Exception {
//        List<String> provinceList=new ArrayList<>();
        Set<String> provinceSet= new HashSet<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for(String a:roles){
            if(a.equals("3001")||a.equals("10003")){
                provinceSet.add("上海市");
            }
            if(a.equals("3002")){
                provinceSet.add("江苏省");
            }
            if(a.equals("3003")){
                provinceSet.add("安徽省");
            }
            if(a.equals("3004") ){
                provinceSet.add("浙江省");
            }
            if(a.equals("3005") ){
                provinceSet.add("江西省");
            }
            if(a.equals("3006")){
                provinceSet.add("福建省");
            }
            if(a.equals("3007")){
                provinceSet.add("广东省");
            }
            if(a.equals("3008")){
                provinceSet.add("海南省");
            }
            if(a.equals("3009")){
                provinceSet.add("贵州省");
            }
            if(a.equals("3010")){
                provinceSet.add("云南省");
            }
            if(a.equals("3011")){
                provinceSet.add("湖北省");
            }
            if(a.equals("3012")){
                provinceSet.add("湖南省");
            }
            if(a.equals("3013")){
                provinceSet.add("广西壮族自治区");
            }
            if(a.equals("3014")){
                provinceSet.add("陕西省");
            }
            if(a.equals("3015")){
                provinceSet.add("宁夏回族自治区");
            }
            if(a.equals("3016")){
                provinceSet.add("河南省");
            }
            if(a.equals("3017")){
                provinceSet.add("新疆维吾尔自治区");
            }
            if(a.equals("3018")){
                provinceSet.add("青海省");
            }
            if(a.equals("3019")){
                provinceSet.add("重庆市");
            }
            if(a.equals("3020")){
                provinceSet.add("四川省");
            }
            if(a.equals("3021")){
                provinceSet.add("甘肃省");
            }
            if(a.equals("3022")){
                provinceSet.add("北京市");
            }
            if(a.equals("3023")){
                provinceSet.add("内蒙古自治区");
            }
            if(a.equals("3024")){
                provinceSet.add("天津市");
            }
            if(a.equals("3025")){
                provinceSet.add("山西省");
            }
            if(a.equals("3026")){
                provinceSet.add("河北省");
            }
            if(a.equals("3027")){
                provinceSet.add("山东省");
            }
            if(a.equals("3028")){
                provinceSet.add("黑龙江省");
            }
            if(a.equals("3029")){
                provinceSet.add("吉林省");
            }
            if(a.equals("3030")){
                provinceSet.add("辽宁省");
            }
        }



        List<ArchivesInformation> archivesInformationList = archivesInformationService.selectArchivesInformationList(null);
        List<DdiCustomerInformation> ddiCustomerInformationList = ddiCustomerInformationService.selectDdiCustomerInformationList(null);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("archivesInformationList",archivesInformationList);
        jsonObject.put("ddiCustomerInformationList",ddiCustomerInformationList);
        jsonObject.put("businessMonth",ddiMonthSale.getBusinessMonth());
        String str = HttpUtils.sendPostJson("http://localhost:23333/datamatching/system/BaobaoBillController/encodes",jsonObject.toJSONString());
        JSONObject strJson = JSONObject.parseObject(str);
        List<Encode> encodeList = strJson.getJSONArray("result").toJavaList(Encode.class);

        encodeList = encodeList.stream().filter(e -> (provinceSet.contains(e.getReceiptProvince())
                ||provinceSet.contains(e.getShippingProvince()))).collect(Collectors.toList());
        ExcelUtil<Encode> util = new ExcelUtil<Encode>(Encode.class);
        util.exportExcel(response, encodeList, "码上放心监管码");
    }
}
