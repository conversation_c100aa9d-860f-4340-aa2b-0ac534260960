package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.PharmacyInventoryReport2025Mapper;
import com.akesobio.report.ddi.domain.PharmacyInventoryReport2025;
import com.akesobio.report.ddi.service.IPharmacyInventoryReport2025Service;

/**
 * 2025药房进销存报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Service
public class PharmacyInventoryReport2025ServiceImpl implements IPharmacyInventoryReport2025Service {
    @Autowired
    private PharmacyInventoryReport2025Mapper pharmacyInventoryReport2025Mapper;

    /**
     * 查询2025药房进销存报表
     *
     * @param id 2025药房进销存报表主键
     * @return 2025药房进销存报表
     */
    @Override
    public PharmacyInventoryReport2025 selectPharmacyInventoryReport2025ById(Integer id) {
        return pharmacyInventoryReport2025Mapper.selectPharmacyInventoryReport2025ById(id);
    }

    /**
     * 查询2025药房进销存报表列表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 2025药房进销存报表
     */
    @Override
    public List<PharmacyInventoryReport2025> selectPharmacyInventoryReport2025List(PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        return pharmacyInventoryReport2025Mapper.selectPharmacyInventoryReport2025List(pharmacyInventoryReport2025);
    }

    /**
     * 新增2025药房进销存报表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 结果
     */
    @Override
    public int insertPharmacyInventoryReport2025(PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        return pharmacyInventoryReport2025Mapper.insertPharmacyInventoryReport2025(pharmacyInventoryReport2025);
    }

    /**
     * 修改2025药房进销存报表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 结果
     */
    @Override
    public int updatePharmacyInventoryReport2025(PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        return pharmacyInventoryReport2025Mapper.updatePharmacyInventoryReport2025(pharmacyInventoryReport2025);
    }

    /**
     * 批量删除2025药房进销存报表
     *
     * @param ids 需要删除的2025药房进销存报表主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryReport2025ByIds(Integer[] ids) {
        return pharmacyInventoryReport2025Mapper.deletePharmacyInventoryReport2025ByIds(ids);
    }

    /**
     * 删除2025药房进销存报表信息
     *
     * @param id 2025药房进销存报表主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryReport2025ById(Integer id) {
        return pharmacyInventoryReport2025Mapper.deletePharmacyInventoryReport2025ById(id);
    }
}
