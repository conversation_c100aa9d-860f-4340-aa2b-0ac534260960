package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessDaySale2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessDaySale2025;
import com.akesobio.report.ddi.service.IDdiBusinessDaySale2025Service;

/**
 * 2025商业日销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-09
 */
@Service
public class DdiBusinessDaySale2025ServiceImpl implements IDdiBusinessDaySale2025Service {

    @Autowired
    private DdiBusinessDaySale2025Mapper ddiBusinessDaySale2025Mapper;

    /**
     * 查询2025商业日销售列表
     *
     * @param ddiBusinessDaySale2025 2025商业日销售
     * @return 2025商业日销售
     */
    @Override
    public List<DdiBusinessDaySale2025> selectDdiBusinessDaySale2025List(DdiBusinessDaySale2025 ddiBusinessDaySale2025) {
        String standardProductName = ddiBusinessDaySale2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessDaySale2025Mapper.selectKtnDdiBusinessDaySale2025List(ddiBusinessDaySale2025);
        } else {
            return ddiBusinessDaySale2025Mapper.selectYdfDdiBusinessDaySale2025List(ddiBusinessDaySale2025);
        }
    }
}
