package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.DdiMonthProcurement;

/**
 * 月采购Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IDdiMonthProcurementService {

    /**
     * 查询月采购列表
     *
     * @param ddiMonthProcurement 月采购
     * @return 月采购集合
     */
    public List<DdiMonthProcurement> selectDdiMonthProcurementList(DdiMonthProcurement ddiMonthProcurement);

    /**
     * 查询所有月采购列表
     *
     * @param ddiMonthProcurement 月采购
     * @return 月采购集合
     */
    public List<DdiMonthProcurement> selectAllDdiMonthProcurementList(DdiMonthProcurement ddiMonthProcurement);

}
