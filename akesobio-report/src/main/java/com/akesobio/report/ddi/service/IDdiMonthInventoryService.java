package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.DdiMonthInventory;

/**
 * 月库存Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IDdiMonthInventoryService {


    /**
     * 查询月库存列表
     *
     * @param ddiMonthInventory 月库存
     * @return 月库存集合
     */
    public List<DdiMonthInventory> selectDdiMonthInventoryList(DdiMonthInventory ddiMonthInventory);

    /**
     * 查询所有月库存列表
     *
     * @param ddiMonthInventory 月库存
     * @return 月库存集合
     */
    public List<DdiMonthInventory> selectAllDdiMonthInventoryList(DdiMonthInventory ddiMonthInventory);

}
