package com.akesobio.report.ddi.controller;

import java.text.SimpleDateFormat;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.ddi.domain.PharmacyInventoryReport2025;
import com.akesobio.report.ddi.service.IPharmacyInventoryReport2025Service;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 2025药房进销存报表Controller
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@RestController
@RequestMapping("/ddi/pharmacyInventoryReport2025")
public class PharmacyInventoryReport2025Controller extends BaseController {
    @Autowired
    private IPharmacyInventoryReport2025Service pharmacyInventoryReport2025Service;

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 查询2025药房进销存报表列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:list')")
    @GetMapping("/list")
    public TableDataInfo list(PharmacyInventoryReport2025 pharmacyInventoryReport2025) {

        List<String> provinceList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if(provinceList.size()>0){
            pharmacyInventoryReport2025.setProvinceList(provinceList);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar lastCalendar = Calendar.getInstance();
        lastCalendar.setTime(new Date());
        lastCalendar.add(Calendar.MONTH, -1);
        String lastYearMonth = sdf.format(lastCalendar.getTime()).replace("-", "");
        if(lastYearMonth!=null){
            pharmacyInventoryReport2025.setYearMonth(lastYearMonth);
        }
        startPage();
        List<PharmacyInventoryReport2025> list = pharmacyInventoryReport2025Service.selectPharmacyInventoryReport2025List(pharmacyInventoryReport2025);
        return getDataTable(list);
    }

    /**
     * 导出2025药房进销存报表列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:export')")
    @Log(title = "2025药房进销存报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        List<String> provinceList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if(provinceList.size()>0){
            pharmacyInventoryReport2025.setProvinceList(provinceList);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar lastCalendar = Calendar.getInstance();
        lastCalendar.setTime(new Date());
        lastCalendar.add(Calendar.MONTH, -1);
        String lastYearMonth = sdf.format(lastCalendar.getTime()).replace("-", "");
        if(lastYearMonth!=null){
            pharmacyInventoryReport2025.setYearMonth(lastYearMonth);
        }

        List<PharmacyInventoryReport2025> list = pharmacyInventoryReport2025Service.selectPharmacyInventoryReport2025List(pharmacyInventoryReport2025);
        ExcelUtil<PharmacyInventoryReport2025> util = new ExcelUtil<PharmacyInventoryReport2025>(PharmacyInventoryReport2025.class);
        util.exportExcel(response, list, "2025药房进销存报表数据");
    }

    /**
     * 获取2025药房进销存报表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(pharmacyInventoryReport2025Service.selectPharmacyInventoryReport2025ById(id));
    }

    /**
     * 新增2025药房进销存报表
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:add')")
    @Log(title = "2025药房进销存报表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        return toAjax(pharmacyInventoryReport2025Service.insertPharmacyInventoryReport2025(pharmacyInventoryReport2025));
    }

    /**
     * 修改2025药房进销存报表
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:edit')")
    @Log(title = "2025药房进销存报表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PharmacyInventoryReport2025 pharmacyInventoryReport2025) {
        return toAjax(pharmacyInventoryReport2025Service.updatePharmacyInventoryReport2025(pharmacyInventoryReport2025));
    }

    /**
     * 删除2025药房进销存报表
     */
    @PreAuthorize("@ss.hasPermi('ddi:pharmacyInventoryReport2025:remove')")
    @Log(title = "2025药房进销存报表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(pharmacyInventoryReport2025Service.deletePharmacyInventoryReport2025ByIds(ids));
    }
}
