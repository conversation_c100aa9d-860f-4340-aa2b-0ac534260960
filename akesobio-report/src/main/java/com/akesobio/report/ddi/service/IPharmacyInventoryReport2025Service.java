package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.PharmacyInventoryReport2025;

/**
 * 2025药房进销存报表Service接口
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface IPharmacyInventoryReport2025Service {
    /**
     * 查询2025药房进销存报表
     *
     * @param id 2025药房进销存报表主键
     * @return 2025药房进销存报表
     */
    public PharmacyInventoryReport2025 selectPharmacyInventoryReport2025ById(Integer id);

    /**
     * 查询2025药房进销存报表列表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 2025药房进销存报表集合
     */
    public List<PharmacyInventoryReport2025> selectPharmacyInventoryReport2025List(PharmacyInventoryReport2025 pharmacyInventoryReport2025);

    /**
     * 新增2025药房进销存报表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 结果
     */
    public int insertPharmacyInventoryReport2025(PharmacyInventoryReport2025 pharmacyInventoryReport2025);

    /**
     * 修改2025药房进销存报表
     *
     * @param pharmacyInventoryReport2025 2025药房进销存报表
     * @return 结果
     */
    public int updatePharmacyInventoryReport2025(PharmacyInventoryReport2025 pharmacyInventoryReport2025);

    /**
     * 批量删除2025药房进销存报表
     *
     * @param ids 需要删除的2025药房进销存报表主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryReport2025ByIds(Integer[] ids);

    /**
     * 删除2025药房进销存报表信息
     *
     * @param id 2025药房进销存报表主键
     * @return 结果
     */
    public int deletePharmacyInventoryReport2025ById(Integer id);
}
