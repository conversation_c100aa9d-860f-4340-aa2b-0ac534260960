package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.TerminalSalesHospital;

/**
 * 终端销售统计表-医院购进Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface ITerminalSalesHospitalService {
    /**
     * 查询终端销售统计表-医院购进
     *
     * @param id 终端销售统计表-医院购进主键
     * @return 终端销售统计表-医院购进
     */
    public TerminalSalesHospital selectTerminalSalesHospitalById(Integer id);

    /**
     * 查询终端销售统计表-医院购进列表
     *
     * @param terminalSalesHospital 终端销售统计表-医院购进
     * @return 终端销售统计表-医院购进集合
     */
    public List<TerminalSalesHospital> selectTerminalSalesHospitalList(TerminalSalesHospital terminalSalesHospital);

    /**
     * 新增终端销售统计表-医院购进
     *
     * @param terminalSalesHospital 终端销售统计表-医院购进
     * @return 结果
     */
    public int insertTerminalSalesHospital(TerminalSalesHospital terminalSalesHospital);

    /**
     * 修改终端销售统计表-医院购进
     *
     * @param terminalSalesHospital 终端销售统计表-医院购进
     * @return 结果
     */
    public int updateTerminalSalesHospital(TerminalSalesHospital terminalSalesHospital);

    /**
     * 批量删除终端销售统计表-医院购进
     *
     * @param ids 需要删除的终端销售统计表-医院购进主键集合
     * @return 结果
     */
    public int deleteTerminalSalesHospitalByIds(Integer[] ids);

    /**
     * 删除终端销售统计表-医院购进信息
     *
     * @param id 终端销售统计表-医院购进主键
     * @return 结果
     */
    public int deleteTerminalSalesHospitalById(Integer id);
}
