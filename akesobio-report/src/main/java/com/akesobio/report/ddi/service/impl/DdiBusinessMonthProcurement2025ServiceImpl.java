package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessMonthProcurement2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessMonthProcurement2025;
import com.akesobio.report.ddi.service.IDdiBusinessMonthProcurement2025Service;

/**
 * 2025商业月采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Service
public class DdiBusinessMonthProcurement2025ServiceImpl implements IDdiBusinessMonthProcurement2025Service {
    @Autowired
    private DdiBusinessMonthProcurement2025Mapper ddiBusinessMonthProcurement2025Mapper;


    /**
     * 查询2025商业月采购列表
     *
     * @param ddiBusinessMonthProcurement2025 2025商业月采购
     * @return 2025商业月采购
     */
    @Override
    public List<DdiBusinessMonthProcurement2025> selectDdiBusinessMonthProcurement2025List(DdiBusinessMonthProcurement2025 ddiBusinessMonthProcurement2025) {
        String standardProductName = ddiBusinessMonthProcurement2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessMonthProcurement2025Mapper.selectKtnDdiBusinessMonthProcurement2025List(ddiBusinessMonthProcurement2025);
        } else {
            return ddiBusinessMonthProcurement2025Mapper.selectYdfDdiBusinessMonthProcurement2025List(ddiBusinessMonthProcurement2025);
        }

    }
}
