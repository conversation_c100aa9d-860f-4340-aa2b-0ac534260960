package com.akesobio.report.ddi.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.RfExpenses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiCustomerInformationMapper;
import com.akesobio.report.ddi.domain.DdiCustomerInformation;
import com.akesobio.report.ddi.service.IDdiCustomerInformationService;

/**
 * DDI客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Service
public class DdiCustomerInformationServiceImpl implements IDdiCustomerInformationService {

    private static final Logger log = LoggerFactory.getLogger(DdiCustomerInformationServiceImpl.class);

    @Autowired
    private DdiCustomerInformationMapper ddiCustomerInformationMapper;

    /**
     * 查询DDI客户信息
     *
     * @param id DDI客户信息主键
     * @return DDI客户信息
     */
    @Override
    public DdiCustomerInformation selectDdiCustomerInformationById(Integer id) {
        return ddiCustomerInformationMapper.selectDdiCustomerInformationById(id);
    }

    /**
     * 查询DDI客户信息列表
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return DDI客户信息
     */
    @Override
    public List<DdiCustomerInformation> selectDdiCustomerInformationList(DdiCustomerInformation ddiCustomerInformation) {
        return ddiCustomerInformationMapper.selectDdiCustomerInformationList(ddiCustomerInformation);
    }

    /**
     * 新增DDI客户信息
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return 结果
     */
    @Override
    public int insertDdiCustomerInformation(DdiCustomerInformation ddiCustomerInformation) {
        return ddiCustomerInformationMapper.insertDdiCustomerInformation(ddiCustomerInformation);
    }

    /**
     * 修改DDI客户信息
     *
     * @param ddiCustomerInformation DDI客户信息
     * @return 结果
     */
    @Override
    public int updateDdiCustomerInformation(DdiCustomerInformation ddiCustomerInformation) {
        return ddiCustomerInformationMapper.updateDdiCustomerInformation(ddiCustomerInformation);
    }

    /**
     * 批量删除DDI客户信息
     *
     * @param ids 需要删除的DDI客户信息主键
     * @return 结果
     */
    @Override
    public int deleteDdiCustomerInformationByIds(Integer[] ids) {
        return ddiCustomerInformationMapper.deleteDdiCustomerInformationByIds(ids);
    }

    /**
     * 删除DDI客户信息信息
     *
     * @param id DDI客户信息主键
     * @return 结果
     */
    @Override
    public int deleteDdiCustomerInformationById(Integer id) {
        return ddiCustomerInformationMapper.deleteDdiCustomerInformationById(id);
    }

    /**
     * 导入DDI客户信息数据
     *
     * @param list            DDI客户信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importDdiCustomerInformation(List<DdiCustomerInformation> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DdiCustomerInformation ddiCustomerInformation : list) {
            try {
                ddiCustomerInformationMapper.insertDdiCustomerInformation(ddiCustomerInformation);
                successNum++;
                successMsg.append("<br/>" + successNum + "、客户名称 " + ddiCustomerInformation.getCustomerName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户名称 " + ddiCustomerInformation.getCustomerName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
