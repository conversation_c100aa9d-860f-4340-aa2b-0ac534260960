package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.NoTargetHospitalDailyProcurementMapper;
import com.akesobio.report.ddi.domain.NoTargetHospitalDailyProcurement;
import com.akesobio.report.ddi.service.INoTargetHospitalDailyProcurementService;

/**
 * 非目标医院日采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Service
public class NoTargetHospitalDailyProcurementServiceImpl implements INoTargetHospitalDailyProcurementService {
    @Autowired
    private NoTargetHospitalDailyProcurementMapper noTargetHospitalDailyProcurementMapper;


    /**
     * 查询非目标医院日采购列表
     *
     * @param noTargetHospitalDailyProcurement 非目标医院日采购
     * @return 非目标医院日采购
     */
    @Override
    public List<NoTargetHospitalDailyProcurement> selectNoTargetHospitalDailyProcurementList(NoTargetHospitalDailyProcurement noTargetHospitalDailyProcurement) {
        String standardProductName = noTargetHospitalDailyProcurement.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return noTargetHospitalDailyProcurementMapper.selectKtnNoTargetHospitalDailyProcurementList(noTargetHospitalDailyProcurement);
        } else {
            return noTargetHospitalDailyProcurementMapper.selectYdfNoTargetHospitalDailyProcurementList(noTargetHospitalDailyProcurement);
        }
    }
}
