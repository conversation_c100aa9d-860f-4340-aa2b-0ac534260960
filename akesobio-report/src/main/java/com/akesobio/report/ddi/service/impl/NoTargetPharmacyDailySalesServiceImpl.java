package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.NoTargetPharmacyDailySalesMapper;
import com.akesobio.report.ddi.domain.NoTargetPharmacyDailySales;
import com.akesobio.report.ddi.service.INoTargetPharmacyDailySalesService;

/**
 * 非目标药房日销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Service
public class NoTargetPharmacyDailySalesServiceImpl implements INoTargetPharmacyDailySalesService {
    @Autowired
    private NoTargetPharmacyDailySalesMapper noTargetPharmacyDailySalesMapper;


    /**
     * 查询非目标药房日销售列表
     *
     * @param noTargetPharmacyDailySales 非目标药房日销售
     * @return 非目标药房日销售
     */
    @Override
    public List<NoTargetPharmacyDailySales> selectNoTargetPharmacyDailySalesList(NoTargetPharmacyDailySales noTargetPharmacyDailySales) {
        String standardProductName = noTargetPharmacyDailySales.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return noTargetPharmacyDailySalesMapper.selectKtnNoTargetPharmacyDailySalesList(noTargetPharmacyDailySales);
        } else {
            return noTargetPharmacyDailySalesMapper.selectYdfNoTargetPharmacyDailySalesList(noTargetPharmacyDailySales);
        }
    }

}
