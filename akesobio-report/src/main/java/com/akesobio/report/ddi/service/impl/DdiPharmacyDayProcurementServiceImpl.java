package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDayProcurementMapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDayProcurement;
import com.akesobio.report.ddi.service.IDdiPharmacyDayProcurementService;

/**
 * 药房日采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DdiPharmacyDayProcurementServiceImpl implements IDdiPharmacyDayProcurementService {
    @Autowired
    private DdiPharmacyDayProcurementMapper ddiPharmacyDayProcurementMapper;


    /**
     * 查询药房日采购列表
     *
     * @param ddiPharmacyDayProcurement 药房日采购
     * @return 药房日采购
     */
    @Override
    public List<DdiPharmacyDayProcurement> selectDdiPharmacyDayProcurementList(DdiPharmacyDayProcurement ddiPharmacyDayProcurement) {
        return ddiPharmacyDayProcurementMapper.selectDdiPharmacyDayProcurementList(ddiPharmacyDayProcurement);
    }

}
