package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.TerminalSalesProvince;

/**
 * 终端销售统计表-省份Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface ITerminalSalesProvinceService {
    /**
     * 查询终端销售统计表-省份
     *
     * @param id 终端销售统计表-省份主键
     * @return 终端销售统计表-省份
     */
    public TerminalSalesProvince selectTerminalSalesProvinceById(Integer id);

    /**
     * 查询终端销售统计表-省份列表
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 终端销售统计表-省份集合
     */
    public List<TerminalSalesProvince> selectTerminalSalesProvinceList(TerminalSalesProvince terminalSalesProvince);

    /**
     * 新增终端销售统计表-省份
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 结果
     */
    public int insertTerminalSalesProvince(TerminalSalesProvince terminalSalesProvince);

    /**
     * 修改终端销售统计表-省份
     *
     * @param terminalSalesProvince 终端销售统计表-省份
     * @return 结果
     */
    public int updateTerminalSalesProvince(TerminalSalesProvince terminalSalesProvince);

    /**
     * 批量删除终端销售统计表-省份
     *
     * @param ids 需要删除的终端销售统计表-省份主键集合
     * @return 结果
     */
    public int deleteTerminalSalesProvinceByIds(Integer[] ids);

    /**
     * 删除终端销售统计表-省份信息
     *
     * @param id 终端销售统计表-省份主键
     * @return 结果
     */
    public int deleteTerminalSalesProvinceById(Integer id);
}
