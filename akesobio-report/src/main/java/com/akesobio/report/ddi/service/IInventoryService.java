package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.Inventory;

/**
 * DDI-库存Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IInventoryService {
    /**
     * 查询DDI-库存
     *
     * @param id DDI-库存主键
     * @return DDI-库存
     */
    public Inventory selectInventoryById(Integer id);

    /**
     * 查询DDI-库存列表
     *
     * @param inventory DDI-库存
     * @return DDI-库存集合
     */
    public List<Inventory> selectInventoryList(Inventory inventory);

    /**
     * 新增DDI-库存
     *
     * @param inventory DDI-库存
     * @return 结果
     */
    public int insertInventory(Inventory inventory);

    /**
     * 修改DDI-库存
     *
     * @param inventory DDI-库存
     * @return 结果
     */
    public int updateInventory(Inventory inventory);

    /**
     * 批量删除DDI-库存
     *
     * @param ids 需要删除的DDI-库存主键集合
     * @return 结果
     */
    public int deleteInventoryByIds(Integer[] ids);

    /**
     * 删除DDI-库存信息
     *
     * @param id DDI-库存主键
     * @return 结果
     */
    public int deleteInventoryById(Integer id);
}
