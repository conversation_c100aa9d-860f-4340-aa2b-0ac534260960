package com.akesobio.report.ddi.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.ddi.domain.DdiCustomerInformation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.ddi.domain.RegionPerformance;
import com.akesobio.report.ddi.service.IRegionPerformanceService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 地区业绩Controller
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@RestController
@RequestMapping("/ddi/regionPerformance")
public class RegionPerformanceController extends BaseController {
    @Autowired
    private IRegionPerformanceService regionPerformanceService;

    /**
     * 查询地区业绩列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:list')")
    @GetMapping("/list")
    public TableDataInfo list(RegionPerformance regionPerformance) {
        startPage();
        List<RegionPerformance> list = regionPerformanceService.selectRegionPerformanceList(regionPerformance);
        return getDataTable(list);
    }

    /**
     * 导出地区业绩列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:export')")
    @Log(title = "地区业绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RegionPerformance regionPerformance) {
        List<RegionPerformance> list = regionPerformanceService.selectRegionPerformanceList(regionPerformance);
        ExcelUtil<RegionPerformance> util = new ExcelUtil<RegionPerformance>(RegionPerformance.class);
        util.exportExcel(response, list, "地区业绩数据");
    }

    /**
     * 获取地区业绩详细信息
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(regionPerformanceService.selectRegionPerformanceById(id));
    }

    /**
     * 新增地区业绩
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:add')")
    @Log(title = "地区业绩", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RegionPerformance regionPerformance) {
        return toAjax(regionPerformanceService.insertRegionPerformance(regionPerformance));
    }

    /**
     * 修改地区业绩
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:edit')")
    @Log(title = "地区业绩", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RegionPerformance regionPerformance) {
        return toAjax(regionPerformanceService.updateRegionPerformance(regionPerformance));
    }

    /**
     * 删除地区业绩
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:remove')")
    @Log(title = "地区业绩", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(regionPerformanceService.deleteRegionPerformanceByIds(ids));
    }

    /**
     * 导入地区业绩数据
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:import')")
    @Log(title = "地区业绩数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<RegionPerformance> util = new ExcelUtil<RegionPerformance>(RegionPerformance.class);
        List<RegionPerformance> list = util.importExcel(file.getInputStream());
        String message = regionPerformanceService.importRegionPerformance(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     *
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('ddi:regionPerformance:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RegionPerformance> util = new ExcelUtil<RegionPerformance>(RegionPerformance.class);
        util.importTemplateExcel(response, "地区业绩数据");
    }
}
