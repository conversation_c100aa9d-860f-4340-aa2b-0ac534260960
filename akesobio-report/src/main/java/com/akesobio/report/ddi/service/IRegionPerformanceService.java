package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.ProvincePerformance;
import com.akesobio.report.ddi.domain.RegionPerformance;

/**
 * 地区业绩Service接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IRegionPerformanceService {
    /**
     * 查询地区业绩
     *
     * @param id 地区业绩主键
     * @return 地区业绩
     */
    public RegionPerformance selectRegionPerformanceById(Integer id);

    /**
     * 查询地区业绩列表
     *
     * @param regionPerformance 地区业绩
     * @return 地区业绩集合
     */
    public List<RegionPerformance> selectRegionPerformanceList(RegionPerformance regionPerformance);

    /**
     * 新增地区业绩
     *
     * @param regionPerformance 地区业绩
     * @return 结果
     */
    public int insertRegionPerformance(RegionPerformance regionPerformance);

    /**
     * 修改地区业绩
     *
     * @param regionPerformance 地区业绩
     * @return 结果
     */
    public int updateRegionPerformance(RegionPerformance regionPerformance);

    /**
     * 批量删除地区业绩
     *
     * @param ids 需要删除的地区业绩主键集合
     * @return 结果
     */
    public int deleteRegionPerformanceByIds(Integer[] ids);

    /**
     * 删除地区业绩信息
     *
     * @param id 地区业绩主键
     * @return 结果
     */
    public int deleteRegionPerformanceById(Integer id);

    /**
     * 导入地区业绩信息
     *
     * @param list           地区业绩信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importRegionPerformance(List<RegionPerformance> list, Boolean isUpdateSupport);
}
