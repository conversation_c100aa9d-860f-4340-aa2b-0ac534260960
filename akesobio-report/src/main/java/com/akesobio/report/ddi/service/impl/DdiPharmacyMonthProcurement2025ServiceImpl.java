package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyMonthProcurement2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyMonthProcurement2025;
import com.akesobio.report.ddi.service.IDdiPharmacyMonthProcurement2025Service;

/**
 * 2025药房月采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiPharmacyMonthProcurement2025ServiceImpl implements IDdiPharmacyMonthProcurement2025Service {
    @Autowired
    private DdiPharmacyMonthProcurement2025Mapper ddiPharmacyMonthProcurement2025Mapper;


    /**
     * 查询2025药房月采购列表
     *
     * @param ddiPharmacyMonthProcurement2025 2025药房月采购
     * @return 2025药房月采购
     */
    @Override
    public List<DdiPharmacyMonthProcurement2025> selectDdiPharmacyMonthProcurement2025List(DdiPharmacyMonthProcurement2025 ddiPharmacyMonthProcurement2025) {
        String standardProductName = ddiPharmacyMonthProcurement2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyMonthProcurement2025Mapper.selectKtnDdiPharmacyMonthProcurement2025List(ddiPharmacyMonthProcurement2025);
        } else {
            return ddiPharmacyMonthProcurement2025Mapper.selectYdfDdiPharmacyMonthProcurement2025List(ddiPharmacyMonthProcurement2025);
        }
    }
}
