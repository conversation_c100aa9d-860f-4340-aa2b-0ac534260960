package com.akesobio.report.ddi.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.ddi.domain.DdiCustomerInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.ArchivesInformationMapper;
import com.akesobio.report.ddi.domain.ArchivesInformation;
import com.akesobio.report.ddi.service.IArchivesInformationService;

/**
 * 档案信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class ArchivesInformationServiceImpl implements IArchivesInformationService {

    private static final Logger log= LoggerFactory.getLogger(ArchivesInformationServiceImpl.class);

    @Autowired
    private ArchivesInformationMapper archivesInformationMapper;

    /**
     * 查询档案信息
     *
     * @param id 档案信息主键
     * @return 档案信息
     */
    @Override
    public ArchivesInformation selectArchivesInformationById(Integer id) {
        return archivesInformationMapper.selectArchivesInformationById(id);
    }

    /**
     * 查询档案信息列表
     *
     * @param archivesInformation 档案信息
     * @return 档案信息
     */
    @Override
    public List<ArchivesInformation> selectArchivesInformationList(ArchivesInformation archivesInformation) {
        return archivesInformationMapper.selectArchivesInformationList(archivesInformation);
    }

    /**
     * 新增档案信息
     *
     * @param archivesInformation 档案信息
     * @return 结果
     */
    @Override
    public int insertArchivesInformation(ArchivesInformation archivesInformation) {
        return archivesInformationMapper.insertArchivesInformation(archivesInformation);
    }

    /**
     * 修改档案信息
     *
     * @param archivesInformation 档案信息
     * @return 结果
     */
    @Override
    public int updateArchivesInformation(ArchivesInformation archivesInformation) {
        return archivesInformationMapper.updateArchivesInformation(archivesInformation);
    }

    /**
     * 批量删除档案信息
     *
     * @param ids 需要删除的档案信息主键
     * @return 结果
     */
    @Override
    public int deleteArchivesInformationByIds(Integer[] ids) {
        return archivesInformationMapper.deleteArchivesInformationByIds(ids);
    }

    /**
     * 删除档案信息信息
     *
     * @param id 档案信息主键
     * @return 结果
     */
    @Override
    public int deleteArchivesInformationById(Integer id) {
        return archivesInformationMapper.deleteArchivesInformationById(id);
    }

    /**
     * 导入档案信息数据
     *
     * @param list            档案信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importArchivesInformation(List<ArchivesInformation> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ArchivesInformation a : list) {
            try {
                archivesInformationMapper.insertArchivesInformation(a);
                successNum++;
                successMsg.append("<br/>" + successNum + "、机构名称 " + a.getInstitutionalName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、机构名称 " + a.getInstitutionalName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
