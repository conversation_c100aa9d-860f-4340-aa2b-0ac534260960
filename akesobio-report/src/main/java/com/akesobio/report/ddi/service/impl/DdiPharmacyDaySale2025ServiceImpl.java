package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDaySale2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDaySale2025;
import com.akesobio.report.ddi.service.IDdiPharmacyDaySale2025Service;

/**
 * 2025药房日销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Service
public class DdiPharmacyDaySale2025ServiceImpl implements IDdiPharmacyDaySale2025Service {

    @Autowired
    private DdiPharmacyDaySale2025Mapper ddiPharmacyDaySale2025Mapper;


    /**
     * 查询2025药房日销售列表
     *
     * @param ddiPharmacyDaySale2025 2025药房日销售
     * @return 2025药房日销售
     */
    @Override
    public List<DdiPharmacyDaySale2025> selectDdiPharmacyDaySale2025List(DdiPharmacyDaySale2025 ddiPharmacyDaySale2025) {
        String standardProductName = ddiPharmacyDaySale2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyDaySale2025Mapper.selectKtnDdiPharmacyDaySale2025List(ddiPharmacyDaySale2025);
        } else {
            return ddiPharmacyDaySale2025Mapper.selectYdfDdiPharmacyDaySale2025List(ddiPharmacyDaySale2025);
        }
    }

}
