package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.PharmacyInventoryReport;

/**
 * 药房进销存报表Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IPharmacyInventoryReportService {
    /**
     * 查询药房进销存报表
     *
     * @param id 药房进销存报表主键
     * @return 药房进销存报表
     */
    public PharmacyInventoryReport selectPharmacyInventoryReportById(Integer id);

    /**
     * 查询药房进销存报表列表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 药房进销存报表集合
     */
    public List<PharmacyInventoryReport> selectPharmacyInventoryReportList(PharmacyInventoryReport pharmacyInventoryReport);

    /**
     * 新增药房进销存报表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 结果
     */
    public int insertPharmacyInventoryReport(PharmacyInventoryReport pharmacyInventoryReport);

    /**
     * 修改药房进销存报表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 结果
     */
    public int updatePharmacyInventoryReport(PharmacyInventoryReport pharmacyInventoryReport);

    /**
     * 批量删除药房进销存报表
     *
     * @param ids 需要删除的药房进销存报表主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryReportByIds(Integer[] ids);

    /**
     * 删除药房进销存报表信息
     *
     * @param id 药房进销存报表主键
     * @return 结果
     */
    public int deletePharmacyInventoryReportById(Integer id);
}
