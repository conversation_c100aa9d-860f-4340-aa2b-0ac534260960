package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.ArchivesInformation;
import com.akesobio.report.ddi.domain.ArchivesInformation2025;

/**
 * 2025终端档案信息Service接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface IArchivesInformation2025Service {
    /**
     * 查询2025终端档案信息
     *
     * @param id 2025终端档案信息主键
     * @return 2025终端档案信息
     */
    public ArchivesInformation2025 selectArchivesInformation2025ById(Integer id);

    /**
     * 查询2025终端档案信息列表
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 2025终端档案信息集合
     */
    public List<ArchivesInformation2025> selectArchivesInformation2025List(ArchivesInformation2025 archivesInformation2025);

    /**
     * 新增2025终端档案信息
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 结果
     */
    public int insertArchivesInformation2025(ArchivesInformation2025 archivesInformation2025);

    /**
     * 修改2025终端档案信息
     *
     * @param archivesInformation2025 2025终端档案信息
     * @return 结果
     */
    public int updateArchivesInformation2025(ArchivesInformation2025 archivesInformation2025);

    /**
     * 批量删除2025终端档案信息
     *
     * @param ids 需要删除的2025终端档案信息主键集合
     * @return 结果
     */
    public int deleteArchivesInformation2025ByIds(Integer[] ids);

    /**
     * 删除2025终端档案信息信息
     *
     * @param id 2025终端档案信息主键
     * @return 结果
     */
    public int deleteArchivesInformation2025ById(Integer id);

    /**
     * 导入2025终端档案信息数据
     *
     * @param list            2025终端档案信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importArchivesInformation2025(List<ArchivesInformation2025> list, Boolean isUpdateSupport);
}
