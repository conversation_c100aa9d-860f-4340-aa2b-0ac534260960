package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.PharmacyInventoryReportMapper;
import com.akesobio.report.ddi.domain.PharmacyInventoryReport;
import com.akesobio.report.ddi.service.IPharmacyInventoryReportService;

/**
 * 药房进销存报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class PharmacyInventoryReportServiceImpl implements IPharmacyInventoryReportService {
    @Autowired
    private PharmacyInventoryReportMapper pharmacyInventoryReportMapper;

    /**
     * 查询药房进销存报表
     *
     * @param id 药房进销存报表主键
     * @return 药房进销存报表
     */
    @Override
    public PharmacyInventoryReport selectPharmacyInventoryReportById(Integer id) {
        return pharmacyInventoryReportMapper.selectPharmacyInventoryReportById(id);
    }

    /**
     * 查询药房进销存报表列表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 药房进销存报表
     */
    @Override
    public List<PharmacyInventoryReport> selectPharmacyInventoryReportList(PharmacyInventoryReport pharmacyInventoryReport) {
        return pharmacyInventoryReportMapper.selectPharmacyInventoryReportList(pharmacyInventoryReport);
    }

    /**
     * 新增药房进销存报表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 结果
     */
    @Override
    public int insertPharmacyInventoryReport(PharmacyInventoryReport pharmacyInventoryReport) {
        return pharmacyInventoryReportMapper.insertPharmacyInventoryReport(pharmacyInventoryReport);
    }

    /**
     * 修改药房进销存报表
     *
     * @param pharmacyInventoryReport 药房进销存报表
     * @return 结果
     */
    @Override
    public int updatePharmacyInventoryReport(PharmacyInventoryReport pharmacyInventoryReport) {
        return pharmacyInventoryReportMapper.updatePharmacyInventoryReport(pharmacyInventoryReport);
    }

    /**
     * 批量删除药房进销存报表
     *
     * @param ids 需要删除的药房进销存报表主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryReportByIds(Integer[] ids) {
        return pharmacyInventoryReportMapper.deletePharmacyInventoryReportByIds(ids);
    }

    /**
     * 删除药房进销存报表信息
     *
     * @param id 药房进销存报表主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryReportById(Integer id) {
        return pharmacyInventoryReportMapper.deletePharmacyInventoryReportById(id);
    }
}
