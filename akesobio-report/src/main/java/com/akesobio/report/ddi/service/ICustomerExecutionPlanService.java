package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.CustomerExecutionPlan;
import com.akesobio.report.ddi.domain.DdiCustomerInformation;

/**
 * 客户执行计划Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface ICustomerExecutionPlanService {
    /**
     * 查询客户执行计划
     *
     * @param id 客户执行计划主键
     * @return 客户执行计划
     */
    public CustomerExecutionPlan selectCustomerExecutionPlanById(Integer id);

    /**
     * 查询客户执行计划列表
     *
     * @param customerExecutionPlan 客户执行计划
     * @return 客户执行计划集合
     */
    public List<CustomerExecutionPlan> selectCustomerExecutionPlanList(CustomerExecutionPlan customerExecutionPlan);

    /**
     * 新增客户执行计划
     *
     * @param customerExecutionPlan 客户执行计划
     * @return 结果
     */
    public int insertCustomerExecutionPlan(CustomerExecutionPlan customerExecutionPlan);

    /**
     * 修改客户执行计划
     *
     * @param customerExecutionPlan 客户执行计划
     * @return 结果
     */
    public int updateCustomerExecutionPlan(CustomerExecutionPlan customerExecutionPlan);

    /**
     * 批量删除客户执行计划
     *
     * @param ids 需要删除的客户执行计划主键集合
     * @return 结果
     */
    public int deleteCustomerExecutionPlanByIds(Integer[] ids);

    /**
     * 删除客户执行计划信息
     *
     * @param id 客户执行计划主键
     * @return 结果
     */
    public int deleteCustomerExecutionPlanById(Integer id);

    /**
     * 导入客户执行计划数据
     *
     * @param list            客户执行计划数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importCustomerExecutionPlan(List<CustomerExecutionPlan> list, Boolean isUpdateSupport);
}
