package com.akesobio.report.ddi.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.ddi.domain.DdiPharmacyDayInventory2025;
import com.akesobio.report.ddi.service.IDdiPharmacyDayInventory2025Service;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 2025药房日库存Controller
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@RestController
@RequestMapping("/ddi/ddiPharmacyDayInventory2025")
public class DdiPharmacyDayInventory2025Controller extends BaseController {
    @Autowired
    private IDdiPharmacyDayInventory2025Service ddiPharmacyDayInventory2025Service;

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 查询2025药房日库存列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiPharmacyDayInventory2025:list')")
    @GetMapping("/list")
    public TableDataInfo list(DdiPharmacyDayInventory2025 ddiPharmacyDayInventory2025) {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);

        /**
         * 2025年商业运营架构
         * 角色对应省份
         */
        List<String> provinceList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应总监区
         */
        List<String> directorAreaList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应大区
         */
        List<String> regionList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应地区
         */
        List<String> areaList = new ArrayList<>();


        for (String a : roles) {
            /** 省份 */
            if(a.equals("23001")){
                provinceList.add("上海市");
            }
            if(a.equals("23002")){
                provinceList.add("江苏省");
            }
            if(a.equals("23003")){
                provinceList.add("安徽省");
            }
            if(a.equals("23004")){
                provinceList.add("浙江省");
            }
            if(a.equals("23005")){
                provinceList.add("江西省");
            }
            if(a.equals("23006")){
                provinceList.add("福建省");
            }
            if(a.equals("23007")){
                provinceList.add("广东省");
            }
            if(a.equals("23008")){
                provinceList.add("海南省");
            }
            if(a.equals("23009")){
                provinceList.add("广西壮族自治区");
            }
            if(a.equals("23010")){
                provinceList.add("湖北省");
            }
            if(a.equals("23011")){
                provinceList.add("湖南省");
            }
            if(a.equals("23012")){
                provinceList.add("贵州省");
            }
            if(a.equals("23013")){
                provinceList.add("云南省");
            }
            if(a.equals("23014")){
                provinceList.add("河南省");
            }
            if(a.equals("23015")){
                provinceList.add("陕西省");
            }
            if(a.equals("23016")){
                provinceList.add("甘肃省");
            }
            if(a.equals("23017")){
                provinceList.add("宁夏回族自治区");
            }
            if(a.equals("23018")){
                provinceList.add("新疆维吾尔自治区");
            }
            if(a.equals("23019")){
                provinceList.add("青海省");
            }
            if(a.equals("23020")){
                provinceList.add("重庆市");
            }
            if(a.equals("23021")){
                provinceList.add("四川省");
            }
            if(a.equals("23022")){
                provinceList.add("西藏自治区");
            }
            if(a.equals("23023")){
                provinceList.add("北京市");
            }
            if(a.equals("23024")){
                provinceList.add("天津市");
            }
            if(a.equals("23025")){
                provinceList.add("河北省");
            }
            if(a.equals("23026")){
                provinceList.add("内蒙古自治区");
            }
            if(a.equals("23027")){
                provinceList.add("山西省");
            }
            if(a.equals("23028")){
                provinceList.add("山东省");
            }
            if(a.equals("23029")){
                provinceList.add("黑龙江省");
            }
            if(a.equals("23030")){
                provinceList.add("吉林省");
            }
            if(a.equals("23031")){
                provinceList.add("辽宁省");
            }

            /** 总监区 */
            if (a.equals("21001")) {
                directorAreaList.add("东中国区");
            }
            if (a.equals("21002")) {
                directorAreaList.add("南中国一区");
            }
            if (a.equals("21003")) {
                directorAreaList.add("南中国二区");
            }
            if (a.equals("21004")) {
                directorAreaList.add("西中国区");
            }
            if (a.equals("21005")) {
                directorAreaList.add("北中国区");
            }
            if (a.equals("21006")) {
                directorAreaList.add("中中国区");
            }

            /** 大区 */
            if (a.equals("22001")) {
                regionList.add("江苏大区");
            }
            if (a.equals("22002")) {
                regionList.add("浙江1大区");
            }
            if (a.equals("22003")) {
                regionList.add("浙江2大区");
            }
            if (a.equals("22004")) {
                regionList.add("上海大区");
            }
            if (a.equals("22005")) {
                regionList.add("广东1大区");
            }
            if (a.equals("22006")) {
                regionList.add("湖南大区");
            }
            if (a.equals("22007")) {
                regionList.add("闽赣大区");
            }
            if (a.equals("22008")) {
                regionList.add("广东2大区");
            }
            if (a.equals("22009")) {
                regionList.add("广东3大区");
            }
            if (a.equals("22010")) {
                regionList.add("广西大区");
            }
            if (a.equals("22011")) {
                regionList.add("云贵大区");
            }
            if (a.equals("22012")) {
                regionList.add("西北1大区");
            }
            if (a.equals("22013")) {
                regionList.add("西北2大区");
            }
            if (a.equals("22014")) {
                regionList.add("四川大区");
            }
            if (a.equals("22015")) {
                regionList.add("重庆大区");
            }
            if (a.equals("22016")) {
                regionList.add("京蒙大区");
            }
            if (a.equals("22017")) {
                regionList.add("津晋冀大区");
            }
            if (a.equals("22018")) {
                regionList.add("东北大区");
            }
            if (a.equals("22019")) {
                regionList.add("山东大区");
            }
            if (a.equals("22020")) {
                regionList.add("河南大区");
            }
            if (a.equals("22021")) {
                regionList.add("湖北大区");
            }
            if (a.equals("22022")) {
                regionList.add("安徽大区");
            }


            /** 地区 */
            if (a.equals("20001")) {
                areaList.add("常镇区M");
            }
            if (a.equals("20002")) {
                areaList.add("淮宿盐区M");
            }
            if (a.equals("20003")) {
                areaList.add("南京1区");
            }
            if (a.equals("20004")) {
                areaList.add("南京2区");
            }
            if (a.equals("20005")) {
                areaList.add("南京A区");
            }
            if (a.equals("20006")) {
                areaList.add("南京B区");
            }
            if (a.equals("20007")) {
                areaList.add("南通区M");
            }
            if (a.equals("20008")) {
                areaList.add("苏州1区");
            }
            if (a.equals("20009")) {
                areaList.add("苏州A区");
            }
            if (a.equals("20010")) {
                areaList.add("无锡1区");
            }
            if (a.equals("20011")) {
                areaList.add("无锡A区");
            }
            if (a.equals("20012")) {
                areaList.add("徐连区M");
            }
            if (a.equals("20013")) {
                areaList.add("扬泰区M");
            }
            if (a.equals("20014")) {
                areaList.add("杭州1区");
            }
            if (a.equals("20015")) {
                areaList.add("杭州2区");
            }
            if (a.equals("20016")) {
                areaList.add("杭州3区");
            }
            if (a.equals("20017")) {
                areaList.add("宁台舟1区");
            }
            if (a.equals("20018")) {
                areaList.add("温丽金衢1区");
            }
            if (a.equals("20019")) {
                areaList.add("杭州A区");
            }
            if (a.equals("20020")) {
                areaList.add("杭州B区");
            }
            if (a.equals("20021")) {
                areaList.add("杭州C区");
            }
            if (a.equals("20022")) {
                areaList.add("杭州D区");
            }
            if (a.equals("20023")) {
                areaList.add("宁台舟A区");
            }
            if (a.equals("20024")) {
                areaList.add("温丽金衢A区");
            }
            if (a.equals("20025")) {
                areaList.add("上海1区");
            }
            if (a.equals("20026")) {
                areaList.add("上海2区");
            }
            if (a.equals("20027")) {
                areaList.add("上海3区");
            }
            if (a.equals("20028")) {
                areaList.add("上海4区");
            }
            if (a.equals("20029")) {
                areaList.add("上海A区");
            }
            if (a.equals("20030")) {
                areaList.add("上海B区");
            }
            if (a.equals("20031")) {
                areaList.add("上海C区");
            }
            if (a.equals("20032")) {
                areaList.add("上海D区");
            }
            if (a.equals("20033")) {
                areaList.add("广州6区");
            }
            if (a.equals("20034")) {
                areaList.add("广州7区");
            }
            if (a.equals("20035")) {
                areaList.add("广州C区");
            }
            if (a.equals("20036")) {
                areaList.add("广州D区");
            }
            if (a.equals("20037")) {
                areaList.add("江中珠1区M");
            }
            if (a.equals("20038")) {
                areaList.add("江中珠A区");
            }
            if (a.equals("20039")) {
                areaList.add("长沙1区");
            }
            if (a.equals("20040")) {
                areaList.add("长沙2区");
            }
            if (a.equals("20041")) {
                areaList.add("长沙3区");
            }
            if (a.equals("20042")) {
                areaList.add("长沙A区");
            }
            if (a.equals("20043")) {
                areaList.add("长沙B区");
            }
            if (a.equals("20044")) {
                areaList.add("长沙C区");
            }
            if (a.equals("20045")) {
                areaList.add("福建1区");
            }
            if (a.equals("20046")) {
                areaList.add("福建2区");
            }
            if (a.equals("20047")) {
                areaList.add("福建3区M");
            }
            if (a.equals("20048")) {
                areaList.add("福建4区M");
            }
            if (a.equals("20049")) {
                areaList.add("福建A区");
            }
            if (a.equals("20050")) {
                areaList.add("福建B区");
            }
            if (a.equals("20051")) {
                areaList.add("江西1区M");
            }
            if (a.equals("20052")) {
                areaList.add("江西2区M");
            }
            if (a.equals("20053")) {
                areaList.add("江西3区M");
            }
            if (a.equals("20054")) {
                areaList.add("南昌A区");
            }
            if (a.equals("20055")) {
                areaList.add("广州1区");
            }
            if (a.equals("20056")) {
                areaList.add("广州5区M");
            }
            if (a.equals("20057")) {
                areaList.add("广州A区");
            }
            if (a.equals("20058")) {
                areaList.add("广州B区");
            }
            if (a.equals("20059")) {
                areaList.add("深圳1区");
            }
            if (a.equals("20060")) {
                areaList.add("广州2区");
            }
            if (a.equals("20061")) {
                areaList.add("广州3区M");
            }
            if (a.equals("20062")) {
                areaList.add("广州4区M");
            }
            if (a.equals("20063")) {
                areaList.add("海南区M");
            }
            if (a.equals("20064")) {
                areaList.add("粤东区M");
            }
            if (a.equals("20065")) {
                areaList.add("南宁1区");
            }
            if (a.equals("20066")) {
                areaList.add("南宁2区");
            }
            if (a.equals("20067")) {
                areaList.add("南宁3区");
            }
            if (a.equals("20068")) {
                areaList.add("南宁A区");
            }
            if (a.equals("20069")) {
                areaList.add("贵州区M");
            }
            if (a.equals("20070")) {
                areaList.add("昆明1区M");
            }
            if (a.equals("20071")) {
                areaList.add("昆明2区M");
            }
            if (a.equals("20072")) {
                areaList.add("甘肃1区");
            }
            if (a.equals("20073")) {
                areaList.add("青宁区M");
            }
            if (a.equals("20074")) {
                areaList.add("陕西1区");
            }
            if (a.equals("20075")) {
                areaList.add("陕西2区");
            }
            if (a.equals("20076")) {
                areaList.add("陕西3区M");
            }
            if (a.equals("20077")) {
                areaList.add("甘肃A区");
            }
            if (a.equals("20078")) {
                areaList.add("陕西A区");
            }
            if (a.equals("20079")) {
                areaList.add("陕西B区");
            }
            if (a.equals("20080")) {
                areaList.add("新疆1区M");
            }
            if (a.equals("20081")) {
                areaList.add("成都1区");
            }
            if (a.equals("20082")) {
                areaList.add("成都2区");
            }
            if (a.equals("20083")) {
                areaList.add("成都A区");
            }
            if (a.equals("20084")) {
                areaList.add("成都B区");
            }
            if (a.equals("20085")) {
                areaList.add("川北区M");
            }
            if (a.equals("20086")) {
                areaList.add("川南区M");
            }
            if (a.equals("20087")) {
                areaList.add("重庆1区");
            }
            if (a.equals("20088")) {
                areaList.add("重庆2区M");
            }
            if (a.equals("20089")) {
                areaList.add("重庆A区");
            }
            if (a.equals("20090")) {
                areaList.add("北京1区");
            }
            if (a.equals("20091")) {
                areaList.add("北京2区");
            }
            if (a.equals("20092")) {
                areaList.add("北京3区");
            }
            if (a.equals("20093")) {
                areaList.add("北京4区");
            }
            if (a.equals("20094")) {
                areaList.add("北京5区");
            }
            if (a.equals("20095")) {
                areaList.add("北京A区");
            }
            if (a.equals("20096")) {
                areaList.add("北京B区");
            }
            if (a.equals("20097")) {
                areaList.add("北京C区");
            }
            if (a.equals("20098")) {
                areaList.add("蒙1区M");
            }
            if (a.equals("20099")) {
                areaList.add("蒙2区M");
            }
            if (a.equals("20100")) {
                areaList.add("河北1区");
            }
            if (a.equals("20101")) {
                areaList.add("河北2区");
            }
            if (a.equals("20102")) {
                areaList.add("河北A区");
            }
            if (a.equals("20103")) {
                areaList.add("河北B区");
            }
            if (a.equals("20104")) {
                areaList.add("冀北区M");
            }
            if (a.equals("20105")) {
                areaList.add("冀南区M");
            }
            if (a.equals("20106")) {
                areaList.add("晋南区M");
            }
            if (a.equals("20107")) {
                areaList.add("山西1区");
            }
            if (a.equals("20108")) {
                areaList.add("山西A区");
            }
            if (a.equals("20109")) {
                areaList.add("天津1区");
            }
            if (a.equals("20110")) {
                areaList.add("天津A区");
            }
            if (a.equals("20111")) {
                areaList.add("大连1区");
            }
            if (a.equals("20112")) {
                areaList.add("大连A区");
            }
            if (a.equals("20113")) {
                areaList.add("哈尔滨1区");
            }
            if (a.equals("20114")) {
                areaList.add("哈尔滨2区");
            }
            if (a.equals("20115")) {
                areaList.add("哈尔滨A区");
            }
            if (a.equals("20116")) {
                areaList.add("沈阳1区");
            }
            if (a.equals("20117")) {
                areaList.add("沈阳2区");
            }
            if (a.equals("20118")) {
                areaList.add("沈阳A区");
            }
            if (a.equals("20119")) {
                areaList.add("沈阳B区");
            }
            if (a.equals("20120")) {
                areaList.add("长春1区");
            }
            if (a.equals("20121")) {
                areaList.add("长春2区");
            }
            if (a.equals("20122")) {
                areaList.add("长春A区");
            }
            if (a.equals("20123")) {
                areaList.add("济南1区");
            }
            if (a.equals("20124")) {
                areaList.add("济南2区");
            }
            if (a.equals("20125")) {
                areaList.add("济南3区");
            }
            if (a.equals("20126")) {
                areaList.add("济南A区");
            }
            if (a.equals("20127")) {
                areaList.add("临沂1区");
            }
            if (a.equals("20128")) {
                areaList.add("鲁西A区");
            }
            if (a.equals("20129")) {
                areaList.add("鲁中A区");
            }
            if (a.equals("20130")) {
                areaList.add("青岛1区");
            }
            if (a.equals("20131")) {
                areaList.add("青岛A区");
            }
            if (a.equals("20132")) {
                areaList.add("烟威区M");
            }
            if (a.equals("20133")) {
                areaList.add("豫北区M");
            }
            if (a.equals("20134")) {
                areaList.add("豫东区M");
            }
            if (a.equals("20135")) {
                areaList.add("豫南区M");
            }
            if (a.equals("20136")) {
                areaList.add("郑州1区");
            }
            if (a.equals("20137")) {
                areaList.add("郑州2区M");
            }
            if (a.equals("20138")) {
                areaList.add("郑州3区M");
            }
            if (a.equals("20139")) {
                areaList.add("郑州A区");
            }
            if (a.equals("20140")) {
                areaList.add("郑州B区");
            }
            if (a.equals("20141")) {
                areaList.add("鄂西北区M");
            }
            if (a.equals("20142")) {
                areaList.add("鄂西南区M");
            }
            if (a.equals("20143")) {
                areaList.add("武汉1区");
            }
            if (a.equals("20144")) {
                areaList.add("武汉2区");
            }
            if (a.equals("20145")) {
                areaList.add("武汉A区");
            }
            if (a.equals("20146")) {
                areaList.add("武汉B区");
            }
            if (a.equals("20147")) {
                areaList.add("蚌埠区M");
            }
            if (a.equals("20148")) {
                areaList.add("合肥1区");
            }
            if (a.equals("20149")) {
                areaList.add("合肥2区");
            }
            if (a.equals("20150")) {
                areaList.add("合肥A区");
            }
            if (a.equals("20151")) {
                areaList.add("皖北区M");
            }
            if (a.equals("20152")) {
                areaList.add("皖南区M");
            }
        }

        if (provinceList.size() > 0) {
            ddiPharmacyDayInventory2025.setProvinceList(provinceList);
        }

        if (directorAreaList.size() > 0) {
            ddiPharmacyDayInventory2025.setDirectorAreaList(directorAreaList);
        }
        if (regionList.size() > 0) {
            ddiPharmacyDayInventory2025.setRegionList(regionList);
        }
        if (areaList.size() > 0) {
            ddiPharmacyDayInventory2025.setAreaList(areaList);
        }

        startPage();
        List<DdiPharmacyDayInventory2025> list = ddiPharmacyDayInventory2025Service.selectDdiPharmacyDayInventory2025List(ddiPharmacyDayInventory2025);
        return getDataTable(list);
    }

    /**
     * 导出2025药房日库存列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:ddiPharmacyDayInventory2025:export')")
    @Log(title = "2025药房日库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DdiPharmacyDayInventory2025 ddiPharmacyDayInventory2025) {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);

        /**
         * 2025年商业运营架构
         * 角色对应省份
         */
        List<String> provinceList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应总监区
         */
        List<String> directorAreaList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应大区
         */
        List<String> regionList = new ArrayList<>();

        /**
         * 2025年商业运营架构
         * 角色对应地区
         */
        List<String> areaList = new ArrayList<>();


        for (String a : roles) {
            /** 省份 */
            if(a.equals("23001")){
                provinceList.add("上海市");
            }
            if(a.equals("23002")){
                provinceList.add("江苏省");
            }
            if(a.equals("23003")){
                provinceList.add("安徽省");
            }
            if(a.equals("23004")){
                provinceList.add("浙江省");
            }
            if(a.equals("23005")){
                provinceList.add("江西省");
            }
            if(a.equals("23006")){
                provinceList.add("福建省");
            }
            if(a.equals("23007")){
                provinceList.add("广东省");
            }
            if(a.equals("23008")){
                provinceList.add("海南省");
            }
            if(a.equals("23009")){
                provinceList.add("广西壮族自治区");
            }
            if(a.equals("23010")){
                provinceList.add("湖北省");
            }
            if(a.equals("23011")){
                provinceList.add("湖南省");
            }
            if(a.equals("23012")){
                provinceList.add("贵州省");
            }
            if(a.equals("23013")){
                provinceList.add("云南省");
            }
            if(a.equals("23014")){
                provinceList.add("河南省");
            }
            if(a.equals("23015")){
                provinceList.add("陕西省");
            }
            if(a.equals("23016")){
                provinceList.add("甘肃省");
            }
            if(a.equals("23017")){
                provinceList.add("宁夏回族自治区");
            }
            if(a.equals("23018")){
                provinceList.add("新疆维吾尔自治区");
            }
            if(a.equals("23019")){
                provinceList.add("青海省");
            }
            if(a.equals("23020")){
                provinceList.add("重庆市");
            }
            if(a.equals("23021")){
                provinceList.add("四川省");
            }
            if(a.equals("23022")){
                provinceList.add("西藏自治区");
            }
            if(a.equals("23023")){
                provinceList.add("北京市");
            }
            if(a.equals("23024")){
                provinceList.add("天津市");
            }
            if(a.equals("23025")){
                provinceList.add("河北省");
            }
            if(a.equals("23026")){
                provinceList.add("内蒙古自治区");
            }
            if(a.equals("23027")){
                provinceList.add("山西省");
            }
            if(a.equals("23028")){
                provinceList.add("山东省");
            }
            if(a.equals("23029")){
                provinceList.add("黑龙江省");
            }
            if(a.equals("23030")){
                provinceList.add("吉林省");
            }
            if(a.equals("23031")){
                provinceList.add("辽宁省");
            }

            /** 总监区 */
            if (a.equals("21001")) {
                directorAreaList.add("东中国区");
            }
            if (a.equals("21002")) {
                directorAreaList.add("南中国一区");
            }
            if (a.equals("21003")) {
                directorAreaList.add("南中国二区");
            }
            if (a.equals("21004")) {
                directorAreaList.add("西中国区");
            }
            if (a.equals("21005")) {
                directorAreaList.add("北中国区");
            }
            if (a.equals("21006")) {
                directorAreaList.add("中中国区");
            }

            /** 大区 */
            if (a.equals("22001")) {
                regionList.add("江苏大区");
            }
            if (a.equals("22002")) {
                regionList.add("浙江1大区");
            }
            if (a.equals("22003")) {
                regionList.add("浙江2大区");
            }
            if (a.equals("22004")) {
                regionList.add("上海大区");
            }
            if (a.equals("22005")) {
                regionList.add("广东1大区");
            }
            if (a.equals("22006")) {
                regionList.add("湖南大区");
            }
            if (a.equals("22007")) {
                regionList.add("闽赣大区");
            }
            if (a.equals("22008")) {
                regionList.add("广东2大区");
            }
            if (a.equals("22009")) {
                regionList.add("广东3大区");
            }
            if (a.equals("22010")) {
                regionList.add("广西大区");
            }
            if (a.equals("22011")) {
                regionList.add("云贵大区");
            }
            if (a.equals("22012")) {
                regionList.add("西北1大区");
            }
            if (a.equals("22013")) {
                regionList.add("西北2大区");
            }
            if (a.equals("22014")) {
                regionList.add("四川大区");
            }
            if (a.equals("22015")) {
                regionList.add("重庆大区");
            }
            if (a.equals("22016")) {
                regionList.add("京蒙大区");
            }
            if (a.equals("22017")) {
                regionList.add("津晋冀大区");
            }
            if (a.equals("22018")) {
                regionList.add("东北大区");
            }
            if (a.equals("22019")) {
                regionList.add("山东大区");
            }
            if (a.equals("22020")) {
                regionList.add("河南大区");
            }
            if (a.equals("22021")) {
                regionList.add("湖北大区");
            }
            if (a.equals("22022")) {
                regionList.add("安徽大区");
            }


            /** 地区 */
            if (a.equals("20001")) {
                areaList.add("常镇区M");
            }
            if (a.equals("20002")) {
                areaList.add("淮宿盐区M");
            }
            if (a.equals("20003")) {
                areaList.add("南京1区");
            }
            if (a.equals("20004")) {
                areaList.add("南京2区");
            }
            if (a.equals("20005")) {
                areaList.add("南京A区");
            }
            if (a.equals("20006")) {
                areaList.add("南京B区");
            }
            if (a.equals("20007")) {
                areaList.add("南通区M");
            }
            if (a.equals("20008")) {
                areaList.add("苏州1区");
            }
            if (a.equals("20009")) {
                areaList.add("苏州A区");
            }
            if (a.equals("20010")) {
                areaList.add("无锡1区");
            }
            if (a.equals("20011")) {
                areaList.add("无锡A区");
            }
            if (a.equals("20012")) {
                areaList.add("徐连区M");
            }
            if (a.equals("20013")) {
                areaList.add("扬泰区M");
            }
            if (a.equals("20014")) {
                areaList.add("杭州1区");
            }
            if (a.equals("20015")) {
                areaList.add("杭州2区");
            }
            if (a.equals("20016")) {
                areaList.add("杭州3区");
            }
            if (a.equals("20017")) {
                areaList.add("宁台舟1区");
            }
            if (a.equals("20018")) {
                areaList.add("温丽金衢1区");
            }
            if (a.equals("20019")) {
                areaList.add("杭州A区");
            }
            if (a.equals("20020")) {
                areaList.add("杭州B区");
            }
            if (a.equals("20021")) {
                areaList.add("杭州C区");
            }
            if (a.equals("20022")) {
                areaList.add("杭州D区");
            }
            if (a.equals("20023")) {
                areaList.add("宁台舟A区");
            }
            if (a.equals("20024")) {
                areaList.add("温丽金衢A区");
            }
            if (a.equals("20025")) {
                areaList.add("上海1区");
            }
            if (a.equals("20026")) {
                areaList.add("上海2区");
            }
            if (a.equals("20027")) {
                areaList.add("上海3区");
            }
            if (a.equals("20028")) {
                areaList.add("上海4区");
            }
            if (a.equals("20029")) {
                areaList.add("上海A区");
            }
            if (a.equals("20030")) {
                areaList.add("上海B区");
            }
            if (a.equals("20031")) {
                areaList.add("上海C区");
            }
            if (a.equals("20032")) {
                areaList.add("上海D区");
            }
            if (a.equals("20033")) {
                areaList.add("广州6区");
            }
            if (a.equals("20034")) {
                areaList.add("广州7区");
            }
            if (a.equals("20035")) {
                areaList.add("广州C区");
            }
            if (a.equals("20036")) {
                areaList.add("广州D区");
            }
            if (a.equals("20037")) {
                areaList.add("江中珠1区M");
            }
            if (a.equals("20038")) {
                areaList.add("江中珠A区");
            }
            if (a.equals("20039")) {
                areaList.add("长沙1区");
            }
            if (a.equals("20040")) {
                areaList.add("长沙2区");
            }
            if (a.equals("20041")) {
                areaList.add("长沙3区");
            }
            if (a.equals("20042")) {
                areaList.add("长沙A区");
            }
            if (a.equals("20043")) {
                areaList.add("长沙B区");
            }
            if (a.equals("20044")) {
                areaList.add("长沙C区");
            }
            if (a.equals("20045")) {
                areaList.add("福建1区");
            }
            if (a.equals("20046")) {
                areaList.add("福建2区");
            }
            if (a.equals("20047")) {
                areaList.add("福建3区M");
            }
            if (a.equals("20048")) {
                areaList.add("福建4区M");
            }
            if (a.equals("20049")) {
                areaList.add("福建A区");
            }
            if (a.equals("20050")) {
                areaList.add("福建B区");
            }
            if (a.equals("20051")) {
                areaList.add("江西1区M");
            }
            if (a.equals("20052")) {
                areaList.add("江西2区M");
            }
            if (a.equals("20053")) {
                areaList.add("江西3区M");
            }
            if (a.equals("20054")) {
                areaList.add("南昌A区");
            }
            if (a.equals("20055")) {
                areaList.add("广州1区");
            }
            if (a.equals("20056")) {
                areaList.add("广州5区M");
            }
            if (a.equals("20057")) {
                areaList.add("广州A区");
            }
            if (a.equals("20058")) {
                areaList.add("广州B区");
            }
            if (a.equals("20059")) {
                areaList.add("深圳1区");
            }
            if (a.equals("20060")) {
                areaList.add("广州2区");
            }
            if (a.equals("20061")) {
                areaList.add("广州3区M");
            }
            if (a.equals("20062")) {
                areaList.add("广州4区M");
            }
            if (a.equals("20063")) {
                areaList.add("海南区M");
            }
            if (a.equals("20064")) {
                areaList.add("粤东区M");
            }
            if (a.equals("20065")) {
                areaList.add("南宁1区");
            }
            if (a.equals("20066")) {
                areaList.add("南宁2区");
            }
            if (a.equals("20067")) {
                areaList.add("南宁3区");
            }
            if (a.equals("20068")) {
                areaList.add("南宁A区");
            }
            if (a.equals("20069")) {
                areaList.add("贵州区M");
            }
            if (a.equals("20070")) {
                areaList.add("昆明1区M");
            }
            if (a.equals("20071")) {
                areaList.add("昆明2区M");
            }
            if (a.equals("20072")) {
                areaList.add("甘肃1区");
            }
            if (a.equals("20073")) {
                areaList.add("青宁区M");
            }
            if (a.equals("20074")) {
                areaList.add("陕西1区");
            }
            if (a.equals("20075")) {
                areaList.add("陕西2区");
            }
            if (a.equals("20076")) {
                areaList.add("陕西3区M");
            }
            if (a.equals("20077")) {
                areaList.add("甘肃A区");
            }
            if (a.equals("20078")) {
                areaList.add("陕西A区");
            }
            if (a.equals("20079")) {
                areaList.add("陕西B区");
            }
            if (a.equals("20080")) {
                areaList.add("新疆1区M");
            }
            if (a.equals("20081")) {
                areaList.add("成都1区");
            }
            if (a.equals("20082")) {
                areaList.add("成都2区");
            }
            if (a.equals("20083")) {
                areaList.add("成都A区");
            }
            if (a.equals("20084")) {
                areaList.add("成都B区");
            }
            if (a.equals("20085")) {
                areaList.add("川北区M");
            }
            if (a.equals("20086")) {
                areaList.add("川南区M");
            }
            if (a.equals("20087")) {
                areaList.add("重庆1区");
            }
            if (a.equals("20088")) {
                areaList.add("重庆2区M");
            }
            if (a.equals("20089")) {
                areaList.add("重庆A区");
            }
            if (a.equals("20090")) {
                areaList.add("北京1区");
            }
            if (a.equals("20091")) {
                areaList.add("北京2区");
            }
            if (a.equals("20092")) {
                areaList.add("北京3区");
            }
            if (a.equals("20093")) {
                areaList.add("北京4区");
            }
            if (a.equals("20094")) {
                areaList.add("北京5区");
            }
            if (a.equals("20095")) {
                areaList.add("北京A区");
            }
            if (a.equals("20096")) {
                areaList.add("北京B区");
            }
            if (a.equals("20097")) {
                areaList.add("北京C区");
            }
            if (a.equals("20098")) {
                areaList.add("蒙1区M");
            }
            if (a.equals("20099")) {
                areaList.add("蒙2区M");
            }
            if (a.equals("20100")) {
                areaList.add("河北1区");
            }
            if (a.equals("20101")) {
                areaList.add("河北2区");
            }
            if (a.equals("20102")) {
                areaList.add("河北A区");
            }
            if (a.equals("20103")) {
                areaList.add("河北B区");
            }
            if (a.equals("20104")) {
                areaList.add("冀北区M");
            }
            if (a.equals("20105")) {
                areaList.add("冀南区M");
            }
            if (a.equals("20106")) {
                areaList.add("晋南区M");
            }
            if (a.equals("20107")) {
                areaList.add("山西1区");
            }
            if (a.equals("20108")) {
                areaList.add("山西A区");
            }
            if (a.equals("20109")) {
                areaList.add("天津1区");
            }
            if (a.equals("20110")) {
                areaList.add("天津A区");
            }
            if (a.equals("20111")) {
                areaList.add("大连1区");
            }
            if (a.equals("20112")) {
                areaList.add("大连A区");
            }
            if (a.equals("20113")) {
                areaList.add("哈尔滨1区");
            }
            if (a.equals("20114")) {
                areaList.add("哈尔滨2区");
            }
            if (a.equals("20115")) {
                areaList.add("哈尔滨A区");
            }
            if (a.equals("20116")) {
                areaList.add("沈阳1区");
            }
            if (a.equals("20117")) {
                areaList.add("沈阳2区");
            }
            if (a.equals("20118")) {
                areaList.add("沈阳A区");
            }
            if (a.equals("20119")) {
                areaList.add("沈阳B区");
            }
            if (a.equals("20120")) {
                areaList.add("长春1区");
            }
            if (a.equals("20121")) {
                areaList.add("长春2区");
            }
            if (a.equals("20122")) {
                areaList.add("长春A区");
            }
            if (a.equals("20123")) {
                areaList.add("济南1区");
            }
            if (a.equals("20124")) {
                areaList.add("济南2区");
            }
            if (a.equals("20125")) {
                areaList.add("济南3区");
            }
            if (a.equals("20126")) {
                areaList.add("济南A区");
            }
            if (a.equals("20127")) {
                areaList.add("临沂1区");
            }
            if (a.equals("20128")) {
                areaList.add("鲁西A区");
            }
            if (a.equals("20129")) {
                areaList.add("鲁中A区");
            }
            if (a.equals("20130")) {
                areaList.add("青岛1区");
            }
            if (a.equals("20131")) {
                areaList.add("青岛A区");
            }
            if (a.equals("20132")) {
                areaList.add("烟威区M");
            }
            if (a.equals("20133")) {
                areaList.add("豫北区M");
            }
            if (a.equals("20134")) {
                areaList.add("豫东区M");
            }
            if (a.equals("20135")) {
                areaList.add("豫南区M");
            }
            if (a.equals("20136")) {
                areaList.add("郑州1区");
            }
            if (a.equals("20137")) {
                areaList.add("郑州2区M");
            }
            if (a.equals("20138")) {
                areaList.add("郑州3区M");
            }
            if (a.equals("20139")) {
                areaList.add("郑州A区");
            }
            if (a.equals("20140")) {
                areaList.add("郑州B区");
            }
            if (a.equals("20141")) {
                areaList.add("鄂西北区M");
            }
            if (a.equals("20142")) {
                areaList.add("鄂西南区M");
            }
            if (a.equals("20143")) {
                areaList.add("武汉1区");
            }
            if (a.equals("20144")) {
                areaList.add("武汉2区");
            }
            if (a.equals("20145")) {
                areaList.add("武汉A区");
            }
            if (a.equals("20146")) {
                areaList.add("武汉B区");
            }
            if (a.equals("20147")) {
                areaList.add("蚌埠区M");
            }
            if (a.equals("20148")) {
                areaList.add("合肥1区");
            }
            if (a.equals("20149")) {
                areaList.add("合肥2区");
            }
            if (a.equals("20150")) {
                areaList.add("合肥A区");
            }
            if (a.equals("20151")) {
                areaList.add("皖北区M");
            }
            if (a.equals("20152")) {
                areaList.add("皖南区M");
            }
        }

        if (provinceList.size() > 0) {
            ddiPharmacyDayInventory2025.setProvinceList(provinceList);
        }

        if (directorAreaList.size() > 0) {
            ddiPharmacyDayInventory2025.setDirectorAreaList(directorAreaList);
        }
        if (regionList.size() > 0) {
            ddiPharmacyDayInventory2025.setRegionList(regionList);
        }
        if (areaList.size() > 0) {
            ddiPharmacyDayInventory2025.setAreaList(areaList);
        }

        List<DdiPharmacyDayInventory2025> list = ddiPharmacyDayInventory2025Service.selectDdiPharmacyDayInventory2025List(ddiPharmacyDayInventory2025);
        ExcelUtil<DdiPharmacyDayInventory2025> util = new ExcelUtil<DdiPharmacyDayInventory2025>(DdiPharmacyDayInventory2025.class);
        util.exportExcel(response, list, "2025药房日库存数据");
    }

}
