package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.HospitalDailyProcurementMapper;
import com.akesobio.report.ddi.domain.HospitalDailyProcurement;
import com.akesobio.report.ddi.service.IHospitalDailyProcurementService;

/**
 * 医院日采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-15
 */
@Service
public class HospitalDailyProcurementServiceImpl implements IHospitalDailyProcurementService {
    @Autowired
    private HospitalDailyProcurementMapper hospitalDailyProcurementMapper;


    /**
     * 查询医院日采购列表
     *
     * @param hospitalDailyProcurement 医院日采购
     * @return 医院日采购
     */
    @Override
    public List<HospitalDailyProcurement> selectHospitalDailyProcurementList(HospitalDailyProcurement hospitalDailyProcurement) {
        String standardProductName = hospitalDailyProcurement.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return hospitalDailyProcurementMapper.selectKtnHospitalDailyProcurementList(hospitalDailyProcurement);
        } else {
            return hospitalDailyProcurementMapper.selectYdfHospitalDailyProcurementList(hospitalDailyProcurement);
        }
    }

}
