package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiBusinessMonthSale2025Mapper;
import com.akesobio.report.ddi.domain.DdiBusinessMonthSale2025;
import com.akesobio.report.ddi.service.IDdiBusinessMonthSale2025Service;

/**
 * 2025商业月销售Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Service
public class DdiBusinessMonthSale2025ServiceImpl implements IDdiBusinessMonthSale2025Service {
    @Autowired
    private DdiBusinessMonthSale2025Mapper ddiBusinessMonthSale2025Mapper;

    /**
     * 查询2025商业月销售列表
     *
     * @param ddiBusinessMonthSale2025 2025商业月销售
     * @return 2025商业月销售
     */
    @Override
    public List<DdiBusinessMonthSale2025> selectDdiBusinessMonthSale2025List(DdiBusinessMonthSale2025 ddiBusinessMonthSale2025) {
        String standardProductName = ddiBusinessMonthSale2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiBusinessMonthSale2025Mapper.selectKtnDdiBusinessMonthSale2025List(ddiBusinessMonthSale2025);
        } else {
            return ddiBusinessMonthSale2025Mapper.selectYdfDdiBusinessMonthSale2025List(ddiBusinessMonthSale2025);
        }
    }

}
