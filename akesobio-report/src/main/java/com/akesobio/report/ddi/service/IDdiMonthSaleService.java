package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.DdiMonthSale;

/**
 * 月销售Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IDdiMonthSaleService {


    /**
     * 查询月销售列表
     *
     * @param ddiMonthSale 月销售
     * @return 月销售集合
     */
    public List<DdiMonthSale> selectDdiMonthSaleList(DdiMonthSale ddiMonthSale);

    /**
     * 查询所有月销售列表
     *
     * @param ddiMonthSale 月销售
     * @return 月销售集合
     */
    public List<DdiMonthSale> selectAllDdiMonthSaleList(DdiMonthSale ddiMonthSale);

}
