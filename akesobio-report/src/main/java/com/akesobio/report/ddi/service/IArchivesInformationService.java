package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.report.ddi.domain.ArchivesInformation;
import com.akesobio.report.ddi.domain.DdiCustomerInformation;

/**
 * 档案信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IArchivesInformationService {
    /**
     * 查询档案信息
     *
     * @param id 档案信息主键
     * @return 档案信息
     */
    public ArchivesInformation selectArchivesInformationById(Integer id);

    /**
     * 查询档案信息列表
     *
     * @param archivesInformation 档案信息
     * @return 档案信息集合
     */
    public List<ArchivesInformation> selectArchivesInformationList(ArchivesInformation archivesInformation);

    /**
     * 新增档案信息
     *
     * @param archivesInformation 档案信息
     * @return 结果
     */
    public int insertArchivesInformation(ArchivesInformation archivesInformation);

    /**
     * 修改档案信息
     *
     * @param archivesInformation 档案信息
     * @return 结果
     */
    public int updateArchivesInformation(ArchivesInformation archivesInformation);

    /**
     * 批量删除档案信息
     *
     * @param ids 需要删除的档案信息主键集合
     * @return 结果
     */
    public int deleteArchivesInformationByIds(Integer[] ids);

    /**
     * 删除档案信息信息
     *
     * @param id 档案信息主键
     * @return 结果
     */
    public int deleteArchivesInformationById(Integer id);

    /**
     * 导入档案信息数据
     *
     * @param list            档案信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importArchivesInformation(List<ArchivesInformation> list, Boolean isUpdateSupport);
}
