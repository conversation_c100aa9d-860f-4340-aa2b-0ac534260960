package com.akesobio.report.ddi.controller;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.ddi.domain.Inventory;
import com.akesobio.report.ddi.service.IInventoryService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * DDI-库存Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@RequestMapping("/ddi/inventory")
public class InventoryController extends BaseController {
    @Autowired
    private IInventoryService inventoryService;

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 查询DDI-库存列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:list')")
    @GetMapping("/list")
    public TableDataInfo list(Inventory inventory) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            inventory.setProvinceList(provinceList);
        }
      /*  LocalDate yesterday = LocalDate.now().minusDays(2);
        inventory.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));*/
        startPage();
        List<Inventory> list = inventoryService.selectInventoryList(inventory);
        return getDataTable(list);
    }

    /**
     * 导出DDI-库存列表
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:export')")
    @Log(title = "DDI-库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Inventory inventory) {
        List<String> provinceList = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应省份
         *
         */
        for (String a : roles) {
            if (a.equals("23001")) {
                provinceList.add("上海市");
            }
            if (a.equals("23002")) {
                provinceList.add("江苏省");
            }
            if (a.equals("23003")) {
                provinceList.add("安徽省");
            }
            if (a.equals("23004")) {
                provinceList.add("浙江省");
            }
            if (a.equals("23005")) {
                provinceList.add("江西省");
            }
            if (a.equals("23006")) {
                provinceList.add("福建省");
            }
            if (a.equals("23007")) {
                provinceList.add("广东省");
            }
            if (a.equals("23008")) {
                provinceList.add("海南省");
            }
            if (a.equals("23009")) {
                provinceList.add("广西壮族自治区");
            }
            if (a.equals("23010")) {
                provinceList.add("湖北省");
            }
            if (a.equals("23011")) {
                provinceList.add("湖南省");
            }
            if (a.equals("23012")) {
                provinceList.add("贵州省");
            }
            if (a.equals("23013")) {
                provinceList.add("云南省");
            }
            if (a.equals("23014")) {
                provinceList.add("河南省");
            }
            if (a.equals("23015")) {
                provinceList.add("陕西省");
            }
            if (a.equals("23016")) {
                provinceList.add("甘肃省");
            }
            if (a.equals("23017")) {
                provinceList.add("宁夏回族自治区");
            }
            if (a.equals("23018")) {
                provinceList.add("新疆维吾尔自治区");
            }
            if (a.equals("23019")) {
                provinceList.add("青海省");
            }
            if (a.equals("23020")) {
                provinceList.add("重庆市");
            }
            if (a.equals("23021")) {
                provinceList.add("四川省");
            }
            if (a.equals("23022")) {
                provinceList.add("西藏自治区");
            }
            if (a.equals("23023")) {
                provinceList.add("北京市");
            }
            if (a.equals("23024")) {
                provinceList.add("天津市");
            }
            if (a.equals("23025")) {
                provinceList.add("河北省");
            }
            if (a.equals("23026")) {
                provinceList.add("内蒙古自治区");
            }
            if (a.equals("23027")) {
                provinceList.add("山西省");
            }
            if (a.equals("23028")) {
                provinceList.add("山东省");
            }
            if (a.equals("23029")) {
                provinceList.add("黑龙江省");
            }
            if (a.equals("23030")) {
                provinceList.add("吉林省");
            }
            if (a.equals("23031")) {
                provinceList.add("辽宁省");
            }
        }
        if (provinceList.size() > 0) {
            inventory.setProvinceList(provinceList);
        }
       /* LocalDate yesterday = LocalDate.now().minusDays(2);
        inventory.setSalesDate(Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant()));*/
        List<Inventory> list = inventoryService.selectInventoryList(inventory);
        ExcelUtil<Inventory> util = new ExcelUtil<Inventory>(Inventory.class);
        util.exportExcel(response, list, "DDI-库存数据");
    }

    /**
     * 获取DDI-库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(inventoryService.selectInventoryById(id));
    }

    /**
     * 新增DDI-库存
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:add')")
    @Log(title = "DDI-库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Inventory inventory) {
        return toAjax(inventoryService.insertInventory(inventory));
    }

    /**
     * 修改DDI-库存
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:edit')")
    @Log(title = "DDI-库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Inventory inventory) {
        return toAjax(inventoryService.updateInventory(inventory));
    }

    /**
     * 删除DDI-库存
     */
    @PreAuthorize("@ss.hasPermi('ddi:inventory:remove')")
    @Log(title = "DDI-库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(inventoryService.deleteInventoryByIds(ids));
    }
}
