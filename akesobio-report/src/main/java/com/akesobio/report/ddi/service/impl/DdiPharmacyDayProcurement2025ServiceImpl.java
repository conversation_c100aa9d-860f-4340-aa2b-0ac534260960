package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyDayProcurement2025Mapper;
import com.akesobio.report.ddi.domain.DdiPharmacyDayProcurement2025;
import com.akesobio.report.ddi.service.IDdiPharmacyDayProcurement2025Service;

/**
 * 2025药房日采购Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class DdiPharmacyDayProcurement2025ServiceImpl implements IDdiPharmacyDayProcurement2025Service {
    @Autowired
    private DdiPharmacyDayProcurement2025Mapper ddiPharmacyDayProcurement2025Mapper;

    /**
     * 查询2025药房日采购列表
     *
     * @param ddiPharmacyDayProcurement2025 2025药房日采购
     * @return 2025药房日采购
     */
    @Override
    public List<DdiPharmacyDayProcurement2025> selectDdiPharmacyDayProcurement2025List(DdiPharmacyDayProcurement2025 ddiPharmacyDayProcurement2025) {
        String standardProductName = ddiPharmacyDayProcurement2025.getStandardProductName();
        if (standardProductName.equals("开坦尼")) {
            return ddiPharmacyDayProcurement2025Mapper.selectKtnDdiPharmacyDayProcurement2025List(ddiPharmacyDayProcurement2025);
        } else {
            return ddiPharmacyDayProcurement2025Mapper.selectYdfDdiPharmacyDayProcurement2025List(ddiPharmacyDayProcurement2025);
        }
    }
}
