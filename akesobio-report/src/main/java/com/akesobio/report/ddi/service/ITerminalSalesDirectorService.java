package com.akesobio.report.ddi.service;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ddi.domain.TerminalSalesDirector;

/**
 * 终端销售统计表-总监区Service接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface ITerminalSalesDirectorService {
    /**
     * 查询终端销售统计表-总监区
     *
     * @param id 终端销售统计表-总监区主键
     * @return 终端销售统计表-总监区
     */
    public TerminalSalesDirector selectTerminalSalesDirectorById(Integer id);

    /**
     * 查询终端销售统计表-总监区列表
     *
     * @param terminalSalesDirector 终端销售统计表-总监区
     * @return 终端销售统计表-总监区集合
     */
    public List<TerminalSalesDirector> selectTerminalSalesDirectorList(TerminalSalesDirector terminalSalesDirector);

    /**
     * 新增终端销售统计表-总监区
     *
     * @param terminalSalesDirector 终端销售统计表-总监区
     * @return 结果
     */
    public int insertTerminalSalesDirector(TerminalSalesDirector terminalSalesDirector);

    /**
     * 修改终端销售统计表-总监区
     *
     * @param terminalSalesDirector 终端销售统计表-总监区
     * @return 结果
     */
    public int updateTerminalSalesDirector(TerminalSalesDirector terminalSalesDirector);

    /**
     * 批量删除终端销售统计表-总监区
     *
     * @param ids 需要删除的终端销售统计表-总监区主键集合
     * @return 结果
     */
    public int deleteTerminalSalesDirectorByIds(Integer[] ids);

    /**
     * 删除终端销售统计表-总监区信息
     *
     * @param id 终端销售统计表-总监区主键
     * @return 结果
     */
    public int deleteTerminalSalesDirectorById(Integer id);
}
