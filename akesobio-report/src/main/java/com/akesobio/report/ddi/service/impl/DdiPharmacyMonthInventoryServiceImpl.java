package com.akesobio.report.ddi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.ddi.mapper.DdiPharmacyMonthInventoryMapper;
import com.akesobio.report.ddi.domain.DdiPharmacyMonthInventory;
import com.akesobio.report.ddi.service.IDdiPharmacyMonthInventoryService;

import javax.annotation.Resource;

/**
 * 药房月库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class DdiPharmacyMonthInventoryServiceImpl implements IDdiPharmacyMonthInventoryService {
    @Resource
    private DdiPharmacyMonthInventoryMapper ddiPharmacyMonthInventoryMapper;


    /**
     * 查询药房月库存列表
     *
     * @param ddiPharmacyMonthInventory 药房月库存
     * @return 药房月库存
     */
    @Override
    public List<DdiPharmacyMonthInventory> selectDdiPharmacyMonthInventoryList(DdiPharmacyMonthInventory ddiPharmacyMonthInventory) {
        return ddiPharmacyMonthInventoryMapper.selectDdiPharmacyMonthInventoryList(ddiPharmacyMonthInventory);
    }
}
