package com.akesobio.report.legal.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.legal.mapper.DealerExecutesContractMapper;
import com.akesobio.report.legal.domain.DealerExecutesContract;
import com.akesobio.report.legal.service.IDealerExecutesContractService;

import javax.annotation.Resource;

/**
 * 经销商执行合同Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-04
 */
@Service
public class DealerExecutesContractServiceImpl implements IDealerExecutesContractService 
{
    @Resource
    private DealerExecutesContractMapper dealerExecutesContractMapper;

    /**
     * 查询经销商执行合同
     * 
     * @param dateCreated 经销商执行合同主键
     * @return 经销商执行合同
     */
    @Override
    public DealerExecutesContract selectDealerExecutesContractByDateCreated(Date dateCreated)
    {
        return dealerExecutesContractMapper.selectDealerExecutesContractByDateCreated(dateCreated);
    }

    /**
     * 查询经销商执行合同列表
     * 
     * @param dealerExecutesContract 经销商执行合同
     * @return 经销商执行合同
     */
    @Override
    public List<DealerExecutesContract> selectDealerExecutesContractList(DealerExecutesContract dealerExecutesContract)
    {
        return dealerExecutesContractMapper.selectDealerExecutesContractList(dealerExecutesContract);
    }
}
