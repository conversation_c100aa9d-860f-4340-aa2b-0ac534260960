package com.akesobio.report.meetingApi.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.meetingApi.domain.Pipeline;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Package_Name com.akesobio.report.meetingApi.mapper
 * <AUTHOR>
 * @TIME
 * @Version
 */
@Mapper
@DataSource(value = DataSourceType.EKP)
public interface PipelineMapper {
    List<Pipeline> queryPipeline();
}
