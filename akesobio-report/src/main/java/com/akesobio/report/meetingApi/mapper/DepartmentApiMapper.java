package com.akesobio.report.meetingApi.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.meetingApi.domain.DepartmentApi;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DataSource(value = DataSourceType.EKP)
public interface DepartmentApiMapper {

    List<DepartmentApi> getAll();
    List<DepartmentApi> getAllTow();
}
