package com.akesobio.report.meetingApi.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.meetingApi.domain.ItemNumber;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DataSource(value = DataSourceType.EKP)
public interface ItemNumberMapper {


    List<ItemNumber> getAll();

    List<ItemNumber> queryItemNumber(ItemNumber o);
}
