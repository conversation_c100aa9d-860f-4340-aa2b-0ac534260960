package com.akesobio.report.meetingApi.domain;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class Supplier {
      private String sId;
      private String supplierName;
      private String supplierCode;
      private String supplierType;
      private String establishmentDate;
      private String legalPerson;
      private String enterpriseType;
      private String registerFund;
      private String parentSupplierName;
      private String conatct;
      private String conatctPhone;
      private String email;
      private String depositBank;
      private String bankNumber;
      private String taxpayerIdentificationNumber;
      private String companyLocation;
      private String businessScope;
      private String serviceCharge;
      private String invoiceType;
      private String invoiceContent;
      private String recordDate;
      private String stateCode;
      private MultipartFile multipartFile;

}
