package com.akesobio.report.meetingApi.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.meetingApi.domain.RegionInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 所属区域信息 Mapper
 *
 * @Package_Name com.akesobio.report.meetingApi.mapper
 * <AUTHOR>
 * @TIME
 * @Version
 */
@Mapper
@DataSource(value = DataSourceType.EKP)
public interface RegionInfoMapper {
    List<RegionInfo> queryRegionInfo();
}
