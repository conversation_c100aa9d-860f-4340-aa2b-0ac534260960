package com.akesobio.report.meetingApi;



import cn.hutool.http.HttpUtil;
import com.akesobio.common.utils.http.HttpUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class MeetingApiService {

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.grant_type}")
    private String grant_type;
    @Value("${client.auth.username}")
    private String username;
    @Value("${client.auth.password}")
    private String password;
    @Value("${client.auth.costDepartment}")
    private String costDepartment;
    public String getToken() {

        JSONObject map = new JSONObject();
        map.put("grant_type", grant_type);
        map.put("username", username);
        map.put("password", password);
        String post;
        try {
            log.info("获取token中...");
            post = HttpUtils.sendPostJson(IP+GetToken, map.toString());

            JSONObject jsonObject = JSONObject.parseObject(post);
            log.info("获取token成功：{}", jsonObject.get("access_token"));
            return jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 费用承担部门（批量）
     * 请求地址
     * 接口地址：api/akesobiocsd/costDepartment/save
     * 调用方式：POST
     * 调用实例：
     * POST {{HOST}}/api/akesobiocsd/costDepartment/save
     * Authorization: Bearer {{AUTH_TOKEN}}
     * Content-Type: application/json
     *
     * [
     *   {
     *       "code":"E",
     *       "name":"10",
     *     }]
     * 请求参数
     * 参数名	类型	必填	备注	示例
     * code	varchar	是	编码	W
     * name	varchar	是	承担部门名称	10
     * departmentHeadCode	varchar	是	承担部门负责人	100
     *
     * 响应报文
     * 参数名	类型	备注	必填	示例
     * ErrorCode	Int32	正常：0	是	0
     * Message	varchar	异常描述	否
     * Data	Json	操作结果	是	{}
     * Data.count	Int32	数据受影响数	是	0
     * Data.success	Bool	全部成功为true
     * 其他情况为false	是	true
     * Data.errorInfo	Json	异常描述	否	{
     * “{ businessUnitCode}”:”业务部门编码[BU0024]不存在在”
     * }

     * @return
     * @throws Exception
     */

    public void updateCostDepartment(JSONArray array) throws Exception {
        String token = getToken();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);
        headers.put("Content-Type", "application/json");
        HttpUtils.sendPostJson(IP + costDepartment, array.toString(), headers);
//        String post = HttpUtils.sendPostJson("https://akesobiouat.recloud.com.cn/t/akesobiouat" + "/api/akesobiocsd/costDepartment/save", array.toString(), headers);
    }
}
