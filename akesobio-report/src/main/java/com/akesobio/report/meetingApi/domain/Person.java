package com.akesobio.report.meetingApi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Person {

    private String userInfoCode;
    private String userInfoName;
    private String guid;
    private List<String> roleNames;
    private String owningBusinessUnitCode;
    private String wxAccount;
    private String idCard;
    private String gender;
    private String positionName;
    private String bachelor;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    private String mobilePhone;
    private String email;
    private String managerCode;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;
    private String collage;
    private String otherEducation;
    private String workExperience;
    private String stateCode;
    private String allowLoginApp;
    private String isAllowLoginApp;
}
