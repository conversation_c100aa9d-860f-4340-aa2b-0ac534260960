package com.akesobio.report.meetingApi.TimeTask;


import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.meetingApi.domain.*;
import com.akesobio.report.meetingApi.mapper.*;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component("Recloud")
@EnableScheduling
public class Company {

    @Autowired
    private CompanyApiMapper companyApiMapper;
    @Autowired
    private ItemNumberMapper itemNumberMapper;
    @Autowired
    private DepartmentApiMapper departmentApiMapper;
    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private CenterMapper centerMaper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private SupplierMapper supplierMapper;

    @Autowired
    private PipelineMapper pipelineMapper;

    @Autowired
    private RegionInfoMapper regionInfoMapper;

    private static String token = "";
    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.user}")
    private String userSave;
    @Value("${client.auth.company}")
    private String companySave;
    @Value("${client.auth.number}")
    private String numberSave;
    @Value("${client.auth.department}")
    private String departmentSave;
    @Value("${client.auth.position}")
    private String position;
    @Value("${client.auth.product}")
    private String product;
    @Value("${client.auth.category}")
    private String category;
    @Value("${client.auth.center}")
    private String center;
    @Value("${client.auth.supplier}")
    private String supplier;
    @Value("${client.auth.pipeline}")
    private String pipelineSave;
    @Value("${client.auth.area}")
    private String areaSave;

    /**
     * 获取访问凭证
     */
    public void ryNoParams() {
        System.out.println("执行无参方法");
        JSONObject map = new JSONObject();
        map.put("grant_type", "password");
        map.put("username", "OATB");
        map.put("password", "Jcyv2ori#");
//        map.put("username", "demo2");
//        map.put("password", "Jcyv2ori#");
        String post;
        try {
            System.out.println(map);
            post = HttpUtils.sendPostJson(IP + GetToken, map.toString());

            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 4762 - 1434 - 2861
     * 更新人员信息（批量） 897
     */
    public void userSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Person> list = personMapper.getAll();
            JSONArray array1 = new JSONArray(list);
            log.info(array1.toString());
            List<String> error = new ArrayList();
            final int batchSize = 100;
            // 总数据量
            int totalSize = list.size();
            // 计算需要的批次次数
            List<String> roleNames = new ArrayList<>();
            roleNames.add("待确认");
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整确保所有数据都被处理
            for (int i = 0; i < batchCount; i++) {
                // 计算当前批次的起始和结束索引
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                // 获取当前批次的数据子集
                List<Person> currentBatch = list.subList(start, end);
                for (Person person : currentBatch) {
                    person.setRoleNames(roleNames);
                    person.setIsAllowLoginApp("0");
                }
                // 调用上传方法处理当前批次数据
                JSONArray array = new JSONArray(currentBatch);
                log.info(array.toString());
                String post = HttpUtils.sendPost(IP + userSave, array.toString(), "Bearer " + token);
                if (post.equals("{\"Data\":null,\"Message\":\"发生了意外错误，请稍后重试或联系管理员。\",\"ErrorCode\":1}") || post.equals("{\"Data\":null,\"Message\":\"对外接口数据库操作异常。\",\"ErrorCode\":-1}")) {
                    error.add(array.toString());
                }
            }
            log.info(error.toString());
        }
    }

    /**
     * 更新公司信息 ***
     */

    public void companySave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<CompanyApi> all = companyApiMapper.getAll();
            JSONArray array = new JSONArray(all);
            System.out.println(array);
            String company = HttpUtils.sendPost(IP + companySave, array.toString(), "Bearer " + token);
        }
    }

    /**
     * 更新项目号信息 526
     */

    public void numberSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<ItemNumber> all = itemNumberMapper.getAll();
            JSONArray array = new JSONArray(all);

            String num = HttpUtils.sendPost(IP + numberSave, array.toString(), "Bearer " + token);
            System.out.println(array.toString());
        }
    }

    /**
     * 更新项目号信息 仅IIT 526
     */
    public void IITNumberSave(String o) throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<ItemNumber> all = itemNumberMapper.queryItemNumber(new ItemNumber().setProjectNumberCode(o));
            JSONArray array = new JSONArray(all);
            String num = HttpUtils.sendPost(IP + numberSave, array.toString(), "Bearer " + token);
            System.out.println("========" + num);
            System.out.println(array);
        }
    }

    /**
     * 更新产品管线信息
     */
    public void pipelineSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Pipeline> all = pipelineMapper.queryPipeline();
            JSONArray array = new JSONArray(all);
            String num = HttpUtils.sendPost(IP + pipelineSave, array.toString(), "Bearer " + token);
            System.out.println("========" + num);
            System.out.println(array);
        }
    }

    /**
     * 更新所属区域信息
     */
    public void areaSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<RegionInfo> all = regionInfoMapper.queryRegionInfo();
            JSONArray array = new JSONArray(all);
            String num = HttpUtils.sendPost(IP + areaSave, array.toString(), "Bearer " + token);
            System.out.println("========" + num);
            System.out.println(array);
        }
    }

    /**
     * 更新部门信息1 194
     */

    public void buSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<DepartmentApi> all = departmentApiMapper.getAll();

//        List<DepartmentApi> distinctList = all.stream()
//                .filter(department -> department.getParentBusinessUnitCode() != null)
//                .collect(Collectors.toList());

            List<DepartmentApi> distinctList = all.stream()
                    .filter(department -> department.getParentBusinessUnitCode() != null)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    DepartmentApi::getBusinessUnitCode,
                                    Function.identity(),
                                    (department1, department2) -> department1
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            final int batchSize = 1000;
            // 总数据量
            int totalSize = distinctList.size();
            // 计算需要的批次次数
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整确保所有数据都被处理
            for (int i = 0; i < batchCount; i++) {
                // 计算当前批次的起始和结束索引
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                // 获取当前批次的数据子集
                List<DepartmentApi> currentBatch = distinctList.subList(start, end);

                // 调用上传方法处理当前批次数据
                JSONArray array = new JSONArray(currentBatch);
                String post = HttpUtils.sendPost(IP + departmentSave, array.toString(), "Bearer " + token);
            }
        }
    }

    /**
     * 更新部门信息 只运行一次
     */
    public void buSaveTow() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<DepartmentApi> distinctList = departmentApiMapper.getAllTow();

//            List<DepartmentApi> distinctList = all.stream()
//                    .filter(department -> department.getParentBusinessUnitCode() != null)
//                    .collect(Collectors.toList());


            final int batchSize = 1000;
            // 总数据量
            int totalSize = distinctList.size();
            // 计算需要的批次次数
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整确保所有数据都被处理
            for (int i = 0; i < batchCount; i++) {
                // 计算当前批次的起始和结束索引
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                // 获取当前批次的数据子集
                List<DepartmentApi> currentBatch = distinctList.subList(start, end);

                // 调用上传方法处理当前批次数据
                JSONArray array = new JSONArray(currentBatch);
                String post = HttpUtils.sendPost(IP + departmentSave, array.toString(), "Bearer " + token);
            }
        }
    }

    /**
     * 更新职位信息（批量）3459
     */


    public void positionSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Position> all = positionMapper.getAll();
//            Map<String, Position> map = all.stream()
//                    .collect(Collectors.toMap(
//                            Position::getPositionName, // 使用name作为map的key
//                            Function.identity(), // 保持元素自身作为value
//                            (oldValue, newValue) -> newValue)); // 当key冲突时，保留新值（即最后出现的元素）

//            List<Position> distinctPositions = new ArrayList<>(all.values());

            final int batchSize = 1000;
            // 总数据量
            int totalSize = all.size();
            // 计算需要的批次次数
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整确保所有数据都被处理
            for (int i = 0; i < batchCount; i++) {
                // 计算当前批次的起始和结束索引
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                // 获取当前批次的数据子集
                List<Position> currentBatch = all.subList(start, end);
                // 调用上传方法处理当前批次数据
                JSONArray array = new JSONArray(currentBatch);
                log.info(array.toString());
                String post = HttpUtils.sendPost(IP + position, array.toString(), "Bearer " + token);
            }
        }
    }

    /**
     * 更新产品信息
     */

    public void productSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Product> all = productMapper.getAll();
            JSONArray array = new JSONArray(all);
            log.info(array.toString());
            String post = HttpUtils.sendPost(IP + product, array.toString(), "Bearer " + token);
        }
    }

    /**
     * 更新品种信息（批量）
     */

    public void categorySave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Category> all = categoryMapper.getAll();
            JSONArray array = new JSONArray(all);
            String post = HttpUtils.sendPost(IP + category, array.toString(), "Bearer " + token);
        }
    }


    /**
     * 更新成本中心信息 297
     */

    public void centerSave() throws UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Center> all = centerMaper.getAll().stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(Center::getBusinessUnitCode, Function.identity(), (existing, replacement) -> existing),
                            map -> new ArrayList<>(map.values())
                    ));

            final int batchSize = 1000;
            // 总数据量
            int totalSize = all.size();
            // 计算需要的批次次数
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整确保所有数据都被处理
            for (int i = 0; i < batchCount; i++) {
                // 计算当前批次的起始和结束索引
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                // 获取当前批次的数据子集
                List<Center> currentBatch = all.subList(start, end);

                // 调用上传方法处理当前批次数据
                JSONArray array = new JSONArray(currentBatch);
                System.out.println(array);
                String post = HttpUtils.sendPost(IP + center, array.toString(), "Bearer " + token);
            }

        }
    }


    /**
     * 执行 10 分钟最少
     * 更新供应商信息 2181
     */
//    @Scheduled(cron = "*/3 * * * * ?")
    public void supplierSave() throws InterruptedException, UnknownHostException {
        InetAddress localhost = InetAddress.getLocalHost();
        if (localhost.getHostName().equals("SERVER22")) {
            ryNoParams();
            List<Supplier> all = supplierMapper.getAll();
            JSONArray array = new JSONArray(all);
            System.out.println(array);
            List<String> logs = new ArrayList<>();
            int i = 0;
            int a = 0;
            for (Supplier sup : all) {
                Map<String, String> headers = new HashMap<>();
                Map<String, Object> paramMap = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                File file = FileUtil.file("D:\\img\\yl02.jpg");
//                File file = FileUtil.file("C:\\Users\\<USER>\\Pictures\\R-C.png");
                paramMap.put("multipartFile", file);
                HttpRequest request = HttpUtil.createPost(IP + supplier);
                request.addHeaders(headers);
//                sup.getSId()
                request.form("sId", sup.getSId());
                request.form("supplierName", sup.getSupplierName());
                request.form("supplierType", stringToInteger(sup.getSupplierType()));
                request.form("supplierCode", sup.getSupplierCode().equals("") ? "NA" : sup.getSupplierCode());
                request.form("establishmentDate", "2024-10-11");
                request.form("legalPerson", "akspro");
                request.form("enterpriseType", 10);
                request.form("registerFund", "123456");
                request.form("parentSupplierName", "meiyou");
                request.form("conatct", "aks");
                request.form("conatctPhone", "*********");
                request.form("email", "<EMAIL>");
                request.form("depositBank", sup.getDepositBank().equals("") ? "NA" : sup.getDepositBank());
                request.form("bankNumber", sup.getBankNumber().equals("") ? "NA" : sup.getBankNumber());
                request.form("taxpayerIdentificationNumber", sup.getTaxpayerIdentificationNumber().equals("") ? "NA" : sup.getTaxpayerIdentificationNumber());
                request.form("companyLocation", sup.getCompanyLocation().equals("") ? "NA" : sup.getCompanyLocation());
                request.form("businessScope", "医药");
                request.form("serviceCharge", 0);
                request.form("invoiceType", 10);
                request.form("invoiceContent", "没有");
                request.form("recordDate", "2024-05-01");
                request.form("stateCode", sup.getStateCode());
                request.form("multipartFile", file);
                i++;
                if (i >= 500) {
                    i = 0;
                    Thread.sleep(60000);
                }
                String body = request.execute().body();
                logs.add(sup + "===" + body);
                System.out.println(sup + "===" + body + "\n" + a++);

            }
            if (logs.size() <= 0) {
                System.out.println("无异常");
            }

            logs.forEach(System.out::println);
        }
    }

    public Integer stringToInteger(String strValue) {
        if (strValue == null || strValue.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(strValue);
        } catch (NumberFormatException e) {
            System.out.println(strValue + " 不能转换为整数.");
            return null;
        }
    }
}
