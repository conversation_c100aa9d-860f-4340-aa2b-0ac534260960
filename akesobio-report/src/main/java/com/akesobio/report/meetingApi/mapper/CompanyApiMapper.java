package com.akesobio.report.meetingApi.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.meetingApi.domain.CompanyApi;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DataSource(value = DataSourceType.EKP)
@Mapper
public interface CompanyApiMapper {

     List<CompanyApi> getAll();

}
