package com.akesobio.report.meetingApi.domain;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class CompanyApi {

    private String companyName;
    private String companyCode;
    private String chineseName;
    private String shortName;
    private String englishName;
    private String passportNumber;
    private String companyType;
    private String legalPerson;
    private String registerCapital;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishmentDate;
    private String operatingPeriod;
    private String address;
    private String businessScope;
    private int stateCode;
}
