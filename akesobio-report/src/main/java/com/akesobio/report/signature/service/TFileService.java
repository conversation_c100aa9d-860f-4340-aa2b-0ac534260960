package com.akesobio.report.signature.service;

import com.akesobio.report.signature.domain.FileVO;
import com.akesobio.report.signature.domain.TFile;
import com.baomidou.mybatisplus.extension.service.IService;


import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

public interface TFileService extends IService<TFile> {
    public void downLoadMaterialFile(HttpServletResponse response, List<String> filePathList) throws Exception;
    String generateFileName(String suffix);
    String generateFolder();
    public FileInputStream readFileWithFileName(String path,String name) throws FileNotFoundException;
    /**
     * 根据ID获取视图对象
     * @param id
     * @return
     */
    FileVO getVOById(Integer id);

    /**
     * 文件上传
     * @param content
     * @param originFileName
     * @return
     */
    FileVO storeFile(byte[] content, String originFileName,Long foreignKey);

    /**
     * 存储文件到本地
     * @param content
     * @param path
     * @param fileName
     */
    void storeFileWithFileName(byte[] content, String path, String fileName);

    FileInputStream readFile(TFile tFile) throws Exception;
}
