package com.akesobio.report.signature.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.constant.HttpStatus;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.DateUtils;
import com.akesobio.common.utils.ImageUtils;
import com.akesobio.common.utils.pdf.PdfAddContentParam;
import com.akesobio.common.utils.pdf.PdfUtils;
import com.akesobio.report.signature.domain.ImageBase64;
import com.akesobio.report.signature.domain.TFile;
import com.akesobio.report.signature.service.TFileService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.itextpdf.text.Image;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/SignatureController")
public class SignatureController extends BaseController {

    @Resource
    private TFileService tFileService;

    private String fileAddress = "D:\\file\\入厂须知.pdf";

    @GetMapping("documentList")
    @Log(title = "文件归档列表", businessType = BusinessType.OTHER)
    public TableDataInfo documentList(TFile file){


//        if(file.getBeginTime() == null) file.setBeginTime(DateUtils.getFirstDay().getTime());
//
//        if(file.getEndTime() == null) file.setEndTime(DateUtils.getLastDay().getTime());

        LambdaQueryWrapper<TFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(file.getCreateTime() != null && file.getEndTime() != null)
            lambdaQueryWrapper.between(TFile::getCreateTime,new Date(file.getBeginTime()),new Date(file.getEndTime()));
        lambdaQueryWrapper.orderByAsc(TFile::getCreateTime);
        startPage();
        List<TFile> list = tFileService.list(lambdaQueryWrapper);
        return getDataTable(list);
    }

//    @Log(title = "文件签名", businessType = BusinessType.INSERT)
    @PostMapping("pdfAddImage")
    @Transactional
    public AjaxResult pdfAddImage(@RequestBody ImageBase64 imageBase64) throws Exception {
        String base64String  = imageBase64.getBase64String().replace("data:image/png;base64,","");
        File file =  new File(fileAddress);
        Image image = ImageUtils.convertBase64StrToImage1(base64String,"png");

        String folder = tFileService.generateFolder();
        String filePath = "d:/file/" + folder + File.separatorChar;
        String suffix = ".pdf";
        String fileName = tFileService.generateFileName(suffix);
        // 目录不存在则创建
        File catalogue = new File(filePath);
        if (!catalogue.exists()) {
            catalogue.mkdirs();
        }
        String newPath = filePath+fileName;
        Calendar calendar = Calendar.getInstance();
        // 获取当前年
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月
        int month = calendar.get(Calendar.MONTH) + 1;
        String monthStr = String.valueOf(month);
        if(month < 10)
           monthStr = "0"+month;
        // 获取当前日
        int day = calendar.get(Calendar.DATE);
        String dayStr = String.valueOf(day);
        if(day < 10)
            dayStr = "0"+day;
//            // 按坐标添加批注
        PdfAddContentParam yearParam = new PdfAddContentParam(String.valueOf(year), 1, 379F, 149F, 564F, 170F);
        PdfAddContentParam monthParam = new PdfAddContentParam(monthStr, 1, 415F, 149F, 570F, 170F);
        PdfAddContentParam dayParam = new PdfAddContentParam(dayStr, 1, 439F, 149F, 646F, 170F);
        List<PdfAddContentParam> pdfAddContentParams = new ArrayList<>();
        Collections.addAll(pdfAddContentParams, yearParam,monthParam,dayParam);
        PdfUtils.addImage(file,newPath,420,185,image,pdfAddContentParams);
        // 入库
        TFile tFile = new TFile();
        tFile.setName(fileName);
        tFile.setSuffix(suffix);
        tFile.setPath(folder);
        tFile.setOldName("入厂须知.pdf");
        tFile.setCreateTime(new Date());
        tFile.setUpdateTime(new Date());
        tFile.setForeignKey(null);
        tFile.setIsDelete(0);
        tFileService.save(tFile);
       return AjaxResult.success().put("id",tFile.getId());
    }

    @GetMapping("returnPdfStream")
    public void returnPdfStream(HttpServletResponse response) throws Exception {
        File file =  new File(fileAddress);
        Calendar calendar = Calendar.getInstance();
        // 获取当前年
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月
        int month = calendar.get(Calendar.MONTH) + 1;
        // 获取当前日
        int day = calendar.get(Calendar.DATE);
//            // 按坐标添加批注
        PdfAddContentParam yearParam = new PdfAddContentParam(String.valueOf(year), 1, 345F, 55F, 545F, 102F);
        PdfAddContentParam monthParam = new PdfAddContentParam(String.valueOf(month), 1, 410F, 55F, 610F, 102F);
        PdfAddContentParam dayParam = new PdfAddContentParam(String.valueOf(day), 1, 455F, 55F, 655F, 102F);
        List<PdfAddContentParam> pdfAddContentParams = new ArrayList<>();
        Collections.addAll(pdfAddContentParams, yearParam,monthParam,dayParam);
        byte[] bytes = PdfUtils.addText(file,pdfAddContentParams);

        PdfUtils.returnPdfStream(response,bytes,bytes.length);
    }

    @GetMapping("returnPdfStream/{id}")
//    @Log(title = "归档文件查看", businessType = BusinessType.OTHER)
    public void returnPdfStream(HttpServletResponse response,@PathVariable("id") Long id) throws Exception {
        TFile tFile = tFileService.getById(id);
        String filePath = "d:/file/" + tFile.getPath() + File.separatorChar;
        FileInputStream fileInputStream = tFileService.readFileWithFileName(filePath,tFile.getName());
        PdfUtils.returnPdfStream(response,fileInputStream);
    }

    @PostMapping("downloads")
//    @Log(title = "归档文件下载", businessType = BusinessType.EXPORT)
    public void downloads(HttpServletResponse response,@RequestParam("ids") List<String> ids) throws Exception {
        List<TFile> tFiles = tFileService.listByIds(ids);
        List<String> filePaths = tFiles.stream().map(tFile ->{
            String filePath = "d:/file/" + tFile.getPath() + File.separatorChar+tFile.getName();
            return  filePath;
        }).collect(Collectors.toList());
        tFileService.downLoadMaterialFile(response,filePaths);
    }


}
