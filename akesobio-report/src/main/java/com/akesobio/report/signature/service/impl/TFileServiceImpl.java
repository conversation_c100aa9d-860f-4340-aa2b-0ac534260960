package com.akesobio.report.signature.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.akesobio.report.signature.domain.FileVO;
import com.akesobio.report.signature.domain.TFile;
import com.akesobio.report.signature.mapper.TFileMapper;
import com.akesobio.report.signature.service.TFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class TFileServiceImpl extends ServiceImpl<TFileMapper, TFile> implements TFileService {

//    @Value("${file.path}")
    private String FILE_PATH = "d:/file/";
//    @Value("${access.base-url.file}")
    private String ACCESS_BASE_URL ="d:/file/";
    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    @Override
    public FileVO getVOById(Integer id) {
        if (id != null) {
            TFile TFile = this.getById(id);
            if (TFile != null) {
                FileVO result = BeanUtil.toBean(TFile, FileVO.class);
                result.setUrl(genAccessUrl(TFile.getPath(), TFile.getName()));
                return result;
            }
        }
        return null;
    }
    @Override
    @Transactional
    public FileVO storeFile(byte[] content, String originFileName,Long foreignKey) {
        // 获取文件后缀 生成目录路径
        // 配置文件里的file.path + yyyyMMdd 格式组成文件夹路径
        String folder = LocalDateTime.now().format(dateTimeFormatter);
        String suffix = originFileName.substring(originFileName.lastIndexOf(".")),
               filePath = FILE_PATH + folder + File.separatorChar;
        // 保存文件并返回文件名
        String fileName = this.storeFile(content, filePath, suffix);
        // 入库
        TFile file = new TFile();
        file.setName(fileName);
        file.setSuffix(suffix);
        file.setPath(folder);
        file.setOldName(originFileName);
        file.setCreateTime(new Date());
        file.setUpdateTime(new Date());
        file.setForeignKey(foreignKey);
        file.setIsDelete(0);
        baseMapper.insert(file);
        FileVO result = BeanUtil.toBean(file, FileVO.class);
        result.setUrl(genAccessUrl(folder, fileName));
        result.setId(file.getId());
        return result;
    }

    public FileInputStream readFile(TFile tFile) throws FileNotFoundException {
       String filePath = FILE_PATH + tFile.getPath() + File.separatorChar;
       return readFileWithFileName(filePath,tFile.getName());
    }
    @Override
    public void storeFileWithFileName(byte[] content, String path, String fileName) {
        // 目录不存在则创建
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
        try (FileOutputStream os = new FileOutputStream(path + fileName); ByteArrayInputStream is = new ByteArrayInputStream(content)) {
            IOUtils.copy(is, os);
        } catch (IOException e) {

        }
    }
    public FileInputStream readFileWithFileName(String path,String name) throws FileNotFoundException {
        FileInputStream inputStream = new FileInputStream(new File(path,name));
        return inputStream;
    }
    private String storeFile(byte[] content, String path, String suffix) {
        String fileName = generateFileName(suffix);
        storeFileWithFileName(content, path, fileName);
        return fileName;
    }
    public String generateFileName(String suffix) {
        return generateFileName() + suffix;
    }
    public String generateFolder(){
        return LocalDateTime.now().format(dateTimeFormatter);
    }
    private String genAccessUrl(String folder, String name) {
        return ACCESS_BASE_URL + folder + "/" + name;
    }
    private String generateFileName() {
        return System.currentTimeMillis() + "_" + RandomUtil.randomNumbers(6);
    }
    public void downLoadMaterialFile(HttpServletResponse response,List<String> filePathList) throws Exception {
        if(filePathList == null) return;
        //获取附件路径（数据库获取）
        if(filePathList.size() == 1){
            //只存在一个附件时直接下载对应附件
            File file = new File(filePathList.get(0));
            if(!file.exists()){
                throw new Exception("文件不存在");
            }
            //输出文件流
            writeFileToRes(response, file.getName(), file);
        }else if(filePathList.size() > 1){
            //压缩包名称（会拼上当前时间）
            String datumName = "压缩包名称";
            //压缩文件
            File file = compressedFileToZip(datumName, filePathList);
            //输出文件流
            writeFileToRes(response, file.getName(), file);
            //删除压缩包
            if(file.exists()){
                file.delete();
            }
        }
    }

    /**
     * 压缩文件
     * @param datumName 压缩包名称
     * @param filePathList 附件路径
     * @return File
     * @throws Exception Exception
     */
    private File compressedFileToZip(String datumName, List<String> filePathList) throws Exception {
        //压缩包具体名称（拼接时间戳防止重名）
        String zipFileName = datumName + "-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
        //生成压缩包存储地址（最后会删掉）
        String fileZip = "D:/" + zipFileName;
        OutputStream os=null;
        ZipOutputStream zos = null ;
        File file = new File(fileZip);
        try {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            os=new FileOutputStream(file);
            //压缩文件
            zos = new ZipOutputStream(os);
            byte[] buf = new byte[1024];
            for (String filePath : filePathList) {
                File tempFile = new File(filePath);
                //在压缩包中添加文件夹
                //zos.putNextEntry(new ZipEntry("测试/"+tempFile.getName()));
                //直接在压缩包中添加文件
                zos.putNextEntry(new ZipEntry(tempFile.getName()));
                int len;
                FileInputStream in = new FileInputStream(tempFile);
                while ((len = in.read(buf)) != -1){
                    zos.write(buf, 0, len);
                }
                zos.closeEntry();
                in.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("文件打包:"+e.getMessage());
        }finally {
            //关闭流
            if(zos != null){
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            //关闭流
            if(os!= null){
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    /**
     * 输出文件流到response
     * @param response response
     * @param fileName fileName
     * @param file file
     * @throws IOException IOException
     */
    private void writeFileToRes(HttpServletResponse response, String fileName, File file) throws IOException {
        FileInputStream inputStream = new FileInputStream(file);
        //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
        response.setContentType("application/octet-stream");
        //2.设置文件头：最后一个参数是设置下载文件名
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.addHeader("Content-Length", "" + file.length());

        //3.通过response获取ServletOutputStream对象(out)
        ServletOutputStream out = response.getOutputStream();

        int b = 0;
        byte[] buffer = new byte[1024];
        while (b != -1) {
            b = inputStream.read(buffer);
            //4.写到输出流(out)中
            out.write(buffer, 0, b);
        }
        out.flush();
        out.close();
        inputStream.close();
    }

}
