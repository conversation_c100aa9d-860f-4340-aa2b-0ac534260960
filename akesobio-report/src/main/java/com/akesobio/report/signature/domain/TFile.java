package com.akesobio.report.signature.domain;

import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@TableName("t_file")
public class TFile  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 文件名
     */
    private String name;
    /**
     * 文件类型
     */
    private String suffix;
    /**
     * 文件相对路径
     */
    private String path;
    /**
     * 文件原名字
     */
//    @TableField(exist = false)
    private String oldName;

    private Long foreignKey;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    @TableField(exist = false)
    private Long beginTime;

    @TableField(exist = false)
    private Long endTime;
}
