package com.akesobio.report.mdm.service.impl;

import java.time.LocalDateTime;
import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.mdm.mapper.OaInstitutionalMasterDataMapper;
import com.akesobio.report.mdm.domain.OaInstitutionalMasterData;
import com.akesobio.report.mdm.service.IOaInstitutionalMasterDataService;

/**
 * OA机构主数据清洗Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class OaInstitutionalMasterDataServiceImpl implements IOaInstitutionalMasterDataService {
    @Autowired
    private OaInstitutionalMasterDataMapper oaInstitutionalMasterDataMapper;

    /**
     * 查询OA机构主数据清洗
     *
     * @param id OA机构主数据清洗主键
     * @return OA机构主数据清洗
     */
    @Override
    public OaInstitutionalMasterData selectOaInstitutionalMasterDataById(Integer id) {
        return oaInstitutionalMasterDataMapper.selectOaInstitutionalMasterDataById(id);
    }

    /**
     * 查询OA机构主数据清洗列表
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return OA机构主数据清洗
     */
    @Override
    public List<OaInstitutionalMasterData> selectOaInstitutionalMasterDataList(OaInstitutionalMasterData oaInstitutionalMasterData) {
        return oaInstitutionalMasterDataMapper.selectOaInstitutionalMasterDataList(oaInstitutionalMasterData);
    }

    /**
     * 新增OA机构主数据清洗
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return 结果
     */
    @Override
    public int insertOaInstitutionalMasterData(OaInstitutionalMasterData oaInstitutionalMasterData) {
        // 设置当前时间（年月日时分秒）
        oaInstitutionalMasterData.setCreationTime(new Date());
        return oaInstitutionalMasterDataMapper.insertOaInstitutionalMasterData(oaInstitutionalMasterData);
    }

    /**
     * 修改OA机构主数据清洗
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return 结果
     */
    @Override
    public int updateOaInstitutionalMasterData(OaInstitutionalMasterData oaInstitutionalMasterData) {
        return oaInstitutionalMasterDataMapper.updateOaInstitutionalMasterData(oaInstitutionalMasterData);
    }

    /**
     * 批量删除OA机构主数据清洗
     *
     * @param ids 需要删除的OA机构主数据清洗主键
     * @return 结果
     */
    @Override
    public int deleteOaInstitutionalMasterDataByIds(Integer[] ids) {
        return oaInstitutionalMasterDataMapper.deleteOaInstitutionalMasterDataByIds(ids);
    }

    /**
     * 删除OA机构主数据清洗信息
     *
     * @param id OA机构主数据清洗主键
     * @return 结果
     */
    @Override
    public int deleteOaInstitutionalMasterDataById(Integer id) {
        return oaInstitutionalMasterDataMapper.deleteOaInstitutionalMasterDataById(id);
    }

    @Override
    public Map<String, List<String>> insertOaInstitutionalMasterDataList(List<OaInstitutionalMasterData> list) {
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        for (OaInstitutionalMasterData item : list) {
            try {
                if (item.getCreationTime() == null) {
                    item.setCreationTime(new Date());
                }
                int result = oaInstitutionalMasterDataMapper.insertOaInstitutionalMasterData(item);
                if (result > 0) {
                    successList.add(item.getOaTrackingNumber());
                } else {
                    failList.add(item.getOaTrackingNumber());
                }
            } catch (Exception e) {
                failList.add(item.getOaTrackingNumber());
            }
        }
        Map<String, List<String>> listMap = new HashMap<>();
        listMap.put("successList", successList);
        listMap.put("failList", failList);

        return listMap;
    }
}
