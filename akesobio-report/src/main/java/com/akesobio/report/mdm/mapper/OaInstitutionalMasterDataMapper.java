package com.akesobio.report.mdm.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.mdm.domain.OaInstitutionalMasterData;

/**
 * OA机构主数据清洗Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@DataSource(value = DataSourceType.MDMDATA)
public interface OaInstitutionalMasterDataMapper {
    /**
     * 查询OA机构主数据清洗
     *
     * @param id OA机构主数据清洗主键
     * @return OA机构主数据清洗
     */
    public OaInstitutionalMasterData selectOaInstitutionalMasterDataById(Integer id);

    /**
     * 查询OA机构主数据清洗列表
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return OA机构主数据清洗集合
     */
    public List<OaInstitutionalMasterData> selectOaInstitutionalMasterDataList(OaInstitutionalMasterData oaInstitutionalMasterData);

    /**
     * 新增OA机构主数据清洗
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return 结果
     */
    public int insertOaInstitutionalMasterData(OaInstitutionalMasterData oaInstitutionalMasterData);

    /**
     * 修改OA机构主数据清洗
     *
     * @param oaInstitutionalMasterData OA机构主数据清洗
     * @return 结果
     */
    public int updateOaInstitutionalMasterData(OaInstitutionalMasterData oaInstitutionalMasterData);

    /**
     * 删除OA机构主数据清洗
     *
     * @param id OA机构主数据清洗主键
     * @return 结果
     */
    public int deleteOaInstitutionalMasterDataById(Integer id);

    /**
     * 批量删除OA机构主数据清洗
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaInstitutionalMasterDataByIds(Integer[] ids);

    /**
     * 批量新增OA机构主数据清洗
     *
     * @param list OA机构主数据清洗
     * @return 结果
     */
    public int insertOaInstitutionalMasterDataBatch(List<OaInstitutionalMasterData> list);
}
