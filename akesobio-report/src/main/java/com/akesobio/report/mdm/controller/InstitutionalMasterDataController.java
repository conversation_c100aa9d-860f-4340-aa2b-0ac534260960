package com.akesobio.report.mdm.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.mdm.domain.InstitutionalMasterData;
import com.akesobio.report.mdm.service.IInstitutionalMasterDataService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 机构主数据Controller
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/mdm/institutionalMasterData")
public class InstitutionalMasterDataController extends BaseController {
    @Autowired
    private IInstitutionalMasterDataService institutionalMasterDataService;

    @PostMapping("/addOrEditByInstitutionCode")
    public AjaxResult addOrEditByInstitutionCode(@RequestBody InstitutionalMasterData institutionalMasterData) {
        // 根据机构编码查询是否已存在记录
        InstitutionalMasterData existingData = institutionalMasterDataService.selectInstitutionalMasterDataByCode(institutionalMasterData.getInstitutionCode());
        if (existingData != null) {
            // 如果存在则更新
            institutionalMasterData.setId(existingData.getId());
            return toAjax(institutionalMasterDataService.updateInstitutionalMasterData(institutionalMasterData));
        } else {
            // 如果不存在则新增
            return toAjax(institutionalMasterDataService.insertInstitutionalMasterData(institutionalMasterData));
        }
    }


    /**
     * 查询机构主数据列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstitutionalMasterData institutionalMasterData) {
        startPage();
        List<InstitutionalMasterData> list = institutionalMasterDataService.selectInstitutionalMasterDataList(institutionalMasterData);
        return getDataTable(list);
    }

    /**
     * 导出机构主数据列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:export')")
    @Log(title = "机构主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstitutionalMasterData institutionalMasterData) {
        List<InstitutionalMasterData> list = institutionalMasterDataService.selectInstitutionalMasterDataList(institutionalMasterData);
        ExcelUtil<InstitutionalMasterData> util = new ExcelUtil<InstitutionalMasterData>(InstitutionalMasterData.class);
        util.exportExcel(response, list, "机构主数据数据");
    }

    /**
     * 获取机构主数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(institutionalMasterDataService.selectInstitutionalMasterDataById(id));
    }

    /**
     * 新增机构主数据
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:add')")
    @Log(title = "机构主数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstitutionalMasterData institutionalMasterData) {
        return toAjax(institutionalMasterDataService.insertInstitutionalMasterData(institutionalMasterData));
    }

    /**
     * 修改机构主数据
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:edit')")
    @Log(title = "机构主数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstitutionalMasterData institutionalMasterData) {
        return toAjax(institutionalMasterDataService.updateInstitutionalMasterData(institutionalMasterData));
    }

    /**
     * 删除机构主数据
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:remove')")
    @Log(title = "机构主数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(institutionalMasterDataService.deleteInstitutionalMasterDataByIds(ids));
    }

    /**
     * 导入机构主数据
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:import')")
    @Log(title = "机构主数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<InstitutionalMasterData> util = new ExcelUtil<InstitutionalMasterData>(InstitutionalMasterData.class);
        List<InstitutionalMasterData> list = util.importExcel(file.getInputStream());
        String message = institutionalMasterDataService.importInstitutionalMasterData(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     *
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('mdm:institutionalMasterData:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstitutionalMasterData> util = new ExcelUtil<InstitutionalMasterData>(InstitutionalMasterData.class);
        util.importTemplateExcel(response, "机构主数据");
    }
}
