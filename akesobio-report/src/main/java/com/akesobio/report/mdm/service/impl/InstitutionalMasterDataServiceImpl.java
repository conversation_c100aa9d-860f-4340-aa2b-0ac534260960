package com.akesobio.report.mdm.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.mdm.mapper.InstitutionalMasterDataMapper;
import com.akesobio.report.mdm.domain.InstitutionalMasterData;
import com.akesobio.report.mdm.service.IInstitutionalMasterDataService;

/**
 * 机构主数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class InstitutionalMasterDataServiceImpl implements IInstitutionalMasterDataService {

    private static final Logger log = LoggerFactory.getLogger(InstitutionalMasterDataServiceImpl.class);

    @Autowired
    private InstitutionalMasterDataMapper institutionalMasterDataMapper;

    @Override
    public InstitutionalMasterData selectInstitutionalMasterDataByCode(String institutionCode) {
        return institutionalMasterDataMapper.selectInstitutionalMasterDataByCode(institutionCode);
    }

    /**
     * 查询机构主数据
     *
     * @param id 机构主数据主键
     * @return 机构主数据
     */
    @Override
    public InstitutionalMasterData selectInstitutionalMasterDataById(Integer id) {
        return institutionalMasterDataMapper.selectInstitutionalMasterDataById(id);
    }

    /**
     * 查询机构主数据列表
     *
     * @param institutionalMasterData 机构主数据
     * @return 机构主数据
     */
    @Override
    public List<InstitutionalMasterData> selectInstitutionalMasterDataList(InstitutionalMasterData institutionalMasterData) {
        return institutionalMasterDataMapper.selectInstitutionalMasterDataList(institutionalMasterData);
    }

    /**
     * 新增机构主数据
     *
     * @param institutionalMasterData 机构主数据
     * @return 结果
     */
    @Override
    public int insertInstitutionalMasterData(InstitutionalMasterData institutionalMasterData) {
        return institutionalMasterDataMapper.insertInstitutionalMasterData(institutionalMasterData);
    }

    /**
     * 修改机构主数据
     *
     * @param institutionalMasterData 机构主数据
     * @return 结果
     */
    @Override
    public int updateInstitutionalMasterData(InstitutionalMasterData institutionalMasterData) {
        return institutionalMasterDataMapper.updateInstitutionalMasterData(institutionalMasterData);
    }

    /**
     * 批量删除机构主数据
     *
     * @param ids 需要删除的机构主数据主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionalMasterDataByIds(Integer[] ids) {
        return institutionalMasterDataMapper.deleteInstitutionalMasterDataByIds(ids);
    }

    /**
     * 删除机构主数据信息
     *
     * @param id 机构主数据主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionalMasterDataById(Integer id) {
        return institutionalMasterDataMapper.deleteInstitutionalMasterDataById(id);
    }

    /**
     * 导入机构主数据信息数据
     *
     * @param list            机构主数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importInstitutionalMasterData(List<InstitutionalMasterData> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (InstitutionalMasterData a : list) {
            try {
                institutionalMasterDataMapper.insertInstitutionalMasterData(a);
                successNum++;
                successMsg.append("<br/>" + successNum + "、机构名称 " + a.getInstitutionName() + "、机构编码 " + a.getInstitutionCode() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、机构名称 " + a.getInstitutionName() + "、机构编码 " + a.getInstitutionCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
