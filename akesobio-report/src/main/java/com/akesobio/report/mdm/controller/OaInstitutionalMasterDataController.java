package com.akesobio.report.mdm.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.mdm.domain.InstitutionalMasterData;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.mdm.domain.OaInstitutionalMasterData;
import com.akesobio.report.mdm.service.IOaInstitutionalMasterDataService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * OA机构主数据清洗Controller
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/mdm/oaInstitutionalMasterData")
public class OaInstitutionalMasterDataController extends BaseController {
    @Autowired
    private IOaInstitutionalMasterDataService oaInstitutionalMasterDataService;


    @PostMapping("/addOaInstitutionalMasterData")
    public AjaxResult addOaInstitutionalMasterData(@RequestBody List<OaInstitutionalMasterData> list) {
        Map<String, List<String>> listMap = oaInstitutionalMasterDataService.insertOaInstitutionalMasterDataList(list);
        return AjaxResult.success(listMap);
    }

    /**
     * 查询OA机构主数据清洗列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaInstitutionalMasterData oaInstitutionalMasterData) {
        startPage();
        List<OaInstitutionalMasterData> list = oaInstitutionalMasterDataService.selectOaInstitutionalMasterDataList(oaInstitutionalMasterData);
        return getDataTable(list);
    }

    /**
     * 导出OA机构主数据清洗列表
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:export')")
    @Log(title = "OA机构主数据清洗", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaInstitutionalMasterData oaInstitutionalMasterData) {
        List<OaInstitutionalMasterData> list = oaInstitutionalMasterDataService.selectOaInstitutionalMasterDataList(oaInstitutionalMasterData);
        ExcelUtil<OaInstitutionalMasterData> util = new ExcelUtil<OaInstitutionalMasterData>(OaInstitutionalMasterData.class);
        util.exportExcel(response, list, "OA机构主数据清洗数据");
    }

    /**
     * 获取OA机构主数据清洗详细信息
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(oaInstitutionalMasterDataService.selectOaInstitutionalMasterDataById(id));
    }

    /**
     * 新增OA机构主数据清洗
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:add')")
    @Log(title = "OA机构主数据清洗", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaInstitutionalMasterData oaInstitutionalMasterData) {
        return toAjax(oaInstitutionalMasterDataService.insertOaInstitutionalMasterData(oaInstitutionalMasterData));
    }

    /**
     * 修改OA机构主数据清洗
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:edit')")
    @Log(title = "OA机构主数据清洗", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaInstitutionalMasterData oaInstitutionalMasterData) {
        return toAjax(oaInstitutionalMasterDataService.updateOaInstitutionalMasterData(oaInstitutionalMasterData));
    }

    /**
     * 删除OA机构主数据清洗
     */
    @PreAuthorize("@ss.hasPermi('mdm:oaInstitutionalMasterData:remove')")
    @Log(title = "OA机构主数据清洗", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(oaInstitutionalMasterDataService.deleteOaInstitutionalMasterDataByIds(ids));
    }
}
