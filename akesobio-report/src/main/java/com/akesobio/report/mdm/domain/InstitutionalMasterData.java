package com.akesobio.report.mdm.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 机构主数据对象 institutional_master_data
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public class InstitutionalMasterData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 机构名称
     */
    @Excel(name = "机构名称")
    private String institutionName;

    /**
     * 机构编码
     */
    @Excel(name = "机构编码")
    private String institutionCode;

    /**
     * 机构别名
     */
    @Excel(name = "机构别名")
    private String institutionAlias;


    /**
     * 客户类型
     */
    @Excel(name = "客户类型")
    private String customerType;

    /**
     * 省份
     */
    @Excel(name = "省份")
    private String province;

    /**
     * 城市
     */
    @Excel(name = "城市")
    private String city;

    /**
     * 区县
     */
    @Excel(name = "区县")
    private String district;

    /**
     * 地址
     */
    @Excel(name = "地址")
    private String address;

    /**
     * 一级属性
     */
    @Excel(name = "一级属性")
    private String primaryAttribute;

    /**
     * 二级属性
     */
    @Excel(name = "二级属性")
    private String secondaryAttribute;

    /**
     * 三级属性
     */
    @Excel(name = "三级属性")
    private String thirdLevelAttribute;

    /**
     * 社会统一信用代码
     */
    @Excel(name = "社会统一信用代码")
    private String creditCode;

    /**
     * 医院级别
     */
    @Excel(name = "医院级别")
    private String hospitalLevel;

    /**
     * 人社等次
     */
    @Excel(name = "人社等次")
    private String grade;

    /**
     * 经度
     */
    //@Excel(name = "经度")
    private String iongitude;

    /**
     * 医疗机构总分院关联关系
     */
    @Excel(name = "医疗机构总分院关联关系")
    private String medicalInstitutionRelations;


    /**
     * 状态
     */
    @Excel(name = "状态")
    private String state;

    public String getMedicalInstitutionRelations() {
        return medicalInstitutionRelations;
    }

    public void setMedicalInstitutionRelations(String medicalInstitutionRelations) {
        this.medicalInstitutionRelations = medicalInstitutionRelations;
    }

    public String getInstitutionAlias() {
        return institutionAlias;
    }

    public void setInstitutionAlias(String institutionAlias) {
        this.institutionAlias = institutionAlias;
    }

    public String getPrimaryAttribute() {
        return primaryAttribute;
    }

    public void setPrimaryAttribute(String primaryAttribute) {
        this.primaryAttribute = primaryAttribute;
    }

    public String getSecondaryAttribute() {
        return secondaryAttribute;
    }

    public void setSecondaryAttribute(String secondaryAttribute) {
        this.secondaryAttribute = secondaryAttribute;
    }

    public String getThirdLevelAttribute() {
        return thirdLevelAttribute;
    }

    public void setThirdLevelAttribute(String thirdLevelAttribute) {
        this.thirdLevelAttribute = thirdLevelAttribute;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getHospitalLevel() {
        return hospitalLevel;
    }

    public void setHospitalLevel(String hospitalLevel) {
        this.hospitalLevel = hospitalLevel;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvince() {
        return province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCity() {
        return city;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getDistrict() {
        return district;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setIongitude(String iongitude) {
        this.iongitude = iongitude;
    }

    public String getIongitude() {
        return iongitude;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("institutionName", getInstitutionName())
                .append("institutionCode", getInstitutionCode())
                .append("customerType", getCustomerType())
                .append("province", getProvince())
                .append("city", getCity())
                .append("district", getDistrict())
                .append("address", getAddress())
                .append("iongitude", getIongitude())
                .append("state", getState())
                .append("institutionAlias", getInstitutionAlias())
                .append("primaryAttribute", getPrimaryAttribute())
                .append("secondaryAttribute", getSecondaryAttribute())
                .append("thirdLevelAttribute", getThirdLevelAttribute())
                .append("creditCode", getCreditCode())
                .append("hospitalLevel", getHospitalLevel())
                .append("grade", getGrade())
                .append("medicalInstitutionRelations", getMedicalInstitutionRelations())
                .toString();
    }
}
