package com.akesobio.report.mdm.domain;

import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * OA机构主数据清洗对象 oa_institutional_master_data
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
public class OaInstitutionalMasterData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * DMS单号
     */
    @Excel(name = "CRM单号")
    private String oaTrackingNumber;

    /**
     * 主数据处理状态
     */
    @Excel(name = "主数据处理状态")
    private String state;

    /**
     * 机构编码
     */
    @Excel(name = "机构编码")
    private String institutionCode;

    /**
     * 机构名称
     */
    @Excel(name = "机构名称")
    private String institutionName;

    /**
     * 机构别名
     */
    @Excel(name = "机构别名")
    private String institutionAlias;

    /**
     * 注册地址
     */
    @Excel(name = "注册地址")
    private String address;

    /**
     * 注册省份
     */
    @Excel(name = "注册省份")
    private String province;

    /**
     * 注册地市
     */
    @Excel(name = "注册地市")
    private String city;

    /**
     * 注册区县
     */
    @Excel(name = "注册区县")
    private String district;

    /**
     * 经营状态
     */
    @Excel(name = "经营状态")
    private String businessStatus;

    /**
     * 社会统一信用代码
     */
    @Excel(name = "社会统一信用代码")
    private String creditCode;

    /**
     * 医院级别
     */
    @Excel(name = "医院级别")
    private String hospitalLevel;

    /**
     * 人社等次
     */
    @Excel(name = "人社等次")
    private String grade;

    /**
     * 医疗机构总分院关联关系
     */
    @Excel(name = "医疗机构总分院关联关系")
    private String medicalInstitutionRelations;

    /**
     * 一级属性
     */
    @Excel(name = "一级属性")
    private String primaryAttribute;

    /**
     * 二级属性
     */
    @Excel(name = "二级属性")
    private String secondaryAttribute;

    /**
     * 三级属性
     */
    @Excel(name = "三级属性")
    private String thirdLevelAttribute;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creationTime;


}
