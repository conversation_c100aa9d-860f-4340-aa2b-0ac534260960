package com.akesobio.report.mdm.service;

import java.util.List;

import com.akesobio.report.mdm.domain.InstitutionalMasterData;

/**
 * 机构主数据Service接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface IInstitutionalMasterDataService {


    /**
     * 查询机构主数据
     *
     * @param institutionCode 机构主数据编码
     * @return 机构主数据
     */
    public InstitutionalMasterData selectInstitutionalMasterDataByCode(String institutionCode);

    /**
     * 查询机构主数据
     *
     * @param id 机构主数据主键
     * @return 机构主数据
     */
    public InstitutionalMasterData selectInstitutionalMasterDataById(Integer id);

    /**
     * 查询机构主数据列表
     *
     * @param institutionalMasterData 机构主数据
     * @return 机构主数据集合
     */
    public List<InstitutionalMasterData> selectInstitutionalMasterDataList(InstitutionalMasterData institutionalMasterData);

    /**
     * 新增机构主数据
     *
     * @param institutionalMasterData 机构主数据
     * @return 结果
     */
    public int insertInstitutionalMasterData(InstitutionalMasterData institutionalMasterData);

    /**
     * 修改机构主数据
     *
     * @param institutionalMasterData 机构主数据
     * @return 结果
     */
    public int updateInstitutionalMasterData(InstitutionalMasterData institutionalMasterData);

    /**
     * 批量删除机构主数据
     *
     * @param ids 需要删除的机构主数据主键集合
     * @return 结果
     */
    public int deleteInstitutionalMasterDataByIds(Integer[] ids);

    /**
     * 删除机构主数据信息
     *
     * @param id 机构主数据主键
     * @return 结果
     */
    public int deleteInstitutionalMasterDataById(Integer id);

    /**
     * 导入机构主数据信息数据
     *
     * @param list            机构主数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importInstitutionalMasterData(List<InstitutionalMasterData> list, Boolean isUpdateSupport);
}
