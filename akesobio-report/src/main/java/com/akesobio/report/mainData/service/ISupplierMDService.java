package com.akesobio.report.mainData.service;

import java.util.List;

import com.akesobio.report.autoacct.domain.AutoAccReceivedInventory;
import com.akesobio.report.mainData.domain.SupplierMD;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 供应商主数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface ISupplierMDService extends IService<SupplierMD>
{
    /**
     * 查询供应商主数据
     * 
     * @param sid 供应商主数据主键
     * @return 供应商主数据
     */
    public SupplierMD selectSupplierMDBySid(Long sid);

    /**
     * 查询供应商主数据列表
     * 
     * @param supplierMD 供应商主数据
     * @return 供应商主数据集合
     */
    public List<SupplierMD> selectSupplierMDList(SupplierMD supplierMD);

    /**
     * 新增供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    public int insertSupplierMD(SupplierMD supplierMD);

    /**
     * 修改供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    public int updateSupplierMD(SupplierMD supplierMD);

    /**
     * 批量删除供应商主数据
     * 
     * @param sids 需要删除的供应商主数据主键集合
     * @return 结果
     */
    public int deleteSupplierMDBySids(Long[] sids);

    /**
     * 删除供应商主数据信息
     * 
     * @param sid 供应商主数据主键
     * @return 结果
     */
    public int deleteSupplierMDBySid(Long sid);
}
