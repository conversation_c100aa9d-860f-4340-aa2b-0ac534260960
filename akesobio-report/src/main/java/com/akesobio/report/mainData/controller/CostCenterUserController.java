package com.akesobio.report.mainData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.mainData.domain.CostCenterUser;
import com.akesobio.report.mainData.service.ICostCenterUserService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 成本中心中的用户Controller
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/mainData/costCenterUser")
public class CostCenterUserController extends BaseController
{
//    @Autowired
//    private ICostCenterUserService costCenterUserService;
//
//    /**
//     * 查询成本中心中的用户列表
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(CostCenterUser costCenterUser)
//    {
//        startPage();
//        List<CostCenterUser> list = costCenterUserService.selectCostCenterUserList(costCenterUser);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出成本中心中的用户列表
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:export')")
//    @Log(title = "成本中心中的用户", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, CostCenterUser costCenterUser)
//    {
//        List<CostCenterUser> list = costCenterUserService.selectCostCenterUserList(costCenterUser);
//        ExcelUtil<CostCenterUser> util = new ExcelUtil<CostCenterUser>(CostCenterUser.class);
//        util.exportExcel(response, list, "成本中心中的用户数据");
//    }
//
//    /**
//     * 获取成本中心中的用户详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(costCenterUserService.selectCostCenterUserById(id));
//    }
//
//    /**
//     * 新增成本中心中的用户
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:add')")
//    @Log(title = "成本中心中的用户", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody CostCenterUser costCenterUser)
//    {
//        return toAjax(costCenterUserService.insertCostCenterUser(costCenterUser));
//    }
//
//    /**
//     * 修改成本中心中的用户
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:edit')")
//    @Log(title = "成本中心中的用户", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody CostCenterUser costCenterUser)
//    {
//        return toAjax(costCenterUserService.updateCostCenterUser(costCenterUser));
//    }
//
//    /**
//     * 删除成本中心中的用户
//     */
//    @PreAuthorize("@ss.hasPermi('mainData:costCenterUser:remove')")
//    @Log(title = "成本中心中的用户", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(costCenterUserService.deleteCostCenterUserByIds(ids));
//    }
}
