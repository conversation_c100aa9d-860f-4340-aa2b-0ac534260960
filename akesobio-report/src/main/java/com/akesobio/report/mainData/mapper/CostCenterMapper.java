package com.akesobio.report.mainData.mapper;

import java.util.List;
import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.domain.SupplierMD;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 成本中心架构Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface CostCenterMapper extends BaseMapper<CostCenter>
{
    /**
     * 查询成本中心架构
     * 
     * @param id 成本中心架构主键
     * @return 成本中心架构
     */
    public CostCenter selectCostCenterById(Long id);

    /**
     * 查询成本中心架构列表
     * 
     * @param costCenter 成本中心架构
     * @return 成本中心架构集合
     */
    public List<CostCenter> selectCostCenterList(CostCenter costCenter);

    /**
     * 新增成本中心架构
     * 
     * @param costCenter 成本中心架构
     * @return 结果
     */
    public int insertCostCenter(CostCenter costCenter);

    /**
     * 修改成本中心架构
     * 
     * @param costCenter 成本中心架构
     * @return 结果
     */
    public int updateCostCenter(CostCenter costCenter);

    /**
     * 删除成本中心架构
     * 
     * @param id 成本中心架构主键
     * @return 结果
     */
    public int deleteCostCenterById(Long id);

    /**
     * 批量删除成本中心架构
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCostCenterByIds(Long[] ids);
}
