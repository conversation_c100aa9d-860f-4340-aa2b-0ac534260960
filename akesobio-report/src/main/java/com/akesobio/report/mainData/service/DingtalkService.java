package com.akesobio.report.mainData.service;

//import com.aliyun.tea.TeaException;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubidRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubidResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DingtalkService {


    private String appKey;
    private String appSecret;


    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
//    public com.aliyun.dingtalkoauth2_1_0.Client createClient() throws Exception {
//        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
//        config.protocol = "https";
//        config.regionId = "central";
//        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
//    }
//    public String getAccessTokenRequest() throws Exception {
////        java.util.List<String> args = java.util.Arrays.asList(args_);
//        com.aliyun.dingtalkoauth2_1_0.Client client = createClient();
//        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
//                .setAppKey(appKey)
//                .setAppSecret(appSecret);
//        try {
//            log.info("获取access_token请求参数：{}", getAccessTokenRequest);
//            com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse getAccessTokenResponse = client.getAccessToken(getAccessTokenRequest);
//            log.info("获取access_token响应参数：{}", getAccessTokenResponse);
//            return getAccessTokenResponse.getBody().getAccessToken();
//        } catch (TeaException err) {
//            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
//                // err 中含有 code 和 message 属性，可帮助开发定位问题
//                log.error(err.toString());
//                log.error("code:{}, message:{}", err.code, err.message);
//                throw new Exception(err.message);
//            }
//
//        } catch (Exception _err) {
//            TeaException err = new TeaException(_err.getMessage(), _err);
//            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
//                // err 中含有 code 和 message 属性，可帮助开发定位问题
//                log.error(err.toString());
//                log.error("code:{}, message:{}", err.code, err.message);
//                throw new Exception(err.message);
//            }
//
//        }
//        return null;
//    }



  //获取子部门列表ID
//    public static List<Long> getSubDeptIdList(Long deptId, String access_token) throws ApiException {
//        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsubid");
//        OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
//        req.setDeptId(deptId);
//        OapiV2DepartmentListsubidResponse rsp = client.execute(req, access_token);
//        log.info("获取子部门列表ID成功{}",rsp.getBody());
//        return rsp.getResult().getDeptIdList();
//    }
//
//    public static void main(String[] args) throws Exception {
//        DingtalkService dingtalkService = new DingtalkService();
//        dingtalkService.appKey = "dingoaldkfjsdkfj";
//        dingtalkService.appSecret = "dingoaldkfjsdkfj";
//        String access_token = dingtalkService.getAccessTokenRequest();
//        log.info("获取access_token成功{}",access_token);
//        List<Long> visitedNodes = new ArrayList<>();
//        dfs(1L, visitedNodes, access_token);
//        log.info("深度遍历子部门列表ID成功{}",visitedNodes);
//
//        //根据部门id 获取员工钉钉 uesrId
//
//
//    }
//
//    //深度遍历
//    public static void dfs(Long deptId, List<Long> visitedNodes, String access_token) throws ApiException {
//        if (deptId == null) {
//            return;
//        }
//        visitedNodes.add(deptId);
//        List<Long> subDeptIdList = getSubDeptIdList(deptId, access_token);
//        for (Long subDeptId : subDeptIdList) {
//            if (!visitedNodes.contains(subDeptId)) {
//                dfs(subDeptId, visitedNodes, access_token);
//            }
//        }
//    }


}
