package com.akesobio.report.mainData.service;

import java.util.List;

import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.domain.CostCenterUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 成本中心中的用户Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
public interface ICostCenterUserService extends IService<CostCenterUser>
{
//    /**
//     * 查询成本中心中的用户
//     *
//     * @param id 成本中心中的用户主键
//     * @return 成本中心中的用户
//     */
//    public CostCenterUser selectCostCenterUserById(Long id);
//
//    /**
//     * 查询成本中心中的用户列表
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 成本中心中的用户集合
//     */
//    public List<CostCenterUser> selectCostCenterUserList(CostCenterUser costCenterUser);
//
//    /**
//     * 新增成本中心中的用户
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 结果
//     */
//    public int insertCostCenterUser(CostCenterUser costCenterUser);
//
//    /**
//     * 修改成本中心中的用户
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 结果
//     */
//    public int updateCostCenterUser(CostCenterUser costCenterUser);
//
//    /**
//     * 批量删除成本中心中的用户
//     *
//     * @param ids 需要删除的成本中心中的用户主键集合
//     * @return 结果
//     */
//    public int deleteCostCenterUserByIds(Long[] ids);
//
//    /**
//     * 删除成本中心中的用户信息
//     *
//     * @param id 成本中心中的用户主键
//     * @return 结果
//     */
//    public int deleteCostCenterUserById(Long id);
}
