package com.akesobio.report.mainData.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 供应商主数据对象 SupplierMD
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public class SupplierMD extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long sid;

    /** 业务伙伴编码 */
    @Excel(name = "业务伙伴编码")
    private String partner;

    /** 银行明细标识 */
    @Excel(name = "银行明细标识")
    private String bkvid;

    /** 业务伙伴名称 */
    @Excel(name = "业务伙伴名称")
    private String nameOrg1;

    /** 搜索词1 */
    @Excel(name = "搜索词1")
    private String buSort1;

    /** 业务伙伴分组 */
    @Excel(name = "业务伙伴分组")
    private String buGroup;

    /** 社会统一信用代码 */
    @Excel(name = "社会统一信用代码")
    private String idnumber;

    /** 国家 */
    @Excel(name = "国家")
    private String country;

    /** 语言 */
    @Excel(name = "语言")
    private String langu;

    /** 时区 */
    @Excel(name = "时区")
    private String timeZone;

    /** 街道 */
    @Excel(name = "街道")
    private String steet;

    /** 电话 */
    @Excel(name = "电话")
    private String telNumber;

    /** 移动电话 */
    @Excel(name = "移动电话")
    private String mobNumber;

    /** E-mail */
    @Excel(name = "E-mail")
    private String smtpAddr;

    /** 统驭科目 */
    @Excel(name = "统驭科目")
    private String akont;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private String loevm;

    /** 银行国家 */
    @Excel(name = "银行国家")
    private String banks;

    /** 银行代码 */
    @Excel(name = "银行代码")
    private String bankl;

    /** 账户持有人 */
    @Excel(name = "账户持有人")
    private String koinh;

    /** 银行帐号 */
    @Excel(name = "银行帐号")
    private String bankn;

    /** 控制代码 */
    @Excel(name = "控制代码")
    private String bkont;

    /** 默认开票银行账号 */
    @Excel(name = "默认开票银行账号")
    private String xezer;

    /** 银行账户名称 */
    @Excel(name = "银行账户名称")
    private String accname;

    /** 是否有效 */
    @Excel(name = "是否有效")
    private Long efficient;

    /** 银行名称 */
    @Excel(name = "银行名称")
    private String banka;

    /** 营销供应商标识 */
    @Excel(name = "营销供应商标识")
    private String zsfyxgys;

    /** 营销类别 */
    @Excel(name = "营销类别")
    private String zyxlb;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setPartner(String partner) 
    {
        this.partner = partner;
    }

    public String getPartner() 
    {
        return partner;
    }
    public void setBkvid(String bkvid) 
    {
        this.bkvid = bkvid;
    }

    public String getBkvid() 
    {
        return bkvid;
    }
    public void setNameOrg1(String nameOrg1) 
    {
        this.nameOrg1 = nameOrg1;
    }

    public String getNameOrg1() 
    {
        return nameOrg1;
    }
    public void setBuSort1(String buSort1) 
    {
        this.buSort1 = buSort1;
    }

    public String getBuSort1() 
    {
        return buSort1;
    }
    public void setBuGroup(String buGroup) 
    {
        this.buGroup = buGroup;
    }

    public String getBuGroup() 
    {
        return buGroup;
    }
    public void setIdnumber(String idnumber) 
    {
        this.idnumber = idnumber;
    }

    public String getIdnumber() 
    {
        return idnumber;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setLangu(String langu) 
    {
        this.langu = langu;
    }

    public String getLangu() 
    {
        return langu;
    }
    public void setTimeZone(String timeZone) 
    {
        this.timeZone = timeZone;
    }

    public String getTimeZone() 
    {
        return timeZone;
    }
    public void setSteet(String steet) 
    {
        this.steet = steet;
    }

    public String getSteet() 
    {
        return steet;
    }
    public void setTelNumber(String telNumber) 
    {
        this.telNumber = telNumber;
    }

    public String getTelNumber() 
    {
        return telNumber;
    }
    public void setMobNumber(String mobNumber) 
    {
        this.mobNumber = mobNumber;
    }

    public String getMobNumber() 
    {
        return mobNumber;
    }
    public void setSmtpAddr(String smtpAddr) 
    {
        this.smtpAddr = smtpAddr;
    }

    public String getSmtpAddr() 
    {
        return smtpAddr;
    }
    public void setAkont(String akont) 
    {
        this.akont = akont;
    }

    public String getAkont() 
    {
        return akont;
    }
    public void setLoevm(String loevm) 
    {
        this.loevm = loevm;
    }

    public String getLoevm() 
    {
        return loevm;
    }
    public void setBanks(String banks) 
    {
        this.banks = banks;
    }

    public String getBanks() 
    {
        return banks;
    }
    public void setBankl(String bankl) 
    {
        this.bankl = bankl;
    }

    public String getBankl() 
    {
        return bankl;
    }
    public void setKoinh(String koinh) 
    {
        this.koinh = koinh;
    }

    public String getKoinh() 
    {
        return koinh;
    }
    public void setBankn(String bankn) 
    {
        this.bankn = bankn;
    }

    public String getBankn() 
    {
        return bankn;
    }
    public void setBkont(String bkont) 
    {
        this.bkont = bkont;
    }

    public String getBkont() 
    {
        return bkont;
    }
    public void setXezer(String xezer) 
    {
        this.xezer = xezer;
    }

    public String getXezer() 
    {
        return xezer;
    }
    public void setAccname(String accname) 
    {
        this.accname = accname;
    }

    public String getAccname() 
    {
        return accname;
    }
    public void setEfficient(Long efficient) 
    {
        this.efficient = efficient;
    }

    public Long getEfficient() 
    {
        return efficient;
    }
    public void setBanka(String banka) 
    {
        this.banka = banka;
    }

    public String getBanka() 
    {
        return banka;
    }
    public void setZsfyxgys(String zsfyxgys) 
    {
        this.zsfyxgys = zsfyxgys;
    }

    public String getZsfyxgys() 
    {
        return zsfyxgys;
    }
    public void setZyxlb(String zyxlb) 
    {
        this.zyxlb = zyxlb;
    }

    public String getZyxlb() 
    {
        return zyxlb;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("partner", getPartner())
            .append("bkvid", getBkvid())
            .append("nameOrg1", getNameOrg1())
            .append("buSort1", getBuSort1())
            .append("buGroup", getBuGroup())
            .append("idnumber", getIdnumber())
            .append("country", getCountry())
            .append("langu", getLangu())
            .append("timeZone", getTimeZone())
            .append("steet", getSteet())
            .append("telNumber", getTelNumber())
            .append("mobNumber", getMobNumber())
            .append("smtpAddr", getSmtpAddr())
            .append("akont", getAkont())
            .append("loevm", getLoevm())
            .append("banks", getBanks())
            .append("bankl", getBankl())
            .append("koinh", getKoinh())
            .append("bankn", getBankn())
            .append("bkont", getBkont())
            .append("xezer", getXezer())
            .append("accname", getAccname())
            .append("efficient", getEfficient())
            .append("createTime", getCreateTime())
//            .append("updatetime", getUpdatetime())
            .append("banka", getBanka())
            .append("zsfyxgys", getZsfyxgys())
            .append("zyxlb", getZyxlb())
            .toString();
    }
}
