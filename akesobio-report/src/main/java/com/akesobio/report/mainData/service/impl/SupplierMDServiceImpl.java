package com.akesobio.report.mainData.service.impl;

import java.util.List;
import com.akesobio.common.utils.DateUtils;
import com.akesobio.report.autoacct.domain.AutoAccProjectMaterial;
import com.akesobio.report.autoacct.mapper.AutoAccProjectMaterialMapper;
import com.akesobio.report.autoacct.service.IAutoAccProjectMaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.mainData.mapper.SupplierMDMapper;
import com.akesobio.report.mainData.domain.SupplierMD;
import com.akesobio.report.mainData.service.ISupplierMDService;

import javax.annotation.Resource;

/**
 * 供应商主数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class SupplierMDServiceImpl extends ServiceImpl<SupplierMDMapper, SupplierMD>  implements ISupplierMDService
{
    @Resource
    private SupplierMDMapper supplierMDMapper;

    /**
     * 查询供应商主数据
     * 
     * @param sid 供应商主数据主键
     * @return 供应商主数据
     */
    @Override
    public SupplierMD selectSupplierMDBySid(Long sid)
    {
        return supplierMDMapper.selectSupplierMDBySid(sid);
    }

    /**
     * 查询供应商主数据列表
     * 
     * @param supplierMD 供应商主数据
     * @return 供应商主数据
     */
    @Override
    public List<SupplierMD> selectSupplierMDList(SupplierMD supplierMD)
    {
        return supplierMDMapper.selectSupplierMDList(supplierMD);
    }

    /**
     * 新增供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    @Override
    public int insertSupplierMD(SupplierMD supplierMD)
    {
        supplierMD.setCreateTime(DateUtils.getNowDate());
        return supplierMDMapper.insertSupplierMD(supplierMD);
    }

    /**
     * 修改供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    @Override
    public int updateSupplierMD(SupplierMD supplierMD)
    {
        return supplierMDMapper.updateSupplierMD(supplierMD);
    }

    /**
     * 批量删除供应商主数据
     * 
     * @param sids 需要删除的供应商主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMDBySids(Long[] sids)
    {
        return supplierMDMapper.deleteSupplierMDBySids(sids);
    }

    /**
     * 删除供应商主数据信息
     * 
     * @param sid 供应商主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMDBySid(Long sid)
    {
        return supplierMDMapper.deleteSupplierMDBySid(sid);
    }
}
