package com.akesobio.report.mainData.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.service.ICostCenterService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 成本中心架构Controller
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@RequestMapping("/mainData/costCenter")
public class CostCenterController extends BaseController
{
    @Resource
    private ICostCenterService costCenterService;

    /**
     * 查询成本中心架构列表
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:list')")
    @GetMapping("/list")
    public TableDataInfo list(CostCenter costCenter)
    {
        startPage();
        List<CostCenter> list = costCenterService.selectCostCenterList(costCenter);
        return getDataTable(list);
    }

    /**
     * 导出成本中心架构列表
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:export')")
    @Log(title = "成本中心架构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CostCenter costCenter)
    {
        List<CostCenter> list = costCenterService.selectCostCenterList(costCenter);
        ExcelUtil<CostCenter> util = new ExcelUtil<CostCenter>(CostCenter.class);
        util.exportExcel(response, list, "成本中心架构数据");
    }

    /**
     * 获取成本中心架构详细信息
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(costCenterService.selectCostCenterById(id));
    }

    /**
     * 新增成本中心架构
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:add')")
    @Log(title = "成本中心架构", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CostCenter costCenter)
    {
        return toAjax(costCenterService.insertCostCenter(costCenter));
    }

    /**
     * 修改成本中心架构
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:edit')")
    @Log(title = "成本中心架构", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CostCenter costCenter)
    {
        return toAjax(costCenterService.updateCostCenter(costCenter));
    }

    /**
     * 删除成本中心架构
     */
    @PreAuthorize("@ss.hasPermi('mainData:costCenter:remove')")
    @Log(title = "成本中心架构", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(costCenterService.deleteCostCenterByIds(ids));
    }
}
