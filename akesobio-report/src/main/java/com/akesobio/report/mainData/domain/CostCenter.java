package com.akesobio.report.mainData.domain;

import com.akesobio.common.annotation.Excel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 成本中心架构对象 cost_center
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
public class CostCenter
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "上级成本中心代码", readConverterExp = "$column.readConverterExp()")
    private Long parentId;

    /** $column.columnComment */
    @Excel(name = "成本中心名称", readConverterExp = "$column.readConverterExp()")
    private String costCenterName;

    /** $column.columnComment */
    private Long id;
    /** $column.columnComment */
    @Excel(name = "成本中心代码", readConverterExp = "$column.readConverterExp()")
    private String area;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sapSuffixCode;

    private String supervisor;

    //business_terminal_node
    private String businessTerminalNode;

    //enabled_flag
    private String enabledFlag;

    private String fullName;

    private String code;

    /** 外部系统ID集合，多个ID用逗号分隔 */
    @Excel(name = "外部系统ID集合", readConverterExp = "$column.readConverterExp()")
    private String ekpIds;

    //business_terminal_node_flag
    private String businessTerminalNodeFlag;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date updateTime;
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEkpIds() {
        return ekpIds;
    }

    public void setEkpIds(String ekpIds) {
        this.ekpIds = ekpIds;
    }

    public String getBusinessTerminalNodeFlag() {
        return businessTerminalNodeFlag;
    }
    public void setBusinessTerminalNodeFlag(String businessTerminalNodeFlag) {
        this.businessTerminalNodeFlag = businessTerminalNodeFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getBusinessTerminalNode() {
        return businessTerminalNode;
    }

    public void setBusinessTerminalNode(String businessTerminalNode) {
        this.businessTerminalNode = businessTerminalNode;
    }

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        this.supervisor = supervisor;
    }
    public void setArea(String area)
    {
        this.area = area;
    }

    public String getArea()
    {
        return area;
    }
    public void setSapSuffixCode(String sapSuffixCode)
    {
        this.sapSuffixCode = sapSuffixCode;
    }

    public String getSapSuffixCode()
    {
        return sapSuffixCode;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setCostCenterName(String costCenterName) 
    {
        this.costCenterName = costCenterName;
    }

    public String getCostCenterName() 
    {
        return costCenterName;
    }
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("parentId", getParentId())
            .append("costCenterName", getCostCenterName())
            .append("id", getId())
            .append("area", getArea())
            .append("sapSuffixCode", getSapSuffixCode())
            .append("supervisor", getSupervisor())
            .append("businessTerminalNode", getBusinessTerminalNode())
            .append("enabledFlag", getEnabledFlag())
            .append("fullName", getFullName())
            .append("code", getCode())
            .append("ekpIds", getEkpIds())
            .append("businessTerminalNodeFlag", getBusinessTerminalNodeFlag())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
