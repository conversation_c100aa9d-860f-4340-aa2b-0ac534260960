package com.akesobio.report.mainData.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 成本中心中的用户对象 cost_center_user
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
public class CostCenterUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long costCenterId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String userCode;

    /** $column.columnComment */
    private Long id;

    public void setCostCenterId(Long costCenterId) 
    {
        this.costCenterId = costCenterId;
    }

    public Long getCostCenterId() 
    {
        return costCenterId;
    }
    public void setUserCode(String userCode) 
    {
        this.userCode = userCode;
    }

    public String getUserCode() 
    {
        return userCode;
    }
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("costCenterId", getCostCenterId())
            .append("userCode", getUserCode())
            .append("id", getId())
            .toString();
    }
}
