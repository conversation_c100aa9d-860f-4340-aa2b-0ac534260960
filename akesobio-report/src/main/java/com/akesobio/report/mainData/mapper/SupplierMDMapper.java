package com.akesobio.report.mainData.mapper;

import java.util.List;
import com.akesobio.report.mainData.domain.SupplierMD;
import com.akesobio.report.marketingFinance.vo.AddedTaxInvoiceVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 供应商主数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface SupplierMDMapper extends BaseMapper<SupplierMD>
{
    /**
     * 查询供应商主数据
     * 
     * @param sid 供应商主数据主键
     * @return 供应商主数据
     */
    public SupplierMD selectSupplierMDBySid(Long sid);

    /**
     * 查询供应商主数据列表
     * 
     * @param supplierMD 供应商主数据
     * @return 供应商主数据集合
     */
    public List<SupplierMD> selectSupplierMDList(SupplierMD supplierMD);

    /**
     * 新增供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    public int insertSupplierMD(SupplierMD supplierMD);

    /**
     * 修改供应商主数据
     * 
     * @param supplierMD 供应商主数据
     * @return 结果
     */
    public int updateSupplierMD(SupplierMD supplierMD);

    /**
     * 删除供应商主数据
     * 
     * @param sid 供应商主数据主键
     * @return 结果
     */
    public int deleteSupplierMDBySid(Long sid);

    /**
     * 批量删除供应商主数据
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierMDBySids(Long[] sids);
}
