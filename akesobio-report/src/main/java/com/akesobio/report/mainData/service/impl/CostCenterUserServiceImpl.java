package com.akesobio.report.mainData.service.impl;

import java.util.List;

import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.mapper.CostCenterMapper;
import com.akesobio.report.mainData.service.ICostCenterService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.mainData.mapper.CostCenterUserMapper;
import com.akesobio.report.mainData.domain.CostCenterUser;
import com.akesobio.report.mainData.service.ICostCenterUserService;

import javax.annotation.Resource;

/**
 * 成本中心中的用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
@Service
public class CostCenterUserServiceImpl extends ServiceImpl<CostCenterUserMapper, CostCenterUser>  implements ICostCenterUserService
{
    @Resource
    private CostCenterUserMapper costCenterUserMapper;

//    /**
//     * 查询成本中心中的用户
//     *
//     * @param id 成本中心中的用户主键
//     * @return 成本中心中的用户
//     */
//    @Override
//    public CostCenterUser selectCostCenterUserById(Long id)
//    {
//        return costCenterUserMapper.selectCostCenterUserById(id);
//    }
//
//    /**
//     * 查询成本中心中的用户列表
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 成本中心中的用户
//     */
//    @Override
//    public List<CostCenterUser> selectCostCenterUserList(CostCenterUser costCenterUser)
//    {
//        return costCenterUserMapper.selectCostCenterUserList(costCenterUser);
//    }
//
//    /**
//     * 新增成本中心中的用户
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 结果
//     */
//    @Override
//    public int insertCostCenterUser(CostCenterUser costCenterUser)
//    {
//        return costCenterUserMapper.insertCostCenterUser(costCenterUser);
//    }
//
//    /**
//     * 修改成本中心中的用户
//     *
//     * @param costCenterUser 成本中心中的用户
//     * @return 结果
//     */
//    @Override
//    public int updateCostCenterUser(CostCenterUser costCenterUser)
//    {
//        return costCenterUserMapper.updateCostCenterUser(costCenterUser);
//    }
//
//    /**
//     * 批量删除成本中心中的用户
//     *
//     * @param ids 需要删除的成本中心中的用户主键
//     * @return 结果
//     */
//    @Override
//    public int deleteCostCenterUserByIds(Long[] ids)
//    {
//        return costCenterUserMapper.deleteCostCenterUserByIds(ids);
//    }
//
//    /**
//     * 删除成本中心中的用户信息
//     *
//     * @param id 成本中心中的用户主键
//     * @return 结果
//     */
//    @Override
//    public int deleteCostCenterUserById(Long id)
//    {
//        return costCenterUserMapper.deleteCostCenterUserById(id);
//    }
}
