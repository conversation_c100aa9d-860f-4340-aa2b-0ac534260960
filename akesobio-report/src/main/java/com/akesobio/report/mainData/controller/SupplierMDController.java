package com.akesobio.report.mainData.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.mainData.domain.SupplierMD;
import com.akesobio.report.mainData.service.ISupplierMDService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 供应商主数据Controller
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@RequestMapping("/mainData/SupplierMD")
public class SupplierMDController extends BaseController
{
    @Resource
    private ISupplierMDService supplierMDService;


    /**
     * 收OA表单数据创建 供应商主数据 并同步到各个业务子系统
     */
    @Log(title = "OA供应商主数据创建申请", businessType = BusinessType.IMPORT)
    @PostMapping("/addSupplierMDFromOAForm")
    public AjaxResult addSupplierMDFromOAForm(@RequestBody String oaFormString)
    {
        return success();
    }



    /**
     * 查询供应商主数据列表
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:list')")
    @GetMapping("/list")
    public TableDataInfo list(SupplierMD supplierMD)
    {
        startPage();
        List<SupplierMD> list = supplierMDService.selectSupplierMDList(supplierMD);
        return getDataTable(list);
    }

    /**
     * 导出供应商主数据列表
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:export')")
    @Log(title = "供应商主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupplierMD supplierMD)
    {
        List<SupplierMD> list = supplierMDService.selectSupplierMDList(supplierMD);
        ExcelUtil<SupplierMD> util = new ExcelUtil<SupplierMD>(SupplierMD.class);
        util.exportExcel(response, list, "供应商主数据数据");
    }

    /**
     * 获取供应商主数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:query')")
    @GetMapping(value = "/{sid}")
    public AjaxResult getInfo(@PathVariable("sid") Long sid)
    {
        return success(supplierMDService.selectSupplierMDBySid(sid));
    }

    /**
     * 新增供应商主数据
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:add')")
    @Log(title = "供应商主数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupplierMD supplierMD)
    {
        return toAjax(supplierMDService.insertSupplierMD(supplierMD));
    }

    /**
     * 修改供应商主数据
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:edit')")
    @Log(title = "供应商主数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupplierMD supplierMD)
    {
        return toAjax(supplierMDService.updateSupplierMD(supplierMD));
    }

    /**
     * 删除供应商主数据
     */
    @PreAuthorize("@ss.hasPermi('mainData:SupplierMD:remove')")
    @Log(title = "供应商主数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sids}")
    public AjaxResult remove(@PathVariable Long[] sids)
    {
        return toAjax(supplierMDService.deleteSupplierMDBySids(sids));
    }
}
