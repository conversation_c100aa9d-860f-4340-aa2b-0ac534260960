package com.akesobio.report.mainData.service.impl;

import java.util.List;

import com.akesobio.report.mainData.domain.SupplierMD;
import com.akesobio.report.mainData.mapper.SupplierMDMapper;
import com.akesobio.report.mainData.service.ISupplierMDService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.mainData.mapper.CostCenterMapper;
import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.mainData.service.ICostCenterService;

import javax.annotation.Resource;

/**
 * 成本中心架构Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class CostCenterServiceImpl extends ServiceImpl<CostCenterMapper, CostCenter>  implements ICostCenterService
{
    @Resource
    private CostCenterMapper costCenterMapper;

    /**
     * 查询成本中心架构
     * 
     * @param id 成本中心架构主键
     * @return 成本中心架构
     */
    @Override
    public CostCenter selectCostCenterById(Long id)
    {
        return costCenterMapper.selectCostCenterById(id);
    }

    /**
     * 查询成本中心架构列表
     * 
     * @param costCenter 成本中心架构
     * @return 成本中心架构
     */
    @Override
    public List<CostCenter> selectCostCenterList(CostCenter costCenter)
    {
        return costCenterMapper.selectCostCenterList(costCenter);
    }

    /**
     * 新增成本中心架构
     * 
     * @param costCenter 成本中心架构
     * @return 结果
     */
    @Override
    public int insertCostCenter(CostCenter costCenter)
    {
        return costCenterMapper.insertCostCenter(costCenter);
    }

    /**
     * 修改成本中心架构
     * 
     * @param costCenter 成本中心架构
     * @return 结果
     */
    @Override
    public int updateCostCenter(CostCenter costCenter)
    {
        return costCenterMapper.updateCostCenter(costCenter);
    }

    /**
     * 批量删除成本中心架构
     * 
     * @param ids 需要删除的成本中心架构主键
     * @return 结果
     */
    @Override
    public int deleteCostCenterByIds(Long[] ids)
    {
        return costCenterMapper.deleteCostCenterByIds(ids);
    }

    /**
     * 删除成本中心架构信息
     * 
     * @param id 成本中心架构主键
     * @return 结果
     */
    @Override
    public int deleteCostCenterById(Long id)
    {
        return costCenterMapper.deleteCostCenterById(id);
    }
}
