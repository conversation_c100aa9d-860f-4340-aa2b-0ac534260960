package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.FinancialExpenseSummaryTableMapper;
import com.akesobio.report.sap.domain.FinancialExpenseSummaryTable;
import com.akesobio.report.sap.service.IFinancialExpenseSummaryTableService;

import javax.annotation.Resource;

/**
 * 财务费用汇总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class FinancialExpenseSummaryTableServiceImpl implements IFinancialExpenseSummaryTableService 
{
    @Resource
    private FinancialExpenseSummaryTableMapper financialExpenseSummaryTableMapper;

    /**
     * 查询财务费用汇总
     * 
     * @param id 财务费用汇总主键
     * @return 财务费用汇总
     */
    @Override
    public FinancialExpenseSummaryTable selectFinancialExpenseSummaryTableById(String id)
    {
        return financialExpenseSummaryTableMapper.selectFinancialExpenseSummaryTableById(id);
    }

    /**
     * 查询财务费用汇总列表
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 财务费用汇总
     */
    @Override
    public List<FinancialExpenseSummaryTable> selectFinancialExpenseSummaryTableList(FinancialExpenseSummaryTable financialExpenseSummaryTable)
    {
        return financialExpenseSummaryTableMapper.selectFinancialExpenseSummaryTableList(financialExpenseSummaryTable);
    }

    /**
     * 新增财务费用汇总
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 结果
     */
    @Override
    public int insertFinancialExpenseSummaryTable(FinancialExpenseSummaryTable financialExpenseSummaryTable)
    {
        return financialExpenseSummaryTableMapper.insertFinancialExpenseSummaryTable(financialExpenseSummaryTable);
    }

    /**
     * 修改财务费用汇总
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 结果
     */
    @Override
    public int updateFinancialExpenseSummaryTable(FinancialExpenseSummaryTable financialExpenseSummaryTable)
    {
        return financialExpenseSummaryTableMapper.updateFinancialExpenseSummaryTable(financialExpenseSummaryTable);
    }

    /**
     * 批量删除财务费用汇总
     * 
     * @param ids 需要删除的财务费用汇总主键
     * @return 结果
     */
    @Override
    public int deleteFinancialExpenseSummaryTableByIds(String[] ids)
    {
        return financialExpenseSummaryTableMapper.deleteFinancialExpenseSummaryTableByIds(ids);
    }

    /**
     * 删除财务费用汇总信息
     * 
     * @param id 财务费用汇总主键
     * @return 结果
     */
    @Override
    public int deleteFinancialExpenseSummaryTableById(String id)
    {
        return financialExpenseSummaryTableMapper.deleteFinancialExpenseSummaryTableById(id);
    }
}
