package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.SapInnerOrderData;

/**
 * SAP内部订单输出Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface ISapInnerOrderDataService 
{
    /**
     * 查询SAP内部订单输出
     * 
     * @param id SAP内部订单输出主键
     * @return SAP内部订单输出
     */
    public SapInnerOrderData selectSapInnerOrderDataById(Long id);

    /**
     * 查询SAP内部订单输出列表
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return SAP内部订单输出集合
     */
    public List<SapInnerOrderData> selectSapInnerOrderDataList(SapInnerOrderData sapInnerOrderData);

    /**
     * 新增SAP内部订单输出
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return 结果
     */
    public int insertSapInnerOrderData(SapInnerOrderData sapInnerOrderData);

    /**
     * 修改SAP内部订单输出
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return 结果
     */
    public int updateSapInnerOrderData(SapInnerOrderData sapInnerOrderData);

    /**
     * 批量删除SAP内部订单输出
     * 
     * @param ids 需要删除的SAP内部订单输出主键集合
     * @return 结果
     */
    public int deleteSapInnerOrderDataByIds(Long[] ids);

    /**
     * 删除SAP内部订单输出信息
     * 
     * @param id SAP内部订单输出主键
     * @return 结果
     */
    public int deleteSapInnerOrderDataById(Long id);
}
