package com.akesobio.report.sap.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sap.domain.CashFlowReport;

/**
 * SAP现金流量报表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-30
 */
@DataSource(DataSourceType.SAP)
public interface CashFlowReportMapper 
{
    /**
     * 查询SAP现金流量报表
     * 
     * @param sid SAP现金流量报表主键
     * @return SAP现金流量报表
     */
    public CashFlowReport selectCashFlowReportBySid(Long sid);

    /**
     * 查询SAP现金流量报表列表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return SAP现金流量报表集合
     */
    public List<CashFlowReport> selectCashFlowReportList(CashFlowReport cashFlowReport);

    /**
     * 新增SAP现金流量报表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return 结果
     */
    public int insertCashFlowReport(CashFlowReport cashFlowReport);

    /**
     * 修改SAP现金流量报表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return 结果
     */
    public int updateCashFlowReport(CashFlowReport cashFlowReport);

    /**
     * 删除SAP现金流量报表
     * 
     * @param sid SAP现金流量报表主键
     * @return 结果
     */
    public int deleteCashFlowReportBySid(Long sid);

    /**
     * 批量删除SAP现金流量报表
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCashFlowReportBySids(Long[] sids);
}
