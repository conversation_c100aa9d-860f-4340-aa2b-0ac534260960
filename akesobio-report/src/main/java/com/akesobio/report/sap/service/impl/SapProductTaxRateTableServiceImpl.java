package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SapProductTaxRateTableMapper;
import com.akesobio.report.sap.domain.SapProductTaxRateTable;
import com.akesobio.report.sap.service.ISapProductTaxRateTableService;

import javax.annotation.Resource;

/**
 * SAP产品税率Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Service
public class SapProductTaxRateTableServiceImpl implements ISapProductTaxRateTableService 
{
    @Resource
    private SapProductTaxRateTableMapper sapProductTaxRateTableMapper;

    /**
     * 查询SAP产品税率
     * 
     * @param id SAP产品税率主键
     * @return SAP产品税率
     */
    @Override
    public SapProductTaxRateTable selectSapProductTaxRateTableById(Long id)
    {
        return sapProductTaxRateTableMapper.selectSapProductTaxRateTableById(id);
    }

    /**
     * 查询SAP产品税率列表
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return SAP产品税率
     */
    @Override
    public List<SapProductTaxRateTable> selectSapProductTaxRateTableList(SapProductTaxRateTable sapProductTaxRateTable)
    {
        return sapProductTaxRateTableMapper.selectSapProductTaxRateTableList(sapProductTaxRateTable);
    }

    /**
     * 新增SAP产品税率
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return 结果
     */
    @Override
    public int insertSapProductTaxRateTable(SapProductTaxRateTable sapProductTaxRateTable)
    {
        return sapProductTaxRateTableMapper.insertSapProductTaxRateTable(sapProductTaxRateTable);
    }

    /**
     * 修改SAP产品税率
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return 结果
     */
    @Override
    public int updateSapProductTaxRateTable(SapProductTaxRateTable sapProductTaxRateTable)
    {
        return sapProductTaxRateTableMapper.updateSapProductTaxRateTable(sapProductTaxRateTable);
    }

    /**
     * 批量删除SAP产品税率
     * 
     * @param ids 需要删除的SAP产品税率主键
     * @return 结果
     */
    @Override
    public int deleteSapProductTaxRateTableByIds(Long[] ids)
    {
        return sapProductTaxRateTableMapper.deleteSapProductTaxRateTableByIds(ids);
    }

    /**
     * 删除SAP产品税率信息
     * 
     * @param id SAP产品税率主键
     * @return 结果
     */
    @Override
    public int deleteSapProductTaxRateTableById(Long id)
    {
        return sapProductTaxRateTableMapper.deleteSapProductTaxRateTableById(id);
    }
}
