package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.SapCostCenterData;

/**
 * SAP成本中心输出Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface SapCostCenterDataMapper 
{
    /**
     * 查询SAP成本中心输出
     * 
     * @param id SAP成本中心输出主键
     * @return SAP成本中心输出
     */
    public SapCostCenterData selectSapCostCenterDataById(Long id);

    /**
     * 查询SAP成本中心输出列表
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return SAP成本中心输出集合
     */
    public List<SapCostCenterData> selectSapCostCenterDataList(SapCostCenterData sapCostCenterData);

    /**
     * 新增SAP成本中心输出
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return 结果
     */
    public int insertSapCostCenterData(SapCostCenterData sapCostCenterData);

    /**
     * 修改SAP成本中心输出
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return 结果
     */
    public int updateSapCostCenterData(SapCostCenterData sapCostCenterData);

    /**
     * 删除SAP成本中心输出
     * 
     * @param id SAP成本中心输出主键
     * @return 结果
     */
    public int deleteSapCostCenterDataById(Long id);

    /**
     * 批量删除SAP成本中心输出
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSapCostCenterDataByIds(Long[] ids);
}
