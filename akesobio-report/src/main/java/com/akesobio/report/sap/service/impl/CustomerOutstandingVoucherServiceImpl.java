package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.CustomerOutstandingVoucherMapper;
import com.akesobio.report.sap.domain.CustomerOutstandingVoucher;
import com.akesobio.report.sap.service.ICustomerOutstandingVoucherService;

/**
 * 客户未清凭证报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
@Service
public class CustomerOutstandingVoucherServiceImpl implements ICustomerOutstandingVoucherService 
{
    @Autowired
    private CustomerOutstandingVoucherMapper customerOutstandingVoucherMapper;

    /**
     * 查询客户未清凭证报表
     * 
     * @param sid 客户未清凭证报表主键
     * @return 客户未清凭证报表
     */
    @Override
    public CustomerOutstandingVoucher selectCustomerOutstandingVoucherBySid(Long sid)
    {
        return customerOutstandingVoucherMapper.selectCustomerOutstandingVoucherBySid(sid);
    }

    /**
     * 查询客户未清凭证报表列表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 客户未清凭证报表
     */
    @Override
    public List<CustomerOutstandingVoucher> selectCustomerOutstandingVoucherList(CustomerOutstandingVoucher customerOutstandingVoucher)
    {
        return customerOutstandingVoucherMapper.selectCustomerOutstandingVoucherList(customerOutstandingVoucher);
    }

    /**
     * 新增客户未清凭证报表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 结果
     */
    @Override
    public int insertCustomerOutstandingVoucher(CustomerOutstandingVoucher customerOutstandingVoucher)
    {
        return customerOutstandingVoucherMapper.insertCustomerOutstandingVoucher(customerOutstandingVoucher);
    }

    /**
     * 修改客户未清凭证报表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 结果
     */
    @Override
    public int updateCustomerOutstandingVoucher(CustomerOutstandingVoucher customerOutstandingVoucher)
    {
        return customerOutstandingVoucherMapper.updateCustomerOutstandingVoucher(customerOutstandingVoucher);
    }

    /**
     * 批量删除客户未清凭证报表
     * 
     * @param sids 需要删除的客户未清凭证报表主键
     * @return 结果
     */
    @Override
    public int deleteCustomerOutstandingVoucherBySids(Long[] sids)
    {
        return customerOutstandingVoucherMapper.deleteCustomerOutstandingVoucherBySids(sids);
    }

    /**
     * 删除客户未清凭证报表信息
     * 
     * @param sid 客户未清凭证报表主键
     * @return 结果
     */
    @Override
    public int deleteCustomerOutstandingVoucherBySid(Long sid)
    {
        return customerOutstandingVoucherMapper.deleteCustomerOutstandingVoucherBySid(sid);
    }
}
