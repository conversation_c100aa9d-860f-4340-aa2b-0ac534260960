package com.akesobio.report.sap.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sap.domain.CollectionFlowReport;

/**
 * 营销财务收款流水报表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
@DataSource(DataSourceType.SAP)
public interface CollectionFlowReportMapper 
{
    /**
     * 查询营销财务收款流水报表
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 营销财务收款流水报表
     */
    public CollectionFlowReport selectCollectionFlowReportBySid(Long sid);

    /**
     * 查询营销财务收款流水报表列表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 营销财务收款流水报表集合
     */
    public List<CollectionFlowReport> selectCollectionFlowReportList(CollectionFlowReport collectionFlowReport);

    /**
     * 新增营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    public int insertCollectionFlowReport(CollectionFlowReport collectionFlowReport);

    /**
     * 修改营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    public int updateCollectionFlowReport(CollectionFlowReport collectionFlowReport);

    /**
     * 删除营销财务收款流水报表
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 结果
     */
    public int deleteCollectionFlowReportBySid(Long sid);

    /**
     * 批量删除营销财务收款流水报表
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCollectionFlowReportBySids(Long[] sids);
}
