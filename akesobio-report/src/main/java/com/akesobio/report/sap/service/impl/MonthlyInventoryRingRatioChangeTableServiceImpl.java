package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.MonthlyInventoryRingRatioChangeTableMapper;
import com.akesobio.report.sap.domain.MonthlyInventoryRingRatioChangeTable;
import com.akesobio.report.sap.service.IMonthlyInventoryRingRatioChangeTableService;

import javax.annotation.Resource;

/**
 * 月度期末库存环比变更Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
@Service
public class MonthlyInventoryRingRatioChangeTableServiceImpl implements IMonthlyInventoryRingRatioChangeTableService 
{
    @Resource
    private MonthlyInventoryRingRatioChangeTableMapper monthlyInventoryRingRatioChangeTableMapper;

    /**
     * 查询月度期末库存环比变更
     * 
     * @param id 月度期末库存环比变更主键
     * @return 月度期末库存环比变更
     */
    @Override
    public MonthlyInventoryRingRatioChangeTable selectMonthlyInventoryRingRatioChangeTableById(Long id)
    {
        return monthlyInventoryRingRatioChangeTableMapper.selectMonthlyInventoryRingRatioChangeTableById(id);
    }

    /**
     * 查询月度期末库存环比变更列表
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 月度期末库存环比变更
     */
    @Override
    public List<MonthlyInventoryRingRatioChangeTable> selectMonthlyInventoryRingRatioChangeTableList(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable)
    {
        return monthlyInventoryRingRatioChangeTableMapper.selectMonthlyInventoryRingRatioChangeTableList(monthlyInventoryRingRatioChangeTable);
    }

    /**
     * 新增月度期末库存环比变更
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 结果
     */
    @Override
    public int insertMonthlyInventoryRingRatioChangeTable(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable)
    {
        return monthlyInventoryRingRatioChangeTableMapper.insertMonthlyInventoryRingRatioChangeTable(monthlyInventoryRingRatioChangeTable);
    }

    /**
     * 修改月度期末库存环比变更
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 结果
     */
    @Override
    public int updateMonthlyInventoryRingRatioChangeTable(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable)
    {
        return monthlyInventoryRingRatioChangeTableMapper.updateMonthlyInventoryRingRatioChangeTable(monthlyInventoryRingRatioChangeTable);
    }

    /**
     * 批量删除月度期末库存环比变更
     * 
     * @param ids 需要删除的月度期末库存环比变更主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyInventoryRingRatioChangeTableByIds(Long[] ids)
    {
        return monthlyInventoryRingRatioChangeTableMapper.deleteMonthlyInventoryRingRatioChangeTableByIds(ids);
    }

    /**
     * 删除月度期末库存环比变更信息
     * 
     * @param id 月度期末库存环比变更主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyInventoryRingRatioChangeTableById(Long id)
    {
        return monthlyInventoryRingRatioChangeTableMapper.deleteMonthlyInventoryRingRatioChangeTableById(id);
    }
}
