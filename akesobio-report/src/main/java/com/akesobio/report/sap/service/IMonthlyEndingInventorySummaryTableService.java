package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.MonthlyEndingInventorySummaryTable;

/**
 * 月度期末库存汇总Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface IMonthlyEndingInventorySummaryTableService 
{
    /**
     * 查询月度期末库存汇总
     * 
     * @param id 月度期末库存汇总主键
     * @return 月度期末库存汇总
     */
    public MonthlyEndingInventorySummaryTable selectMonthlyEndingInventorySummaryTableById(Long id);

    /**
     * 查询月度期末库存汇总列表
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 月度期末库存汇总集合
     */
    public List<MonthlyEndingInventorySummaryTable> selectMonthlyEndingInventorySummaryTableList(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 新增月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    public int insertMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 修改月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    public int updateMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 批量删除月度期末库存汇总
     * 
     * @param ids 需要删除的月度期末库存汇总主键集合
     * @return 结果
     */
    public int deleteMonthlyEndingInventorySummaryTableByIds(Long[] ids);

    /**
     * 删除月度期末库存汇总信息
     * 
     * @param id 月度期末库存汇总主键
     * @return 结果
     */
    public int deleteMonthlyEndingInventorySummaryTableById(Long id);
}
