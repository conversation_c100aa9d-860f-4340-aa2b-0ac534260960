package com.akesobio.report.sap.client;

import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
public class SapClient {
    public int CommonConnect(String interfaceName,Map<String,String> paramsMap) {
        //SAP本地测试环境
//        String result= HttpUtil.post("http://10.10.44.173:7777/sap/api/" + interfaceName, String.valueOf(paramsMap));
        //SAP测试环境
//        String result= HttpUtil.post("http://10.10.2.33:4893/sap/api/" + interfaceName, String.valueOf(paramsMap));
        //SAP正式环境
        String result= HttpUtil.post("http://10.10.2.33:9911/sap/api/" + interfaceName, String.valueOf(paramsMap));
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = null;
        int code = 0;
        try {
            jsonNode = objectMapper.readTree(result);
            code = jsonNode.get("code").asInt();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return code;
    }
    public ArrayNode returnSapData(String interfaceName, Map<String,String> paramsMap) {
        String result= HttpUtil.post("http://10.10.2.33:9911/sap/api/" + interfaceName, String.valueOf(paramsMap));
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = null;
        ArrayNode msg = null;
        try {
            jsonNode = objectMapper.readTree(result);
            if (jsonNode.get("code").asInt() == 200){
                msg = (ArrayNode)jsonNode.get("msg");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return msg;
    }
}
