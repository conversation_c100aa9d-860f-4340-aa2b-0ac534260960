package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP科目输出对象 sap_subject_data
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public class SapSubjectData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 科目表 */
    @Excel(name = "科目表")
    @JSONField(name="KTOPL")
    private String subjectTable;

    /** 科目 */
    @Excel(name = "科目")
    @JSONField(name="SAKNR")
    private String subject;

    /** 科目名称 */
    @Excel(name = "科目名称")
    @JSONField(name="TXT20")
    private String subjectName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSubjectTable(String subjectTable) 
    {
        this.subjectTable = subjectTable;
    }

    public String getSubjectTable() 
    {
        return subjectTable;
    }
    public void setSubject(String subject) 
    {
        this.subject = subject;
    }

    public String getSubject() 
    {
        return subject;
    }
    public void setSubjectName(String subjectName) 
    {
        this.subjectName = subjectName;
    }

    public String getSubjectName() 
    {
        return subjectName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("subjectTable", getSubjectTable())
            .append("subject", getSubject())
            .append("subjectName", getSubjectName())
            .toString();
    }
}
