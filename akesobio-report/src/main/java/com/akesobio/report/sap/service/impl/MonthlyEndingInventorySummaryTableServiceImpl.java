package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.MonthlyEndingInventorySummaryTableMapper;
import com.akesobio.report.sap.domain.MonthlyEndingInventorySummaryTable;
import com.akesobio.report.sap.service.IMonthlyEndingInventorySummaryTableService;

import javax.annotation.Resource;

/**
 * 月度期末库存汇总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
@Service
public class MonthlyEndingInventorySummaryTableServiceImpl implements IMonthlyEndingInventorySummaryTableService 
{
    @Resource
    private MonthlyEndingInventorySummaryTableMapper monthlyEndingInventorySummaryTableMapper;

    /**
     * 查询月度期末库存汇总
     * 
     * @param id 月度期末库存汇总主键
     * @return 月度期末库存汇总
     */
    @Override
    public MonthlyEndingInventorySummaryTable selectMonthlyEndingInventorySummaryTableById(Long id)
    {
        return monthlyEndingInventorySummaryTableMapper.selectMonthlyEndingInventorySummaryTableById(id);
    }

    /**
     * 查询月度期末库存汇总列表
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 月度期末库存汇总
     */
    @Override
    public List<MonthlyEndingInventorySummaryTable> selectMonthlyEndingInventorySummaryTableList(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable)
    {
        return monthlyEndingInventorySummaryTableMapper.selectMonthlyEndingInventorySummaryTableList(monthlyEndingInventorySummaryTable);
    }

    /**
     * 新增月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    @Override
    public int insertMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable)
    {
        return monthlyEndingInventorySummaryTableMapper.insertMonthlyEndingInventorySummaryTable(monthlyEndingInventorySummaryTable);
    }

    /**
     * 修改月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    @Override
    public int updateMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable)
    {
        return monthlyEndingInventorySummaryTableMapper.updateMonthlyEndingInventorySummaryTable(monthlyEndingInventorySummaryTable);
    }

    /**
     * 批量删除月度期末库存汇总
     * 
     * @param ids 需要删除的月度期末库存汇总主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyEndingInventorySummaryTableByIds(Long[] ids)
    {
        return monthlyEndingInventorySummaryTableMapper.deleteMonthlyEndingInventorySummaryTableByIds(ids);
    }

    /**
     * 删除月度期末库存汇总信息
     * 
     * @param id 月度期末库存汇总主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyEndingInventorySummaryTableById(Long id)
    {
        return monthlyEndingInventorySummaryTableMapper.deleteMonthlyEndingInventorySummaryTableById(id);
    }
}
