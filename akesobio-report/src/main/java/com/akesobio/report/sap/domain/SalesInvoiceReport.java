package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * SAP销售发票查询报表对象 SalesInvoiceReport
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
public class SalesInvoiceReport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long sid;

    /**
     * 发票号
     */
    @Excel(name = "发票号")
    private String vbeln;

    /**
     * 已被取消
     */
    @Excel(name = "已被取消")
    private String fksto;

    /**
     * 开票类型
     */
    @Excel(name = "开票类型")
    private String fkart;

    /**
     * 发票过账状态
     */
    @Excel(name = "发票过账状态")
    private String rfbsk;

    /**
     * 开票类型描述
     */
    @Excel(name = "开票类型描述")
    private String vtext;

    /**
     * 开票日期
     */
    @Excel(name = "开票日期")
    private String fkdat;

    /**
     * 发票行项目
     */
    @Excel(name = "发票行项目")
    private String posnr;

    /**
     * 销售组织
     */
    @Excel(name = "销售组织")
    private String vkorgAuft;

    /**
     * 销售组织描述
     */
    @Excel(name = "销售组织描述")
    private String zxszzms;

    /**
     * 分销渠道
     */
    @Excel(name = "分销渠道")
    private String vtwegAuft;

    /**
     * 分销渠道描述
     */
    @Excel(name = "分销渠道描述")
    private String zfxqdms;

    /**
     * 产品组
     */
    @Excel(name = "产品组")
    private String spara;

    /**
     * 产品组描述
     */
    @Excel(name = "产品组描述")
    private String zcpzms;

    /**
     * 销售办公室
     */
    @Excel(name = "销售办公室")
    private String vkbur;

    /**
     * 销售办公室描述
     */
    @Excel(name = "销售办公室描述")
    private String zxsbgsms;

    /**
     * 销售组
     */
    @Excel(name = "销售组")
    private String vkgrp;

    /**
     * 销售组描述
     */
    @Excel(name = "销售组描述")
    private String zxszms;

    /**
     * 销售地区
     */
    @Excel(name = "销售地区")
    private String bzirkAuft;

    /**
     * 销售地区描述
     */
    @Excel(name = "销售地区描述")
    private String zxsdqs;

    /**
     * 售达方
     */
    @Excel(name = "售达方")
    private String kunag;

    /**
     * 售达方名称
     */
    @Excel(name = "售达方名称")
    private String zsdfmc;

    /**
     * 付款方
     */
    @Excel(name = "付款方")
    private String kunrg;

    /**
     * 付款方描述
     */
    @Excel(name = "付款方描述")
    private String zfkfms;

    /**
     * 送达方
     */
    @Excel(name = "送达方")
    private String kunnr;

    /**
     * 送达方描述
     */
    @Excel(name = "送达方描述")
    private String zsdfms;

    /**
     * 类别
     */
    @Excel(name = "类别")
    private String pstyv;

    /**
     * 工厂
     */
    @Excel(name = "工厂")
    private String werks;

    /**
     * 发货日期
     */
    @Excel(name = "发货日期")
    private String wadatIst;

    /**
     * POD确认日期
     */
    @Excel(name = "POD确认日期")
    private String podat;

    /**
     * 物料编号
     */
    @Excel(name = "物料编号")
    private String matnr;

    /**
     * 物料描述
     */
    @Excel(name = "物料描述")
    private String arktx;

    /**
     * 开票数量
     */
    @Excel(name = "开票数量")
    private BigDecimal fklmg;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String mseh6;

    /**
     * 含税单价
     */
    @Excel(name = "含税单价")
    private String zhsdj;

    /**
     * 货币
     */
    @Excel(name = "货币")
    private String waerk;

    /**
     * 发票净值
     */
    @Excel(name = "发票净值")
    private String netwr;

    /**
     * 税额
     */
    @Excel(name = "税额")
    private String mwsbp;

    /**
     * 含税总金额
     */
    @Excel(name = "含税总金额")
    private String zhsje;

    /**
     * 付款条件
     */
    @Excel(name = "付款条件")
    private String vtext1;

    /**
     * 交货单号
     */
    @Excel(name = "交货单号")
    private String vgbel;

    /**
     * 交货单行号
     */
    @Excel(name = "交货单行号")
    private String vgpos;

    /**
     * 销售单行号
     */
    @Excel(name = "销售单行号")
    private String aubel;

    /**
     * 订单行号
     */
    @Excel(name = "订单行号")
    private String aupos;

    /**
     * 被取消发票号
     */
    @Excel(name = "被取消发票号")
    private String sfakn;

    /**
     * 康方销售价格
     */
    @Excel(name = "康方销售价格")
    private String zxsjg;

    /**
     * 销售价格
     */
    @Excel(name = "销售价格")
    private String zxszk;

    /**
     * 票后净值调整
     */
    @Excel(name = "票后净值调整")
    private String zphjz;

    /**
     * 票后税额调整
     */
    @Excel(name = "票后税额调整")
    private String zphse;

    /**
     * 补差
     */
    @Excel(name = "补差")
    private String zbcje;

    /**
     * 创建者
     */
    @Excel(name = "创建者")
    private String ernam1;

    /**
     * 创建日期
     */
    @Excel(name = "创建日期")
    private String erdat;

    /**
     * 地区经理
     */
    @Excel(name = "地区经理")
    private String lifnr1;

    /**
     * 地区经理
     */
    @Excel(name = "地区经理")
    private String name1;

    /**
     * 大区经理
     */
    @Excel(name = "大区经理")
    private String lifnr2;

    /**
     * 大区经理
     */
    @Excel(name = "大区经理")
    private String name2;

    /**
     * 总监区经理
     */
    @Excel(name = "总监区经理")
    private String lifnr3;

    /**
     * 总监区经理
     */
    @Excel(name = "总监区经理")
    private String name3;

    /**
     * 补差类型
     */
    @Excel(name = "补差类型")
    private String zbclx;

    /**
     * OA申请单号
     */
    @Excel(name = "OA申请单号")
    private String zoadh;

    /**
     * 生产批次
     */
    @Excel(name = "生产批次")
    private String znbph;

    /**
     * 客户参考
     */
    @Excel(name = "客户参考")
    private String bstnk;

    /**
     * 批次
     */
    @Excel(name = "批次")
    private String charg;

    /**
     * 评估类型
     */
    @Excel(name = "评估类型")
    private String bwtar;

    /**
     * 实际成本
     */
    @Excel(name = "实际成本")
    private String vv003;

    /**
     * 抬头文本
     */
    @Excel(name = "抬头文本")
    private String htxt;

    /**
     * 金税发票编号1
     */
    @Excel(name = "金税发票编号1")
    private String zzjsbh1;

    /**
     * 金税发票编号2
     */
    @Excel(name = "金税发票编号2")
    private String zzjsbh2;

    /**
     * 金税开票日期
     */
    @Excel(name = "金税开票日期")
    private String zzjsrq;

    /**
     * 金税PDF链接地址
     */
    @Excel(name = "金税PDF链接地址")
    private String zpdfAddr;

    /**
     * 收票地址
     */
    @Excel(name = "收票地址")
    private String zrecEmail;

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getSid() {
        return sid;
    }

    public void setVbeln(String vbeln) {
        this.vbeln = vbeln;
    }

    public String getVbeln() {
        return vbeln;
    }

    public void setFksto(String fksto) {
        this.fksto = fksto;
    }

    public String getFksto() {
        return fksto;
    }

    public void setFkart(String fkart) {
        this.fkart = fkart;
    }

    public String getFkart() {
        return fkart;
    }

    public void setRfbsk(String rfbsk) {
        this.rfbsk = rfbsk;
    }

    public String getRfbsk() {
        return rfbsk;
    }

    public void setVtext(String vtext) {
        this.vtext = vtext;
    }

    public String getVtext() {
        return vtext;
    }

    public void setFkdat(String fkdat) {
        this.fkdat = fkdat;
    }

    public String getFkdat() {
        return fkdat;
    }

    public void setPosnr(String posnr) {
        this.posnr = posnr;
    }

    public String getPosnr() {
        return posnr;
    }

    public void setVkorgAuft(String vkorgAuft) {
        this.vkorgAuft = vkorgAuft;
    }

    public String getVkorgAuft() {
        return vkorgAuft;
    }

    public void setZxszzms(String zxszzms) {
        this.zxszzms = zxszzms;
    }

    public String getZxszzms() {
        return zxszzms;
    }

    public void setVtwegAuft(String vtwegAuft) {
        this.vtwegAuft = vtwegAuft;
    }

    public String getVtwegAuft() {
        return vtwegAuft;
    }

    public void setZfxqdms(String zfxqdms) {
        this.zfxqdms = zfxqdms;
    }

    public String getZfxqdms() {
        return zfxqdms;
    }

    public void setSpara(String spara) {
        this.spara = spara;
    }

    public String getSpara() {
        return spara;
    }

    public void setZcpzms(String zcpzms) {
        this.zcpzms = zcpzms;
    }

    public String getZcpzms() {
        return zcpzms;
    }

    public void setVkbur(String vkbur) {
        this.vkbur = vkbur;
    }

    public String getVkbur() {
        return vkbur;
    }

    public void setZxsbgsms(String zxsbgsms) {
        this.zxsbgsms = zxsbgsms;
    }

    public String getZxsbgsms() {
        return zxsbgsms;
    }

    public void setVkgrp(String vkgrp) {
        this.vkgrp = vkgrp;
    }

    public String getVkgrp() {
        return vkgrp;
    }

    public void setZxszms(String zxszms) {
        this.zxszms = zxszms;
    }

    public String getZxszms() {
        return zxszms;
    }

    public void setBzirkAuft(String bzirkAuft) {
        this.bzirkAuft = bzirkAuft;
    }

    public String getBzirkAuft() {
        return bzirkAuft;
    }

    public void setZxsdqs(String zxsdqs) {
        this.zxsdqs = zxsdqs;
    }

    public String getZxsdqs() {
        return zxsdqs;
    }

    public void setKunag(String kunag) {
        this.kunag = kunag;
    }

    public String getKunag() {
        return kunag;
    }

    public void setZsdfmc(String zsdfmc) {
        this.zsdfmc = zsdfmc;
    }

    public String getZsdfmc() {
        return zsdfmc;
    }

    public void setKunrg(String kunrg) {
        this.kunrg = kunrg;
    }

    public String getKunrg() {
        return kunrg;
    }

    public void setZfkfms(String zfkfms) {
        this.zfkfms = zfkfms;
    }

    public String getZfkfms() {
        return zfkfms;
    }

    public void setKunnr(String kunnr) {
        this.kunnr = kunnr;
    }

    public String getKunnr() {
        return kunnr;
    }

    public void setZsdfms(String zsdfms) {
        this.zsdfms = zsdfms;
    }

    public String getZsdfms() {
        return zsdfms;
    }

    public void setPstyv(String pstyv) {
        this.pstyv = pstyv;
    }

    public String getPstyv() {
        return pstyv;
    }

    public void setWerks(String werks) {
        this.werks = werks;
    }

    public String getWerks() {
        return werks;
    }

    public void setWadatIst(String wadatIst) {
        this.wadatIst = wadatIst;
    }

    public String getWadatIst() {
        return wadatIst;
    }

    public void setPodat(String podat) {
        this.podat = podat;
    }

    public String getPodat() {
        return podat;
    }

    public void setMatnr(String matnr) {
        this.matnr = matnr;
    }

    public String getMatnr() {
        return matnr;
    }

    public void setArktx(String arktx) {
        this.arktx = arktx;
    }

    public String getArktx() {
        return arktx;
    }

    public void setFklmg(BigDecimal fklmg) {
        this.fklmg = fklmg;
    }

    public BigDecimal getFklmg() {
        return fklmg;
    }

    public void setMseh6(String mseh6) {
        this.mseh6 = mseh6;
    }

    public String getMseh6() {
        return mseh6;
    }

    public void setZhsdj(String zhsdj) {
        this.zhsdj = zhsdj;
    }

    public String getZhsdj() {
        return zhsdj;
    }

    public void setWaerk(String waerk) {
        this.waerk = waerk;
    }

    public String getWaerk() {
        return waerk;
    }

    public void setNetwr(String netwr) {
        this.netwr = netwr;
    }

    public String getNetwr() {
        return netwr;
    }

    public void setMwsbp(String mwsbp) {
        this.mwsbp = mwsbp;
    }

    public String getMwsbp() {
        return mwsbp;
    }

    public void setZhsje(String zhsje) {
        this.zhsje = zhsje;
    }

    public String getZhsje() {
        return zhsje;
    }

    public void setVtext1(String vtext1) {
        this.vtext1 = vtext1;
    }

    public String getVtext1() {
        return vtext1;
    }

    public void setVgbel(String vgbel) {
        this.vgbel = vgbel;
    }

    public String getVgbel() {
        return vgbel;
    }

    public void setVgpos(String vgpos) {
        this.vgpos = vgpos;
    }

    public String getVgpos() {
        return vgpos;
    }

    public void setAubel(String aubel) {
        this.aubel = aubel;
    }

    public String getAubel() {
        return aubel;
    }

    public void setAupos(String aupos) {
        this.aupos = aupos;
    }

    public String getAupos() {
        return aupos;
    }

    public void setSfakn(String sfakn) {
        this.sfakn = sfakn;
    }

    public String getSfakn() {
        return sfakn;
    }

    public void setZxsjg(String zxsjg) {
        this.zxsjg = zxsjg;
    }

    public String getZxsjg() {
        return zxsjg;
    }

    public void setZxszk(String zxszk) {
        this.zxszk = zxszk;
    }

    public String getZxszk() {
        return zxszk;
    }

    public void setZphjz(String zphjz) {
        this.zphjz = zphjz;
    }

    public String getZphjz() {
        return zphjz;
    }

    public void setZphse(String zphse) {
        this.zphse = zphse;
    }

    public String getZphse() {
        return zphse;
    }

    public void setZbcje(String zbcje) {
        this.zbcje = zbcje;
    }

    public String getZbcje() {
        return zbcje;
    }

    public void setErnam1(String ernam1) {
        this.ernam1 = ernam1;
    }

    public String getErnam1() {
        return ernam1;
    }

    public void setErdat(String erdat) {
        this.erdat = erdat;
    }

    public String getErdat() {
        return erdat;
    }

    public void setLifnr1(String lifnr1) {
        this.lifnr1 = lifnr1;
    }

    public String getLifnr1() {
        return lifnr1;
    }

    public void setName1(String name1) {
        this.name1 = name1;
    }

    public String getName1() {
        return name1;
    }

    public void setLifnr2(String lifnr2) {
        this.lifnr2 = lifnr2;
    }

    public String getLifnr2() {
        return lifnr2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }

    public String getName2() {
        return name2;
    }

    public void setLifnr3(String lifnr3) {
        this.lifnr3 = lifnr3;
    }

    public String getLifnr3() {
        return lifnr3;
    }

    public void setName3(String name3) {
        this.name3 = name3;
    }

    public String getName3() {
        return name3;
    }

    public void setZbclx(String zbclx) {
        this.zbclx = zbclx;
    }

    public String getZbclx() {
        return zbclx;
    }

    public void setZoadh(String zoadh) {
        this.zoadh = zoadh;
    }

    public String getZoadh() {
        return zoadh;
    }

    public void setZnbph(String znbph) {
        this.znbph = znbph;
    }

    public String getZnbph() {
        return znbph;
    }

    public void setBstnk(String bstnk) {
        this.bstnk = bstnk;
    }

    public String getBstnk() {
        return bstnk;
    }

    public void setCharg(String charg) {
        this.charg = charg;
    }

    public String getCharg() {
        return charg;
    }

    public void setBwtar(String bwtar) {
        this.bwtar = bwtar;
    }

    public String getBwtar() {
        return bwtar;
    }

    public void setVv003(String vv003) {
        this.vv003 = vv003;
    }

    public String getVv003() {
        return vv003;
    }

    public void setHtxt(String htxt) {
        this.htxt = htxt;
    }

    public String getHtxt() {
        return htxt;
    }

    public void setZzjsbh1(String zzjsbh1) {
        this.zzjsbh1 = zzjsbh1;
    }

    public String getZzjsbh1() {
        return zzjsbh1;
    }

    public void setZzjsbh2(String zzjsbh2) {
        this.zzjsbh2 = zzjsbh2;
    }

    public String getZzjsbh2() {
        return zzjsbh2;
    }

    public void setZzjsrq(String zzjsrq) {
        this.zzjsrq = zzjsrq;
    }

    public String getZzjsrq() {
        return zzjsrq;
    }

    public void setZpdfAddr(String zpdfAddr) {
        this.zpdfAddr = zpdfAddr;
    }

    public String getZpdfAddr() {
        return zpdfAddr;
    }

    public void setZrecEmail(String zrecEmail) {
        this.zrecEmail = zrecEmail;
    }

    public String getZrecEmail() {
        return zrecEmail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("sid", getSid())
                .append("vbeln", getVbeln())
                .append("fksto", getFksto())
                .append("fkart", getFkart())
                .append("rfbsk", getRfbsk())
                .append("vtext", getVtext())
                .append("fkdat", getFkdat())
                .append("posnr", getPosnr())
                .append("vkorgAuft", getVkorgAuft())
                .append("zxszzms", getZxszzms())
                .append("vtwegAuft", getVtwegAuft())
                .append("zfxqdms", getZfxqdms())
                .append("spara", getSpara())
                .append("zcpzms", getZcpzms())
                .append("vkbur", getVkbur())
                .append("zxsbgsms", getZxsbgsms())
                .append("vkgrp", getVkgrp())
                .append("zxszms", getZxszms())
                .append("bzirkAuft", getBzirkAuft())
                .append("zxsdqs", getZxsdqs())
                .append("kunag", getKunag())
                .append("zsdfmc", getZsdfmc())
                .append("kunrg", getKunrg())
                .append("zfkfms", getZfkfms())
                .append("kunnr", getKunnr())
                .append("zsdfms", getZsdfms())
                .append("pstyv", getPstyv())
                .append("werks", getWerks())
                .append("wadatIst", getWadatIst())
                .append("podat", getPodat())
                .append("matnr", getMatnr())
                .append("arktx", getArktx())
                .append("fklmg", getFklmg())
                .append("mseh6", getMseh6())
                .append("zhsdj", getZhsdj())
                .append("waerk", getWaerk())
                .append("netwr", getNetwr())
                .append("mwsbp", getMwsbp())
                .append("zhsje", getZhsje())
                .append("vtext1", getVtext1())
                .append("vgbel", getVgbel())
                .append("vgpos", getVgpos())
                .append("aubel", getAubel())
                .append("aupos", getAupos())
                .append("sfakn", getSfakn())
                .append("zxsjg", getZxsjg())
                .append("zxszk", getZxszk())
                .append("zphjz", getZphjz())
                .append("zphse", getZphse())
                .append("zbcje", getZbcje())
                .append("ernam1", getErnam1())
                .append("erdat", getErdat())
                .append("lifnr1", getLifnr1())
                .append("name1", getName1())
                .append("lifnr2", getLifnr2())
                .append("name2", getName2())
                .append("lifnr3", getLifnr3())
                .append("name3", getName3())
                .append("zbclx", getZbclx())
                .append("zoadh", getZoadh())
                .append("znbph", getZnbph())
                .append("bstnk", getBstnk())
                .append("charg", getCharg())
                .append("bwtar", getBwtar())
                .append("vv003", getVv003())
                .append("htxt", getHtxt())
                .append("zzjsbh1", getZzjsbh1())
                .append("zzjsbh2", getZzjsbh2())
                .append("zzjsrq", getZzjsrq())
                .append("zpdfAddr", getZpdfAddr())
                .append("zrecEmail", getZrecEmail())
                .toString();
    }
}
