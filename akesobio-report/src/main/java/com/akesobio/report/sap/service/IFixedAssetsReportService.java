package com.akesobio.report.sap.service;

import java.math.BigDecimal;
import java.util.List;
import com.akesobio.report.sap.domain.FixedAssetsReport;
import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo2;

/**
 * 固定资产清单报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-06
 */
public interface IFixedAssetsReportService 
{
    /**
     * 查询固定资产清单报表
     * 
     * @param sid 固定资产清单报表主键
     * @return 固定资产清单报表
     */
    public FixedAssetsReport selectFixedAssetsReportBySid(Long sid);

    /**
     * 查询固定资产清单报表列表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 固定资产清单报表集合
     */
    public List<FixedAssetsReport> selectFixedAssetsReportList(FixedAssetsReport fixedAssetsReport);

    /**
     * 查询固定资产明细表2列表
     *
     * @param fixedAssetsReport 固定资产清单报表
     * @return 固定资产清单报表集合
     */
    public List<FixedAssetsDetailsTableVo2> selectFixedAssetsReport2List(FixedAssetsReport fixedAssetsReport);

    /**
     * 新增固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    public int insertFixedAssetsReport(FixedAssetsReport fixedAssetsReport);

    /**
     * 修改固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    public int updateFixedAssetsReport(FixedAssetsReport fixedAssetsReport);

    /**
     * 批量删除固定资产清单报表
     * 
     * @param sids 需要删除的固定资产清单报表主键集合
     * @return 结果
     */
    public int deleteFixedAssetsReportBySids(Long[] sids);

    /**
     * 删除固定资产清单报表信息
     * 
     * @param sid 固定资产清单报表主键
     * @return 结果
     */
    public int deleteFixedAssetsReportBySid(Long sid);
}
