package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.MonthlyInventoryRingRatioChangeTable;

/**
 * 月度期末库存环比变更Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface MonthlyInventoryRingRatioChangeTableMapper 
{
    /**
     * 查询月度期末库存环比变更
     * 
     * @param id 月度期末库存环比变更主键
     * @return 月度期末库存环比变更
     */
    public MonthlyInventoryRingRatioChangeTable selectMonthlyInventoryRingRatioChangeTableById(Long id);

    /**
     * 查询月度期末库存环比变更列表
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 月度期末库存环比变更集合
     */
    public List<MonthlyInventoryRingRatioChangeTable> selectMonthlyInventoryRingRatioChangeTableList(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable);

    /**
     * 新增月度期末库存环比变更
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 结果
     */
    public int insertMonthlyInventoryRingRatioChangeTable(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable);

    /**
     * 修改月度期末库存环比变更
     * 
     * @param monthlyInventoryRingRatioChangeTable 月度期末库存环比变更
     * @return 结果
     */
    public int updateMonthlyInventoryRingRatioChangeTable(MonthlyInventoryRingRatioChangeTable monthlyInventoryRingRatioChangeTable);

    /**
     * 删除月度期末库存环比变更
     * 
     * @param id 月度期末库存环比变更主键
     * @return 结果
     */
    public int deleteMonthlyInventoryRingRatioChangeTableById(Long id);

    /**
     * 批量删除月度期末库存环比变更
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyInventoryRingRatioChangeTableByIds(Long[] ids);
}
