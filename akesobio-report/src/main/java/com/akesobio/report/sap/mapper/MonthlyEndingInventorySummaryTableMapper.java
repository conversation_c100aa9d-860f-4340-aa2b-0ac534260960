package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.MonthlyEndingInventorySummaryTable;

/**
 * 月度期末库存汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface MonthlyEndingInventorySummaryTableMapper 
{
    /**
     * 查询月度期末库存汇总
     * 
     * @param id 月度期末库存汇总主键
     * @return 月度期末库存汇总
     */
    public MonthlyEndingInventorySummaryTable selectMonthlyEndingInventorySummaryTableById(Long id);

    /**
     * 查询月度期末库存汇总列表
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 月度期末库存汇总集合
     */
    public List<MonthlyEndingInventorySummaryTable> selectMonthlyEndingInventorySummaryTableList(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 新增月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    public int insertMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 修改月度期末库存汇总
     * 
     * @param monthlyEndingInventorySummaryTable 月度期末库存汇总
     * @return 结果
     */
    public int updateMonthlyEndingInventorySummaryTable(MonthlyEndingInventorySummaryTable monthlyEndingInventorySummaryTable);

    /**
     * 删除月度期末库存汇总
     * 
     * @param id 月度期末库存汇总主键
     * @return 结果
     */
    public int deleteMonthlyEndingInventorySummaryTableById(Long id);

    /**
     * 批量删除月度期末库存汇总
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyEndingInventorySummaryTableByIds(Long[] ids);
}
