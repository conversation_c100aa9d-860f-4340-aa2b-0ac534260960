package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 采购订单发票明细对象 purchase_order_Invoice_details_table
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
public class PurchaseOrderInvoiceDetailsTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 工厂 */
    @Excel(name = "工厂")
    @JSONField(name="WERKS")
    private String werks;

    /** 订单类型（采购） */
    @Excel(name = "订单类型", readConverterExp = "采=购")
    @JSONField(name="BSART")
    private String bsart;

    /** 采购凭证类型的简短描述 */
    @Excel(name = "采购凭证类型的简短描述")
    @JSONField(name="BATXT")
    private String batxt;

    /** 采购凭证编号 */
    @Excel(name = "采购凭证编号")
    @JSONField(name="EBELN")
    private String ebeln;

    /** 采购凭证的项目编号 */
    @Excel(name = "采购凭证的项目编号")
    @JSONField(name="EBELP")
    private String ebelp;

    /** 最后更改的日期 */
    @Excel(name = "最后更改的日期")
    @JSONField(name="AEDAT")
    private String aedat;

    /** OA采购订单号 */
    @Excel(name = "OA采购订单号")
    @JSONField(name="ZZOAID")
    private String zzoaid;

    /** OA单行项目号 */
    @Excel(name = "OA单行项目号")
    @JSONField(name="ZZOAITEM")
    private String zzoaitem;

    /** 物料编号 */
    @Excel(name = "物料编号")
    @JSONField(name="MATNR")
    private String matnr;

    /** 物料组 */
    @Excel(name = "物料组")
    @JSONField(name="MATKL")
    private String matkl;

    /** 物料组描述 */
    @Excel(name = "物料组描述")
    @JSONField(name="WGBEZ")
    private String wgbez;

    /** 数量 */
    @Excel(name = "数量")
    @JSONField(name="MENGE")
    private String menge;

    /** 基本计量单位 */
    @Excel(name = "基本计量单位")
    @JSONField(name="MEINS")
    private String meins;

    /** 物料长描述 */
    @Excel(name = "物料长描述")
    @JSONField(name="ZZCMS")
    private String zzcms;

    /** 供应商或债权人的帐号 */
    @Excel(name = "供应商或债权人的帐号")
    @JSONField(name="LIFNR")
    private String lifnr;

    /** 名称 1 */
    @Excel(name = "名称 1")
    @JSONField(name="NAME1")
    private String name1;

    /** 大小/量纲 */
    @Excel(name = "大小/量纲")
    @JSONField(name="GROES")
    private String groes;

    /** 物料凭证编号 */
    @Excel(name = "物料凭证编号")
    @JSONField(name="MBLNR")
    private String mblnr;

    /** 物料凭证的年份 */
    @Excel(name = "物料凭证的年份")
    @JSONField(name="MJAHR")
    private String mjahr;

    /** 物料凭证中的项目 */
    @Excel(name = "物料凭证中的项目")
    @JSONField(name="ZEILE")
    private String zeile;

    /** 借/贷标识 */
    @Excel(name = "借/贷标识")
    @JSONField(name="SHKZG")
    private String shkzg;

    /** 数量 */
    @Excel(name = "数量")
    @JSONField(name="MENGE_WH")
    private String mengeWh;

    /** 凭证中的过账日期 */
    @Excel(name = "凭证中的过账日期")
    @JSONField(name="BUDAT_MKPF")
    private String budatMkpf;

    /** 供应商批次编号 */
    @Excel(name = "供应商批次编号")
    @JSONField(name="LICHA")
    private String licha;

    /** 批次编号 */
    @Excel(name = "批次编号")
    @JSONField(name="CHARG")
    private String charg;

    /** 会计凭证号码 */
    @Excel(name = "会计凭证号码")
    @JSONField(name="BELNR_RSEG")
    private String belnrRseg;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @JSONField(name="GJAHR_RSEG")
    private String gjahrRseg;

    /** 发票凭证中的凭证项目 */
    @Excel(name = "发票凭证中的凭证项目")
    @JSONField(name="BUZEI_RSEG")
    private String buzeiRseg;

    /** 发票代码 */
    @Excel(name = "发票代码")
    @JSONField(name="ZFPDM")
    private String zfpdm;

    /** 发票号码 */
    @Excel(name = "发票号码")
    @JSONField(name="ZFPHM")
    private String zfphm;

    /** 会计凭证号码 */
    @Excel(name = "会计凭证号码")
    @JSONField(name="BELNR_BKPF")
    private String belnrBkpf;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @JSONField(name="GJAHR_BKPF")
    private String gjahrBkpf;

    /** 销售和分销凭证号 */
    @Excel(name = "销售和分销凭证号")
    @JSONField(name="VBELN")
    private String vbeln;

    /** 销售和分销凭证的项目号 */
    @Excel(name = "销售和分销凭证的项目号")
    @JSONField(name="POSNR")
    private String posnr;

    /** 批次编号 */
    @Excel(name = "批次编号")
    @JSONField(name="CHARG_L")
    private String chargL;

    /** 过账期间 */
    @Excel(name = "过账期间")
    @JSONField(name="MONAT")
    private String monat;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setWerks(String werks)
    {
        this.werks = werks;
    }

    public String getWerks()
    {
        return werks;
    }
    public void setBsart(String bsart)
    {
        this.bsart = bsart;
    }

    public String getBsart()
    {
        return bsart;
    }
    public void setBatxt(String batxt)
    {
        this.batxt = batxt;
    }

    public String getBatxt()
    {
        return batxt;
    }
    public void setEbeln(String ebeln)
    {
        this.ebeln = ebeln;
    }

    public String getEbeln()
    {
        return ebeln;
    }
    public void setEbelp(String ebelp)
    {
        this.ebelp = ebelp;
    }

    public String getEbelp()
    {
        return ebelp;
    }
    public void setAedat(String aedat)
    {
        this.aedat = aedat;
    }

    public String getAedat()
    {
        return aedat;
    }
    public void setZzoaid(String zzoaid)
    {
        this.zzoaid = zzoaid;
    }

    public String getZzoaid()
    {
        return zzoaid;
    }
    public void setZzoaitem(String zzoaitem)
    {
        this.zzoaitem = zzoaitem;
    }

    public String getZzoaitem()
    {
        return zzoaitem;
    }
    public void setMatnr(String matnr)
    {
        this.matnr = matnr;
    }

    public String getMatnr()
    {
        return matnr;
    }
    public void setMatkl(String matkl)
    {
        this.matkl = matkl;
    }

    public String getMatkl()
    {
        return matkl;
    }
    public void setWgbez(String wgbez)
    {
        this.wgbez = wgbez;
    }

    public String getWgbez()
    {
        return wgbez;
    }
    public void setMenge(String menge)
    {
        this.menge = menge;
    }

    public String getMenge()
    {
        return menge;
    }
    public void setMeins(String meins)
    {
        this.meins = meins;
    }

    public String getMeins()
    {
        return meins;
    }
    public void setZzcms(String zzcms)
    {
        this.zzcms = zzcms;
    }

    public String getZzcms()
    {
        return zzcms;
    }
    public void setLifnr(String lifnr)
    {
        this.lifnr = lifnr;
    }

    public String getLifnr()
    {
        return lifnr;
    }
    public void setName1(String name1)
    {
        this.name1 = name1;
    }

    public String getName1()
    {
        return name1;
    }
    public void setGroes(String groes)
    {
        this.groes = groes;
    }

    public String getGroes()
    {
        return groes;
    }
    public void setMblnr(String mblnr)
    {
        this.mblnr = mblnr;
    }

    public String getMblnr()
    {
        return mblnr;
    }
    public void setMjahr(String mjahr)
    {
        this.mjahr = mjahr;
    }

    public String getMjahr()
    {
        return mjahr;
    }
    public void setZeile(String zeile)
    {
        this.zeile = zeile;
    }

    public String getZeile()
    {
        return zeile;
    }
    public void setShkzg(String shkzg)
    {
        this.shkzg = shkzg;
    }

    public String getShkzg()
    {
        return shkzg;
    }
    public void setMengeWh(String mengeWh)
    {
        this.mengeWh = mengeWh;
    }

    public String getMengeWh()
    {
        return mengeWh;
    }
    public void setBudatMkpf(String budatMkpf)
    {
        this.budatMkpf = budatMkpf;
    }

    public String getBudatMkpf()
    {
        return budatMkpf;
    }
    public void setLicha(String licha)
    {
        this.licha = licha;
    }

    public String getLicha()
    {
        return licha;
    }
    public void setCharg(String charg)
    {
        this.charg = charg;
    }

    public String getCharg()
    {
        return charg;
    }
    public void setBelnrRseg(String belnrRseg)
    {
        this.belnrRseg = belnrRseg;
    }

    public String getBelnrRseg()
    {
        return belnrRseg;
    }
    public void setGjahrRseg(String gjahrRseg)
    {
        this.gjahrRseg = gjahrRseg;
    }

    public String getGjahrRseg()
    {
        return gjahrRseg;
    }
    public void setBuzeiRseg(String buzeiRseg)
    {
        this.buzeiRseg = buzeiRseg;
    }

    public String getBuzeiRseg()
    {
        return buzeiRseg;
    }
    public void setZfpdm(String zfpdm)
    {
        this.zfpdm = zfpdm;
    }

    public String getZfpdm()
    {
        return zfpdm;
    }
    public void setZfphm(String zfphm)
    {
        this.zfphm = zfphm;
    }

    public String getZfphm()
    {
        return zfphm;
    }
    public void setBelnrBkpf(String belnrBkpf)
    {
        this.belnrBkpf = belnrBkpf;
    }

    public String getBelnrBkpf()
    {
        return belnrBkpf;
    }
    public void setGjahrBkpf(String gjahrBkpf)
    {
        this.gjahrBkpf = gjahrBkpf;
    }

    public String getGjahrBkpf()
    {
        return gjahrBkpf;
    }
    public void setVbeln(String vbeln)
    {
        this.vbeln = vbeln;
    }

    public String getVbeln()
    {
        return vbeln;
    }
    public void setPosnr(String posnr)
    {
        this.posnr = posnr;
    }

    public String getPosnr()
    {
        return posnr;
    }
    public void setChargL(String chargL)
    {
        this.chargL = chargL;
    }

    public String getChargL()
    {
        return chargL;
    }
    public void setMonat(String monat)
    {
        this.monat = monat;
    }

    public String getMonat()
    {
        return monat;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("werks", getWerks())
                .append("bsart", getBsart())
                .append("batxt", getBatxt())
                .append("ebeln", getEbeln())
                .append("ebelp", getEbelp())
                .append("aedat", getAedat())
                .append("zzoaid", getZzoaid())
                .append("zzoaitem", getZzoaitem())
                .append("matnr", getMatnr())
                .append("matkl", getMatkl())
                .append("wgbez", getWgbez())
                .append("menge", getMenge())
                .append("meins", getMeins())
                .append("zzcms", getZzcms())
                .append("lifnr", getLifnr())
                .append("name1", getName1())
                .append("groes", getGroes())
                .append("mblnr", getMblnr())
                .append("mjahr", getMjahr())
                .append("zeile", getZeile())
                .append("shkzg", getShkzg())
                .append("mengeWh", getMengeWh())
                .append("budatMkpf", getBudatMkpf())
                .append("licha", getLicha())
                .append("charg", getCharg())
                .append("belnrRseg", getBelnrRseg())
                .append("gjahrRseg", getGjahrRseg())
                .append("buzeiRseg", getBuzeiRseg())
                .append("zfpdm", getZfpdm())
                .append("zfphm", getZfphm())
                .append("belnrBkpf", getBelnrBkpf())
                .append("gjahrBkpf", getGjahrBkpf())
                .append("vbeln", getVbeln())
                .append("posnr", getPosnr())
                .append("chargL", getChargL())
                .append("monat", getMonat())
                .toString();
    }
}
