package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP功能范围输出对象 sap_functional_scope_data
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public class SapFunctionalScopeData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 语言代码 */
    @Excel(name = "语言代码")
    @JSONField(name="SPRAS")
    private String languageCode;

    /** 功能范围 */
    @Excel(name = "功能范围")
    @JSONField(name="FKBER")
    private String functionalScope;

    /** 功能范围名称 */
    @Excel(name = "功能范围名称")
    @JSONField(name="FKBTX")
    private String functionalScopeName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLanguageCode(String languageCode) 
    {
        this.languageCode = languageCode;
    }

    public String getLanguageCode() 
    {
        return languageCode;
    }
    public void setFunctionalScope(String functionalScope) 
    {
        this.functionalScope = functionalScope;
    }

    public String getFunctionalScope() 
    {
        return functionalScope;
    }
    public void setFunctionalScopeName(String functionalScopeName) 
    {
        this.functionalScopeName = functionalScopeName;
    }

    public String getFunctionalScopeName() 
    {
        return functionalScopeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("languageCode", getLanguageCode())
            .append("functionalScope", getFunctionalScope())
            .append("functionalScopeName", getFunctionalScopeName())
            .toString();
    }
}
