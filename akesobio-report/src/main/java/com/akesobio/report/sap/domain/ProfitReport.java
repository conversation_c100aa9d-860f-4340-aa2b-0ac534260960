package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP利润报表对象 ProfitReport
 * 
 * <AUTHOR>
 * @date 2023-10-30
 */
public class ProfitReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 项目 */
    @Excel(name = "项目")
    private String f1;

    /** 行号 */
    @Excel(name = "行号")
    private String f2;

    /** 本期金额 */
    @Excel(name = "本期金额")
    private String f3;

    /** 本年累计 */
    @Excel(name = "本年累计")
    private String f4;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String bukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    private String gjahr;

    /** 会计期间 */
    @Excel(name = "会计期间")
    private String monat;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setF1(String f1) 
    {
        this.f1 = f1;
    }

    public String getF1() 
    {
        return f1;
    }
    public void setF2(String f2) 
    {
        this.f2 = f2;
    }

    public String getF2() 
    {
        return f2;
    }
    public void setF3(String f3) 
    {
        this.f3 = f3;
    }

    public String getF3() 
    {
        return f3;
    }
    public void setF4(String f4) 
    {
        this.f4 = f4;
    }

    public String getF4() 
    {
        return f4;
    }
    public void setBukrs(String bukrs) 
    {
        this.bukrs = bukrs;
    }

    public String getBukrs() 
    {
        return bukrs;
    }
    public void setGjahr(String gjahr) 
    {
        this.gjahr = gjahr;
    }

    public String getGjahr() 
    {
        return gjahr;
    }
    public void setMonat(String monat) 
    {
        this.monat = monat;
    }

    public String getMonat() 
    {
        return monat;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("f1", getF1())
            .append("f2", getF2())
            .append("f3", getF3())
            .append("f4", getF4())
            .append("bukrs", getBukrs())
            .append("gjahr", getGjahr())
            .append("monat", getMonat())
            .toString();
    }
}
