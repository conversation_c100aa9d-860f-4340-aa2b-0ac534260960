package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class FundReceiptSummaryTable extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Excel(name = "会计年度")
    @JSONField(name="GJAHR")
    private String year;

    @Excel(name = "会计期间")
    @JSONField(name="MONAT")
    private String month;

    @Excel(name = "银行回单交易的日期")
    @JSONField(name="BANK_DATE")
    private String bankDate;

    @Excel(name = "公司代码")
    @JSONField(name="BUKRS")
    private String companyCode;

    @Excel(name = "公司的名称")
    @JSONField(name="BUTXT")
    private String companyName;

    @Excel(name = "银行回单中的账户货币代码")
    @JSONField(name="CURRENCY")
    private String currencyCode;

    @Excel(name = "销售回款")
    @JSONField(name="EC03")
    private String salePaymentCollection;

    @Excel(name = "其他销售回款")
    @JSONField(name="EC")
    private String elseSalePaymentCollection;

    @Excel(name = "集团内其他回款")
    @JSONField(name="IC")
    private String groupElsePaymentCollection;

    @Excel(name = "集团内销售回款")
    @JSONField(name="IC03")
    private String groupSalePaymentCollection;

    @Excel(name = "存款利息及理财收益")
    @JSONField(name="IC07")
    private String financingIncome;

    @Excel(name = "供应商其他回款")
    @JSONField(name="IV")
    private String supplierElsePaymentCollection;

    @Excel(name = "临床退款")
    @JSONField(name="IV08")
    private String clinicalRefund;

    @Excel(name = "其他")
    @JSONField(name="OT")
    private String other;

    @Excel(name = "财政专项")
    @JSONField(name="OT02")
    private String financialSpecialProject;

    @Excel(name = "银行回单中的交易金额汇总")
    @JSONField(name="SUM_AMT")
    private String amountSummary;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof FundReceiptSummaryTable) {
            FundReceiptSummaryTable data = (FundReceiptSummaryTable) obj;
            return this.getYear().equals(data.getYear()) && this.getMonth().equals(data.getMonth())
                    && this.getBankDate().equals(data.getBankDate()) && this.getCompanyCode().equals(data.getCompanyCode());
        }
        return false;
    }

    @Override
    public int hashCode() {
        return 31 * 17 + (this.getYear().hashCode() + this.getMonth().hashCode() + this.getBankDate().hashCode() + this.getCompanyCode().hashCode());
    }
}
