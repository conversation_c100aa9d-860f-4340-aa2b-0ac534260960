package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP成本中心输出对象 sap_cost_center_data
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public class SapCostCenterData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 集团 */
    @Excel(name = "集团")
    @JSONField(name="KOKRS")
    private String bloc;

    /** 公司 */
    @Excel(name = "公司")
    @JSONField(name="BUKRS")
    private String company;

    /** 成本中心 */
    @Excel(name = "成本中心")
    @JSONField(name="KOSTL")
    private String costCenter;

    /** 成本中心描述 */
    @Excel(name = "成本中心描述")
    @JSONField(name="KTEXT")
    private String costCenterText;

    /** 功能范围 */
    @Excel(name = "功能范围")
    @JSONField(name="FUNC_AREA")
    private String functionalScope;

    public String getFunctionalScope() {
        return functionalScope;
    }

    public void setFunctionalScope(String functionalScope) {
        this.functionalScope = functionalScope;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBloc(String bloc)
    {
        this.bloc = bloc;
    }

    public String getBloc()
    {
        return bloc;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setCostCenter(String costCenter) 
    {
        this.costCenter = costCenter;
    }

    public String getCostCenter() 
    {
        return costCenter;
    }
    public void setCostCenterText(String costCenterText) 
    {
        this.costCenterText = costCenterText;
    }

    public String getCostCenterText() 
    {
        return costCenterText;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bloc", getBloc())
            .append("company", getCompany())
            .append("costCenter", getCostCenter())
            .append("costCenterText", getCostCenterText())
            .append("functionalScope", getFunctionalScope())
            .toString();
    }
}
