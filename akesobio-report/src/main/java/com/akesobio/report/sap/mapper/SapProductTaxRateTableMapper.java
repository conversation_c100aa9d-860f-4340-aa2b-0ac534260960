package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.SapProductTaxRateTable;

/**
 * SAP产品税率Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface SapProductTaxRateTableMapper 
{
    /**
     * 查询SAP产品税率
     * 
     * @param id SAP产品税率主键
     * @return SAP产品税率
     */
    public SapProductTaxRateTable selectSapProductTaxRateTableById(Long id);

    /**
     * 查询SAP产品税率列表
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return SAP产品税率集合
     */
    public List<SapProductTaxRateTable> selectSapProductTaxRateTableList(SapProductTaxRateTable sapProductTaxRateTable);

    /**
     * 新增SAP产品税率
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return 结果
     */
    public int insertSapProductTaxRateTable(SapProductTaxRateTable sapProductTaxRateTable);

    /**
     * 修改SAP产品税率
     * 
     * @param sapProductTaxRateTable SAP产品税率
     * @return 结果
     */
    public int updateSapProductTaxRateTable(SapProductTaxRateTable sapProductTaxRateTable);

    /**
     * 删除SAP产品税率
     * 
     * @param id SAP产品税率主键
     * @return 结果
     */
    public int deleteSapProductTaxRateTableById(Long id);

    /**
     * 批量删除SAP产品税率
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSapProductTaxRateTableByIds(Long[] ids);
}
