package com.akesobio.report.sap.domain;

import cn.hutool.core.annotation.Alias;
import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 各公司销售费用明细对象 sales_expense_details_report
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Data
public class SalesExpenseDetailsReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long sid;

    /** 公司主体 */
    @Excel(name = "公司主体")
    @Alias("BUTXT")
    @JSONField(name="BUTXT")
    private String butxt;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @Alias("RBUKRS")
    @JSONField(name="RBUKRS")
    private String rbukrs;

    /** 凭证编号 */
    @Excel(name = "凭证编号")
    @Alias("BELNR")
    @JSONField(name="BELNR")
    private String belnr;

    /** 行项目 */
    @Excel(name = "行项目")
    @Alias("DOCLN")
    @JSONField(name="DOCLN")
    private String docln;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @Alias("GJAHR")
    @JSONField(name="GJAHR")
    private String gjahr;

    /** 过账期间 */
    @Excel(name = "过账期间")
    @Alias("POPER")
    @JSONField(name="POPER")
    private String poper;

    /** 凭证类型 */
    @Excel(name = "凭证类型")
    @Alias("BLART")
    @JSONField(name="BLART")
    private String blart;

    /** 科目号 */
    @Excel(name = "科目号")
    @Alias("RACCT")
    @JSONField(name="RACCT")
    private String racct;

    /** 账面科目 */
    @Excel(name = "账面科目")
    @Alias("TXT20")
    @JSONField(name="TXT20")
    private String txt20;

    /** 功能范围 */
    @Excel(name = "功能范围")
    @Alias("RFAREA")
    @JSONField(name="RFAREA")
    private String rfarea;

    /** 汇总科目 */
    @Excel(name = "汇总科目")
    @Alias("ZHZKM")
    @JSONField(name="ZHZKM")
    private String zhzkm;

    /** 披露科目 */
    @Excel(name = "披露科目")
    @Alias("ZPLKM")
    @JSONField(name="ZPLKM")
    private String zplkm;

    /** 成本中心 */
    @Excel(name = "成本中心")
    @Alias("RCNTR")
    @JSONField(name="RCNTR")
    private String rcntr;

    /** 部门 */
    @Excel(name = "部门")
    @Alias("KTEXT")
    @JSONField(name="KTEXT")
    private String ktext;

    /** 部门名称(财务) */
    @Excel(name = "部门名称(财务)")
    @Alias("ZBMMC")
    @JSONField(name="ZBMMC")
    private String zbmmc;

    /** 二级部门(财务) */
    @Excel(name = "二级部门(财务)")
    @Alias("ZEJBM_FI")
    @JSONField(name="ZEJBM_FI")
    private String zejbmFi;

    /** 文本 */
    @Excel(name = "文本")
    @Alias("SGTXT")
    @JSONField(name="SGTXT")
    private String sgtxt;

    /** 公司代码货币 */
    @Excel(name = "公司代码货币")
    @Alias("RHCUR")
    @JSONField(name="RHCUR")
    private String rhcur;

    /** 以公司代码货币计的金额 */
    @Excel(name = "以公司代码货币计的金额")
    @Alias("HSL")
    @JSONField(name="HSL")
    private String hsl;

    /** 汇率 */
    @Excel(name = "汇率")
    @Alias("KURSF")
    @JSONField(name="KURSF")
    private String kursf;

    /** 交易货币 */
    @Excel(name = "交易货币")
    @Alias("RWCUR")
    @JSONField(name="RWCUR")
    private String rwcur;

    /** 以交易货币计的金额 */
    @Excel(name = "以交易货币计的金额")
    @Alias("WSL")
    @JSONField(name="WSL")
    private String wsl;

    /** 全球货币 */
    @Excel(name = "全球货币")
    @Alias("RKCUR")
    @JSONField(name="RKCUR")
    private String rkcur;

    /** 以全球货币计的金额 */
    @Excel(name = "以全球货币计的金额")
    @Alias("KSL")
    @JSONField(name="KSL")
    private String ksl;

    /** 月份 */
    @Excel(name = "月份")
    @Alias("MONAT")
    @JSONField(name="MONAT")
    private String monat;
}
