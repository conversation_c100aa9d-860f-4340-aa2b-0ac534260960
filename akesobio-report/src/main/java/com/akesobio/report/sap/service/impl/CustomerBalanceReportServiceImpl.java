package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.CustomerBalanceReportMapper;
import com.akesobio.report.sap.domain.CustomerBalanceReport;
import com.akesobio.report.sap.service.ICustomerBalanceReportService;

/**
 * 以本币计的客户余额报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
@Service
public class CustomerBalanceReportServiceImpl implements ICustomerBalanceReportService 
{
    @Autowired
    private CustomerBalanceReportMapper customerBalanceReportMapper;

    /**
     * 查询以本币计的客户余额报表
     * 
     * @param sid 以本币计的客户余额报表主键
     * @return 以本币计的客户余额报表
     */
    @Override
    public CustomerBalanceReport selectCustomerBalanceReportBySid(Long sid)
    {
        return customerBalanceReportMapper.selectCustomerBalanceReportBySid(sid);
    }

    /**
     * 查询以本币计的客户余额报表列表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 以本币计的客户余额报表
     */
    @Override
    public List<CustomerBalanceReport> selectCustomerBalanceReportList(CustomerBalanceReport customerBalanceReport)
    {
        return customerBalanceReportMapper.selectCustomerBalanceReportList(customerBalanceReport);
    }

    /**
     * 新增以本币计的客户余额报表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 结果
     */
    @Override
    public int insertCustomerBalanceReport(CustomerBalanceReport customerBalanceReport)
    {
        return customerBalanceReportMapper.insertCustomerBalanceReport(customerBalanceReport);
    }

    /**
     * 修改以本币计的客户余额报表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 结果
     */
    @Override
    public int updateCustomerBalanceReport(CustomerBalanceReport customerBalanceReport)
    {
        return customerBalanceReportMapper.updateCustomerBalanceReport(customerBalanceReport);
    }

    /**
     * 批量删除以本币计的客户余额报表
     * 
     * @param sids 需要删除的以本币计的客户余额报表主键
     * @return 结果
     */
    @Override
    public int deleteCustomerBalanceReportBySids(Long[] sids)
    {
        return customerBalanceReportMapper.deleteCustomerBalanceReportBySids(sids);
    }

    /**
     * 删除以本币计的客户余额报表信息
     * 
     * @param sid 以本币计的客户余额报表主键
     * @return 结果
     */
    @Override
    public int deleteCustomerBalanceReportBySid(Long sid)
    {
        return customerBalanceReportMapper.deleteCustomerBalanceReportBySid(sid);
    }
}
