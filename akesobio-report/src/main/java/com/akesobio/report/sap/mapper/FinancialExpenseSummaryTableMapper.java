package com.akesobio.report.sap.mapper;

import java.util.List;
import java.util.Map;

import com.akesobio.report.sap.domain.FinancialExpenseSummaryTable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 财务费用汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface FinancialExpenseSummaryTableMapper extends BaseMapper<FinancialExpenseSummaryTable>
{
    /**
     * 查询财务费用汇总
     * 
     * @param id 财务费用汇总主键
     * @return 财务费用汇总
     */
    public FinancialExpenseSummaryTable selectFinancialExpenseSummaryTableById(String id);

    /**
     * 查询财务费用汇总列表
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 财务费用汇总集合
     */
    public List<FinancialExpenseSummaryTable> selectFinancialExpenseSummaryTableList(FinancialExpenseSummaryTable financialExpenseSummaryTable);

    /**
     * 新增财务费用汇总
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 结果
     */
    public int insertFinancialExpenseSummaryTable(FinancialExpenseSummaryTable financialExpenseSummaryTable);

    /**
     * 修改财务费用汇总
     * 
     * @param financialExpenseSummaryTable 财务费用汇总
     * @return 结果
     */
    public int updateFinancialExpenseSummaryTable(FinancialExpenseSummaryTable financialExpenseSummaryTable);

    /**
     * 删除财务费用汇总
     * 
     * @param id 财务费用汇总主键
     * @return 结果
     */
    public int deleteFinancialExpenseSummaryTableById(String id);

    /**
     * 批量删除财务费用汇总
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialExpenseSummaryTableByIds(String[] ids);

    public void deleteByYearAndMonth(Map<String, String> map);
}
