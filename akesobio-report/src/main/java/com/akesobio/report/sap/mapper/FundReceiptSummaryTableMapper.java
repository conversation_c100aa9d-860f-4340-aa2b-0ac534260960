package com.akesobio.report.sap.mapper;

import com.akesobio.report.sap.domain.FundReceiptSummaryTable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金收款汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface FundReceiptSummaryTableMapper 
{
    /**
     * 查询资金收款汇总
     * 
     * @param id 资金收款汇总主键
     * @return 资金收款汇总
     */
    public FundReceiptSummaryTable selectFundReceiptSummaryTableById(Long id);

    /**
     * 查询资金收款汇总列表
     * 
     * @param fundReceiptSummaryTable 资金收款汇总
     * @return 资金收款汇总集合
     */
    public List<FundReceiptSummaryTable> selectFundReceiptSummaryTableList(FundReceiptSummaryTable fundReceiptSummaryTable);

    /**
     * 新增资金收款汇总
     * 
     * @param fundReceiptSummaryTable 资金收款汇总
     * @return 结果
     */
    public int insertFundReceiptSummaryTable(FundReceiptSummaryTable fundReceiptSummaryTable);

    /**
     * 修改资金收款汇总
     * 
     * @param fundReceiptSummaryTable 资金收款汇总
     * @return 结果
     */
    public int updateFundReceiptSummaryTable(FundReceiptSummaryTable fundReceiptSummaryTable);

    /**
     * 批量查询
     *
     * @param fundReceiptSummaryTable 资金收款汇总
     * @return 结果
     */
    public List<FundReceiptSummaryTable> selectList(@Param("list") List<FundReceiptSummaryTable> fundReceiptSummaryTable);

    /**
     * 批量插入
     *
     * @param fundReceiptSummaryTable 资金收款汇总
     * @return 结果
     */
    public void insertList(List<FundReceiptSummaryTable> fundReceiptSummaryTable);

}
