package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 财务费用汇总对象 financial_expense_summary_table
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public class FinancialExpenseSummaryTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.AUTO)
    private int id;

    /** 公司代码或公司的名称 */
    @Excel(name = "公司代码或公司的名称")
    @JSONField(name="BUTXT")
    private String butxt;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @JSONField(name="RBUKRS")
    private String rbukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @JSONField(name="GJAHR")
    private String gjahr;

    /** 过账期间 */
    @Excel(name = "过账期间")
    @JSONField(name="POPER")
    private String poper;

    /** 科目号 */
    @Excel(name = "科目号")
    @JSONField(name="RACCT")
    private String racct;

    /** 账面科目 */
    @Excel(name = "账面科目")
    @JSONField(name="TXT20")
    private String txt20;

    /** 公司代码货币 */
    @Excel(name = "公司代码货币")
    @JSONField(name="RHCUR")
    private String rhcur;

    /** 以公司代码货币计的金额 */
    @Excel(name = "以公司代码货币计的金额")
    @JSONField(name="HSL")
    private String hsl;

    /** 全球货币 */
    @Excel(name = "全球货币")
    @JSONField(name="RKCUR")
    private String rkcur;

    /** 以全球货币计的金额 */
    @Excel(name = "以全球货币计的金额")
    @JSONField(name="KSL")
    private String ksl;

    /** 月份 */
    @Excel(name = "月份")
    @JSONField(name="MONAT")
    private String monat;


    public void setId(int id)
    {
        this.id = id;
    }

    public int getId()
    {
        return id;
    }
    public void setButxt(String butxt) 
    {
        this.butxt = butxt;
    }

    public String getButxt() 
    {
        return butxt;
    }
    public void setRbukrs(String rbukrs) 
    {
        this.rbukrs = rbukrs;
    }

    public String getRbukrs() 
    {
        return rbukrs;
    }
    public void setGjahr(String gjahr) 
    {
        this.gjahr = gjahr;
    }

    public String getGjahr() 
    {
        return gjahr;
    }
    public void setPoper(String poper) 
    {
        this.poper = poper;
    }

    public String getPoper() 
    {
        return poper;
    }
    public void setRacct(String racct) 
    {
        this.racct = racct;
    }

    public String getRacct() 
    {
        return racct;
    }
    public void setTxt20(String txt20) 
    {
        this.txt20 = txt20;
    }

    public String getTxt20() 
    {
        return txt20;
    }
    public void setRhcur(String rhcur) 
    {
        this.rhcur = rhcur;
    }

    public String getRhcur() 
    {
        return rhcur;
    }
    public void setHsl(String hsl) 
    {
        this.hsl = hsl;
    }

    public String getHsl() 
    {
        return hsl;
    }
    public void setRkcur(String rkcur) 
    {
        this.rkcur = rkcur;
    }

    public String getRkcur() 
    {
        return rkcur;
    }
    public void setKsl(String ksl) 
    {
        this.ksl = ksl;
    }

    public String getKsl() 
    {
        return ksl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("butxt", getButxt())
            .append("rbukrs", getRbukrs())
            .append("gjahr", getGjahr())
            .append("poper", getPoper())
            .append("racct", getRacct())
            .append("txt20", getTxt20())
            .append("rhcur", getRhcur())
            .append("hsl", getHsl())
            .append("rkcur", getRkcur())
            .append("ksl", getKsl())
            .append("monat", getMonat())
            .toString();
    }

    public String getMonat() {
        return monat;
    }

    public void setMonat(String monat) {
        this.monat = monat;
    }
}
