package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SalesInvoiceReportMapper;
import com.akesobio.report.sap.domain.SalesInvoiceReport;
import com.akesobio.report.sap.service.ISalesInvoiceReportService;

/**
 * SAP销售发票查询报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@Service
public class SalesInvoiceReportServiceImpl implements ISalesInvoiceReportService 
{
    @Autowired
    private SalesInvoiceReportMapper salesInvoiceReportMapper;

    /**
     * 查询SAP销售发票查询报表
     * 
     * @param sid SAP销售发票查询报表主键
     * @return SAP销售发票查询报表
     */
    @Override
    public SalesInvoiceReport selectSalesInvoiceReportBySid(Long sid)
    {
        return salesInvoiceReportMapper.selectSalesInvoiceReportBySid(sid);
    }

    /**
     * 查询SAP销售发票查询报表列表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return SAP销售发票查询报表
     */
    @Override
    public List<SalesInvoiceReport> selectSalesInvoiceReportList(SalesInvoiceReport salesInvoiceReport)
    {
        return salesInvoiceReportMapper.selectSalesInvoiceReportList(salesInvoiceReport);
    }

    /**
     * 新增SAP销售发票查询报表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return 结果
     */
    @Override
    public int insertSalesInvoiceReport(SalesInvoiceReport salesInvoiceReport)
    {
        return salesInvoiceReportMapper.insertSalesInvoiceReport(salesInvoiceReport);
    }

    /**
     * 修改SAP销售发票查询报表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return 结果
     */
    @Override
    public int updateSalesInvoiceReport(SalesInvoiceReport salesInvoiceReport)
    {
        return salesInvoiceReportMapper.updateSalesInvoiceReport(salesInvoiceReport);
    }

    /**
     * 批量删除SAP销售发票查询报表
     * 
     * @param sids 需要删除的SAP销售发票查询报表主键
     * @return 结果
     */
    @Override
    public int deleteSalesInvoiceReportBySids(Long[] sids)
    {
        return salesInvoiceReportMapper.deleteSalesInvoiceReportBySids(sids);
    }

    /**
     * 删除SAP销售发票查询报表信息
     * 
     * @param sid SAP销售发票查询报表主键
     * @return 结果
     */
    @Override
    public int deleteSalesInvoiceReportBySid(Long sid)
    {
        return salesInvoiceReportMapper.deleteSalesInvoiceReportBySid(sid);
    }
}
