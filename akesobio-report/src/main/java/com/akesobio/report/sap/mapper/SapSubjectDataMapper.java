package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.SapSubjectData;

/**
 * SAP科目输出Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface SapSubjectDataMapper 
{
    /**
     * 查询SAP科目输出
     * 
     * @param id SAP科目输出主键
     * @return SAP科目输出
     */
    public SapSubjectData selectSapSubjectDataById(Long id);

    /**
     * 查询SAP科目输出列表
     * 
     * @param sapSubjectData SAP科目输出
     * @return SAP科目输出集合
     */
    public List<SapSubjectData> selectSapSubjectDataList(SapSubjectData sapSubjectData);

    /**
     * 新增SAP科目输出
     * 
     * @param sapSubjectData SAP科目输出
     * @return 结果
     */
    public int insertSapSubjectData(SapSubjectData sapSubjectData);

    /**
     * 修改SAP科目输出
     * 
     * @param sapSubjectData SAP科目输出
     * @return 结果
     */
    public int updateSapSubjectData(SapSubjectData sapSubjectData);

    /**
     * 删除SAP科目输出
     * 
     * @param id SAP科目输出主键
     * @return 结果
     */
    public int deleteSapSubjectDataById(Long id);

    /**
     * 批量删除SAP科目输出
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSapSubjectDataByIds(Long[] ids);
}
