package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 客户未清凭证报表对象 CustomerOutstandingVoucherReport
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
@Data
public class CustomerOutstandingVoucher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String bukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    private String gjahr;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String kunnr;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String name1;

    /** 货币码  */
    @Excel(name = "货币码 ")
    private String waers;

    /** 以本币计的金额  */
    @Excel(name = "以本币计的金额 ")
    private String dmbtr;

    /** 凭证中的过账日期 */
    @Excel(name = "凭证中的过账日期")
    private String budat;

    /** 凭证中的凭证日期 */
    @Excel(name = "凭证中的凭证日期")
    private String bldat;

    /** 凭证类型 */
    @Excel(name = "凭证类型")
    private String blart;

    /** 分配 */
    @Excel(name = "分配")
    private String zuonr;

    /** 参考 */
    @Excel(name = "参考")
    private String xblnr;

    /** 项目文本 */
    @Excel(name = "项目文本")
    private String sgtxt;

    /** 发票编号1 */
    @Excel(name = "发票编号1")
    private String zzjsbh1;

    /** 发票编号2 */
    @Excel(name = "发票编号2")
    private String zzjsbh2;

    /** 付款起算日期  */
    @Excel(name = "付款起算日期 ")
    private String zfbdt;

    /** 特殊总账标识 */
    @Excel(name = "特殊总账标识")
    private String umskz;

    /** 会计凭证号码 */
    @Excel(name = "会计凭证号码")
    private String belnr;

    /** 会计凭证行项目编号  */
    @Excel(name = "会计凭证行项目编号 ")
    private String buzei;

    /** 付款天数  */
    @Excel(name = "付款天数")
    private Integer zbd1t;

    /** 到期日期  */
    @Excel(name = "到期日期")
    private String zdqdat;

    /** 逾期天数  */
    @Excel(name = "逾期天数")
    private Integer zyqts;

    /** 商务经理  */
    @Excel(name = "商务经理")
    private String lifnr_z2;

    /** 经理编号  */
    @Excel(name = "经理编号")
    private String uname;
}
