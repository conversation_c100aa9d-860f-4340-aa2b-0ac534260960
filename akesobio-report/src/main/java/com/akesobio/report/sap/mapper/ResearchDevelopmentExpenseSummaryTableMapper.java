package com.akesobio.report.sap.mapper;

import java.util.List;
import java.util.Map;

import com.akesobio.report.sap.domain.ResearchDevelopmentExpenseSummaryTable;

/**
 * 研发费用汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-18
 */
public interface ResearchDevelopmentExpenseSummaryTableMapper 
{
    /**
     * 查询研发费用汇总
     * 
     * @param id 研发费用汇总主键
     * @return 研发费用汇总
     */
    public ResearchDevelopmentExpenseSummaryTable selectResearchDevelopmentExpenseSummaryTableById(Long id);

    /**
     * 查询研发费用汇总列表
     * 
     * @param researchDevelopmentExpenseSummaryTable 研发费用汇总
     * @return 研发费用汇总集合
     */
    public List<ResearchDevelopmentExpenseSummaryTable> selectResearchDevelopmentExpenseSummaryTableList(ResearchDevelopmentExpenseSummaryTable researchDevelopmentExpenseSummaryTable);

    /**
     * 新增研发费用汇总
     * 
     * @param researchDevelopmentExpenseSummaryTable 研发费用汇总
     * @return 结果
     */
    public int insertResearchDevelopmentExpenseSummaryTable(ResearchDevelopmentExpenseSummaryTable researchDevelopmentExpenseSummaryTable);

    /**
     * 修改研发费用汇总
     * 
     * @param researchDevelopmentExpenseSummaryTable 研发费用汇总
     * @return 结果
     */
    public int updateResearchDevelopmentExpenseSummaryTable(ResearchDevelopmentExpenseSummaryTable researchDevelopmentExpenseSummaryTable);

    /**
     * 删除研发费用汇总
     * 
     * @param id 研发费用汇总主键
     * @return 结果
     */
    public int deleteResearchDevelopmentExpenseSummaryTableById(Long id);

    /**
     * 批量删除研发费用汇总
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResearchDevelopmentExpenseSummaryTableByIds(Long[] ids);

    public void deleteByYearAndMonth(Map<String, String> map);

    /**
     * 批量插入
     *
     * @param ResearchDevelopmentExpenseSummaryTableList 列表
     * @return 结果
     */
    public void insertList(List<ResearchDevelopmentExpenseSummaryTable> ResearchDevelopmentExpenseSummaryTableList);
}
