package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.CollectionFlowReport;

/**
 * 营销财务收款流水报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
public interface ICollectionFlowReportService 
{
    /**
     * 查询营销财务收款流水报表
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 营销财务收款流水报表
     */
    public CollectionFlowReport selectCollectionFlowReportBySid(Long sid);

    /**
     * 查询营销财务收款流水报表列表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 营销财务收款流水报表集合
     */
    public List<CollectionFlowReport> selectCollectionFlowReportList(CollectionFlowReport collectionFlowReport);

    /**
     * 新增营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    public int insertCollectionFlowReport(CollectionFlowReport collectionFlowReport);

    /**
     * 修改营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    public int updateCollectionFlowReport(CollectionFlowReport collectionFlowReport);

    /**
     * 批量删除营销财务收款流水报表
     * 
     * @param sids 需要删除的营销财务收款流水报表主键集合
     * @return 结果
     */
    public int deleteCollectionFlowReportBySids(Long[] sids);

    /**
     * 删除营销财务收款流水报表信息
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 结果
     */
    public int deleteCollectionFlowReportBySid(Long sid);
}
