package com.akesobio.report.sap.domain;

import cn.hutool.core.annotation.Alias;
import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP销售交货单查询对象 SalesDeliveryOrderReport
 * 
 * <AUTHOR>
 * @date 2023-09-20
 */
public class SalesDeliveryOrderReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "sid",type = IdType.AUTO)
    private Long sid;

    /** 装运点 */
    @Excel(name = "装运点")
    @Alias("VSTEL")
    private String vstel;

    /** 装运点描述 */
    @Excel(name = "装运点描述")
    @Alias("VTEXT")
    private String vtext;

    /** 发货工厂 */
    @Excel(name = "发货工厂")
    @Alias("WERKS")
    private String werks;

    /** 发货工厂描述 */
    @Excel(name = "发货工厂描述")
    @Alias("NAME1")
    private String name1;

    /** 库位 */
    @Excel(name = "库位")
    @Alias("LGORT")
    private String lgort;

    /** 库位描述 */
    @Excel(name = "库位描述")
    @Alias("LGOBE")
    private String lgobe;

    /** 交货类型 */
    @Excel(name = "交货类型")
    @Alias("LFART")
    private String lfart;

    /** 交货类型描述 */
    @Excel(name = "交货类型描述")
    @Alias("ZHLXMS")
    private String zhlxms;

    /** 售达方 */
    @Excel(name = "售达方")
    @Alias("KUNAG")
    private String kunag;

    /** 售达方名称 */
    @Excel(name = "售达方名称")
    @Alias("SDFMC")
    private String sdfmc;

    /** 送达方 */
    @Excel(name = "送达方")
    @Alias("KUNNR")
    private String kunnr;

    /** 送达方名称 */
    @Excel(name = "送达方名称")
    @Alias("ZSDFMC")
    private String zsdfmc;

    /** 发货单号 */
    @Excel(name = "发货单号")
    @Alias("VBELN")
    private String vbeln;

    /** 发货单行项目 */
    @Excel(name = "发货单行项目")
    @Alias("POSNR")
    private String posnr;

    /** 物料编号 */
    @Excel(name = "物料编号")
    @Alias("MATNR")
    private String matnr;

    /** 物料描述 */
    @Excel(name = "物料描述")
    @Alias("MAKTX")
    private String maktx;

    /** 批次 */
    @Excel(name = "批次")
    @Alias("CHARG")
    private String charg;

    /** 生产批次 */
    @Excel(name = "生产批次")
    @Alias("ZSCPC")
    private String zscpc;

    /** 生产线 */
    @Excel(name = "生产线")
    @Alias("ZSCX")
    private String zscx;

    /** 生产日期 */
    @Excel(name = "生产日期")
    @Alias("HSDAT")
    private String hsdat;

    /** 货架到期 */
    @Excel(name = "货架到期")
    @Alias("VFDAT")
    private String vfdat;

    /** 交货数量 */
    @Excel(name = "交货数量")
    @Alias("LFIMG")
    private String lfimg;

    /** 单位 */
    @Excel(name = "单位")
    @Alias("MSEH6")
    private String mseh6;

    /** 实际发货日期 */
    @Excel(name = "实际发货日期")
    @Alias("WADATIST")
    private String wadatIst;

    /** 发货过账状态 */
    @Excel(name = "发货过账状态")
    @Alias("WBSTK")
    private String wbstk;

    /** POD确认日期 */
    @Excel(name = "POD确认日期")
    @Alias("PODAT")
    private String podat;

    /** POD确认状态 */
    @Excel(name = "POD确认状态")
    @Alias("PDSTK")
    private String pdstk;

    /** 计划发货日期 */
    @Excel(name = "计划发货日期")
    @Alias("WADAT")
    private String wadat;

    /** 发货单凭证日期 */
    @Excel(name = "发货单凭证日期")
    @Alias("BLDAT")
    private String bldat;

    /** 出具发票状态 */
    @Excel(name = "出具发票状态")
    @Alias("FKSTK")
    private String fkstk;

    /** 发票号 */
    @Excel(name = "发票号")
    @Alias("BVBELN")
    private String bvbeln;

    /** 发票行号 */
    @Excel(name = "发票行号")
    @Alias("BPOSNR")
    private String bposnr;

    /** 发票过账状态 */
    @Excel(name = "发票过账状态")
    @Alias("RFBSK")
    private String rfbsk;

    /** 参考凭证号 */
    @Excel(name = "参考凭证号")
    @Alias("VGBEL")
    private String vgbel;

    /** 客户参考 */
    @Excel(name = "客户参考")
    @Alias("BSTNK")
    private String bstnk;

    /** 参考凭证行号 */
    @Excel(name = "参考凭证行号")
    @Alias("VGPOS")
    private String vgpos;

    /** 参考凭证数量 */
    @Excel(name = "参考凭证数量")
    @Alias("KWMENG")
    private String kwmeng;

    /** 数量单位 */
    @Excel(name = "数量单位")
    @Alias("QTYUNITE")
    private String qtyUnite;

    /** 单价 */
    @Excel(name = "单价")
    @Alias("KBETRDJ")
    private String kbetrDj;

    /** 单价单位 */
    @Excel(name = "单价单位")
    @Alias("DJPER")
    private String djPer;

    /** 金额 */
    @Excel(name = "金额")
    @Alias("JE")
    private String je;

    /** 净值 */
    @Excel(name = "净值")
    @Alias("JZ")
    private String jz;

    /** 税额 */
    @Excel(name = "税额")
    @Alias("TAX")
    private String tax;

    /** 交货单创建日期 */
    @Excel(name = "交货单创建日期")
    @Alias("ERDAT")
    private String erdat;

    /** 交货单创建人 */
    @Excel(name = "交货单创建人")
    @Alias("ERNAM1")
    private String ernam1;

    /** 联系人 */
    @Excel(name = "联系人")
    @Alias("ZNAMECO")
    private String znameCo;

    /** 电话 */
    @Excel(name = "电话")
    @Alias("ZTELNUMBER")
    private String ztelNumber;

    /** 收货地址 */
    @Excel(name = "收货地址")
    @Alias("ZSTREET")
    private String zstreet;

    /** 分销渠道 */
    @Excel(name = "分销渠道")
    @Alias("VTWEG")
    private String vtweg;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setVstel(String vstel) 
    {
        this.vstel = vstel;
    }

    public String getVstel() 
    {
        return vstel;
    }
    public void setVtext(String vtext) 
    {
        this.vtext = vtext;
    }

    public String getVtext() 
    {
        return vtext;
    }
    public void setWerks(String werks) 
    {
        this.werks = werks;
    }

    public String getWerks() 
    {
        return werks;
    }
    public void setName1(String name1) 
    {
        this.name1 = name1;
    }

    public String getName1() 
    {
        return name1;
    }
    public void setLgort(String lgort) 
    {
        this.lgort = lgort;
    }

    public String getLgort() 
    {
        return lgort;
    }
    public void setLgobe(String lgobe) 
    {
        this.lgobe = lgobe;
    }

    public String getLgobe() 
    {
        return lgobe;
    }
    public void setLfart(String lfart) 
    {
        this.lfart = lfart;
    }

    public String getLfart() 
    {
        return lfart;
    }
    public void setZhlxms(String zhlxms) 
    {
        this.zhlxms = zhlxms;
    }

    public String getZhlxms() 
    {
        return zhlxms;
    }
    public void setKunag(String kunag) 
    {
        this.kunag = kunag;
    }

    public String getKunag() 
    {
        return kunag;
    }
    public void setSdfmc(String sdfmc) 
    {
        this.sdfmc = sdfmc;
    }

    public String getSdfmc() 
    {
        return sdfmc;
    }
    public void setKunnr(String kunnr) 
    {
        this.kunnr = kunnr;
    }

    public String getKunnr() 
    {
        return kunnr;
    }
    public void setZsdfmc(String zsdfmc) 
    {
        this.zsdfmc = zsdfmc;
    }

    public String getZsdfmc() 
    {
        return zsdfmc;
    }
    public void setVbeln(String vbeln) 
    {
        this.vbeln = vbeln;
    }

    public String getVbeln() 
    {
        return vbeln;
    }
    public void setPosnr(String posnr) 
    {
        this.posnr = posnr;
    }

    public String getPosnr() 
    {
        return posnr;
    }
    public void setMatnr(String matnr) 
    {
        this.matnr = matnr;
    }

    public String getMatnr() 
    {
        return matnr;
    }
    public void setMaktx(String maktx) 
    {
        this.maktx = maktx;
    }

    public String getMaktx() 
    {
        return maktx;
    }
    public void setCharg(String charg) 
    {
        this.charg = charg;
    }

    public String getCharg() 
    {
        return charg;
    }
    public void setZscpc(String zscpc) 
    {
        this.zscpc = zscpc;
    }

    public String getZscpc() 
    {
        return zscpc;
    }
    public void setZscx(String zscx) 
    {
        this.zscx = zscx;
    }

    public String getZscx() 
    {
        return zscx;
    }
    public void setHsdat(String hsdat) 
    {
        this.hsdat = hsdat;
    }

    public String getHsdat() 
    {
        return hsdat;
    }
    public void setVfdat(String vfdat) 
    {
        this.vfdat = vfdat;
    }

    public String getVfdat() 
    {
        return vfdat;
    }
    public void setLfimg(String lfimg) 
    {
        this.lfimg = lfimg;
    }

    public String getLfimg() 
    {
        return lfimg;
    }
    public void setMseh6(String mseh6) 
    {
        this.mseh6 = mseh6;
    }

    public String getMseh6() 
    {
        return mseh6;
    }
    public void setWadatIst(String wadatIst) 
    {
        this.wadatIst = wadatIst;
    }

    public String getWadatIst() 
    {
        return wadatIst;
    }
    public void setWbstk(String wbstk) 
    {
        this.wbstk = wbstk;
    }

    public String getWbstk() 
    {
        return wbstk;
    }
    public void setPodat(String podat) 
    {
        this.podat = podat;
    }

    public String getPodat() 
    {
        return podat;
    }
    public void setPdstk(String pdstk) 
    {
        this.pdstk = pdstk;
    }

    public String getPdstk() 
    {
        return pdstk;
    }
    public void setWadat(String wadat) 
    {
        this.wadat = wadat;
    }

    public String getWadat() 
    {
        return wadat;
    }
    public void setBldat(String bldat) 
    {
        this.bldat = bldat;
    }

    public String getBldat() 
    {
        return bldat;
    }
    public void setFkstk(String fkstk) 
    {
        this.fkstk = fkstk;
    }

    public String getFkstk() 
    {
        return fkstk;
    }
    public void setBvbeln(String bvbeln) 
    {
        this.bvbeln = bvbeln;
    }

    public String getBvbeln() 
    {
        return bvbeln;
    }
    public void setBposnr(String bposnr) 
    {
        this.bposnr = bposnr;
    }

    public String getBposnr() 
    {
        return bposnr;
    }
    public void setRfbsk(String rfbsk) 
    {
        this.rfbsk = rfbsk;
    }

    public String getRfbsk() 
    {
        return rfbsk;
    }
    public void setVgbel(String vgbel) 
    {
        this.vgbel = vgbel;
    }

    public String getVgbel() 
    {
        return vgbel;
    }
    public void setBstnk(String bstnk) 
    {
        this.bstnk = bstnk;
    }

    public String getBstnk() 
    {
        return bstnk;
    }
    public void setVgpos(String vgpos) 
    {
        this.vgpos = vgpos;
    }

    public String getVgpos() 
    {
        return vgpos;
    }
    public void setKwmeng(String kwmeng) 
    {
        this.kwmeng = kwmeng;
    }

    public String getKwmeng() 
    {
        return kwmeng;
    }
    public void setQtyUnite(String qtyUnite) 
    {
        this.qtyUnite = qtyUnite;
    }

    public String getQtyUnite() 
    {
        return qtyUnite;
    }
    public void setKbetrDj(String kbetrDj) 
    {
        this.kbetrDj = kbetrDj;
    }

    public String getKbetrDj() 
    {
        return kbetrDj;
    }
    public void setDjPer(String djPer) 
    {
        this.djPer = djPer;
    }

    public String getDjPer() 
    {
        return djPer;
    }
    public void setJe(String je) 
    {
        this.je = je;
    }

    public String getJe() 
    {
        return je;
    }
    public void setJz(String jz) 
    {
        this.jz = jz;
    }

    public String getJz() 
    {
        return jz;
    }
    public void setTax(String tax) 
    {
        this.tax = tax;
    }

    public String getTax() 
    {
        return tax;
    }
    public void setErdat(String erdat) 
    {
        this.erdat = erdat;
    }

    public String getErdat() 
    {
        return erdat;
    }
    public void setErnam1(String ernam1) 
    {
        this.ernam1 = ernam1;
    }

    public String getErnam1() 
    {
        return ernam1;
    }
    public void setZnameCo(String znameCo) 
    {
        this.znameCo = znameCo;
    }

    public String getZnameCo() 
    {
        return znameCo;
    }
    public void setZtelNumber(String ztelNumber) 
    {
        this.ztelNumber = ztelNumber;
    }

    public String getZtelNumber() 
    {
        return ztelNumber;
    }
    public void setZstreet(String zstreet) 
    {
        this.zstreet = zstreet;
    }

    public String getZstreet() 
    {
        return zstreet;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("vstel", getVstel())
            .append("vtext", getVtext())
            .append("werks", getWerks())
            .append("name1", getName1())
            .append("lgort", getLgort())
            .append("lgobe", getLgobe())
            .append("lfart", getLfart())
            .append("zhlxms", getZhlxms())
            .append("kunag", getKunag())
            .append("sdfmc", getSdfmc())
            .append("kunnr", getKunnr())
            .append("zsdfmc", getZsdfmc())
            .append("vbeln", getVbeln())
            .append("posnr", getPosnr())
            .append("matnr", getMatnr())
            .append("maktx", getMaktx())
            .append("charg", getCharg())
            .append("zscpc", getZscpc())
            .append("zscx", getZscx())
            .append("hsdat", getHsdat())
            .append("vfdat", getVfdat())
            .append("lfimg", getLfimg())
            .append("mseh6", getMseh6())
            .append("wadatIst", getWadatIst())
            .append("wbstk", getWbstk())
            .append("podat", getPodat())
            .append("pdstk", getPdstk())
            .append("wadat", getWadat())
            .append("bldat", getBldat())
            .append("fkstk", getFkstk())
            .append("bvbeln", getBvbeln())
            .append("bposnr", getBposnr())
            .append("rfbsk", getRfbsk())
            .append("vgbel", getVgbel())
            .append("bstnk", getBstnk())
            .append("vgpos", getVgpos())
            .append("kwmeng", getKwmeng())
            .append("qtyUnite", getQtyUnite())
            .append("kbetrDj", getKbetrDj())
            .append("djPer", getDjPer())
            .append("je", getJe())
            .append("jz", getJz())
            .append("tax", getTax())
            .append("erdat", getErdat())
            .append("ernam1", getErnam1())
            .append("znameCo", getZnameCo())
            .append("ztelNumber", getZtelNumber())
            .append("zstreet", getZstreet())
            .append("vtweg", getVtweg())
            .toString();
    }

    public String getVtweg() {
        return vtweg;
    }

    public void setVtweg(String vtweg) {
        this.vtweg = vtweg;
    }
}
