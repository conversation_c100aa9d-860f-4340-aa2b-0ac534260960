package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SalesExpenseDetailsReportMapper;
import com.akesobio.report.sap.domain.SalesExpenseDetailsReport;
import com.akesobio.report.sap.service.ISalesExpenseDetailsReportService;

import javax.annotation.Resource;

/**
 * 各公司销售费用明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
@Service
public class SalesExpenseDetailsReportServiceImpl implements ISalesExpenseDetailsReportService 
{
    @Resource
    private SalesExpenseDetailsReportMapper salesExpenseDetailsReportMapper;

    /**
     * 查询各公司销售费用明细
     * 
     * @param sid 各公司销售费用明细主键
     * @return 各公司销售费用明细
     */
    @Override
    public SalesExpenseDetailsReport selectSalesExpenseDetailsReportBySid(Long sid)
    {
        return salesExpenseDetailsReportMapper.selectSalesExpenseDetailsReportBySid(sid);
    }

    /**
     * 查询各公司销售费用明细列表
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 各公司销售费用明细
     */
    @Override
    public List<SalesExpenseDetailsReport> selectSalesExpenseDetailsReportList(SalesExpenseDetailsReport salesExpenseDetailsReport)
    {
        return salesExpenseDetailsReportMapper.selectSalesExpenseDetailsReportList(salesExpenseDetailsReport);
    }

    /**
     * 新增各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    @Override
    public int insertSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport)
    {
        return salesExpenseDetailsReportMapper.insertSalesExpenseDetailsReport(salesExpenseDetailsReport);
    }

    /**
     * 修改各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    @Override
    public int updateSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport)
    {
        return salesExpenseDetailsReportMapper.updateSalesExpenseDetailsReport(salesExpenseDetailsReport);
    }
}
