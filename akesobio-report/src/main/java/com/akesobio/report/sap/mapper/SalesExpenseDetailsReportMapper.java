package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.SalesExpenseDetailsReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 各公司销售费用明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface SalesExpenseDetailsReportMapper extends BaseMapper<SalesExpenseDetailsReport>
{
    /**
     * 查询各公司销售费用明细
     * 
     * @param sid 各公司销售费用明细主键
     * @return 各公司销售费用明细
     */
    public SalesExpenseDetailsReport selectSalesExpenseDetailsReportBySid(Long sid);

    /**
     * 查询各公司销售费用明细列表
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 各公司销售费用明细集合
     */
    public List<SalesExpenseDetailsReport> selectSalesExpenseDetailsReportList(SalesExpenseDetailsReport salesExpenseDetailsReport);

    /**
     * 新增各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public int insertSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport);

    /**
     * 修改各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public int updateSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport);

    /**
     * 批量插入各公司销售费用明细
     *
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public void insertList(List<SalesExpenseDetailsReport> salesExpenseDetailsReport);

    /**
     * 批量查询各公司销售费用明细
     *
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public List<SalesExpenseDetailsReport> selectList(SalesExpenseDetailsReport salesExpenseDetailsReport);
}
