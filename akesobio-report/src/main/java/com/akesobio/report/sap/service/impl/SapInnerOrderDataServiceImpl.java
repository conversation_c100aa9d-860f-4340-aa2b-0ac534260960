package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SapInnerOrderDataMapper;
import com.akesobio.report.sap.domain.SapInnerOrderData;
import com.akesobio.report.sap.service.ISapInnerOrderDataService;

import javax.annotation.Resource;

/**
 * SAP内部订单输出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SapInnerOrderDataServiceImpl implements ISapInnerOrderDataService 
{
    @Resource
    private SapInnerOrderDataMapper sapInnerOrderDataMapper;

    /**
     * 查询SAP内部订单输出
     * 
     * @param id SAP内部订单输出主键
     * @return SAP内部订单输出
     */
    @Override
    public SapInnerOrderData selectSapInnerOrderDataById(Long id)
    {
        return sapInnerOrderDataMapper.selectSapInnerOrderDataById(id);
    }

    /**
     * 查询SAP内部订单输出列表
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return SAP内部订单输出
     */
    @Override
    public List<SapInnerOrderData> selectSapInnerOrderDataList(SapInnerOrderData sapInnerOrderData)
    {
        return sapInnerOrderDataMapper.selectSapInnerOrderDataList(sapInnerOrderData);
    }

    /**
     * 新增SAP内部订单输出
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return 结果
     */
    @Override
    public int insertSapInnerOrderData(SapInnerOrderData sapInnerOrderData)
    {
        return sapInnerOrderDataMapper.insertSapInnerOrderData(sapInnerOrderData);
    }

    /**
     * 修改SAP内部订单输出
     * 
     * @param sapInnerOrderData SAP内部订单输出
     * @return 结果
     */
    @Override
    public int updateSapInnerOrderData(SapInnerOrderData sapInnerOrderData)
    {
        return sapInnerOrderDataMapper.updateSapInnerOrderData(sapInnerOrderData);
    }

    /**
     * 批量删除SAP内部订单输出
     * 
     * @param ids 需要删除的SAP内部订单输出主键
     * @return 结果
     */
    @Override
    public int deleteSapInnerOrderDataByIds(Long[] ids)
    {
        return sapInnerOrderDataMapper.deleteSapInnerOrderDataByIds(ids);
    }

    /**
     * 删除SAP内部订单输出信息
     * 
     * @param id SAP内部订单输出主键
     * @return 结果
     */
    @Override
    public int deleteSapInnerOrderDataById(Long id)
    {
        return sapInnerOrderDataMapper.deleteSapInnerOrderDataById(id);
    }
}
