package com.akesobio.report.sap.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.FixedAssetsReportMapper;
import com.akesobio.report.sap.domain.FixedAssetsReport;
import com.akesobio.report.sap.service.IFixedAssetsReportService;

import javax.annotation.Resource;

/**
 * 固定资产清单报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-06
 */
@Service
public class FixedAssetsReportServiceImpl implements IFixedAssetsReportService 
{
    @Resource
    private FixedAssetsReportMapper fixedAssetsReportMapper;

    /**
     * 查询固定资产清单报表
     * 
     * @param sid 固定资产清单报表主键
     * @return 固定资产清单报表
     */
    @Override
    public FixedAssetsReport selectFixedAssetsReportBySid(Long sid)
    {
        return fixedAssetsReportMapper.selectFixedAssetsReportBySid(sid);
    }

    /**
     * 查询固定资产清单报表列表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 固定资产清单报表
     */
    @Override
    public List<FixedAssetsReport> selectFixedAssetsReportList(FixedAssetsReport fixedAssetsReport)
    {
        return fixedAssetsReportMapper.selectFixedAssetsReportList(fixedAssetsReport);
    }

    @Override
    public List<FixedAssetsDetailsTableVo2> selectFixedAssetsReport2List(FixedAssetsReport fixedAssetsReport) {
        //A1、A2类别的总和
        BigDecimal zqmljzjSum1 = BigDecimal.ZERO;
        BigDecimal zqmyzSum1 = BigDecimal.ZERO;
        BigDecimal zjzSum1 = BigDecimal.ZERO;
        //A3类别总和
        BigDecimal zqmljzjSum2 = BigDecimal.ZERO;
        BigDecimal zqmyzSum2 = BigDecimal.ZERO;
        BigDecimal zjzSum2 = BigDecimal.ZERO;
        //所有类别总和
        BigDecimal zqmljzjSum3 = BigDecimal.ZERO;
        BigDecimal zqmyzSum3 = BigDecimal.ZERO;
        BigDecimal zjzSum3 = BigDecimal.ZERO;
        List<FixedAssetsDetailsTableVo2> list = fixedAssetsReportMapper.selectFixedAssetsTable2List(fixedAssetsReport);
        for (FixedAssetsDetailsTableVo2 fixedAsset : list) {
            if (fixedAsset.getAnlkl().contains("A1") || fixedAsset.getAnlkl().contains("A2")){
                zqmljzjSum1 = zqmljzjSum1.add(fixedAsset.getZqmljzj());
                zqmyzSum1 = zqmyzSum1.add(fixedAsset.getZqmyz());
                zjzSum1 = zjzSum1.add(fixedAsset.getZjz());
            }
            else if (fixedAsset.getAnlkl().contains("A3")) {
                zqmljzjSum2 = zqmljzjSum2.add(fixedAsset.getZqmljzj());
                zqmyzSum2 = zqmyzSum2.add(fixedAsset.getZqmyz());
                zjzSum2 = zjzSum2.add(fixedAsset.getZjz());
            }
            else {
                zqmljzjSum3 = zqmljzjSum3.add(fixedAsset.getZqmljzj());
                zqmyzSum3 = zqmyzSum3.add(fixedAsset.getZqmyz());
                zjzSum3 = zjzSum3.add(fixedAsset.getZjz());
            }
        }
        FixedAssetsDetailsTableVo2 detailsTableVo1 = new FixedAssetsDetailsTableVo2();
        FixedAssetsDetailsTableVo2 detailsTableVo2 = new FixedAssetsDetailsTableVo2();
        FixedAssetsDetailsTableVo2 detailsTableVo3 = new FixedAssetsDetailsTableVo2();
        detailsTableVo1.setTxk20("固定资产小计：");
        detailsTableVo1.setZqmljzj(zqmljzjSum1);
        detailsTableVo1.setZqmyz(zqmyzSum1);
        detailsTableVo1.setZjz(zjzSum1);
        detailsTableVo2.setTxk20("无形资产小计：");
        detailsTableVo2.setZqmljzj(zqmljzjSum2);
        detailsTableVo2.setZqmyz(zqmyzSum2);
        detailsTableVo2.setZjz(zjzSum2);
        detailsTableVo3.setTxk20("所有分类总计：");
        BigDecimal totalZqmyz = zqmyzSum1.add(zqmyzSum2).add(zqmyzSum3);
        BigDecimal totalZqmljzj = zqmljzjSum1.add(zqmljzjSum2).add(zqmljzjSum3);
        BigDecimal totalZjz = zjzSum1.add(zjzSum2).add(zjzSum3);
        detailsTableVo3.setZqmljzj(totalZqmljzj);
        detailsTableVo3.setZqmyz(totalZqmyz);
        detailsTableVo3.setZjz(totalZjz);
        //把汇总插入到list
        list.add(detailsTableVo1);
        list.add(detailsTableVo2);
        list.add(detailsTableVo3);
        return list;
    }

    /**
     * 新增固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    @Override
    public int insertFixedAssetsReport(FixedAssetsReport fixedAssetsReport)
    {
        return fixedAssetsReportMapper.insertFixedAssetsReport(fixedAssetsReport);
    }

    /**
     * 修改固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    @Override
    public int updateFixedAssetsReport(FixedAssetsReport fixedAssetsReport)
    {
        return fixedAssetsReportMapper.updateFixedAssetsReport(fixedAssetsReport);
    }

    /**
     * 批量删除固定资产清单报表
     * 
     * @param sids 需要删除的固定资产清单报表主键
     * @return 结果
     */
    @Override
    public int deleteFixedAssetsReportBySids(Long[] sids)
    {
        return fixedAssetsReportMapper.deleteFixedAssetsReportBySids(sids);
    }

    /**
     * 删除固定资产清单报表信息
     * 
     * @param sid 固定资产清单报表主键
     * @return 结果
     */
    @Override
    public int deleteFixedAssetsReportBySid(Long sid)
    {
        return fixedAssetsReportMapper.deleteFixedAssetsReportBySid(sid);
    }
}
