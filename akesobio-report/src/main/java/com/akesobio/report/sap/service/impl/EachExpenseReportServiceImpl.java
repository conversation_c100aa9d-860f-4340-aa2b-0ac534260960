package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.EachExpenseReportMapper;
import com.akesobio.report.sap.domain.EachExpenseReport;
import com.akesobio.report.sap.service.IEachExpenseReportService;

import javax.annotation.Resource;

/**
 * 各费用汇总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class EachExpenseReportServiceImpl implements IEachExpenseReportService 
{
    @Resource
    private EachExpenseReportMapper eachExpenseReportMapper;

    /**
     * 查询各费用汇总
     * 
     * @param id 各费用汇总主键
     * @return 各费用汇总
     */
    @Override
    public EachExpenseReport selectEachExpenseReportById(String id)
    {
        return eachExpenseReportMapper.selectEachExpenseReportById(id);
    }

    /**
     * 查询各费用汇总列表
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 各费用汇总
     */
    @Override
    public List<EachExpenseReport> selectEachExpenseReportList(EachExpenseReport eachExpenseReport)
    {
        return eachExpenseReportMapper.selectEachExpenseReportList(eachExpenseReport);
    }

    /**
     * 新增各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    @Override
    public int insertEachExpenseReport(EachExpenseReport eachExpenseReport)
    {
        return eachExpenseReportMapper.insertEachExpenseReport(eachExpenseReport);
    }

    /**
     * 修改各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    @Override
    public int updateEachExpenseReport(EachExpenseReport eachExpenseReport)
    {
        return eachExpenseReportMapper.updateEachExpenseReport(eachExpenseReport);
    }

    /**
     * 批量删除各费用汇总
     * 
     * @param ids 需要删除的各费用汇总主键
     * @return 结果
     */
    @Override
    public int deleteEachExpenseReportByIds(String[] ids)
    {
        return eachExpenseReportMapper.deleteEachExpenseReportByIds(ids);
    }

    /**
     * 删除各费用汇总信息
     * 
     * @param id 各费用汇总主键
     * @return 结果
     */
    @Override
    public int deleteEachExpenseReportById(String id)
    {
        return eachExpenseReportMapper.deleteEachExpenseReportById(id);
    }
}
