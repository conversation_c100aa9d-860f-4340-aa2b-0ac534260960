package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.CustomerOutstandingVoucher;

/**
 * 客户未清凭证报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public interface ICustomerOutstandingVoucherService 
{
    /**
     * 查询客户未清凭证报表
     * 
     * @param sid 客户未清凭证报表主键
     * @return 客户未清凭证报表
     */
    public CustomerOutstandingVoucher selectCustomerOutstandingVoucherBySid(Long sid);

    /**
     * 查询客户未清凭证报表列表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 客户未清凭证报表集合
     */
    public List<CustomerOutstandingVoucher> selectCustomerOutstandingVoucherList(CustomerOutstandingVoucher customerOutstandingVoucher);

    /**
     * 新增客户未清凭证报表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 结果
     */
    public int insertCustomerOutstandingVoucher(CustomerOutstandingVoucher customerOutstandingVoucher);

    /**
     * 修改客户未清凭证报表
     * 
     * @param customerOutstandingVoucher 客户未清凭证报表
     * @return 结果
     */
    public int updateCustomerOutstandingVoucher(CustomerOutstandingVoucher customerOutstandingVoucher);

    /**
     * 批量删除客户未清凭证报表
     * 
     * @param sids 需要删除的客户未清凭证报表主键集合
     * @return 结果
     */
    public int deleteCustomerOutstandingVoucherBySids(Long[] sids);

    /**
     * 删除客户未清凭证报表信息
     * 
     * @param sid 客户未清凭证报表主键
     * @return 结果
     */
    public int deleteCustomerOutstandingVoucherBySid(Long sid);
}
