package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 以本币计的客户余额报表对象 CustomerBalanceReport
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public class CustomerBalanceReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 会计年度 */
    @Excel(name = "会计年度")
    private String gjahr;

    /** 统驭科目 */
    @Excel(name = "统驭科目")
    private String saknr;

    /** 科目长文本 */
    @Excel(name = "科目长文本")
    private String txt50;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String kunnr;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String bukrs;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String name1;

    /** 特殊总账标识 */
    @Excel(name = "特殊总账标识")
    private String umskz;

    /** 货币码 */
    @Excel(name = "货币码")
    private String waers;

    /** 余额结转 */
    @Excel(name = "余额结转")
    private String opbal;

    /** 报表期间的借方余额 */
    @Excel(name = "报表期间的借方余额")
    private String debbal;

    /** 报表期间的贷方余额  */
    @Excel(name = "报表期间的贷方余额 ")
    private String credbal;

    /** 借方总计余额 */
    @Excel(name = "借方总计余额")
    private String salds;

    /** 贷方总计余额 */
    @Excel(name = "贷方总计余额")
    private String saldh;

    /** 在报表期末累积的余额 */
    @Excel(name = "在报表期末累积的余额")
    private String acytdBal;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setGjahr(String gjahr) 
    {
        this.gjahr = gjahr;
    }

    public String getGjahr() 
    {
        return gjahr;
    }
    public void setSaknr(String saknr) 
    {
        this.saknr = saknr;
    }

    public String getSaknr() 
    {
        return saknr;
    }
    public void setTxt50(String txt50) 
    {
        this.txt50 = txt50;
    }

    public String getTxt50() 
    {
        return txt50;
    }
    public void setKunnr(String kunnr) 
    {
        this.kunnr = kunnr;
    }

    public String getKunnr() 
    {
        return kunnr;
    }
    public void setBukrs(String bukrs) 
    {
        this.bukrs = bukrs;
    }

    public String getBukrs() 
    {
        return bukrs;
    }
    public void setName1(String name1) 
    {
        this.name1 = name1;
    }

    public String getName1() 
    {
        return name1;
    }
    public void setUmskz(String umskz) 
    {
        this.umskz = umskz;
    }

    public String getUmskz() 
    {
        return umskz;
    }
    public void setWaers(String waers) 
    {
        this.waers = waers;
    }

    public String getWaers() 
    {
        return waers;
    }
    public void setOpbal(String opbal) 
    {
        this.opbal = opbal;
    }

    public String getOpbal() 
    {
        return opbal;
    }
    public void setDebbal(String debbal) 
    {
        this.debbal = debbal;
    }

    public String getDebbal() 
    {
        return debbal;
    }
    public void setCredbal(String credbal) 
    {
        this.credbal = credbal;
    }

    public String getCredbal() 
    {
        return credbal;
    }
    public void setSalds(String salds) 
    {
        this.salds = salds;
    }

    public String getSalds() 
    {
        return salds;
    }
    public void setSaldh(String saldh) 
    {
        this.saldh = saldh;
    }

    public String getSaldh() 
    {
        return saldh;
    }
    public void setAcytdBal(String acytdBal) 
    {
        this.acytdBal = acytdBal;
    }

    public String getAcytdBal() 
    {
        return acytdBal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("gjahr", getGjahr())
            .append("saknr", getSaknr())
            .append("txt50", getTxt50())
            .append("kunnr", getKunnr())
            .append("bukrs", getBukrs())
            .append("name1", getName1())
            .append("umskz", getUmskz())
            .append("waers", getWaers())
            .append("opbal", getOpbal())
            .append("debbal", getDebbal())
            .append("credbal", getCredbal())
            .append("salds", getSalds())
            .append("saldh", getSaldh())
            .append("acytdBal", getAcytdBal())
            .toString();
    }
}
