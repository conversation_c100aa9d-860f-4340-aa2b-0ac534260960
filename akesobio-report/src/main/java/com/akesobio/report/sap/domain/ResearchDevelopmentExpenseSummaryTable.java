package com.akesobio.report.sap.domain;

import cn.hutool.core.annotation.Alias;
import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 研发费用汇总对象 research_development_expense_summary_table
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
public class ResearchDevelopmentExpenseSummaryTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 公司代码或公司的名称 */
    @Excel(name = "公司代码或公司的名称")
    @JSONField(name="BUTXT")
    @Alias("BUTXT")
    private String butxt;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @Alias("RBUKRS")
    @JSONField(name="RBUKRS")
    private String rbukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @Alias("GJAHR")
    @JSONField(name="GJAHR")
    private String gjahr;

    /** 过账期间 */
    @Excel(name = "过账期间")
    @Alias("POPER")
    @JSONField(name="POPER")
    private String poper;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @Alias("AUFNR")
    @JSONField(name="AUFNR")
    private String aufnr;

    /** 订单号  */
    @Excel(name = "订单号 ")
    @Alias("KTEXT")
    @JSONField(name="KTEXT")
    private String ktext;

    /** 科目号  */
    @Excel(name = "科目号 ")
    @Alias("RACCT")
    @JSONField(name="RACCT")
    private String racct;

    /** 账面科目 */
    @Excel(name = "账面科目")
    @Alias("ZZMKM")
    @JSONField(name="ZZMKM")
    private String zzmkm;

    /** 汇总科目  */
    @Excel(name = "汇总科目 ")
    @Alias("ZHZKM")
    @JSONField(name="ZHZKM")
    private String zhzkm;

    /** 披露科目 */
    @Excel(name = "披露科目")
    @Alias("ZPLKM")
    @JSONField(name="ZPLKM")
    private String zplkm;

    /** 公司代码货币 */
    @Excel(name = "公司代码货币")
    @Alias("RHCUR")
    @JSONField(name="RHCUR")
    private String rhcur;

    /** 以公司代码货币计的金额  */
    @Excel(name = "以公司代码货币计的金额 ")
    @Alias("HSL")
    @JSONField(name="HSL")
    private String hsl;

    /** 全球货币 */
    @Excel(name = "全球货币")
    @Alias("RKCUR")
    @JSONField(name="RKCUR")
    private String rkcur;

    /** 以全球货币计的金额  */
    @Excel(name = "以全球货币计的金额 ")
    @Alias("KSL")
    @JSONField(name="KSL")
    private String ksl;

    /** 发送方成本中心 */
    @Excel(name = "发送方成本中心")
    @Alias("SCNTR")
    @JSONField(name="SCNTR")
    private String scntr;

    /** 成本中心 */
    @Excel(name = "成本中心")
    @Alias("RCNTR")
    @JSONField(name="RCNTR")
    private String rcntr;

    /** 部门SAP名称 */
    @Excel(name = "部门SAP名称")
    @Alias("ZBMMC")
    @JSONField(name="ZBMMC")
    private String zbmmc;

    /** 功能范围 */
    @Excel(name = "功能范围")
    @Alias("RFAREA")
    @JSONField(name="RFAREA")
    private String rfarea;

    /** 成本分类 */
    @Excel(name = "成本分类")
    @Alias("ZCBFL")
    @JSONField(name="ZCBFL")
    private String zcbfl;

    /** 数据类型 */
    @Excel(name = "数据类型")
    @Alias("ZSJLX")
    @JSONField(name="ZSJLX")
    private String zsjlx;

    /** 管线分类 */
    @Excel(name = "管线分类")
    @Alias("ZGXFL")
    @JSONField(name="ZGXFL")
    private String zgxfl;

    /** 药品管线 */
    @Excel(name = "药品管线")
    @Alias("ZYPGX")
    @JSONField(name="ZYPGX")
    private String zypgx;

    /** 适应症名称  */
    @Excel(name = "适应症名称 ")
    @Alias("ZSYZMC")
    @JSONField(name="ZSYZMC")
    private String zsyzmc;

    /** 月份 */
    @Excel(name = "月份")
    @Alias("MONAT")
    @JSONField(name="MONAT")
    private String monat;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setButxt(String butxt)
    {
        this.butxt = butxt;
    }

    public String getButxt()
    {
        return butxt;
    }
    public void setRbukrs(String rbukrs)
    {
        this.rbukrs = rbukrs;
    }

    public String getRbukrs()
    {
        return rbukrs;
    }
    public void setGjahr(String gjahr)
    {
        this.gjahr = gjahr;
    }

    public String getGjahr()
    {
        return gjahr;
    }
    public void setPoper(String poper)
    {
        this.poper = poper;
    }

    public String getPoper()
    {
        return poper;
    }
    public void setAufnr(String aufnr)
    {
        this.aufnr = aufnr;
    }

    public String getAufnr()
    {
        return aufnr;
    }
    public void setKtext(String ktext)
    {
        this.ktext = ktext;
    }

    public String getKtext()
    {
        return ktext;
    }
    public void setRacct(String racct)
    {
        this.racct = racct;
    }

    public String getRacct()
    {
        return racct;
    }
    public void setZzmkm(String zzmkm)
    {
        this.zzmkm = zzmkm;
    }

    public String getZzmkm()
    {
        return zzmkm;
    }
    public void setZhzkm(String zhzkm)
    {
        this.zhzkm = zhzkm;
    }

    public String getZhzkm()
    {
        return zhzkm;
    }
    public void setZplkm(String zplkm)
    {
        this.zplkm = zplkm;
    }

    public String getZplkm()
    {
        return zplkm;
    }
    public void setRhcur(String rhcur)
    {
        this.rhcur = rhcur;
    }

    public String getRhcur()
    {
        return rhcur;
    }
    public void setHsl(String hsl)
    {
        this.hsl = hsl;
    }

    public String getHsl()
    {
        return hsl;
    }
    public void setRkcur(String rkcur)
    {
        this.rkcur = rkcur;
    }

    public String getRkcur()
    {
        return rkcur;
    }
    public void setKsl(String ksl)
    {
        this.ksl = ksl;
    }

    public String getKsl()
    {
        return ksl;
    }
    public void setScntr(String scntr)
    {
        this.scntr = scntr;
    }

    public String getScntr()
    {
        return scntr;
    }
    public void setRcntr(String rcntr)
    {
        this.rcntr = rcntr;
    }

    public String getRcntr()
    {
        return rcntr;
    }
    public void setZbmmc(String zbmmc)
    {
        this.zbmmc = zbmmc;
    }

    public String getZbmmc()
    {
        return zbmmc;
    }
    public void setRfarea(String rfarea)
    {
        this.rfarea = rfarea;
    }

    public String getRfarea()
    {
        return rfarea;
    }
    public void setZcbfl(String zcbfl)
    {
        this.zcbfl = zcbfl;
    }

    public String getZcbfl()
    {
        return zcbfl;
    }
    public void setZsjlx(String zsjlx)
    {
        this.zsjlx = zsjlx;
    }

    public String getZsjlx()
    {
        return zsjlx;
    }
    public void setZgxfl(String zgxfl)
    {
        this.zgxfl = zgxfl;
    }

    public String getZgxfl()
    {
        return zgxfl;
    }
    public void setZypgx(String zypgx)
    {
        this.zypgx = zypgx;
    }

    public String getZypgx()
    {
        return zypgx;
    }
    public void setZsyzmc(String zsyzmc)
    {
        this.zsyzmc = zsyzmc;
    }

    public String getZsyzmc()
    {
        return zsyzmc;
    }
    public void setMonat(String monat)
    {
        this.monat = monat;
    }

    public String getMonat()
    {
        return monat;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("butxt", getButxt())
                .append("rbukrs", getRbukrs())
                .append("gjahr", getGjahr())
                .append("poper", getPoper())
                .append("aufnr", getAufnr())
                .append("ktext", getKtext())
                .append("racct", getRacct())
                .append("zzmkm", getZzmkm())
                .append("zhzkm", getZhzkm())
                .append("zplkm", getZplkm())
                .append("rhcur", getRhcur())
                .append("hsl", getHsl())
                .append("rkcur", getRkcur())
                .append("ksl", getKsl())
                .append("scntr", getScntr())
                .append("rcntr", getRcntr())
                .append("zbmmc", getZbmmc())
                .append("rfarea", getRfarea())
                .append("zcbfl", getZcbfl())
                .append("zsjlx", getZsjlx())
                .append("zgxfl", getZgxfl())
                .append("zypgx", getZypgx())
                .append("zsyzmc", getZsyzmc())
                .append("monat", getMonat())
                .toString();
    }
}
