package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.SalesExpenseDetailsReport;

/**
 * 各公司销售费用明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface ISalesExpenseDetailsReportService 
{
    /**
     * 查询各公司销售费用明细
     * 
     * @param sid 各公司销售费用明细主键
     * @return 各公司销售费用明细
     */
    public SalesExpenseDetailsReport selectSalesExpenseDetailsReportBySid(Long sid);

    /**
     * 查询各公司销售费用明细列表
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 各公司销售费用明细集合
     */
    public List<SalesExpenseDetailsReport> selectSalesExpenseDetailsReportList(SalesExpenseDetailsReport salesExpenseDetailsReport);

    /**
     * 新增各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public int insertSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport);

    /**
     * 修改各公司销售费用明细
     * 
     * @param salesExpenseDetailsReport 各公司销售费用明细
     * @return 结果
     */
    public int updateSalesExpenseDetailsReport(SalesExpenseDetailsReport salesExpenseDetailsReport);
}
