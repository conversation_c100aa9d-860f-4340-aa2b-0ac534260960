package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SalesDeliveryOrderReportMapper;
import com.akesobio.report.sap.domain.SalesDeliveryOrderReport;
import com.akesobio.report.sap.service.ISalesDeliveryOrderReportService;

/**
 * SAP销售交货单查询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-20
 */
@Service
public class SalesDeliveryOrderReportServiceImpl implements ISalesDeliveryOrderReportService 
{
    @Autowired
    private SalesDeliveryOrderReportMapper salesDeliveryOrderReportMapper;

    /**
     * 查询SAP销售交货单查询
     * 
     * @param sid SAP销售交货单查询主键
     * @return SAP销售交货单查询
     */
    @Override
    public SalesDeliveryOrderReport selectSalesDeliveryOrderReportBySid(Long sid)
    {
        return salesDeliveryOrderReportMapper.selectSalesDeliveryOrderReportBySid(sid);
    }

    /**
     * 查询SAP销售交货单查询列表
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return SAP销售交货单查询
     */
    @Override
    public List<SalesDeliveryOrderReport> selectSalesDeliveryOrderReportList(SalesDeliveryOrderReport salesDeliveryOrderReport)
    {
        return salesDeliveryOrderReportMapper.selectSalesDeliveryOrderReportList(salesDeliveryOrderReport);
    }

    /**
     * 新增SAP销售交货单查询
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return 结果
     */
    @Override
    public int insertSalesDeliveryOrderReport(SalesDeliveryOrderReport salesDeliveryOrderReport)
    {
        return salesDeliveryOrderReportMapper.insertSalesDeliveryOrderReport(salesDeliveryOrderReport);
    }

    /**
     * 修改SAP销售交货单查询
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return 结果
     */
    @Override
    public int updateSalesDeliveryOrderReport(SalesDeliveryOrderReport salesDeliveryOrderReport)
    {
        return salesDeliveryOrderReportMapper.updateSalesDeliveryOrderReport(salesDeliveryOrderReport);
    }

    /**
     * 批量删除SAP销售交货单查询
     * 
     * @param sids 需要删除的SAP销售交货单查询主键
     * @return 结果
     */
    @Override
    public int deleteSalesDeliveryOrderReportBySids(Long[] sids)
    {
        return salesDeliveryOrderReportMapper.deleteSalesDeliveryOrderReportBySids(sids);
    }

    /**
     * 删除SAP销售交货单查询信息
     * 
     * @param sid SAP销售交货单查询主键
     * @return 结果
     */
    @Override
    public int deleteSalesDeliveryOrderReportBySid(Long sid)
    {
        return salesDeliveryOrderReportMapper.deleteSalesDeliveryOrderReportBySid(sid);
    }
}
