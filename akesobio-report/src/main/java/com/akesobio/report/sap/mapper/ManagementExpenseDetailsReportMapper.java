package com.akesobio.report.sap.mapper;

import java.util.List;
import com.akesobio.report.sap.domain.ManagementExpenseDetailsReport;
import com.akesobio.report.sap.domain.SalesExpenseDetailsReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 各公司管理费用明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface ManagementExpenseDetailsReportMapper extends BaseMapper<ManagementExpenseDetailsReport>
{
    /**
     * 查询各公司管理费用明细
     * 
     * @param sid 各公司管理费用明细主键
     * @return 各公司管理费用明细
     */
    public ManagementExpenseDetailsReport selectManagementExpenseDetailsReportBySid(Long sid);

    /**
     * 查询各公司管理费用明细列表
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 各公司管理费用明细集合
     */
    public List<ManagementExpenseDetailsReport> selectManagementExpenseDetailsReportList(ManagementExpenseDetailsReport managementExpenseDetailsReport);

    /**
     * 新增各公司管理费用明细
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 结果
     */
    public int insertManagementExpenseDetailsReport(ManagementExpenseDetailsReport managementExpenseDetailsReport);

    /**
     * 批量插入各公司管理费用明细
     *
     * @param salesExpenseDetailsReport 各公司管理费用明细
     * @return 结果
     */
    public void insertList(List<ManagementExpenseDetailsReport> salesExpenseDetailsReport);

    /**
     * 修改各公司管理费用明细
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 结果
     */
    public int updateManagementExpenseDetailsReport(ManagementExpenseDetailsReport managementExpenseDetailsReport);

    /**
     * 删除各公司管理费用明细
     * 
     * @param sid 各公司管理费用明细主键
     * @return 结果
     */
    public int deleteManagementExpenseDetailsReportBySid(Long sid);

    /**
     * 批量删除各公司管理费用明细
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteManagementExpenseDetailsReportBySids(Long[] sids);
}
