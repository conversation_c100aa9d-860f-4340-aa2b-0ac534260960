package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.ProfitReportMapper;
import com.akesobio.report.sap.domain.ProfitReport;
import com.akesobio.report.sap.service.IProfitReportService;

/**
 * SAP利润报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class ProfitReportServiceImpl implements IProfitReportService 
{
    @Autowired
    private ProfitReportMapper profitReportMapper;

    /**
     * 查询SAP利润报表
     * 
     * @param sid SAP利润报表主键
     * @return SAP利润报表
     */
    @Override
    public ProfitReport selectProfitReportBySid(Long sid)
    {
        return profitReportMapper.selectProfitReportBySid(sid);
    }

    /**
     * 查询SAP利润报表列表
     * 
     * @param profitReport SAP利润报表
     * @return SAP利润报表
     */
    @Override
    public List<ProfitReport> selectProfitReportList(ProfitReport profitReport)
    {
        return profitReportMapper.selectProfitReportList(profitReport);
    }

    /**
     * 新增SAP利润报表
     * 
     * @param profitReport SAP利润报表
     * @return 结果
     */
    @Override
    public int insertProfitReport(ProfitReport profitReport)
    {
        return profitReportMapper.insertProfitReport(profitReport);
    }

    /**
     * 修改SAP利润报表
     * 
     * @param profitReport SAP利润报表
     * @return 结果
     */
    @Override
    public int updateProfitReport(ProfitReport profitReport)
    {
        return profitReportMapper.updateProfitReport(profitReport);
    }

    /**
     * 批量删除SAP利润报表
     * 
     * @param sids 需要删除的SAP利润报表主键
     * @return 结果
     */
    @Override
    public int deleteProfitReportBySids(Long[] sids)
    {
        return profitReportMapper.deleteProfitReportBySids(sids);
    }

    /**
     * 删除SAP利润报表信息
     * 
     * @param sid SAP利润报表主键
     * @return 结果
     */
    @Override
    public int deleteProfitReportBySid(Long sid)
    {
        return profitReportMapper.deleteProfitReportBySid(sid);
    }
}
