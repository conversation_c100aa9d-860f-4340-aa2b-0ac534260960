package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 月度期末库存汇总对象 monthly_ending_inventory_summary_table
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public class MonthlyEndingInventorySummaryTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 会计年度 */
    @Excel(name = "会计年度")
    @JSONField(name="RYEAR")
    private String ryear;

    /** 科目号 */
    @Excel(name = "科目号")
    @JSONField(name="RACCT")
    private String racct;

    /** 总账科目名称 */
    @Excel(name = "总账科目名称")
    @JSONField(name="TXT20")
    private String txt20;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @JSONField(name="RBUKRS")
    private String rbukrs;

    /** 公司代码或公司的名称 */
    @Excel(name = "公司代码或公司的名称")
    @JSONField(name="BUTXT")
    private String butxt;

    /** 1月 */
    @Excel(name = "1月")
    @JSONField(name="HSL01")
    private String hsl01;

    /** 2月 */
    @Excel(name = "2月")
    @JSONField(name="HSL02")
    private String hsl02;

    /** 3月 */
    @Excel(name = "3月")
    @JSONField(name="HSL03")
    private String hsl03;

    /** 4月 */
    @Excel(name = "4月")
    @JSONField(name="HSL04")
    private String hsl04;

    /** 5月 */
    @Excel(name = "5月")
    @JSONField(name="HSL05")
    private String hsl05;

    /** 6月 */
    @Excel(name = "6月")
    @JSONField(name="HSL06")
    private String hsl06;

    /** 7月 */
    @Excel(name = "7月")
    @JSONField(name="HSL07")
    private String hsl07;

    /** 8月 */
    @Excel(name = "8月")
    @JSONField(name="HSL08")
    private String hsl08;

    /** 9月 */
    @Excel(name = "9月")
    @JSONField(name="HSL09")
    private String hsl09;

    /** 10月 */
    @Excel(name = "10月")
    @JSONField(name="HSL10")
    private String hsl10;

    /** 11月 */
    @Excel(name = "11月")
    @JSONField(name="HSL11")
    private String hsl11;

    /** 12月 */
    @Excel(name = "12月")
    @JSONField(name="HSL12")
    private String hsl12;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRyear(String ryear) 
    {
        this.ryear = ryear;
    }

    public String getRyear() 
    {
        return ryear;
    }
    public void setRacct(String racct) 
    {
        this.racct = racct;
    }

    public String getRacct() 
    {
        return racct;
    }
    public void setTxt20(String txt20) 
    {
        this.txt20 = txt20;
    }

    public String getTxt20() 
    {
        return txt20;
    }
    public void setRbukrs(String rbukrs) 
    {
        this.rbukrs = rbukrs;
    }

    public String getRbukrs() 
    {
        return rbukrs;
    }
    public void setButxt(String butxt) 
    {
        this.butxt = butxt;
    }

    public String getButxt() 
    {
        return butxt;
    }
    public void setHsl01(String hsl01) 
    {
        this.hsl01 = hsl01;
    }

    public String getHsl01() 
    {
        return hsl01;
    }
    public void setHsl02(String hsl02) 
    {
        this.hsl02 = hsl02;
    }

    public String getHsl02() 
    {
        return hsl02;
    }
    public void setHsl03(String hsl03) 
    {
        this.hsl03 = hsl03;
    }

    public String getHsl03() 
    {
        return hsl03;
    }
    public void setHsl04(String hsl04) 
    {
        this.hsl04 = hsl04;
    }

    public String getHsl04() 
    {
        return hsl04;
    }
    public void setHsl05(String hsl05) 
    {
        this.hsl05 = hsl05;
    }

    public String getHsl05() 
    {
        return hsl05;
    }
    public void setHsl06(String hsl06) 
    {
        this.hsl06 = hsl06;
    }

    public String getHsl06() 
    {
        return hsl06;
    }
    public void setHsl07(String hsl07) 
    {
        this.hsl07 = hsl07;
    }

    public String getHsl07() 
    {
        return hsl07;
    }
    public void setHsl08(String hsl08) 
    {
        this.hsl08 = hsl08;
    }

    public String getHsl08() 
    {
        return hsl08;
    }
    public void setHsl09(String hsl09) 
    {
        this.hsl09 = hsl09;
    }

    public String getHsl09() 
    {
        return hsl09;
    }
    public void setHsl10(String hsl10) 
    {
        this.hsl10 = hsl10;
    }

    public String getHsl10() 
    {
        return hsl10;
    }
    public void setHsl11(String hsl11) 
    {
        this.hsl11 = hsl11;
    }

    public String getHsl11() 
    {
        return hsl11;
    }
    public void setHsl12(String hsl12) 
    {
        this.hsl12 = hsl12;
    }

    public String getHsl12() 
    {
        return hsl12;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("ryear", getRyear())
            .append("racct", getRacct())
            .append("txt20", getTxt20())
            .append("rbukrs", getRbukrs())
            .append("butxt", getButxt())
            .append("hsl01", getHsl01())
            .append("hsl02", getHsl02())
            .append("hsl03", getHsl03())
            .append("hsl04", getHsl04())
            .append("hsl05", getHsl05())
            .append("hsl06", getHsl06())
            .append("hsl07", getHsl07())
            .append("hsl08", getHsl08())
            .append("hsl09", getHsl09())
            .append("hsl10", getHsl10())
            .append("hsl11", getHsl11())
            .append("hsl12", getHsl12())
            .toString();
    }
}
