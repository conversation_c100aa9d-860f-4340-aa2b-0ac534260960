package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.ManagementExpenseDetailsReportMapper;
import com.akesobio.report.sap.domain.ManagementExpenseDetailsReport;
import com.akesobio.report.sap.service.IManagementExpenseDetailsReportService;

import javax.annotation.Resource;

/**
 * 各公司管理费用明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
@Service
public class ManagementExpenseDetailsReportServiceImpl implements IManagementExpenseDetailsReportService 
{
    @Resource
    private ManagementExpenseDetailsReportMapper managementExpenseDetailsReportMapper;

    /**
     * 查询各公司管理费用明细
     * 
     * @param sid 各公司管理费用明细主键
     * @return 各公司管理费用明细
     */
    @Override
    public ManagementExpenseDetailsReport selectManagementExpenseDetailsReportBySid(Long sid)
    {
        return managementExpenseDetailsReportMapper.selectManagementExpenseDetailsReportBySid(sid);
    }

    /**
     * 查询各公司管理费用明细列表
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 各公司管理费用明细
     */
    @Override
    public List<ManagementExpenseDetailsReport> selectManagementExpenseDetailsReportList(ManagementExpenseDetailsReport managementExpenseDetailsReport)
    {
        return managementExpenseDetailsReportMapper.selectManagementExpenseDetailsReportList(managementExpenseDetailsReport);
    }

    /**
     * 新增各公司管理费用明细
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 结果
     */
    @Override
    public int insertManagementExpenseDetailsReport(ManagementExpenseDetailsReport managementExpenseDetailsReport)
    {
        return managementExpenseDetailsReportMapper.insertManagementExpenseDetailsReport(managementExpenseDetailsReport);
    }

    /**
     * 修改各公司管理费用明细
     * 
     * @param managementExpenseDetailsReport 各公司管理费用明细
     * @return 结果
     */
    @Override
    public int updateManagementExpenseDetailsReport(ManagementExpenseDetailsReport managementExpenseDetailsReport)
    {
        return managementExpenseDetailsReportMapper.updateManagementExpenseDetailsReport(managementExpenseDetailsReport);
    }

    /**
     * 批量删除各公司管理费用明细
     * 
     * @param sids 需要删除的各公司管理费用明细主键
     * @return 结果
     */
    @Override
    public int deleteManagementExpenseDetailsReportBySids(Long[] sids)
    {
        return managementExpenseDetailsReportMapper.deleteManagementExpenseDetailsReportBySids(sids);
    }

    /**
     * 删除各公司管理费用明细信息
     * 
     * @param sid 各公司管理费用明细主键
     * @return 结果
     */
    @Override
    public int deleteManagementExpenseDetailsReportBySid(Long sid)
    {
        return managementExpenseDetailsReportMapper.deleteManagementExpenseDetailsReportBySid(sid);
    }
}
