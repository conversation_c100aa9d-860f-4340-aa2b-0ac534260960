package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.CashFlowReportMapper;
import com.akesobio.report.sap.domain.CashFlowReport;
import com.akesobio.report.sap.service.ICashFlowReportService;

/**
 * SAP现金流量报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class CashFlowReportServiceImpl implements ICashFlowReportService 
{
    @Autowired
    private CashFlowReportMapper cashFlowReportMapper;

    /**
     * 查询SAP现金流量报表
     * 
     * @param sid SAP现金流量报表主键
     * @return SAP现金流量报表
     */
    @Override
    public CashFlowReport selectCashFlowReportBySid(Long sid)
    {
        return cashFlowReportMapper.selectCashFlowReportBySid(sid);
    }

    /**
     * 查询SAP现金流量报表列表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return SAP现金流量报表
     */
    @Override
    public List<CashFlowReport> selectCashFlowReportList(CashFlowReport cashFlowReport)
    {
        return cashFlowReportMapper.selectCashFlowReportList(cashFlowReport);
    }

    /**
     * 新增SAP现金流量报表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return 结果
     */
    @Override
    public int insertCashFlowReport(CashFlowReport cashFlowReport)
    {
        return cashFlowReportMapper.insertCashFlowReport(cashFlowReport);
    }

    /**
     * 修改SAP现金流量报表
     * 
     * @param cashFlowReport SAP现金流量报表
     * @return 结果
     */
    @Override
    public int updateCashFlowReport(CashFlowReport cashFlowReport)
    {
        return cashFlowReportMapper.updateCashFlowReport(cashFlowReport);
    }

    /**
     * 批量删除SAP现金流量报表
     * 
     * @param sids 需要删除的SAP现金流量报表主键
     * @return 结果
     */
    @Override
    public int deleteCashFlowReportBySids(Long[] sids)
    {
        return cashFlowReportMapper.deleteCashFlowReportBySids(sids);
    }

    /**
     * 删除SAP现金流量报表信息
     * 
     * @param sid SAP现金流量报表主键
     * @return 结果
     */
    @Override
    public int deleteCashFlowReportBySid(Long sid)
    {
        return cashFlowReportMapper.deleteCashFlowReportBySid(sid);
    }
}
