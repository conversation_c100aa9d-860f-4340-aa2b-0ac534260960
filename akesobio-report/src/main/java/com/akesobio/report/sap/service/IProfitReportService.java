package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.ProfitReport;

/**
 * SAP利润报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface IProfitReportService 
{
    /**
     * 查询SAP利润报表
     * 
     * @param sid SAP利润报表主键
     * @return SAP利润报表
     */
    public ProfitReport selectProfitReportBySid(Long sid);

    /**
     * 查询SAP利润报表列表
     * 
     * @param profitReport SAP利润报表
     * @return SAP利润报表集合
     */
    public List<ProfitReport> selectProfitReportList(ProfitReport profitReport);

    /**
     * 新增SAP利润报表
     * 
     * @param profitReport SAP利润报表
     * @return 结果
     */
    public int insertProfitReport(ProfitReport profitReport);

    /**
     * 修改SAP利润报表
     * 
     * @param profitReport SAP利润报表
     * @return 结果
     */
    public int updateProfitReport(ProfitReport profitReport);

    /**
     * 批量删除SAP利润报表
     * 
     * @param sids 需要删除的SAP利润报表主键集合
     * @return 结果
     */
    public int deleteProfitReportBySids(Long[] sids);

    /**
     * 删除SAP利润报表信息
     * 
     * @param sid SAP利润报表主键
     * @return 结果
     */
    public int deleteProfitReportBySid(Long sid);
}
