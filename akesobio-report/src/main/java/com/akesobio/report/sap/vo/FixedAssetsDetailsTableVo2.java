package com.akesobio.report.sap.vo;

import com.akesobio.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class FixedAssetsDetailsTableVo2 {

    /** 资产分类描述 */
    @Excel(name = "资产分类描述")
    private String txk20;

    /** 资产分类 */
    @Excel(name = "资产分类")
    private String anlkl;

    /** 期末原值 */
    @Excel(name = "期末原值")
    private BigDecimal zqmyz;

    /** 期末数累计折旧 */
    @Excel(name = "期末数累计折旧")
    private BigDecimal zqmljzj;

    /** 账面净值 */
    @Excel(name = "账面净值")
    private BigDecimal zjz;
}
