package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.PurchaseOrderInvoiceDetailsTable;

/**
 * 采购订单发票明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface IPurchaseOrderInvoiceDetailsTableService 
{
    /**
     * 查询采购订单发票明细
     * 
     * @param id 采购订单发票明细主键
     * @return 采购订单发票明细
     */
    public PurchaseOrderInvoiceDetailsTable selectPurchaseOrderInvoiceDetailsTableById(Long id);

    /**
     * 查询采购订单发票明细列表
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 采购订单发票明细集合
     */
    public List<PurchaseOrderInvoiceDetailsTable> selectPurchaseOrderInvoiceDetailsTableList(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable);

    /**
     * 新增采购订单发票明细
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 结果
     */
    public int insertPurchaseOrderInvoiceDetailsTable(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable);

    /**
     * 修改采购订单发票明细
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 结果
     */
    public int updatePurchaseOrderInvoiceDetailsTable(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable);

    /**
     * 批量删除采购订单发票明细
     * 
     * @param ids 需要删除的采购订单发票明细主键集合
     * @return 结果
     */
    public int deletePurchaseOrderInvoiceDetailsTableByIds(Long[] ids);

    /**
     * 删除采购订单发票明细信息
     * 
     * @param id 采购订单发票明细主键
     * @return 结果
     */
    public int deletePurchaseOrderInvoiceDetailsTableById(Long id);
}
