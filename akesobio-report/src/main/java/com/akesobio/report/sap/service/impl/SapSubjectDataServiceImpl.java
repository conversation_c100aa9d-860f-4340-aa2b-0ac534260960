package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SapSubjectDataMapper;
import com.akesobio.report.sap.domain.SapSubjectData;
import com.akesobio.report.sap.service.ISapSubjectDataService;

import javax.annotation.Resource;

/**
 * SAP科目输出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SapSubjectDataServiceImpl implements ISapSubjectDataService 
{
    @Resource
    private SapSubjectDataMapper sapSubjectDataMapper;

    /**
     * 查询SAP科目输出
     * 
     * @param id SAP科目输出主键
     * @return SAP科目输出
     */
    @Override
    public SapSubjectData selectSapSubjectDataById(Long id)
    {
        return sapSubjectDataMapper.selectSapSubjectDataById(id);
    }

    /**
     * 查询SAP科目输出列表
     * 
     * @param sapSubjectData SAP科目输出
     * @return SAP科目输出
     */
    @Override
    public List<SapSubjectData> selectSapSubjectDataList(SapSubjectData sapSubjectData)
    {
        return sapSubjectDataMapper.selectSapSubjectDataList(sapSubjectData);
    }

    /**
     * 新增SAP科目输出
     * 
     * @param sapSubjectData SAP科目输出
     * @return 结果
     */
    @Override
    public int insertSapSubjectData(SapSubjectData sapSubjectData)
    {
        return sapSubjectDataMapper.insertSapSubjectData(sapSubjectData);
    }

    /**
     * 修改SAP科目输出
     * 
     * @param sapSubjectData SAP科目输出
     * @return 结果
     */
    @Override
    public int updateSapSubjectData(SapSubjectData sapSubjectData)
    {
        return sapSubjectDataMapper.updateSapSubjectData(sapSubjectData);
    }

    /**
     * 批量删除SAP科目输出
     * 
     * @param ids 需要删除的SAP科目输出主键
     * @return 结果
     */
    @Override
    public int deleteSapSubjectDataByIds(Long[] ids)
    {
        return sapSubjectDataMapper.deleteSapSubjectDataByIds(ids);
    }

    /**
     * 删除SAP科目输出信息
     * 
     * @param id SAP科目输出主键
     * @return 结果
     */
    @Override
    public int deleteSapSubjectDataById(Long id)
    {
        return sapSubjectDataMapper.deleteSapSubjectDataById(id);
    }
}
