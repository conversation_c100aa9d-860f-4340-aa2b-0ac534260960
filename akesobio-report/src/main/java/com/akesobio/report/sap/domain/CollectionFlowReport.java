package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 营销财务收款流水报表对象 CollectionFlowReport
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
public class CollectionFlowReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 工厂 */
    @Excel(name = "工厂")
    private String bukrs;

    /** 开户行 */
    @Excel(name = "开户行")
    private String hbkid;

    /** 账户标识 */
    @Excel(name = "账户标识")
    private String hktid;

    /** 收款类别 */
    @Excel(name = "收款类别")
    private String zcinpay;

    /** 交易日期 */
    @Excel(name = "交易日期")
    private String bankDate;

    /** 业务伙伴 */
    @Excel(name = "业务伙伴")
    private String partner;

    /** 对方银行账户 */
    @Excel(name = "对方银行账户")
    private String partBankAcctName;

    /** 对方银行账号 */
    @Excel(name = "对方银行账号")
    private String partBankAcctNo;

    /** 方向 */
    @Excel(name = "方向")
    private String direction;

    /** 账户货币 */
    @Excel(name = "账户货币")
    private String currency;

    /** 交易金额 */
    @Excel(name = "交易金额")
    private String otherAmount;

    /** 分配 */
    @Excel(name = "分配")
    private String zuonr;

    /** 原始流水号 */
    @Excel(name = "原始流水号")
    private String oriseq;

    /** 回单用途 */
    @Excel(name = "回单用途")
    private String ebrUse;

    /** 收款凭证 */
    @Excel(name = "收款凭证")
    private String belnr;

    /** 回单编码 */
    @Excel(name = "回单编码")
    private String ebrNo;

    /** 冲销 */
    @Excel(name = "冲销")
    private String xreversed;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setBukrs(String bukrs) 
    {
        this.bukrs = bukrs;
    }

    public String getBukrs() 
    {
        return bukrs;
    }
    public void setHbkid(String hbkid) 
    {
        this.hbkid = hbkid;
    }

    public String getHbkid() 
    {
        return hbkid;
    }
    public void setHktid(String hktid) 
    {
        this.hktid = hktid;
    }

    public String getHktid() 
    {
        return hktid;
    }
    public void setZcinpay(String zcinpay) 
    {
        this.zcinpay = zcinpay;
    }

    public String getZcinpay() 
    {
        return zcinpay;
    }
    public void setBankDate(String bankDate) 
    {
        this.bankDate = bankDate;
    }

    public String getBankDate() 
    {
        return bankDate;
    }
    public void setPartner(String partner) 
    {
        this.partner = partner;
    }

    public String getPartner() 
    {
        return partner;
    }
    public void setPartBankAcctName(String partBankAcctName) 
    {
        this.partBankAcctName = partBankAcctName;
    }

    public String getPartBankAcctName() 
    {
        return partBankAcctName;
    }
    public void setPartBankAcctNo(String partBankAcctNo) 
    {
        this.partBankAcctNo = partBankAcctNo;
    }

    public String getPartBankAcctNo() 
    {
        return partBankAcctNo;
    }
    public void setDirection(String direction) 
    {
        this.direction = direction;
    }

    public String getDirection() 
    {
        return direction;
    }
    public void setCurrency(String currency) 
    {
        this.currency = currency;
    }

    public String getCurrency() 
    {
        return currency;
    }
    public void setOtherAmount(String otherAmount) 
    {
        this.otherAmount = otherAmount;
    }

    public String getOtherAmount() 
    {
        return otherAmount;
    }
    public void setZuonr(String zuonr) 
    {
        this.zuonr = zuonr;
    }

    public String getZuonr() 
    {
        return zuonr;
    }
    public void setOriseq(String oriseq) 
    {
        this.oriseq = oriseq;
    }

    public String getOriseq() 
    {
        return oriseq;
    }
    public void setEbrUse(String ebrUse) 
    {
        this.ebrUse = ebrUse;
    }

    public String getEbrUse() 
    {
        return ebrUse;
    }
    public void setBelnr(String belnr) 
    {
        this.belnr = belnr;
    }

    public String getBelnr() 
    {
        return belnr;
    }
    public void setEbrNo(String ebrNo) 
    {
        this.ebrNo = ebrNo;
    }

    public String getEbrNo() 
    {
        return ebrNo;
    }
    public void setXreversed(String xreversed) 
    {
        this.xreversed = xreversed;
    }

    public String getXreversed() 
    {
        return xreversed;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("bukrs", getBukrs())
            .append("hbkid", getHbkid())
            .append("hktid", getHktid())
            .append("zcinpay", getZcinpay())
            .append("bankDate", getBankDate())
            .append("partner", getPartner())
            .append("partBankAcctName", getPartBankAcctName())
            .append("partBankAcctNo", getPartBankAcctNo())
            .append("direction", getDirection())
            .append("currency", getCurrency())
            .append("otherAmount", getOtherAmount())
            .append("zuonr", getZuonr())
            .append("oriseq", getOriseq())
            .append("ebrUse", getEbrUse())
            .append("belnr", getBelnr())
            .append("ebrNo", getEbrNo())
            .append("xreversed", getXreversed())
            .toString();
    }
}
