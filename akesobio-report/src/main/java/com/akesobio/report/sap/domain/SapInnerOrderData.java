package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP内部订单输出对象 sap_inner_order_data
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public class SapInnerOrderData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 内部订单号 */
    @Excel(name = "内部订单号")
    @JSONField(name="AUFNR")
    private String innerOrderNum;

    /** 订单类型 */
    @Excel(name = "订单类型")
    @JSONField(name="AUART")
    private String orderType;

    /** 订单描述 */
    @Excel(name = "订单描述")
    @JSONField(name="KTEXT")
    private String orderText;

    /** 一级分类 */
    @Excel(name = "一级分类")
    @JSONField(name="ZSORT1")
    private String primaryClassification;

    /** 二级分类 */
    @Excel(name = "二级分类")
    @JSONField(name="ZSORT2")
    private String secondaryClassification;

    /** 功能范围 */
    @Excel(name = "功能范围")
    @JSONField(name="FUNC_AREA")
    private String functionalScope;

    public String getFunctionalScope() {
        return functionalScope;
    }

    public void setFunctionalScope(String functionalScope) {
        this.functionalScope = functionalScope;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setInnerOrderNum(String innerOrderNum) 
    {
        this.innerOrderNum = innerOrderNum;
    }

    public String getInnerOrderNum() 
    {
        return innerOrderNum;
    }
    public void setOrderType(String orderType) 
    {
        this.orderType = orderType;
    }

    public String getOrderType() 
    {
        return orderType;
    }
    public void setOrderText(String orderText) 
    {
        this.orderText = orderText;
    }

    public String getOrderText() 
    {
        return orderText;
    }
    public void setPrimaryClassification(String primaryClassification) 
    {
        this.primaryClassification = primaryClassification;
    }

    public String getPrimaryClassification() 
    {
        return primaryClassification;
    }
    public void setSecondaryClassification(String secondaryClassification) 
    {
        this.secondaryClassification = secondaryClassification;
    }

    public String getSecondaryClassification() 
    {
        return secondaryClassification;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("innerOrderNum", getInnerOrderNum())
            .append("orderType", getOrderType())
            .append("orderText", getOrderText())
            .append("primaryClassification", getPrimaryClassification())
            .append("secondaryClassification", getSecondaryClassification())
            .append("functionalScope", getFunctionalScope())
            .toString();
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + (innerOrderNum == null ? 0 : innerOrderNum.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SapInnerOrderData) {
            SapInnerOrderData data = (SapInnerOrderData) obj;
            return this.getInnerOrderNum().equals(data.getInnerOrderNum());
        }
        return false;
    }
}
