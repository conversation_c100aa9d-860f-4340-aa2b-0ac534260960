package com.akesobio.report.sap.mapper;

import java.util.List;
import java.util.Map;

import com.akesobio.report.sap.domain.EachExpenseReport;
import com.akesobio.report.sap.domain.SalesExpenseDetailsReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 各费用汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface EachExpenseReportMapper  extends BaseMapper<EachExpenseReport>
{
    /**
     * 查询各费用汇总
     * 
     * @param id 各费用汇总主键
     * @return 各费用汇总
     */
    public EachExpenseReport selectEachExpenseReportById(String id);

    /**
     * 查询各费用汇总列表
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 各费用汇总集合
     */
    public List<EachExpenseReport> selectEachExpenseReportList(EachExpenseReport eachExpenseReport);

    /**
     * 新增各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    public int insertEachExpenseReport(EachExpenseReport eachExpenseReport);

    /**
     * 批量插入
     *
     * @param eachExpenseReportList 列表
     * @return 结果
     */
    public void insertList(List<EachExpenseReport> eachExpenseReportList);

    /**
     * 修改各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    public int updateEachExpenseReport(EachExpenseReport eachExpenseReport);

    /**
     * 删除各费用汇总
     * 
     * @param id 各费用汇总主键
     * @return 结果
     */
    public int deleteEachExpenseReportById(String id);

    /**
     * 批量删除各费用汇总
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEachExpenseReportByIds(String[] ids);

    public void deleteByYearAndMonth(Map<String, String> map);
}
