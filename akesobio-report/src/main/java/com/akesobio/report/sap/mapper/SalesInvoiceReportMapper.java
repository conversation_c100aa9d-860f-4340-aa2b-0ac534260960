package com.akesobio.report.sap.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sap.domain.SalesInvoiceReport;

/**
 * SAP销售发票查询报表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@DataSource(DataSourceType.SAP)
public interface SalesInvoiceReportMapper 
{
    /**
     * 查询SAP销售发票查询报表
     * 
     * @param sid SAP销售发票查询报表主键
     * @return SAP销售发票查询报表
     */
    public SalesInvoiceReport selectSalesInvoiceReportBySid(Long sid);

    /**
     * 查询SAP销售发票查询报表列表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return SAP销售发票查询报表集合
     */
    public List<SalesInvoiceReport> selectSalesInvoiceReportList(SalesInvoiceReport salesInvoiceReport);

    /**
     * 新增SAP销售发票查询报表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return 结果
     */
    public int insertSalesInvoiceReport(SalesInvoiceReport salesInvoiceReport);

    /**
     * 修改SAP销售发票查询报表
     * 
     * @param salesInvoiceReport SAP销售发票查询报表
     * @return 结果
     */
    public int updateSalesInvoiceReport(SalesInvoiceReport salesInvoiceReport);

    /**
     * 删除SAP销售发票查询报表
     * 
     * @param sid SAP销售发票查询报表主键
     * @return 结果
     */
    public int deleteSalesInvoiceReportBySid(Long sid);

    /**
     * 批量删除SAP销售发票查询报表
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesInvoiceReportBySids(Long[] sids);
}
