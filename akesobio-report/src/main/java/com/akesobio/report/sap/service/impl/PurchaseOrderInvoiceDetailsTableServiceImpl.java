package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.PurchaseOrderInvoiceDetailsTableMapper;
import com.akesobio.report.sap.domain.PurchaseOrderInvoiceDetailsTable;
import com.akesobio.report.sap.service.IPurchaseOrderInvoiceDetailsTableService;

import javax.annotation.Resource;

/**
 * 采购订单发票明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class PurchaseOrderInvoiceDetailsTableServiceImpl implements IPurchaseOrderInvoiceDetailsTableService 
{
    @Resource
    private PurchaseOrderInvoiceDetailsTableMapper purchaseOrderInvoiceDetailsTableMapper;

    /**
     * 查询采购订单发票明细
     * 
     * @param id 采购订单发票明细主键
     * @return 采购订单发票明细
     */
    @Override
    public PurchaseOrderInvoiceDetailsTable selectPurchaseOrderInvoiceDetailsTableById(Long id)
    {
        return purchaseOrderInvoiceDetailsTableMapper.selectPurchaseOrderInvoiceDetailsTableById(id);
    }

    /**
     * 查询采购订单发票明细列表
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 采购订单发票明细
     */
    @Override
    public List<PurchaseOrderInvoiceDetailsTable> selectPurchaseOrderInvoiceDetailsTableList(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable)
    {
        return purchaseOrderInvoiceDetailsTableMapper.selectPurchaseOrderInvoiceDetailsTableList(purchaseOrderInvoiceDetailsTable);
    }

    /**
     * 新增采购订单发票明细
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 结果
     */
    @Override
    public int insertPurchaseOrderInvoiceDetailsTable(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable)
    {
        return purchaseOrderInvoiceDetailsTableMapper.insertPurchaseOrderInvoiceDetailsTable(purchaseOrderInvoiceDetailsTable);
    }

    /**
     * 修改采购订单发票明细
     * 
     * @param purchaseOrderInvoiceDetailsTable 采购订单发票明细
     * @return 结果
     */
    @Override
    public int updatePurchaseOrderInvoiceDetailsTable(PurchaseOrderInvoiceDetailsTable purchaseOrderInvoiceDetailsTable)
    {
        return purchaseOrderInvoiceDetailsTableMapper.updatePurchaseOrderInvoiceDetailsTable(purchaseOrderInvoiceDetailsTable);
    }

    /**
     * 批量删除采购订单发票明细
     * 
     * @param ids 需要删除的采购订单发票明细主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderInvoiceDetailsTableByIds(Long[] ids)
    {
        return purchaseOrderInvoiceDetailsTableMapper.deletePurchaseOrderInvoiceDetailsTableByIds(ids);
    }

    /**
     * 删除采购订单发票明细信息
     * 
     * @param id 采购订单发票明细主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderInvoiceDetailsTableById(Long id)
    {
        return purchaseOrderInvoiceDetailsTableMapper.deletePurchaseOrderInvoiceDetailsTableById(id);
    }
}
