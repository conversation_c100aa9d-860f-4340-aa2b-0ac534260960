package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.ZrpFi002Mapper;
import com.akesobio.report.sap.domain.ZrpFi002;
import com.akesobio.report.sap.service.IZrpFi002Service;

import javax.annotation.Resource;

/**
 * SAP执行获利能力报告报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class ZrpFi002ServiceImpl implements IZrpFi002Service 
{
    @Resource
    private ZrpFi002Mapper zrpFi002Mapper;

    /**
     * 查询SAP执行获利能力报告报表
     * 
     * @param sid SAP执行获利能力报告报表主键
     * @return SAP执行获利能力报告报表
     */
    @Override
    public ZrpFi002 selectZrpFi002BySid(Long sid)
    {
        return zrpFi002Mapper.selectZrpFi002BySid(sid);
    }

    /**
     * 查询SAP执行获利能力报告报表列表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return SAP执行获利能力报告报表
     */
    @Override
    public List<ZrpFi002> selectZrpFi002List(ZrpFi002 zrpFi002)
    {
        return zrpFi002Mapper.selectZrpFi002List(zrpFi002);
    }

    /**
     * 新增SAP执行获利能力报告报表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return 结果
     */
    @Override
    public int insertZrpFi002(ZrpFi002 zrpFi002)
    {
        return zrpFi002Mapper.insertZrpFi002(zrpFi002);
    }

    /**
     * 修改SAP执行获利能力报告报表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return 结果
     */
    @Override
    public int updateZrpFi002(ZrpFi002 zrpFi002)
    {
        return zrpFi002Mapper.updateZrpFi002(zrpFi002);
    }

    /**
     * 批量删除SAP执行获利能力报告报表
     * 
     * @param sids 需要删除的SAP执行获利能力报告报表主键
     * @return 结果
     */
    @Override
    public int deleteZrpFi002BySids(Long[] sids)
    {
        return zrpFi002Mapper.deleteZrpFi002BySids(sids);
    }

    /**
     * 删除SAP执行获利能力报告报表信息
     * 
     * @param sid SAP执行获利能力报告报表主键
     * @return 结果
     */
    @Override
    public int deleteZrpFi002BySid(Long sid)
    {
        return zrpFi002Mapper.deleteZrpFi002BySid(sid);
    }
}
