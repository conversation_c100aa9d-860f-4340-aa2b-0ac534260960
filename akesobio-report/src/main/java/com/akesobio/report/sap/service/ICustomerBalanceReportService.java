package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.CustomerBalanceReport;

/**
 * 以本币计的客户余额报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public interface ICustomerBalanceReportService 
{
    /**
     * 查询以本币计的客户余额报表
     * 
     * @param sid 以本币计的客户余额报表主键
     * @return 以本币计的客户余额报表
     */
    public CustomerBalanceReport selectCustomerBalanceReportBySid(Long sid);

    /**
     * 查询以本币计的客户余额报表列表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 以本币计的客户余额报表集合
     */
    public List<CustomerBalanceReport> selectCustomerBalanceReportList(CustomerBalanceReport customerBalanceReport);

    /**
     * 新增以本币计的客户余额报表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 结果
     */
    public int insertCustomerBalanceReport(CustomerBalanceReport customerBalanceReport);

    /**
     * 修改以本币计的客户余额报表
     * 
     * @param customerBalanceReport 以本币计的客户余额报表
     * @return 结果
     */
    public int updateCustomerBalanceReport(CustomerBalanceReport customerBalanceReport);

    /**
     * 批量删除以本币计的客户余额报表
     * 
     * @param sids 需要删除的以本币计的客户余额报表主键集合
     * @return 结果
     */
    public int deleteCustomerBalanceReportBySids(Long[] sids);

    /**
     * 删除以本币计的客户余额报表信息
     * 
     * @param sid 以本币计的客户余额报表主键
     * @return 结果
     */
    public int deleteCustomerBalanceReportBySid(Long sid);
}
