package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SapCostCenterDataMapper;
import com.akesobio.report.sap.domain.SapCostCenterData;
import com.akesobio.report.sap.service.ISapCostCenterDataService;

import javax.annotation.Resource;

/**
 * SAP成本中心输出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SapCostCenterDataServiceImpl implements ISapCostCenterDataService 
{
    @Resource
    private SapCostCenterDataMapper sapCostCenterDataMapper;

    /**
     * 查询SAP成本中心输出
     * 
     * @param id SAP成本中心输出主键
     * @return SAP成本中心输出
     */
    @Override
    public SapCostCenterData selectSapCostCenterDataById(Long id)
    {
        return sapCostCenterDataMapper.selectSapCostCenterDataById(id);
    }

    /**
     * 查询SAP成本中心输出列表
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return SAP成本中心输出
     */
    @Override
    public List<SapCostCenterData> selectSapCostCenterDataList(SapCostCenterData sapCostCenterData)
    {
        return sapCostCenterDataMapper.selectSapCostCenterDataList(sapCostCenterData);
    }

    /**
     * 新增SAP成本中心输出
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return 结果
     */
    @Override
    public int insertSapCostCenterData(SapCostCenterData sapCostCenterData)
    {
        return sapCostCenterDataMapper.insertSapCostCenterData(sapCostCenterData);
    }

    /**
     * 修改SAP成本中心输出
     * 
     * @param sapCostCenterData SAP成本中心输出
     * @return 结果
     */
    @Override
    public int updateSapCostCenterData(SapCostCenterData sapCostCenterData)
    {
        return sapCostCenterDataMapper.updateSapCostCenterData(sapCostCenterData);
    }

    /**
     * 批量删除SAP成本中心输出
     * 
     * @param ids 需要删除的SAP成本中心输出主键
     * @return 结果
     */
    @Override
    public int deleteSapCostCenterDataByIds(Long[] ids)
    {
        return sapCostCenterDataMapper.deleteSapCostCenterDataByIds(ids);
    }

    /**
     * 删除SAP成本中心输出信息
     * 
     * @param id SAP成本中心输出主键
     * @return 结果
     */
    @Override
    public int deleteSapCostCenterDataById(Long id)
    {
        return sapCostCenterDataMapper.deleteSapCostCenterDataById(id);
    }
}
