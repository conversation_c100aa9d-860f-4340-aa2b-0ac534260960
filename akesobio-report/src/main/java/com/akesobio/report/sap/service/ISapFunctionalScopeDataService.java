package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.SapFunctionalScopeData;

/**
 * SAP功能范围输出Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface ISapFunctionalScopeDataService 
{
    /**
     * 查询SAP功能范围输出
     * 
     * @param id SAP功能范围输出主键
     * @return SAP功能范围输出
     */
    public SapFunctionalScopeData selectSapFunctionalScopeDataById(Long id);

    /**
     * 查询SAP功能范围输出列表
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return SAP功能范围输出集合
     */
    public List<SapFunctionalScopeData> selectSapFunctionalScopeDataList(SapFunctionalScopeData sapFunctionalScopeData);

    /**
     * 新增SAP功能范围输出
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return 结果
     */
    public int insertSapFunctionalScopeData(SapFunctionalScopeData sapFunctionalScopeData);

    /**
     * 修改SAP功能范围输出
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return 结果
     */
    public int updateSapFunctionalScopeData(SapFunctionalScopeData sapFunctionalScopeData);

    /**
     * 批量删除SAP功能范围输出
     * 
     * @param ids 需要删除的SAP功能范围输出主键集合
     * @return 结果
     */
    public int deleteSapFunctionalScopeDataByIds(Long[] ids);

    /**
     * 删除SAP功能范围输出信息
     * 
     * @param id SAP功能范围输出主键
     * @return 结果
     */
    public int deleteSapFunctionalScopeDataById(Long id);
}
