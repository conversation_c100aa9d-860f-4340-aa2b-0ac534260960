package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP执行获利能力报告报表对象 ZrpFi002
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public class ZrpFi002 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long sid;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String rbukrs;

    /** 会计年度 */
    @Excel(name = "会计年度")
    private String gjahr;

    /** 期间/年度 */
    @Excel(name = "期间/年度")
    private String fiscyearper;

    /** 凭证类型 */
    @Excel(name = "凭证类型")
    private String blart;

    /** 销售订单数 */
    @Excel(name = "销售订单数")
    private String kdauf;

    /** 销售订单中的项目编号 */
    @Excel(name = "销售订单中的项目编号")
    private String kdpos;

    /** 采购凭证编号 */
    @Excel(name = "采购凭证编号")
    private String ebeln;

    /** 采购凭证的项目编号 */
    @Excel(name = "采购凭证的项目编号")
    private String ebelp;

    /** 数量 */
    @Excel(name = "数量")
    private String msl;

    /** 基本计量单位 */
    @Excel(name = "基本计量单位")
    private String runit;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String kunnr;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String matnr;

    /** 物料长描述 */
    @Excel(name = "物料长描述")
    private String zzcms;

    /** 评估类 */
    @Excel(name = "评估类")
    private String bklas;

    /** 科目号 */
    @Excel(name = "科目号")
    private String racct;

    /** 分销渠道 */
    @Excel(name = "分销渠道")
    private String vtweg;

    /** 物料类型 */
    @Excel(name = "物料类型")
    private String mtartPa;

    /** 物料组 */
    @Excel(name = "物料组")
    private String matklMm;

    /** 标识：获利能力段与 CO 兼容性相关 */
    @Excel(name = "标识：获利能力段与 CO 兼容性相关")
    private String xpaobjnrCoRel;

    /** 销售凭证类型 */
    @Excel(name = "销售凭证类型")
    private String auartPa;

    /** 销售办事处 */
    @Excel(name = "销售办事处")
    private String vkburPa;

    /** 产品组 */
    @Excel(name = "产品组")
    private String spart;

    /** 销售和分销凭证货币 */
    @Excel(name = "销售和分销凭证货币")
    private String waerk;

    /** 净价 */
    @Excel(name = "净价")
    private String netpr;

    /** 决定价格的汇率 */
    @Excel(name = "决定价格的汇率")
    private String kursk;

    /** 不含税单价 */
    @Excel(name = "不含税单价")
    private String znetpr;

    /** 用汇率算的订单金额 */
    @Excel(name = "用汇率算的订单金额")
    private String zhslKursk;

    /** 销售价格含税 */
    @Excel(name = "销售价格含税")
    private String zgtpr;

    /** 以公司代码货币计的金额 */
    @Excel(name = "以公司代码货币计的金额")
    private String hsl;

    /** 收入(含税) */
    @Excel(name = "收入(含税)")
    private String kzwi1;

    /** 总实际成本 */
    @Excel(name = "总实际成本")
    private String vv003;

    /** 实际材料成本 */
    @Excel(name = "实际材料成本")
    private String sj010;

    /** 实际人工成本 */
    @Excel(name = "实际人工成本")
    private String sj020;

    /** 实际折旧摊销 */
    @Excel(name = "实际折旧摊销")
    private String sj030;

    /** 实际水电能源 */
    @Excel(name = "实际水电能源")
    private String sj040;

    /** 实际检验费 */
    @Excel(name = "实际检验费")
    private String sj050;

    /** 实际填料使用费 */
    @Excel(name = "实际填料使用费")
    private String sj060;

    /** 实际物料消耗及其他 */
    @Excel(name = "实际物料消耗及其他")
    private String sj070;

    /** 以全球货币计的实际毛利 */
    @Excel(name = "以全球货币计的实际毛利")
    private String zsjml;

    /** 总订单标准成本 */
    @Excel(name = "总订单标准成本")
    private String vv002;

    /** 标准材料成本 */
    @Excel(name = "标准材料成本")
    private String bz010;

    /** 标准人工成本 */
    @Excel(name = "标准人工成本")
    private String bz020;

    /** 标准折旧摊销 */
    @Excel(name = "标准折旧摊销")
    private String bz030;

    /** 标准水电能源 */
    @Excel(name = "标准水电能源")
    private String bz040;

    /** 标准检验费 */
    @Excel(name = "标准检验费")
    private String bz050;

    /** 标准填料使用费 */
    @Excel(name = "标准填料使用费")
    private String bz060;

    /** 标准物料消耗及其他 */
    @Excel(name = "标准物料消耗及其他")
    private String bz070;

    /** 毛利润 */
    @Excel(name = "毛利润")
    private String zbzml;

    /** 标准毛利率 */
    @Excel(name = "标准毛利率")
    private String zbzmll;

    /** 实际毛利率 */
    @Excel(name = "实际毛利率")
    private String zsjmll;

    /** 客户组 */
    @Excel(name = "客户组")
    private String kdgrpAna;

    /** 销售员 */
    @Excel(name = "销售员")
    private String perveAna;

    /** 销售地区 */
    @Excel(name = "销售地区")
    private String bzirkAna;

    public void setSid(Long sid) 
    {
        this.sid = sid;
    }

    public Long getSid() 
    {
        return sid;
    }
    public void setRbukrs(String rbukrs) 
    {
        this.rbukrs = rbukrs;
    }

    public String getRbukrs() 
    {
        return rbukrs;
    }
    public void setGjahr(String gjahr) 
    {
        this.gjahr = gjahr;
    }

    public String getGjahr() 
    {
        return gjahr;
    }
    public void setFiscyearper(String fiscyearper) 
    {
        this.fiscyearper = fiscyearper;
    }

    public String getFiscyearper() 
    {
        return fiscyearper;
    }
    public void setBlart(String blart) 
    {
        this.blart = blart;
    }

    public String getBlart() 
    {
        return blart;
    }
    public void setKdauf(String kdauf) 
    {
        this.kdauf = kdauf;
    }

    public String getKdauf() 
    {
        return kdauf;
    }
    public void setKdpos(String kdpos) 
    {
        this.kdpos = kdpos;
    }

    public String getKdpos() 
    {
        return kdpos;
    }
    public void setEbeln(String ebeln) 
    {
        this.ebeln = ebeln;
    }

    public String getEbeln() 
    {
        return ebeln;
    }
    public void setEbelp(String ebelp) 
    {
        this.ebelp = ebelp;
    }

    public String getEbelp() 
    {
        return ebelp;
    }
    public void setMsl(String msl)
    {
        this.msl = msl;
    }

    public String getMsl()
    {
        return msl;
    }
    public void setRunit(String runit) 
    {
        this.runit = runit;
    }

    public String getRunit() 
    {
        return runit;
    }
    public void setKunnr(String kunnr) 
    {
        this.kunnr = kunnr;
    }

    public String getKunnr() 
    {
        return kunnr;
    }
    public void setMatnr(String matnr) 
    {
        this.matnr = matnr;
    }

    public String getMatnr() 
    {
        return matnr;
    }
    public void setZzcms(String zzcms) 
    {
        this.zzcms = zzcms;
    }

    public String getZzcms() 
    {
        return zzcms;
    }
    public void setBklas(String bklas) 
    {
        this.bklas = bklas;
    }

    public String getBklas() 
    {
        return bklas;
    }
    public void setRacct(String racct) 
    {
        this.racct = racct;
    }

    public String getRacct() 
    {
        return racct;
    }
    public void setVtweg(String vtweg) 
    {
        this.vtweg = vtweg;
    }

    public String getVtweg() 
    {
        return vtweg;
    }
    public void setMtartPa(String mtartPa) 
    {
        this.mtartPa = mtartPa;
    }

    public String getMtartPa() 
    {
        return mtartPa;
    }
    public void setMatklMm(String matklMm) 
    {
        this.matklMm = matklMm;
    }

    public String getMatklMm() 
    {
        return matklMm;
    }
    public void setXpaobjnrCoRel(String xpaobjnrCoRel) 
    {
        this.xpaobjnrCoRel = xpaobjnrCoRel;
    }

    public String getXpaobjnrCoRel() 
    {
        return xpaobjnrCoRel;
    }
    public void setAuartPa(String auartPa) 
    {
        this.auartPa = auartPa;
    }

    public String getAuartPa() 
    {
        return auartPa;
    }
    public void setVkburPa(String vkburPa) 
    {
        this.vkburPa = vkburPa;
    }

    public String getVkburPa() 
    {
        return vkburPa;
    }
    public void setSpart(String spart) 
    {
        this.spart = spart;
    }

    public String getSpart() 
    {
        return spart;
    }
    public void setWaerk(String waerk) 
    {
        this.waerk = waerk;
    }

    public String getWaerk() 
    {
        return waerk;
    }
    public void setNetpr(String netpr)
    {
        this.netpr = netpr;
    }

    public String getNetpr()
    {
        return netpr;
    }
    public void setKursk(String kursk)
    {
        this.kursk = kursk;
    }

    public String getKursk()
    {
        return kursk;
    }
    public void setZnetpr(String znetpr) 
    {
        this.znetpr = znetpr;
    }

    public String getZnetpr() 
    {
        return znetpr;
    }
    public void setZhslKursk(String zhslKursk) 
    {
        this.zhslKursk = zhslKursk;
    }

    public String getZhslKursk() 
    {
        return zhslKursk;
    }
    public void setZgtpr(String zgtpr) 
    {
        this.zgtpr = zgtpr;
    }

    public String getZgtpr() 
    {
        return zgtpr;
    }
    public void setHsl(String hsl) 
    {
        this.hsl = hsl;
    }

    public String getHsl() 
    {
        return hsl;
    }
    public void setKzwi1(String kzwi1) 
    {
        this.kzwi1 = kzwi1;
    }

    public String getKzwi1() 
    {
        return kzwi1;
    }
    public void setVv003(String vv003) 
    {
        this.vv003 = vv003;
    }

    public String getVv003() 
    {
        return vv003;
    }
    public void setSj010(String sj010) 
    {
        this.sj010 = sj010;
    }

    public String getSj010() 
    {
        return sj010;
    }
    public void setSj020(String sj020) 
    {
        this.sj020 = sj020;
    }

    public String getSj020() 
    {
        return sj020;
    }
    public void setSj030(String sj030) 
    {
        this.sj030 = sj030;
    }

    public String getSj030() 
    {
        return sj030;
    }
    public void setSj040(String sj040) 
    {
        this.sj040 = sj040;
    }

    public String getSj040() 
    {
        return sj040;
    }
    public void setSj050(String sj050) 
    {
        this.sj050 = sj050;
    }

    public String getSj050() 
    {
        return sj050;
    }
    public void setSj060(String sj060) 
    {
        this.sj060 = sj060;
    }

    public String getSj060() 
    {
        return sj060;
    }
    public void setSj070(String sj070) 
    {
        this.sj070 = sj070;
    }

    public String getSj070() 
    {
        return sj070;
    }
    public void setZsjml(String zsjml) 
    {
        this.zsjml = zsjml;
    }

    public String getZsjml() 
    {
        return zsjml;
    }
    public void setVv002(String vv002) 
    {
        this.vv002 = vv002;
    }

    public String getVv002() 
    {
        return vv002;
    }
    public void setBz010(String bz010) 
    {
        this.bz010 = bz010;
    }

    public String getBz010() 
    {
        return bz010;
    }
    public void setBz020(String bz020) 
    {
        this.bz020 = bz020;
    }

    public String getBz020() 
    {
        return bz020;
    }
    public void setBz030(String bz030) 
    {
        this.bz030 = bz030;
    }

    public String getBz030() 
    {
        return bz030;
    }
    public void setBz040(String bz040) 
    {
        this.bz040 = bz040;
    }

    public String getBz040() 
    {
        return bz040;
    }
    public void setBz050(String bz050) 
    {
        this.bz050 = bz050;
    }

    public String getBz050() 
    {
        return bz050;
    }
    public void setBz060(String bz060) 
    {
        this.bz060 = bz060;
    }

    public String getBz060() 
    {
        return bz060;
    }
    public void setBz070(String bz070) 
    {
        this.bz070 = bz070;
    }

    public String getBz070() 
    {
        return bz070;
    }
    public void setZbzml(String zbzml) 
    {
        this.zbzml = zbzml;
    }

    public String getZbzml() 
    {
        return zbzml;
    }
    public void setZbzmll(String zbzmll) 
    {
        this.zbzmll = zbzmll;
    }

    public String getZbzmll() 
    {
        return zbzmll;
    }
    public void setZsjmll(String zsjmll) 
    {
        this.zsjmll = zsjmll;
    }

    public String getZsjmll() 
    {
        return zsjmll;
    }
    public void setKdgrpAna(String kdgrpAna) 
    {
        this.kdgrpAna = kdgrpAna;
    }

    public String getKdgrpAna() 
    {
        return kdgrpAna;
    }
    public void setPerveAna(String perveAna) 
    {
        this.perveAna = perveAna;
    }

    public String getPerveAna() 
    {
        return perveAna;
    }
    public void setBzirkAna(String bzirkAna) 
    {
        this.bzirkAna = bzirkAna;
    }

    public String getBzirkAna() 
    {
        return bzirkAna;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("rbukrs", getRbukrs())
            .append("gjahr", getGjahr())
            .append("fiscyearper", getFiscyearper())
            .append("blart", getBlart())
            .append("kdauf", getKdauf())
            .append("kdpos", getKdpos())
            .append("ebeln", getEbeln())
            .append("ebelp", getEbelp())
            .append("msl", getMsl())
            .append("runit", getRunit())
            .append("kunnr", getKunnr())
            .append("matnr", getMatnr())
            .append("zzcms", getZzcms())
            .append("bklas", getBklas())
            .append("racct", getRacct())
            .append("vtweg", getVtweg())
            .append("mtartPa", getMtartPa())
            .append("matklMm", getMatklMm())
            .append("xpaobjnrCoRel", getXpaobjnrCoRel())
            .append("auartPa", getAuartPa())
            .append("vkburPa", getVkburPa())
            .append("spart", getSpart())
            .append("waerk", getWaerk())
            .append("netpr", getNetpr())
            .append("kursk", getKursk())
            .append("znetpr", getZnetpr())
            .append("zhslKursk", getZhslKursk())
            .append("zgtpr", getZgtpr())
            .append("hsl", getHsl())
            .append("kzwi1", getKzwi1())
            .append("vv003", getVv003())
            .append("sj010", getSj010())
            .append("sj020", getSj020())
            .append("sj030", getSj030())
            .append("sj040", getSj040())
            .append("sj050", getSj050())
            .append("sj060", getSj060())
            .append("sj070", getSj070())
            .append("zsjml", getZsjml())
            .append("vv002", getVv002())
            .append("bz010", getBz010())
            .append("bz020", getBz020())
            .append("bz030", getBz030())
            .append("bz040", getBz040())
            .append("bz050", getBz050())
            .append("bz060", getBz060())
            .append("bz070", getBz070())
            .append("zbzml", getZbzml())
            .append("zbzmll", getZbzmll())
            .append("zsjmll", getZsjmll())
            .append("kdgrpAna", getKdgrpAna())
            .append("perveAna", getPerveAna())
            .append("bzirkAna", getBzirkAna())
            .toString();
    }
}
