package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.SapFunctionalScopeDataMapper;
import com.akesobio.report.sap.domain.SapFunctionalScopeData;
import com.akesobio.report.sap.service.ISapFunctionalScopeDataService;

import javax.annotation.Resource;

/**
 * SAP功能范围输出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SapFunctionalScopeDataServiceImpl implements ISapFunctionalScopeDataService 
{
    @Resource
    private SapFunctionalScopeDataMapper sapFunctionalScopeDataMapper;

    /**
     * 查询SAP功能范围输出
     * 
     * @param id SAP功能范围输出主键
     * @return SAP功能范围输出
     */
    @Override
    public SapFunctionalScopeData selectSapFunctionalScopeDataById(Long id)
    {
        return sapFunctionalScopeDataMapper.selectSapFunctionalScopeDataById(id);
    }

    /**
     * 查询SAP功能范围输出列表
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return SAP功能范围输出
     */
    @Override
    public List<SapFunctionalScopeData> selectSapFunctionalScopeDataList(SapFunctionalScopeData sapFunctionalScopeData)
    {
        return sapFunctionalScopeDataMapper.selectSapFunctionalScopeDataList(sapFunctionalScopeData);
    }

    /**
     * 新增SAP功能范围输出
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return 结果
     */
    @Override
    public int insertSapFunctionalScopeData(SapFunctionalScopeData sapFunctionalScopeData)
    {
        return sapFunctionalScopeDataMapper.insertSapFunctionalScopeData(sapFunctionalScopeData);
    }

    /**
     * 修改SAP功能范围输出
     * 
     * @param sapFunctionalScopeData SAP功能范围输出
     * @return 结果
     */
    @Override
    public int updateSapFunctionalScopeData(SapFunctionalScopeData sapFunctionalScopeData)
    {
        return sapFunctionalScopeDataMapper.updateSapFunctionalScopeData(sapFunctionalScopeData);
    }

    /**
     * 批量删除SAP功能范围输出
     * 
     * @param ids 需要删除的SAP功能范围输出主键
     * @return 结果
     */
    @Override
    public int deleteSapFunctionalScopeDataByIds(Long[] ids)
    {
        return sapFunctionalScopeDataMapper.deleteSapFunctionalScopeDataByIds(ids);
    }

    /**
     * 删除SAP功能范围输出信息
     * 
     * @param id SAP功能范围输出主键
     * @return 结果
     */
    @Override
    public int deleteSapFunctionalScopeDataById(Long id)
    {
        return sapFunctionalScopeDataMapper.deleteSapFunctionalScopeDataById(id);
    }
}
