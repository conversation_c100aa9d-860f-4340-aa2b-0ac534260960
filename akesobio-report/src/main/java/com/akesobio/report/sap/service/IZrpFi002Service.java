package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.ZrpFi002;

/**
 * SAP执行获利能力报告报表Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface IZrpFi002Service 
{
    /**
     * 查询SAP执行获利能力报告报表
     * 
     * @param sid SAP执行获利能力报告报表主键
     * @return SAP执行获利能力报告报表
     */
    public ZrpFi002 selectZrpFi002BySid(Long sid);

    /**
     * 查询SAP执行获利能力报告报表列表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return SAP执行获利能力报告报表集合
     */
    public List<ZrpFi002> selectZrpFi002List(ZrpFi002 zrpFi002);

    /**
     * 新增SAP执行获利能力报告报表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return 结果
     */
    public int insertZrpFi002(ZrpFi002 zrpFi002);

    /**
     * 修改SAP执行获利能力报告报表
     * 
     * @param zrpFi002 SAP执行获利能力报告报表
     * @return 结果
     */
    public int updateZrpFi002(ZrpFi002 zrpFi002);

    /**
     * 批量删除SAP执行获利能力报告报表
     * 
     * @param sids 需要删除的SAP执行获利能力报告报表主键集合
     * @return 结果
     */
    public int deleteZrpFi002BySids(Long[] sids);

    /**
     * 删除SAP执行获利能力报告报表信息
     * 
     * @param sid SAP执行获利能力报告报表主键
     * @return 结果
     */
    public int deleteZrpFi002BySid(Long sid);
}
