package com.akesobio.report.sap.service;

import java.util.List;
import com.akesobio.report.sap.domain.EachExpenseReport;

/**
 * 各费用汇总Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface IEachExpenseReportService 
{
    /**
     * 查询各费用汇总
     * 
     * @param id 各费用汇总主键
     * @return 各费用汇总
     */
    public EachExpenseReport selectEachExpenseReportById(String id);

    /**
     * 查询各费用汇总列表
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 各费用汇总集合
     */
    public List<EachExpenseReport> selectEachExpenseReportList(EachExpenseReport eachExpenseReport);

    /**
     * 新增各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    public int insertEachExpenseReport(EachExpenseReport eachExpenseReport);

    /**
     * 修改各费用汇总
     * 
     * @param eachExpenseReport 各费用汇总
     * @return 结果
     */
    public int updateEachExpenseReport(EachExpenseReport eachExpenseReport);

    /**
     * 批量删除各费用汇总
     * 
     * @param ids 需要删除的各费用汇总主键集合
     * @return 结果
     */
    public int deleteEachExpenseReportByIds(String[] ids);

    /**
     * 删除各费用汇总信息
     * 
     * @param id 各费用汇总主键
     * @return 结果
     */
    public int deleteEachExpenseReportById(String id);
}
