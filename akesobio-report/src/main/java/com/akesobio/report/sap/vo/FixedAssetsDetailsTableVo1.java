package com.akesobio.report.sap.vo;

import com.akesobio.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FixedAssetsDetailsTableVo1 {
    /** 公司代码 */
    private String bukrs;

    /** 资产分类描述 */
    @Excel(name = "资产分类描述")
    private String txk20;

    /** 资产编号 */
    @Excel(name = "资产编号")
    private String anln1;

    /** 资产描述1 */
    @Excel(name = "资产描述1")
    private String txt50;

    /** 资本化日期 */
    @Excel(name = "资本化日期")
    private String aktiv;

    /** 开始折旧时间 */
    @Excel(name = "开始折旧时间")
    private String afabg;

    /** 已折旧期间数 */
    @Excel(name = "已折旧期间数")
    private String zyzjqjs;

    /** 期末原值 */
    @Excel(name = "期末原值")
    private BigDecimal zqmyz;

    /** 期末数累计折旧 */
    @Excel(name = "期末数累计折旧")
    private BigDecimal zqmljzj;

    /** 账面净值 */
    @Excel(name = "账面净值")
    private BigDecimal zjz;

    /** 资产描述2 */
    @Excel(name = "资产描述2")
    private String txa50;

    /** 账面净值占比 */
    @Excel(name = "账面净值占比")
    private String netBookValueRatio;
}
