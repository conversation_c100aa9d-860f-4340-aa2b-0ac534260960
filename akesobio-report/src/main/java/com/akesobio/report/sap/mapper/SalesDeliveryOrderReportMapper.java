package com.akesobio.report.sap.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sap.domain.SalesDeliveryOrderReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * SAP销售交货单查询Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-20
 */
public interface SalesDeliveryOrderReportMapper extends BaseMapper<SalesDeliveryOrderReport>
{
    /**
     * 查询SAP销售交货单查询
     * 
     * @param sid SAP销售交货单查询主键
     * @return SAP销售交货单查询
     */
    public SalesDeliveryOrderReport selectSalesDeliveryOrderReportBySid(Long sid);

    /**
     * 查询SAP销售交货单查询列表
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return SAP销售交货单查询集合
     */
    public List<SalesDeliveryOrderReport> selectSalesDeliveryOrderReportList(SalesDeliveryOrderReport salesDeliveryOrderReport);

    /**
     * 新增SAP销售交货单查询
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return 结果
     */
    public int insertSalesDeliveryOrderReport(SalesDeliveryOrderReport salesDeliveryOrderReport);

    /**
     * 修改SAP销售交货单查询
     * 
     * @param salesDeliveryOrderReport SAP销售交货单查询
     * @return 结果
     */
    public int updateSalesDeliveryOrderReport(SalesDeliveryOrderReport salesDeliveryOrderReport);

    /**
     * 删除SAP销售交货单查询
     * 
     * @param sid SAP销售交货单查询主键
     * @return 结果
     */
    public int deleteSalesDeliveryOrderReportBySid(Long sid);

    /**
     * 批量删除SAP销售交货单查询
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesDeliveryOrderReportBySids(Long[] sids);
}
