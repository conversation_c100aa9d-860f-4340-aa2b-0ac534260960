package com.akesobio.report.sap.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sap.domain.FixedAssetsReport;
import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo1;
import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo2;
import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo3;
import com.akesobio.report.sap.vo.FixedAssetsDetailsTableVo4;

/**
 * 固定资产清单报表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-06
 */
@DataSource(DataSourceType.SAP)
public interface FixedAssetsReportMapper 
{
    /**
     * 查询固定资产清单报表
     * 
     * @param sid 固定资产清单报表主键
     * @return 固定资产清单报表
     */
    public FixedAssetsReport selectFixedAssetsReportBySid(Long sid);

    /**
     * 查询固定资产清单报表列表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 固定资产清单报表集合
     */
    public List<FixedAssetsReport> selectFixedAssetsReportList(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表1
     * @return 明细表1
     */
    public List<FixedAssetsDetailsTableVo1> selectFixedAssetsTable1List(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表2
     * @return 明细表2
     */
    public List<FixedAssetsDetailsTableVo2> selectFixedAssetsTable2List(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表3
     * @return 明细表3
     */
    public List<FixedAssetsDetailsTableVo3> selectFixedAssetsTable3List(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表4
     * @return 明细表4
     */
    public List<FixedAssetsDetailsTableVo4> selectFixedAssetsTable4List(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表5
     * @return 明细表5
     */
    public List<FixedAssetsDetailsTableVo4> selectFixedAssetsTable5List(FixedAssetsReport fixedAssetsReport);

    /**
     * 获取明细表6
     * @return 明细表6
     */
    public List<FixedAssetsDetailsTableVo4> selectFixedAssetsTable6List(FixedAssetsReport fixedAssetsReport);

    /**
     * 新增固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    public int insertFixedAssetsReport(FixedAssetsReport fixedAssetsReport);

    /**
     * 修改固定资产清单报表
     * 
     * @param fixedAssetsReport 固定资产清单报表
     * @return 结果
     */
    public int updateFixedAssetsReport(FixedAssetsReport fixedAssetsReport);

    /**
     * 删除固定资产清单报表
     * 
     * @param sid 固定资产清单报表主键
     * @return 结果
     */
    public int deleteFixedAssetsReportBySid(Long sid);

    /**
     * 批量删除固定资产清单报表
     * 
     * @param sids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFixedAssetsReportBySids(Long[] sids);
}
