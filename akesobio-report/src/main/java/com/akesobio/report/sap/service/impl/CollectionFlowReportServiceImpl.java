package com.akesobio.report.sap.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.sap.mapper.CollectionFlowReportMapper;
import com.akesobio.report.sap.domain.CollectionFlowReport;
import com.akesobio.report.sap.service.ICollectionFlowReportService;

/**
 * 营销财务收款流水报表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
@Service
public class CollectionFlowReportServiceImpl implements ICollectionFlowReportService 
{
    @Autowired
    private CollectionFlowReportMapper collectionFlowReportMapper;

    /**
     * 查询营销财务收款流水报表
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 营销财务收款流水报表
     */
    @Override
    public CollectionFlowReport selectCollectionFlowReportBySid(Long sid)
    {
        return collectionFlowReportMapper.selectCollectionFlowReportBySid(sid);
    }

    /**
     * 查询营销财务收款流水报表列表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 营销财务收款流水报表
     */
    @Override
    public List<CollectionFlowReport> selectCollectionFlowReportList(CollectionFlowReport collectionFlowReport)
    {
        return collectionFlowReportMapper.selectCollectionFlowReportList(collectionFlowReport);
    }

    /**
     * 新增营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    @Override
    public int insertCollectionFlowReport(CollectionFlowReport collectionFlowReport)
    {
        return collectionFlowReportMapper.insertCollectionFlowReport(collectionFlowReport);
    }

    /**
     * 修改营销财务收款流水报表
     * 
     * @param collectionFlowReport 营销财务收款流水报表
     * @return 结果
     */
    @Override
    public int updateCollectionFlowReport(CollectionFlowReport collectionFlowReport)
    {
        return collectionFlowReportMapper.updateCollectionFlowReport(collectionFlowReport);
    }

    /**
     * 批量删除营销财务收款流水报表
     * 
     * @param sids 需要删除的营销财务收款流水报表主键
     * @return 结果
     */
    @Override
    public int deleteCollectionFlowReportBySids(Long[] sids)
    {
        return collectionFlowReportMapper.deleteCollectionFlowReportBySids(sids);
    }

    /**
     * 删除营销财务收款流水报表信息
     * 
     * @param sid 营销财务收款流水报表主键
     * @return 结果
     */
    @Override
    public int deleteCollectionFlowReportBySid(Long sid)
    {
        return collectionFlowReportMapper.deleteCollectionFlowReportBySid(sid);
    }
}
