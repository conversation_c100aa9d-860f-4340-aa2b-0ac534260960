package com.akesobio.report.sap.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.alibaba.fastjson2.annotation.JSONField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * SAP产品税率对象 sap_product_tax_rate_table
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public class SapProductTaxRateTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 物料编号 */
    @Excel(name = "物料编号")
    @JSONField(name="MATNR")
    private String materialNum;

    /** 国家代码 */
    @Excel(name = "国家代码")
    @JSONField(name="ALAND")
    private String countryCode;

    /** 销售组织 */
    @Excel(name = "销售组织")
    @JSONField(name="VKORG")
    private String saleOrganization;

    /** 物料税分类 */
    @Excel(name = "物料税分类")
    @JSONField(name="TAXM1")
    private String taxClassification;

    /** 税分类描述 */
    @Excel(name = "税分类描述")
    @JSONField(name="VTEXT")
    private String taxClassificationText;

    /** 税码 */
    @Excel(name = "税码")
    @JSONField(name="MWSK1")
    private String taxCode;

    /** 税码描述 */
    @Excel(name = "税码描述")
    @JSONField(name="TEXT1")
    private String taxCodeText;

    /** 税率 */
    @Excel(name = "税率")
    @JSONField(name="TAXRATE")
    private String taxRate;

    /** 税率（百分比） */
    @Excel(name = "税率", readConverterExp = "百=分比")
    @JSONField(name="TAXRATE_PCT")
    private String taxRatePercentage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMaterialNum(String materialNum) 
    {
        this.materialNum = materialNum;
    }

    public String getMaterialNum() 
    {
        return materialNum;
    }
    public void setCountryCode(String countryCode) 
    {
        this.countryCode = countryCode;
    }

    public String getCountryCode() 
    {
        return countryCode;
    }
    public void setSaleOrganization(String saleOrganization) 
    {
        this.saleOrganization = saleOrganization;
    }

    public String getSaleOrganization() 
    {
        return saleOrganization;
    }
    public void setTaxClassification(String taxClassification) 
    {
        this.taxClassification = taxClassification;
    }

    public String getTaxClassification() 
    {
        return taxClassification;
    }
    public void setTaxClassificationText(String taxClassificationText) 
    {
        this.taxClassificationText = taxClassificationText;
    }

    public String getTaxClassificationText() 
    {
        return taxClassificationText;
    }
    public void setTaxCode(String taxCode) 
    {
        this.taxCode = taxCode;
    }

    public String getTaxCode() 
    {
        return taxCode;
    }
    public void setTaxCodeText(String taxCodeText) 
    {
        this.taxCodeText = taxCodeText;
    }

    public String getTaxCodeText() 
    {
        return taxCodeText;
    }
    public void setTaxRate(String taxRate) 
    {
        this.taxRate = taxRate;
    }

    public String getTaxRate() 
    {
        return taxRate;
    }
    public void setTaxRatePercentage(String taxRatePercentage) 
    {
        this.taxRatePercentage = taxRatePercentage;
    }

    public String getTaxRatePercentage() 
    {
        return taxRatePercentage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("materialNum", getMaterialNum())
            .append("countryCode", getCountryCode())
            .append("saleOrganization", getSaleOrganization())
            .append("taxClassification", getTaxClassification())
            .append("taxClassificationText", getTaxClassificationText())
            .append("taxCode", getTaxCode())
            .append("taxCodeText", getTaxCodeText())
            .append("taxRate", getTaxRate())
            .append("taxRatePercentage", getTaxRatePercentage())
            .toString();
    }
}
