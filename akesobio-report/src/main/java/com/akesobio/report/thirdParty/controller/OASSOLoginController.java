package com.akesobio.report.thirdParty.controller;

import com.akesobio.common.constant.Constants;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.hr.service.impl.SSOLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;

/**
 * OA 单点登录Controller
 *
 * @name: ThirdPartyLoginController
 * @author: shenglin.qin
 * @date: 2023-07-14 11:57
 **/
@RestController
@Slf4j
public class OASSOLoginController {


    @Autowired
    private SSOLoginService ssoLoginService;

    @GetMapping("/login")
    public AjaxResult ssoLogin(String code, @RequestParam(defaultValue = "index") String redirect, HttpServletResponse response) throws IOException {
        AjaxResult ajax = AjaxResult.success();
        log.info("通过单点登录进入了方法, 重定向地址:{}, code:{}", redirect, code);
        if (!StringUtils.hasText(code)) {
            String url = "https://sso.akesobio.com/sso/oauth2.0/authorize?client_id=1894e0087det411c01213f8f47c18723&redirect_uri=https%3A%2F%2Fdas.akesobio.com%2Fprod-api%2Flogin%3Fredirect%3D" + redirect + "&response_type=code";
            response.sendRedirect(url);
        } else {
            String localIp = InetAddress.getLocalHost().getHostAddress();
            String token = ssoLoginService.login(code);
            log.info("token: {}", token);
            ajax.put(Constants.TOKEN, token);
            response.sendRedirect("https://das.akesobio.com/sso?token=" + token + "&redirect=" + redirect);
        }
        return ajax;
    }
}
