package com.akesobio.report.thirdParty.controller;

import cn.hutool.json.JSONObject;
import com.akesobio.common.annotation.Anonymous;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.domain.entity.EkpSysUser;
import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.exception.ServiceException;
import com.akesobio.framework.web.service.SysLoginService;
import com.akesobio.report.hr.service.impl.SSOLoginService;
import com.akesobio.system.service.IEkpSysUserService;
import com.akesobio.system.service.ISysUserService;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;


@RestController
@Slf4j
public class DingDingController {

    private static final String appKey = "dinguefgodbxtpu3z8zc";
    private static final String appSecret = "YSzn6_sA0Az8Rc7M0QdgDodnP9Vu8tbxZlIuVVCa01FXjHIQ2Fx9cvAqrps61Zjm";

    @Value("${ding-talk.sfe.key}")
    private String sfeAppKey;
    @Value("${ding-talk.sfe.secret}")
    private String sfeAppSecret;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SSOLoginService ssoLoginService;

    @Autowired
    private IEkpSysUserService ekpUserService;
    /**
     *钉钉端获取本系统token
     *
     * @return
     * @throws ApiException
     */
    @PostMapping("/oaToken")
    @Anonymous
    public AjaxResult oaToken(@RequestBody JSONObject jsonObject) throws ApiException {
        String code = jsonObject.get("code").toString();
        System.out.println("code="+code);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(code);
        OapiV2UserGetuserinfoResponse rsp = client.execute(req, selectAccessToken());
        System.out.println("钉钉返回body="+rsp.getBody());
        if (rsp.getErrcode()!=0){
            throw new ServiceException(rsp.getErrmsg());
        }
        String jobNumber=getUserInfo(rsp.getResult().getUserid());
        //String userid=rsp.getResult().getUserid();
        System.out.println("工号="+jobNumber);
        SysUser sysUser=sysUserService.selectUserByUserName(jobNumber);
        System.out.println("工号="+jobNumber+"个人信息为："+sysUser);
        String token=sysLoginService.thirdPartyLogin(sysUser);
        System.out.println("token---"+token);
        return AjaxResult.success(token);
    }

    @PostMapping("/sfeToken")
    @Anonymous
    public AjaxResult sfeToken(@RequestBody JSONObject jsonObject) throws ApiException{
        String code = jsonObject.get("code").toString();
        log.info("code="+code);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(code);
        OapiV2UserGetuserinfoResponse rsp = client.execute(req, selectAccessToken(sfeAppKey,sfeAppSecret));

        log.info("钉钉返回body="+rsp.getBody());
        if (rsp.getErrcode()!=0){
            throw new ServiceException(rsp.getErrmsg());
        }
        String jobNumber=getUserInfo(rsp.getResult().getUserid(),sfeAppKey,sfeAppSecret);
        //String userid=rsp.getResult().getUserid();


        log.info("工号="+jobNumber);
        SysUser sysUser= sysUserService.selectUserByUserName(jobNumber);
        // 系统没有该用户，新建用户
        if (sysUser == null) {
            EkpSysUser ekpUser = ekpUserService.selectUserByLoginName(jobNumber);
            SysUser user = new SysUser();
            user.setUserName(ekpUser.getLoginName());
            user.setNickName(ekpUser.getName());
            user.setPassword(ekpUser.getPassword());
            user.setEmail(ekpUser.getEmail());
            user.setPhonenumber(ekpUser.getPhone());
            user.setSex(ekpUser.getSex());
            user.setEkpId(ekpUser.getId());
            user.setCreateBy("SSO");
            user.setRoles(new ArrayList<>());
            sysUserService.insertUser(user);
            sysUser= sysUserService.selectUserByUserName(jobNumber);
        }

        log.info("工号="+jobNumber+"个人信息为："+sysUser);
        String token=sysLoginService.thirdPartyLogin(sysUser);
        log.info("token---"+token);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token",token);

        return ajax;
    }





    /**
     * 根据 appKey  appSecret 获取钉钉 access_token
     *
     * @return
     * @throws ApiException
     */
    private String selectAccessToken() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(appKey);
        request.setAppsecret(appSecret);
        request.setHttpMethod("GET");
        OapiGettokenResponse response = client.execute(request);
        return response.getAccessToken();
    }
    private String selectAccessToken(String appKey, String appSecret) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(appKey);
        request.setAppsecret(appSecret);
        request.setHttpMethod("GET");
        OapiGettokenResponse response = client.execute(request);
        return response.getAccessToken();
    }

    /**
     * 根据userid 获取钉钉用户信息
     *
     * @return
     * @throws ApiException
     */
    private String  getUserInfo(String userid) throws ApiException{
        log.info("userid="+userid);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userid);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = client.execute(req, selectAccessToken());
        log.info("userid="+userid+"用户信息："+rsp.getBody());
        return rsp.getResult().getJobNumber();
    }

    private String  getUserInfo(String userid,String appKey, String appSecret) throws ApiException{
        log.info("userid="+userid);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userid);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = client.execute(req, selectAccessToken(appKey,appSecret));
        log.info("userid="+userid+"用户信息："+rsp.getBody());
        return rsp.getResult().getJobNumber();
    }


    @PostMapping("/dingTalk")
    @Anonymous
    public AjaxResult dingTalkLogin(@RequestBody com.alibaba.fastjson2.JSONObject bodyJSON) throws ApiException {
        long begin = System.currentTimeMillis();
        Map<String, Object> map = ssoLoginService.loginDingTalk(bodyJSON.getString("authCode"));
        log.info(System.currentTimeMillis() - begin + " ms");
        return AjaxResult.success(map);
    }

}
