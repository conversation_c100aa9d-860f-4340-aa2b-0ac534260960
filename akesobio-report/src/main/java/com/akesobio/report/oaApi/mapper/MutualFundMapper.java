package com.akesobio.report.oaApi.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.oaApi.domain.MutualFund;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * OA互助基金Mapper接口
 */
@DataSource(value = DataSourceType.EKP)
public interface MutualFundMapper {

    /**
     * 查询OA互助基金信息
     */
    public List<MutualFund> selectMutualFundList(MutualFund mutualFund);

}
