package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 财务指标基础数据对象 financial_indicators_basic_data
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public class FinancialIndicatorsBasicData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 能力指标
     */
    @Excel(name = "能力指标")
    private String capabilityIndicators;

    /**
     * 项目
     */
    @Excel(name = "项目")
    private String project;

    /**
     * 年月
     */
    @Excel(name = "年月")
    private String yearMonth;

    /**
     * 金额
     */
    @Excel(name = "金额")
    private BigDecimal amount;

    /**
     * 删除状态
     */
    private Integer deleteStatus;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCapabilityIndicators(String capabilityIndicators) {
        this.capabilityIndicators = capabilityIndicators;
    }

    public String getCapabilityIndicators() {
        return capabilityIndicators;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getProject() {
        return project;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("capabilityIndicators", getCapabilityIndicators())
                .append("project", getProject())
                .append("yearMonth", getYearMonth())
                .append("amount", getAmount())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
