package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.ClinicalProjectCostsMapper;
import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import com.akesobio.report.basicData.service.IClinicalProjectCostsService;

/**
 * 临床项目成本各数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
public class ClinicalProjectCostsServiceImpl implements IClinicalProjectCostsService {

    private  static final Logger log= LoggerFactory.getLogger(ClinicalProjectCostsServiceImpl.class);
    @Autowired
    private ClinicalProjectCostsMapper clinicalProjectCostsMapper;

    /**
     * 查询临床项目成本各数据
     *
     * @param id 临床项目成本各数据主键
     * @return 临床项目成本各数据
     */
    @Override
    public ClinicalProjectCosts selectClinicalProjectCostsById(Long id) {
        return clinicalProjectCostsMapper.selectClinicalProjectCostsById(id);
    }

    /**
     * 查询临床项目成本各数据列表
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 临床项目成本各数据
     */
    @Override
    public List<ClinicalProjectCosts> selectClinicalProjectCostsList(ClinicalProjectCosts clinicalProjectCosts) {
        return clinicalProjectCostsMapper.selectClinicalProjectCostsList(clinicalProjectCosts);
    }

    /**
     * 新增临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    @Override
    public int insertClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts) {
        return clinicalProjectCostsMapper.insertClinicalProjectCosts(clinicalProjectCosts);
    }

    /**
     * 修改临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    @Override
    public int updateClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts) {
        return clinicalProjectCostsMapper.updateClinicalProjectCosts(clinicalProjectCosts);
    }

    /**
     * 批量删除临床项目成本各数据
     *
     * @param ids 需要删除的临床项目成本各数据主键
     * @return 结果
     */
    @Override
    public int deleteClinicalProjectCostsByIds(Long[] ids) {
        return clinicalProjectCostsMapper.deleteClinicalProjectCostsByIds(ids);
    }

    /**
     * 删除临床项目成本各数据信息
     *
     * @param id 临床项目成本各数据主键
     * @return 结果
     */
    @Override
    public int deleteClinicalProjectCostsById(Long id) {
        return clinicalProjectCostsMapper.deleteClinicalProjectCostsById(id);
    }

    /**
     * 导入临床项目成本各数据
     *
     * @param list 临床项目成本各数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importClinicalProjectCosts(List<ClinicalProjectCosts> list, Boolean isUpdateSupport) {

        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ClinicalProjectCosts clinicalProjectCosts : list)
        {
            try {
                clinicalProjectCostsMapper.insertClinicalProjectCosts(clinicalProjectCosts);
                successNum++;
                successMsg.append("<br/>" + successNum + "、药品管线 " + clinicalProjectCosts.getDrugPipeline() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、药品管线 " + clinicalProjectCosts.getDrugPipeline() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
