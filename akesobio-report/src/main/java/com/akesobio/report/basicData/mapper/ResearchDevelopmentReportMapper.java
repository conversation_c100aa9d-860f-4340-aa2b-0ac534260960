package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;

/**
 * 研发报告数据Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface ResearchDevelopmentReportMapper {
    /**
     * 查询研发报告数据
     *
     * @param id 研发报告数据主键
     * @return 研发报告数据
     */
    public ResearchDevelopmentReport selectResearchDevelopmentReportById(Long id);

    /**
     * 查询研发报告数据列表
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 研发报告数据集合
     */
    public List<ResearchDevelopmentReport> selectResearchDevelopmentReportList(ResearchDevelopmentReport researchDevelopmentReport);

    /**
     * 新增研发报告数据
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 结果
     */
    public int insertResearchDevelopmentReport(ResearchDevelopmentReport researchDevelopmentReport);

    /**
     * 修改研发报告数据
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 结果
     */
    public int updateResearchDevelopmentReport(ResearchDevelopmentReport researchDevelopmentReport);

    /**
     * 删除研发报告数据
     *
     * @param id 研发报告数据主键
     * @return 结果
     */
    public int deleteResearchDevelopmentReportById(Long id);

    /**
     * 批量删除研发报告数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResearchDevelopmentReportByIds(Long[] ids);
}
