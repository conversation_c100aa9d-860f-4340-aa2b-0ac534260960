package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.RepaymentDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.WithdrawalDetails;
import com.akesobio.report.basicData.service.IWithdrawalDetailsService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 提款明细Controller
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/basicData/withdrawalDetails")
public class WithdrawalDetailsController extends BaseController {
    @Autowired
    private IWithdrawalDetailsService withdrawalDetailsService;

    /**
     * 查询提款明细列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(WithdrawalDetails withdrawalDetails) {
        startPage();
        List<WithdrawalDetails> list = withdrawalDetailsService.selectWithdrawalDetailsList(withdrawalDetails);
        return getDataTable(list);
    }

    /**
     * 导出提款明细列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:export')")
    @Log(title = "提款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WithdrawalDetails withdrawalDetails) {
        List<WithdrawalDetails> list = withdrawalDetailsService.selectWithdrawalDetailsList(withdrawalDetails);
        ExcelUtil<WithdrawalDetails> util = new ExcelUtil<WithdrawalDetails>(WithdrawalDetails.class);
        util.exportExcel(response, list, "提款明细数据");
    }

    /**
     * 获取提款明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(withdrawalDetailsService.selectWithdrawalDetailsById(id));
    }

    /**
     * 新增提款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:add')")
    @Log(title = "提款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WithdrawalDetails withdrawalDetails) {
        return toAjax(withdrawalDetailsService.insertWithdrawalDetails(withdrawalDetails));
    }

    /**
     * 修改提款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:edit')")
    @Log(title = "提款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WithdrawalDetails withdrawalDetails) {
        return toAjax(withdrawalDetailsService.updateWithdrawalDetails(withdrawalDetails));
    }

    /**
     * 删除提款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:remove')")
    @Log(title = "提款明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(withdrawalDetailsService.deleteWithdrawalDetailsByIds(ids));
    }

    /**
     * 导入提款明细数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:import')")
    @Log(title = "提款明细数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<WithdrawalDetails> util = new ExcelUtil<WithdrawalDetails>(WithdrawalDetails.class);
        List<WithdrawalDetails> list = util.importExcel(file.getInputStream());
        String message = withdrawalDetailsService.importWithdrawalDetails(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:withdrawalDetails:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WithdrawalDetails> util = new ExcelUtil<WithdrawalDetails>(WithdrawalDetails.class);
        util.importTemplateExcel(response, "提款明细数据");
    }
}
