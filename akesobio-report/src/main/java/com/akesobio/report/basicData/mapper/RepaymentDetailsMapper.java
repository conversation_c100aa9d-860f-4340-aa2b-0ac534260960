package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.RepaymentDetails;

/**
 * 还款明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface RepaymentDetailsMapper {
    /**
     * 查询还款明细
     *
     * @param id 还款明细主键
     * @return 还款明细
     */
    public RepaymentDetails selectRepaymentDetailsById(Integer id);

    /**
     * 查询还款明细列表
     *
     * @param repaymentDetails 还款明细
     * @return 还款明细集合
     */
    public List<RepaymentDetails> selectRepaymentDetailsList(RepaymentDetails repaymentDetails);

    /**
     * 新增还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    public int insertRepaymentDetails(RepaymentDetails repaymentDetails);

    /**
     * 修改还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    public int updateRepaymentDetails(RepaymentDetails repaymentDetails);

    /**
     * 删除还款明细
     *
     * @param id 还款明细主键
     * @return 结果
     */
    public int deleteRepaymentDetailsById(Integer id);

    /**
     * 批量删除还款明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRepaymentDetailsByIds(Integer[] ids);
}
