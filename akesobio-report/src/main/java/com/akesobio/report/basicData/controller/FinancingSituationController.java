package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.WithdrawalDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.FinancingSituation;
import com.akesobio.report.basicData.service.IFinancingSituationService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 融资情况Controller
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@RestController
@RequestMapping("/basicData/financingSituation")
public class FinancingSituationController extends BaseController {
    @Autowired
    private IFinancingSituationService financingSituationService;

    /**
     * 查询融资情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancingSituation financingSituation) {
        startPage();
        List<FinancingSituation> list = financingSituationService.selectFinancingSituationList(financingSituation);
        return getDataTable(list);
    }

    /**
     * 导出融资情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:export')")
    @Log(title = "融资情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancingSituation financingSituation) {
        List<FinancingSituation> list = financingSituationService.selectFinancingSituationList(financingSituation);
        ExcelUtil<FinancingSituation> util = new ExcelUtil<FinancingSituation>(FinancingSituation.class);
        util.exportExcel(response, list, "融资情况数据");
    }

    /**
     * 获取融资情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(financingSituationService.selectFinancingSituationById(id));
    }

    /**
     * 新增融资情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:add')")
    @Log(title = "融资情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancingSituation financingSituation) {
        return toAjax(financingSituationService.insertFinancingSituation(financingSituation));
    }

    /**
     * 修改融资情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:edit')")
    @Log(title = "融资情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancingSituation financingSituation) {
        return toAjax(financingSituationService.updateFinancingSituation(financingSituation));
    }

    /**
     * 删除融资情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:remove')")
    @Log(title = "融资情况", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(financingSituationService.deleteFinancingSituationByIds(ids));
    }

    /**
     * 导入融资情况数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:import')")
    @Log(title = "融资情况数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<FinancingSituation> util = new ExcelUtil<FinancingSituation>(FinancingSituation.class);
        List<FinancingSituation> list = util.importExcel(file.getInputStream());
        String message = financingSituationService.importFinancingSituation(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:financingSituation:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FinancingSituation> util = new ExcelUtil<FinancingSituation>(FinancingSituation.class);
        util.importTemplateExcel(response, "融资情况数据");
    }
}
