package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.RepaymentDetails;
import com.akesobio.report.basicData.domain.RfExpenses;

/**
 * 还款明细Service接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface IRepaymentDetailsService {
    /**
     * 查询还款明细
     *
     * @param id 还款明细主键
     * @return 还款明细
     */
    public RepaymentDetails selectRepaymentDetailsById(Integer id);

    /**
     * 查询还款明细列表
     *
     * @param repaymentDetails 还款明细
     * @return 还款明细集合
     */
    public List<RepaymentDetails> selectRepaymentDetailsList(RepaymentDetails repaymentDetails);

    /**
     * 新增还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    public int insertRepaymentDetails(RepaymentDetails repaymentDetails);

    /**
     * 修改还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    public int updateRepaymentDetails(RepaymentDetails repaymentDetails);

    /**
     * 批量删除还款明细
     *
     * @param ids 需要删除的还款明细主键集合
     * @return 结果
     */
    public int deleteRepaymentDetailsByIds(Integer[] ids);

    /**
     * 删除还款明细信息
     *
     * @param id 还款明细主键
     * @return 结果
     */
    public int deleteRepaymentDetailsById(Integer id);


    /**
     * 导入还款明细数据
     *
     * @param list 还款明细数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importRepaymentDetails(List<RepaymentDetails> list, Boolean isUpdateSupport);
}
