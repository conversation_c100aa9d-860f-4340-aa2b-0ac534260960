package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 还款明细对象 repayment_details
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public class RepaymentDetails extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 公司简称
     */
    @Excel(name = "公司简称")
    private String companyAbbreviation;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String companyCode;

    /**
     * 业务类型
     */
    @Excel(name = "业务类型")
    private String businessType;

    /**
     * 启用年份
     */
    @Excel(name = "启用年份")
    private String yearTime;

    /**
     * 贷款银行
     */
    @Excel(name = "贷款银行")
    private String lendingBank;

    /**
     * 还款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "还款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date repaymentDate;

    /**
     * 还款额
     */
    @Excel(name = "还款额")
    private BigDecimal repaymentAmount;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String notes;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setYearTime(String yearTime) {
        this.yearTime = yearTime;
    }

    public String getYearTime() {
        return yearTime;
    }

    public void setLendingBank(String lendingBank) {
        this.lendingBank = lendingBank;
    }

    public String getLendingBank() {
        return lendingBank;
    }

    public void setRepaymentDate(Date repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    public Date getRepaymentDate() {
        return repaymentDate;
    }

    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getNotes() {
        return notes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("companyAbbreviation", getCompanyAbbreviation())
                .append("companyCode", getCompanyCode())
                .append("businessType", getBusinessType())
                .append("yearTime", getYearTime())
                .append("lendingBank", getLendingBank())
                .append("repaymentDate", getRepaymentDate())
                .append("repaymentAmount", getRepaymentAmount())
                .append("notes", getNotes())
                .toString();
    }
}
