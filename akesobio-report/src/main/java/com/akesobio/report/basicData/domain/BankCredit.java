package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 银行授信批复情况对象 bank_credit
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
public class BankCredit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Integer id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司简称 */
    @Excel(name = "公司简称")
    private String companyAbbreviation;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String companyCode;

    /** 年份 */
    @Excel(name = "年份")
    private String yearTime;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 授信银行 */
    @Excel(name = "授信银行")
    private String creditGrantingBank;

    /** 授信额度 */
    @Excel(name = "授信额度")
    private BigDecimal creditLimit;

    /** 授信敞口 */
    @Excel(name = "授信敞口")
    private BigDecimal creditExposure;

    /** 贷款定价 */
    @Excel(name = "贷款定价")
    private String loanPricing;

    /** 资本金要求 */
    @Excel(name = "资本金要求")
    private String capitalRequirements;

    /** 贷款期限 */
    @Excel(name = "贷款期限")
    private String loanTerm;

    /** 还本宽限期 */
    @Excel(name = "还本宽限期")
    private String repaymentGracePeriod;

    /** 提款期 */
    @Excel(name = "提款期")
    private String availablePeriod;

    /** 担保方式 */
    @Excel(name = "担保方式")
    private String guaranteeMethod;

    /** 资金提款限制 */
    @Excel(name = "资金提款限制")
    private String fundingWithdrawalRestrictions;

    /** 备注 */
    @Excel(name = "备注")
    private String notes;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setCompanyAbbreviation(String companyAbbreviation) 
    {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() 
    {
        return companyAbbreviation;
    }
    public void setCompanyCode(String companyCode) 
    {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() 
    {
        return companyCode;
    }
    public void setYearTime(String yearTime) 
    {
        this.yearTime = yearTime;
    }

    public String getYearTime() 
    {
        return yearTime;
    }
    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }
    public void setCreditGrantingBank(String creditGrantingBank) 
    {
        this.creditGrantingBank = creditGrantingBank;
    }

    public String getCreditGrantingBank() 
    {
        return creditGrantingBank;
    }
    public void setCreditLimit(BigDecimal creditLimit) 
    {
        this.creditLimit = creditLimit;
    }

    public BigDecimal getCreditLimit() 
    {
        return creditLimit;
    }
    public void setCreditExposure(BigDecimal creditExposure) 
    {
        this.creditExposure = creditExposure;
    }

    public BigDecimal getCreditExposure() 
    {
        return creditExposure;
    }
    public void setLoanPricing(String loanPricing) 
    {
        this.loanPricing = loanPricing;
    }

    public String getLoanPricing() 
    {
        return loanPricing;
    }
    public void setCapitalRequirements(String capitalRequirements) 
    {
        this.capitalRequirements = capitalRequirements;
    }

    public String getCapitalRequirements() 
    {
        return capitalRequirements;
    }
    public void setLoanTerm(String loanTerm) 
    {
        this.loanTerm = loanTerm;
    }

    public String getLoanTerm() 
    {
        return loanTerm;
    }
    public void setRepaymentGracePeriod(String repaymentGracePeriod) 
    {
        this.repaymentGracePeriod = repaymentGracePeriod;
    }

    public String getRepaymentGracePeriod() 
    {
        return repaymentGracePeriod;
    }
    public void setAvailablePeriod(String availablePeriod) 
    {
        this.availablePeriod = availablePeriod;
    }

    public String getAvailablePeriod() 
    {
        return availablePeriod;
    }
    public void setGuaranteeMethod(String guaranteeMethod) 
    {
        this.guaranteeMethod = guaranteeMethod;
    }

    public String getGuaranteeMethod() 
    {
        return guaranteeMethod;
    }
    public void setFundingWithdrawalRestrictions(String fundingWithdrawalRestrictions) 
    {
        this.fundingWithdrawalRestrictions = fundingWithdrawalRestrictions;
    }

    public String getFundingWithdrawalRestrictions() 
    {
        return fundingWithdrawalRestrictions;
    }
    public void setNotes(String notes) 
    {
        this.notes = notes;
    }

    public String getNotes() 
    {
        return notes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyName", getCompanyName())
            .append("companyAbbreviation", getCompanyAbbreviation())
            .append("companyCode", getCompanyCode())
            .append("yearTime", getYearTime())
            .append("businessType", getBusinessType())
            .append("creditGrantingBank", getCreditGrantingBank())
            .append("creditLimit", getCreditLimit())
            .append("creditExposure", getCreditExposure())
            .append("loanPricing", getLoanPricing())
            .append("capitalRequirements", getCapitalRequirements())
            .append("loanTerm", getLoanTerm())
            .append("repaymentGracePeriod", getRepaymentGracePeriod())
            .append("availablePeriod", getAvailablePeriod())
            .append("guaranteeMethod", getGuaranteeMethod())
            .append("fundingWithdrawalRestrictions", getFundingWithdrawalRestrictions())
            .append("notes", getNotes())
            .toString();
    }
}
