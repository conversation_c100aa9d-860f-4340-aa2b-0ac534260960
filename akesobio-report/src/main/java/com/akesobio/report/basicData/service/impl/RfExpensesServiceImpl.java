package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.RfExpensesMapper;
import com.akesobio.report.basicData.domain.RfExpenses;
import com.akesobio.report.basicData.service.IRfExpensesService;

/**
 * RF费用支出报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-12
 */
@Service
public class RfExpensesServiceImpl implements IRfExpensesService {

    private static final Logger log= LoggerFactory.getLogger(RfExpensesServiceImpl.class);

    @Autowired
    private RfExpensesMapper rfExpensesMapper;

    /**
     * 查询RF费用支出报告
     *
     * @param id RF费用支出报告主键
     * @return RF费用支出报告
     */
    @Override
    public RfExpenses selectRfExpensesById(Long id) {
        return rfExpensesMapper.selectRfExpensesById(id);
    }

    /**
     * 查询RF费用支出报告列表
     *
     * @param rfExpenses RF费用支出报告
     * @return RF费用支出报告
     */
    @Override
    public List<RfExpenses> selectRfExpensesList(RfExpenses rfExpenses) {
        return rfExpensesMapper.selectRfExpensesList(rfExpenses);
    }

    /**
     * 新增RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    @Override
    public int insertRfExpenses(RfExpenses rfExpenses) {
        return rfExpensesMapper.insertRfExpenses(rfExpenses);
    }

    /**
     * 修改RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    @Override
    public int updateRfExpenses(RfExpenses rfExpenses) {
        return rfExpensesMapper.updateRfExpenses(rfExpenses);
    }

    /**
     * 批量删除RF费用支出报告
     *
     * @param ids 需要删除的RF费用支出报告主键
     * @return 结果
     */
    @Override
    public int deleteRfExpensesByIds(Long[] ids) {
        return rfExpensesMapper.deleteRfExpensesByIds(ids);
    }

    /**
     * 删除RF费用支出报告信息
     *
     * @param id RF费用支出报告主键
     * @return 结果
     */
    @Override
    public int deleteRfExpensesById(Long id) {
        return rfExpensesMapper.deleteRfExpensesById(id);
    }

    /**
     * 导入RF费用支出报告数据
     *
     * @param list RF费用支出报告数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importRfExpenses(List<RfExpenses> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (RfExpenses rfExpenses : list)
        {
            try {
                rfExpensesMapper.insertRfExpenses(rfExpenses);
                successNum++;
                successMsg.append("<br/>" + successNum + "、适应症项目号 " + rfExpenses.getIndicationProjectNo() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、适应症项目号 " + rfExpenses.getIndicationProjectNo()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
