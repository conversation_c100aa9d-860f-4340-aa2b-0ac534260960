package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import com.akesobio.report.basicData.domain.ProjectCostSummary;

/**
 * 临床项目成本各数据Service接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface IClinicalProjectCostsService {
    /**
     * 查询临床项目成本各数据
     *
     * @param id 临床项目成本各数据主键
     * @return 临床项目成本各数据
     */
    public ClinicalProjectCosts selectClinicalProjectCostsById(Long id);

    /**
     * 查询临床项目成本各数据列表
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 临床项目成本各数据集合
     */
    public List<ClinicalProjectCosts> selectClinicalProjectCostsList(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 新增临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    public int insertClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 修改临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    public int updateClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 批量删除临床项目成本各数据
     *
     * @param ids 需要删除的临床项目成本各数据主键集合
     * @return 结果
     */
    public int deleteClinicalProjectCostsByIds(Long[] ids);

    /**
     * 删除临床项目成本各数据信息
     *
     * @param id 临床项目成本各数据主键
     * @return 结果
     */
    public int deleteClinicalProjectCostsById(Long id);

    /**
     * 导入临床项目成本各数据
     *
     * @param list 临床项目成本各数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importClinicalProjectCosts(List<ClinicalProjectCosts> list, Boolean isUpdateSupport);
}
