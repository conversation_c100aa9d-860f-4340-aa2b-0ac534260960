package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.ProjectCostSummary;

/**
 * 项目费用归集汇总Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ProjectCostSummaryMapper {
    /**
     * 查询项目费用归集汇总
     *
     * @param id 项目费用归集汇总主键
     * @return 项目费用归集汇总
     */
    public ProjectCostSummary selectProjectCostSummaryById(Long id);

    /**
     * 查询项目费用归集汇总列表
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 项目费用归集汇总集合
     */
    public List<ProjectCostSummary> selectProjectCostSummaryList(ProjectCostSummary projectCostSummary);

    /**
     * 新增项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    public int insertProjectCostSummary(ProjectCostSummary projectCostSummary);

    /**
     * 修改项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    public int updateProjectCostSummary(ProjectCostSummary projectCostSummary);

    /**
     * 删除项目费用归集汇总
     *
     * @param id 项目费用归集汇总主键
     * @return 结果
     */
    public int deleteProjectCostSummaryById(Long id);

    /**
     * 批量删除项目费用归集汇总
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectCostSummaryByIds(Long[] ids);
}
