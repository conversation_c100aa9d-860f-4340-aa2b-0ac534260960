package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.ProjectProgressSummaryMapper;
import com.akesobio.report.basicData.domain.ProjectProgressSummary;
import com.akesobio.report.basicData.service.IProjectProgressSummaryService;

/**
 * 项目进度汇总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
@Service
public class ProjectProgressSummaryServiceImpl implements IProjectProgressSummaryService {

    private static final Logger log= LoggerFactory.getLogger(ProjectProgressSummaryServiceImpl.class);


    @Autowired
    private ProjectProgressSummaryMapper projectProgressSummaryMapper;

    /**
     * 查询项目进度汇总
     * 
     * @param id 项目进度汇总主键
     * @return 项目进度汇总
     */
    @Override
    public ProjectProgressSummary selectProjectProgressSummaryById(Long id)
    {
        return projectProgressSummaryMapper.selectProjectProgressSummaryById(id);
    }

    /**
     * 查询项目进度汇总列表
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 项目进度汇总
     */
    @Override
    public List<ProjectProgressSummary> selectProjectProgressSummaryList(ProjectProgressSummary projectProgressSummary)
    {
        return projectProgressSummaryMapper.selectProjectProgressSummaryList(projectProgressSummary);
    }

    /**
     * 新增项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    @Override
    public int insertProjectProgressSummary(ProjectProgressSummary projectProgressSummary)
    {
        return projectProgressSummaryMapper.insertProjectProgressSummary(projectProgressSummary);
    }

    /**
     * 修改项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    @Override
    public int updateProjectProgressSummary(ProjectProgressSummary projectProgressSummary)
    {
        return projectProgressSummaryMapper.updateProjectProgressSummary(projectProgressSummary);
    }

    /**
     * 批量删除项目进度汇总
     * 
     * @param ids 需要删除的项目进度汇总主键
     * @return 结果
     */
    @Override
    public int deleteProjectProgressSummaryByIds(Long[] ids)
    {
        return projectProgressSummaryMapper.deleteProjectProgressSummaryByIds(ids);
    }

    /**
     * 删除项目进度汇总信息
     * 
     * @param id 项目进度汇总主键
     * @return 结果
     */
    @Override
    public int deleteProjectProgressSummaryById(Long id)
    {
        return projectProgressSummaryMapper.deleteProjectProgressSummaryById(id);
    }


    /**
     * 导入项目进度汇总数据
     *
     * @param list 项目进度汇总列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProjectProgressSummary(List<ProjectProgressSummary> list, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProjectProgressSummary projectProgressSummary : list)
        {
            try {
                projectProgressSummaryMapper.insertProjectProgressSummary(projectProgressSummary);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目 " + projectProgressSummary.getProject() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目 " +  projectProgressSummary.getProject()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
