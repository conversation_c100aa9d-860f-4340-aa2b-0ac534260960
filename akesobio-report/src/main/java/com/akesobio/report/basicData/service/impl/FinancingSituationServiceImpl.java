package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.WithdrawalDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.FinancingSituationMapper;
import com.akesobio.report.basicData.domain.FinancingSituation;
import com.akesobio.report.basicData.service.IFinancingSituationService;

/**
 * 融资情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Service
public class FinancingSituationServiceImpl implements IFinancingSituationService {

    private static final Logger log = LoggerFactory.getLogger(FinancingSituationServiceImpl.class);

    @Autowired
    private FinancingSituationMapper financingSituationMapper;

    /**
     * 查询融资情况
     *
     * @param id 融资情况主键
     * @return 融资情况
     */
    @Override
    public FinancingSituation selectFinancingSituationById(Integer id) {
        return financingSituationMapper.selectFinancingSituationById(id);
    }

    /**
     * 查询融资情况列表
     *
     * @param financingSituation 融资情况
     * @return 融资情况
     */
    @Override
    public List<FinancingSituation> selectFinancingSituationList(FinancingSituation financingSituation) {
        return financingSituationMapper.selectFinancingSituationList(financingSituation);
    }

    /**
     * 新增融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    @Override
    public int insertFinancingSituation(FinancingSituation financingSituation) {
        return financingSituationMapper.insertFinancingSituation(financingSituation);
    }

    /**
     * 修改融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    @Override
    public int updateFinancingSituation(FinancingSituation financingSituation) {
        return financingSituationMapper.updateFinancingSituation(financingSituation);
    }

    /**
     * 批量删除融资情况
     *
     * @param ids 需要删除的融资情况主键
     * @return 结果
     */
    @Override
    public int deleteFinancingSituationByIds(Integer[] ids) {
        return financingSituationMapper.deleteFinancingSituationByIds(ids);
    }

    /**
     * 删除融资情况信息
     *
     * @param id 融资情况主键
     * @return 结果
     */
    @Override
    public int deleteFinancingSituationById(Integer id) {
        return financingSituationMapper.deleteFinancingSituationById(id);
    }

    /**
     * 导入融资情况明细数据
     *
     * @param list            融资情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importFinancingSituation(List<FinancingSituation> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (FinancingSituation financingSituation : list) {
            try {
                financingSituationMapper.insertFinancingSituation(financingSituation);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司名称 " + financingSituation.getCompanyName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司名称 " + financingSituation.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
