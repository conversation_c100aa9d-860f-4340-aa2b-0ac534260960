package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.RfExpenses;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.RepaymentDetails;
import com.akesobio.report.basicData.service.IRepaymentDetailsService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 还款明细Controller
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/basicData/repaymentDetails")
public class RepaymentDetailsController extends BaseController {
    @Autowired
    private IRepaymentDetailsService repaymentDetailsService;

    /**
     * 查询还款明细列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(RepaymentDetails repaymentDetails) {
        startPage();
        List<RepaymentDetails> list = repaymentDetailsService.selectRepaymentDetailsList(repaymentDetails);
        return getDataTable(list);
    }

    /**
     * 导出还款明细列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:export')")
    @Log(title = "还款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RepaymentDetails repaymentDetails) {
        List<RepaymentDetails> list = repaymentDetailsService.selectRepaymentDetailsList(repaymentDetails);
        ExcelUtil<RepaymentDetails> util = new ExcelUtil<RepaymentDetails>(RepaymentDetails.class);
        util.exportExcel(response, list, "还款明细数据");
    }

    /**
     * 获取还款明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(repaymentDetailsService.selectRepaymentDetailsById(id));
    }

    /**
     * 新增还款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:add')")
    @Log(title = "还款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RepaymentDetails repaymentDetails) {
        return toAjax(repaymentDetailsService.insertRepaymentDetails(repaymentDetails));
    }

    /**
     * 修改还款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:edit')")
    @Log(title = "还款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RepaymentDetails repaymentDetails) {
        return toAjax(repaymentDetailsService.updateRepaymentDetails(repaymentDetails));
    }

    /**
     * 删除还款明细
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:remove')")
    @Log(title = "还款明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(repaymentDetailsService.deleteRepaymentDetailsByIds(ids));
    }

    /**
     * 导入还款明细数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:import')")
    @Log(title = "还款明细数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RepaymentDetails> util = new ExcelUtil<RepaymentDetails>(RepaymentDetails.class);
        List<RepaymentDetails> list = util.importExcel(file.getInputStream());
        String message = repaymentDetailsService.importRepaymentDetails(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:repaymentDetails:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RepaymentDetails> util = new ExcelUtil<RepaymentDetails>(RepaymentDetails.class);
        util.importTemplateExcel(response, "还款明细数据");
    }
}
