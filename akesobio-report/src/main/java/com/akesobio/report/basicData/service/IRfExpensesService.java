package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;
import com.akesobio.report.basicData.domain.RfExpenses;

/**
 * RF费用支出报告Service接口
 *
 * <AUTHOR>
 * @date 2024-01-12
 */
public interface IRfExpensesService {
    /**
     * 查询RF费用支出报告
     *
     * @param id RF费用支出报告主键
     * @return RF费用支出报告
     */
    public RfExpenses selectRfExpensesById(Long id);

    /**
     * 查询RF费用支出报告列表
     *
     * @param rfExpenses RF费用支出报告
     * @return RF费用支出报告集合
     */
    public List<RfExpenses> selectRfExpensesList(RfExpenses rfExpenses);

    /**
     * 新增RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    public int insertRfExpenses(RfExpenses rfExpenses);

    /**
     * 修改RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    public int updateRfExpenses(RfExpenses rfExpenses);

    /**
     * 批量删除RF费用支出报告
     *
     * @param ids 需要删除的RF费用支出报告主键集合
     * @return 结果
     */
    public int deleteRfExpensesByIds(Long[] ids);

    /**
     * 删除RF费用支出报告信息
     *
     * @param id RF费用支出报告主键
     * @return 结果
     */
    public int deleteRfExpensesById(Long id);

    /**
     * 导入RF费用支出报告数据
     *
     * @param list RF费用支出报告数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importRfExpenses(List<RfExpenses> list, Boolean isUpdateSupport);
}
