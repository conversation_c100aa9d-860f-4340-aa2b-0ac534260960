package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.ResearchDevelopmentReportMapper;
import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;
import com.akesobio.report.basicData.service.IResearchDevelopmentReportService;

/**
 * 研发报告数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
public class ResearchDevelopmentReportServiceImpl implements IResearchDevelopmentReportService {

    private static final Logger log= LoggerFactory.getLogger(ResearchDevelopmentReportServiceImpl.class);

    @Autowired
    private ResearchDevelopmentReportMapper researchDevelopmentReportMapper;

    /**
     * 查询研发报告数据
     *
     * @param id 研发报告数据主键
     * @return 研发报告数据
     */
    @Override
    public ResearchDevelopmentReport selectResearchDevelopmentReportById(Long id) {
        return researchDevelopmentReportMapper.selectResearchDevelopmentReportById(id);
    }

    /**
     * 查询研发报告数据列表
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 研发报告数据
     */
    @Override
    public List<ResearchDevelopmentReport> selectResearchDevelopmentReportList(ResearchDevelopmentReport researchDevelopmentReport) {
        return researchDevelopmentReportMapper.selectResearchDevelopmentReportList(researchDevelopmentReport);
    }

    /**
     * 新增研发报告数据
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 结果
     */
    @Override
    public int insertResearchDevelopmentReport(ResearchDevelopmentReport researchDevelopmentReport) {
        return researchDevelopmentReportMapper.insertResearchDevelopmentReport(researchDevelopmentReport);
    }

    /**
     * 修改研发报告数据
     *
     * @param researchDevelopmentReport 研发报告数据
     * @return 结果
     */
    @Override
    public int updateResearchDevelopmentReport(ResearchDevelopmentReport researchDevelopmentReport) {
        return researchDevelopmentReportMapper.updateResearchDevelopmentReport(researchDevelopmentReport);
    }

    /**
     * 批量删除研发报告数据
     *
     * @param ids 需要删除的研发报告数据主键
     * @return 结果
     */
    @Override
    public int deleteResearchDevelopmentReportByIds(Long[] ids) {
        return researchDevelopmentReportMapper.deleteResearchDevelopmentReportByIds(ids);
    }

    /**
     * 删除研发报告数据信息
     *
     * @param id 研发报告数据主键
     * @return 结果
     */
    @Override
    public int deleteResearchDevelopmentReportById(Long id) {
        return researchDevelopmentReportMapper.deleteResearchDevelopmentReportById(id);
    }
    /**
     * 导入研发报告数据数据
     *
     * @param list 研发报告数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importResearchDevelopmentReport(List<ResearchDevelopmentReport> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ResearchDevelopmentReport researchDevelopmentReport : list)
        {
            try {
                researchDevelopmentReportMapper.insertResearchDevelopmentReport(researchDevelopmentReport);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目号 " + researchDevelopmentReport.getProjectNo() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目号 " + researchDevelopmentReport.getProjectNo()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
