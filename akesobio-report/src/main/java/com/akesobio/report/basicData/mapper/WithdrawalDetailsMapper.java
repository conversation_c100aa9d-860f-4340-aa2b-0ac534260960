package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.WithdrawalDetails;

/**
 * 提款明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface WithdrawalDetailsMapper {
    /**
     * 查询提款明细
     *
     * @param id 提款明细主键
     * @return 提款明细
     */
    public WithdrawalDetails selectWithdrawalDetailsById(Integer id);

    /**
     * 查询提款明细列表
     *
     * @param withdrawalDetails 提款明细
     * @return 提款明细集合
     */
    public List<WithdrawalDetails> selectWithdrawalDetailsList(WithdrawalDetails withdrawalDetails);

    /**
     * 新增提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    public int insertWithdrawalDetails(WithdrawalDetails withdrawalDetails);

    /**
     * 修改提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    public int updateWithdrawalDetails(WithdrawalDetails withdrawalDetails);

    /**
     * 删除提款明细
     *
     * @param id 提款明细主键
     * @return 结果
     */
    public int deleteWithdrawalDetailsById(Integer id);

    /**
     * 批量删除提款明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWithdrawalDetailsByIds(Integer[] ids);
}
