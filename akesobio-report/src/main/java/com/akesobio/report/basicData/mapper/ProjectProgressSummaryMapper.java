package com.akesobio.report.basicData.mapper;

import java.util.List;
import com.akesobio.report.basicData.domain.ProjectProgressSummary;

/**
 * 项目进度汇总Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ProjectProgressSummaryMapper {
    /**
     * 查询项目进度汇总
     * 
     * @param id 项目进度汇总主键
     * @return 项目进度汇总
     */
    public ProjectProgressSummary selectProjectProgressSummaryById(Long id);

    /**
     * 查询项目进度汇总列表
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 项目进度汇总集合
     */
    public List<ProjectProgressSummary> selectProjectProgressSummaryList(ProjectProgressSummary projectProgressSummary);

    /**
     * 新增项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    public int insertProjectProgressSummary(ProjectProgressSummary projectProgressSummary);

    /**
     * 修改项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    public int updateProjectProgressSummary(ProjectProgressSummary projectProgressSummary);

    /**
     * 删除项目进度汇总
     * 
     * @param id 项目进度汇总主键
     * @return 结果
     */
    public int deleteProjectProgressSummaryById(Long id);

    /**
     * 批量删除项目进度汇总
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectProgressSummaryByIds(Long[] ids);
}
