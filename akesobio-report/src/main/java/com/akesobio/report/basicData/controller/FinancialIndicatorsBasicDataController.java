package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.BankCredit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.FinancialIndicatorsBasicData;
import com.akesobio.report.basicData.service.IFinancialIndicatorsBasicDataService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 财务指标基础数据Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@RestController
@RequestMapping("/basicData/financialIndicatorsBasicData")
public class FinancialIndicatorsBasicDataController extends BaseController {
    @Autowired
    private IFinancialIndicatorsBasicDataService financialIndicatorsBasicDataService;

    /**
     * 查询财务指标基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialIndicatorsBasicData financialIndicatorsBasicData) {
        startPage();
        List<FinancialIndicatorsBasicData> list = financialIndicatorsBasicDataService.selectFinancialIndicatorsBasicDataList(financialIndicatorsBasicData);
        return getDataTable(list);
    }

    /**
     * 导出财务指标基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:export')")
    @Log(title = "财务指标基础数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialIndicatorsBasicData financialIndicatorsBasicData) {
        List<FinancialIndicatorsBasicData> list = financialIndicatorsBasicDataService.selectFinancialIndicatorsBasicDataList(financialIndicatorsBasicData);
        ExcelUtil<FinancialIndicatorsBasicData> util = new ExcelUtil<FinancialIndicatorsBasicData>(FinancialIndicatorsBasicData.class);
        util.exportExcel(response, list, "财务指标基础数据数据");
    }

    /**
     * 获取财务指标基础数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(financialIndicatorsBasicDataService.selectFinancialIndicatorsBasicDataById(id));
    }

    /**
     * 新增财务指标基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:add')")
    @Log(title = "财务指标基础数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialIndicatorsBasicData financialIndicatorsBasicData) {
        return toAjax(financialIndicatorsBasicDataService.insertFinancialIndicatorsBasicData(financialIndicatorsBasicData));
    }

    /**
     * 修改财务指标基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:edit')")
    @Log(title = "财务指标基础数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancialIndicatorsBasicData financialIndicatorsBasicData) {
        return toAjax(financialIndicatorsBasicDataService.updateFinancialIndicatorsBasicData(financialIndicatorsBasicData));
    }

    /**
     * 删除财务指标基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:remove')")
    @Log(title = "财务指标基础数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(financialIndicatorsBasicDataService.deleteFinancialIndicatorsBasicDataByIds(ids));
    }

    /**
     * 导入财务指标基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:import')")
    @Log(title = "财务指标基础数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<FinancialIndicatorsBasicData> util = new ExcelUtil<FinancialIndicatorsBasicData>(FinancialIndicatorsBasicData.class);
        List<FinancialIndicatorsBasicData> list = util.importExcel(file.getInputStream());
        String message = financialIndicatorsBasicDataService.importFinancialIndicatorsBasicData(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialIndicatorsBasicData:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FinancialIndicatorsBasicData> util = new ExcelUtil<FinancialIndicatorsBasicData>(FinancialIndicatorsBasicData.class);
        util.importTemplateExcel(response, "财务指标基础数据");
    }
}
