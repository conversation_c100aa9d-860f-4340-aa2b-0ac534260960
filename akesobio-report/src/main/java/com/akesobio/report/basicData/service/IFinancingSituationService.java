package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.FinancingSituation;
import com.akesobio.report.basicData.domain.WithdrawalDetails;

/**
 * 融资情况Service接口
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface IFinancingSituationService {
    /**
     * 查询融资情况
     *
     * @param id 融资情况主键
     * @return 融资情况
     */
    public FinancingSituation selectFinancingSituationById(Integer id);

    /**
     * 查询融资情况列表
     *
     * @param financingSituation 融资情况
     * @return 融资情况集合
     */
    public List<FinancingSituation> selectFinancingSituationList(FinancingSituation financingSituation);

    /**
     * 新增融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    public int insertFinancingSituation(FinancingSituation financingSituation);

    /**
     * 修改融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    public int updateFinancingSituation(FinancingSituation financingSituation);

    /**
     * 批量删除融资情况
     *
     * @param ids 需要删除的融资情况主键集合
     * @return 结果
     */
    public int deleteFinancingSituationByIds(Integer[] ids);

    /**
     * 删除融资情况信息
     *
     * @param id 融资情况主键
     * @return 结果
     */
    public int deleteFinancingSituationById(Integer id);

    /**
     * 导入融资情况明细数据
     *
     * @param list 融资情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importFinancingSituation(List<FinancingSituation> list, Boolean isUpdateSupport);
}
