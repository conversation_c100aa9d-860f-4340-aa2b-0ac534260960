package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.model.LoginUser;
import com.akesobio.common.utils.ServletUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;
import com.akesobio.report.basicData.service.IResearchDevelopmentReportService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 研发报告数据Controller
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/basicData/researchDevelopmentReport")
public class ResearchDevelopmentReportController extends BaseController {
    @Autowired
    private IResearchDevelopmentReportService researchDevelopmentReportService;

    /**
     * 查询研发报告数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(ResearchDevelopmentReport researchDevelopmentReport) {
        startPage();
        List<ResearchDevelopmentReport> list = researchDevelopmentReportService.selectResearchDevelopmentReportList(researchDevelopmentReport);
        return getDataTable(list);
    }

    /**
     * 导出研发报告数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:export')")
    @Log(title = "研发报告数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ResearchDevelopmentReport researchDevelopmentReport) {
        List<ResearchDevelopmentReport> list = researchDevelopmentReportService.selectResearchDevelopmentReportList(researchDevelopmentReport);
        ExcelUtil<ResearchDevelopmentReport> util = new ExcelUtil<ResearchDevelopmentReport>(ResearchDevelopmentReport.class);
        util.exportExcel(response, list, "研发报告数据数据");
    }

    /**
     * 获取研发报告数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(researchDevelopmentReportService.selectResearchDevelopmentReportById(id));
    }

    /**
     * 新增研发报告数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:add')")
    @Log(title = "研发报告数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ResearchDevelopmentReport researchDevelopmentReport) {
        return toAjax(researchDevelopmentReportService.insertResearchDevelopmentReport(researchDevelopmentReport));
    }

    /**
     * 修改研发报告数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:edit')")
    @Log(title = "研发报告数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ResearchDevelopmentReport researchDevelopmentReport) {
        return toAjax(researchDevelopmentReportService.updateResearchDevelopmentReport(researchDevelopmentReport));
    }

    /**
     * 删除研发报告数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:remove')")
    @Log(title = "研发报告数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(researchDevelopmentReportService.deleteResearchDevelopmentReportByIds(ids));
    }

    /**
     * 导入研发报告数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:import')")
    @Log(title = "研发报告数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ResearchDevelopmentReport> util = new ExcelUtil<ResearchDevelopmentReport>(ResearchDevelopmentReport.class);
        List<ResearchDevelopmentReport> list = util.importExcel(file.getInputStream());
        String message = researchDevelopmentReportService.importResearchDevelopmentReport(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:researchDevelopmentReport:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ResearchDevelopmentReport> util = new ExcelUtil<ResearchDevelopmentReport>(ResearchDevelopmentReport.class);
        util.importTemplateExcel(response, "研发报告数据");
    }
}
