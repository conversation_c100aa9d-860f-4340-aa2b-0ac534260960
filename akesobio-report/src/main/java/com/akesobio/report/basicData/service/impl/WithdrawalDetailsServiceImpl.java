package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.RepaymentDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.WithdrawalDetailsMapper;
import com.akesobio.report.basicData.domain.WithdrawalDetails;
import com.akesobio.report.basicData.service.IWithdrawalDetailsService;

/**
 * 提款明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class WithdrawalDetailsServiceImpl implements IWithdrawalDetailsService {
    private  static  final Logger log = LoggerFactory.getLogger(WithdrawalDetailsServiceImpl.class);

    @Autowired
    private WithdrawalDetailsMapper withdrawalDetailsMapper;

    /**
     * 查询提款明细
     *
     * @param id 提款明细主键
     * @return 提款明细
     */
    @Override
    public WithdrawalDetails selectWithdrawalDetailsById(Integer id) {
        return withdrawalDetailsMapper.selectWithdrawalDetailsById(id);
    }

    /**
     * 查询提款明细列表
     *
     * @param withdrawalDetails 提款明细
     * @return 提款明细
     */
    @Override
    public List<WithdrawalDetails> selectWithdrawalDetailsList(WithdrawalDetails withdrawalDetails) {
        return withdrawalDetailsMapper.selectWithdrawalDetailsList(withdrawalDetails);
    }

    /**
     * 新增提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    @Override
    public int insertWithdrawalDetails(WithdrawalDetails withdrawalDetails) {
        return withdrawalDetailsMapper.insertWithdrawalDetails(withdrawalDetails);
    }

    /**
     * 修改提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    @Override
    public int updateWithdrawalDetails(WithdrawalDetails withdrawalDetails) {
        return withdrawalDetailsMapper.updateWithdrawalDetails(withdrawalDetails);
    }

    /**
     * 批量删除提款明细
     *
     * @param ids 需要删除的提款明细主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawalDetailsByIds(Integer[] ids) {
        return withdrawalDetailsMapper.deleteWithdrawalDetailsByIds(ids);
    }

    /**
     * 删除提款明细信息
     *
     * @param id 提款明细主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawalDetailsById(Integer id) {
        return withdrawalDetailsMapper.deleteWithdrawalDetailsById(id);
    }

    /**
     * 导入提款明细数据
     *
     * @param list 提款明细数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importWithdrawalDetails(List<WithdrawalDetails> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (WithdrawalDetails withdrawalDetails : list)
        {
            try {
                withdrawalDetailsMapper.insertWithdrawalDetails(withdrawalDetails);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司名称 " + withdrawalDetails.getCompanyName() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司名称 " + withdrawalDetails.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
