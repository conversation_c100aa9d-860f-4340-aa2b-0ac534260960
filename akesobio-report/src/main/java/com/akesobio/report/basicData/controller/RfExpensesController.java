package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.ResearchDevelopmentReport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.RfExpenses;
import com.akesobio.report.basicData.service.IRfExpensesService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * RF费用支出报告Controller
 *
 * <AUTHOR>
 * @date 2024-01-12
 */
@RestController
@RequestMapping("/basicData/rfExpenses")
public class RfExpensesController extends BaseController {
    @Autowired
    private IRfExpensesService rfExpensesService;

    /**
     * 查询RF费用支出报告列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:list')")
    @GetMapping("/list")
    public TableDataInfo list(RfExpenses rfExpenses) {
        startPage();
        List<RfExpenses> list = rfExpensesService.selectRfExpensesList(rfExpenses);
        return getDataTable(list);
    }

    /**
     * 导出RF费用支出报告列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:export')")
    @Log(title = "RF费用支出报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RfExpenses rfExpenses) {
        List<RfExpenses> list = rfExpensesService.selectRfExpensesList(rfExpenses);
        ExcelUtil<RfExpenses> util = new ExcelUtil<RfExpenses>(RfExpenses.class);
        util.exportExcel(response, list, "RF费用支出报告数据");
    }

    /**
     * 获取RF费用支出报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(rfExpensesService.selectRfExpensesById(id));
    }

    /**
     * 新增RF费用支出报告
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:add')")
    @Log(title = "RF费用支出报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RfExpenses rfExpenses) {
        return toAjax(rfExpensesService.insertRfExpenses(rfExpenses));
    }

    /**
     * 修改RF费用支出报告
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:edit')")
    @Log(title = "RF费用支出报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RfExpenses rfExpenses) {
        return toAjax(rfExpensesService.updateRfExpenses(rfExpenses));
    }

    /**
     * 删除RF费用支出报告
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:remove')")
    @Log(title = "RF费用支出报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(rfExpensesService.deleteRfExpensesByIds(ids));
    }

    /**
     * 导入RF费用支出报告数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:import')")
    @Log(title = "RF费用支出报告数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<RfExpenses> util = new ExcelUtil<RfExpenses>(RfExpenses.class);
        List<RfExpenses> list = util.importExcel(file.getInputStream());
        String message = rfExpensesService.importRfExpenses(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:rfExpenses:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RfExpenses> util = new ExcelUtil<RfExpenses>(RfExpenses.class);
        util.importTemplateExcel(response, "RF费用支出报告数据");
    }
}
