package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.LoanUsage;
import com.akesobio.report.basicData.service.ILoanUsageService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 贷款使用情况Controller
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@RestController
@RequestMapping("/basicData/loanUsage")
public class LoanUsageController extends BaseController {
    @Autowired
    private ILoanUsageService loanUsageService;

    /**
     * 查询贷款使用情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanUsage loanUsage) {
        startPage();
        List<LoanUsage> list = loanUsageService.selectLoanUsageList(loanUsage);
        return getDataTable(list);
    }

    /**
     * 导出贷款使用情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:export')")
    @Log(title = "贷款使用情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanUsage loanUsage) {
        List<LoanUsage> list = loanUsageService.selectLoanUsageList(loanUsage);
        ExcelUtil<LoanUsage> util = new ExcelUtil<LoanUsage>(LoanUsage.class);
        util.exportExcel(response, list, "贷款使用情况数据");
    }

    /**
     * 获取贷款使用情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(loanUsageService.selectLoanUsageById(id));
    }

    /**
     * 新增贷款使用情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:add')")
    @Log(title = "贷款使用情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanUsage loanUsage) {
        return toAjax(loanUsageService.insertLoanUsage(loanUsage));
    }

    /**
     * 修改贷款使用情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:edit')")
    @Log(title = "贷款使用情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanUsage loanUsage) {
        return toAjax(loanUsageService.updateLoanUsage(loanUsage));
    }

    /**
     * 删除贷款使用情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:remove')")
    @Log(title = "贷款使用情况", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(loanUsageService.deleteLoanUsageByIds(ids));
    }

    /**
     * 导入贷款使用情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:import')")
    @Log(title = "贷款使用情况数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception{
        ExcelUtil<LoanUsage> util = new ExcelUtil<LoanUsage>(LoanUsage.class);
        List<LoanUsage> list = util.importExcel(file.getInputStream());
        String message = loanUsageService.importLoanUsage(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:loanUsage:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<LoanUsage> util = new ExcelUtil<LoanUsage>(LoanUsage.class);
        util.importTemplateExcel(response, "贷款使用情况数据");
    }
}
