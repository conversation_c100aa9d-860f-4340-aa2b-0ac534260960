package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.ProcurementList;

/**
 * 采购大数据Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface ProcurementListMapper {
    /**
     * 查询采购大数据
     *
     * @param id 采购大数据主键
     * @return 采购大数据
     */
    public ProcurementList selectProcurementListById(Long id);

    /**
     * 查询采购大数据列表
     *
     * @param procurementList 采购大数据
     * @return 采购大数据集合
     */
    public List<ProcurementList> selectProcurementListList(ProcurementList procurementList);

    /**
     * 新增采购大数据
     *
     * @param procurementList 采购大数据
     * @return 结果
     */
    public int insertProcurementList(ProcurementList procurementList);

    /**
     * 修改采购大数据
     *
     * @param procurementList 采购大数据
     * @return 结果
     */
    public int updateProcurementList(ProcurementList procurementList);

    /**
     * 删除采购大数据
     *
     * @param id 采购大数据主键
     * @return 结果
     */
    public int deleteProcurementListById(Long id);

    /**
     * 批量删除采购大数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcurementListByIds(Long[] ids);
}
