package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.RfExpenses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.GroupFinancialSituationMapper;
import com.akesobio.report.basicData.domain.GroupFinancialSituation;
import com.akesobio.report.basicData.service.IGroupFinancialSituationService;

/**
 * 集团理财情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@Service
public class GroupFinancialSituationServiceImpl implements IGroupFinancialSituationService {

    private static final Logger log = LoggerFactory.getLogger(GroupFinancialSituationServiceImpl.class);

    @Autowired
    private GroupFinancialSituationMapper groupFinancialSituationMapper;

    /**
     * 查询集团理财情况
     *
     * @param id 集团理财情况主键
     * @return 集团理财情况
     */
    @Override
    public GroupFinancialSituation selectGroupFinancialSituationById(Integer id) {
        return groupFinancialSituationMapper.selectGroupFinancialSituationById(id);
    }

    /**
     * 查询集团理财情况列表
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 集团理财情况
     */
    @Override
    public List<GroupFinancialSituation> selectGroupFinancialSituationList(GroupFinancialSituation groupFinancialSituation) {
        return groupFinancialSituationMapper.selectGroupFinancialSituationList(groupFinancialSituation);
    }

    /**
     * 新增集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    @Override
    public int insertGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation) {
        return groupFinancialSituationMapper.insertGroupFinancialSituation(groupFinancialSituation);
    }

    /**
     * 修改集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    @Override
    public int updateGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation) {
        return groupFinancialSituationMapper.updateGroupFinancialSituation(groupFinancialSituation);
    }

    /**
     * 批量删除集团理财情况
     *
     * @param ids 需要删除的集团理财情况主键
     * @return 结果
     */
    @Override
    public int deleteGroupFinancialSituationByIds(Integer[] ids) {
        return groupFinancialSituationMapper.deleteGroupFinancialSituationByIds(ids);
    }

    /**
     * 删除集团理财情况信息
     *
     * @param id 集团理财情况主键
     * @return 结果
     */
    @Override
    public int deleteGroupFinancialSituationById(Integer id) {
        return groupFinancialSituationMapper.deleteGroupFinancialSituationById(id);
    }

    /**
     * 导入集团理财情况数据
     *
     * @param list            集团理财情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importGroupFinancialSituation(List<GroupFinancialSituation> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (GroupFinancialSituation groupFinancialSituation : list) {
            try {
                groupFinancialSituationMapper.insertGroupFinancialSituation(groupFinancialSituation);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司名称  " + groupFinancialSituation.getCompanyName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司名称  " + groupFinancialSituation.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
