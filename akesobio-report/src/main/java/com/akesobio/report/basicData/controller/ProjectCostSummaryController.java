package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.core.domain.model.LoginUser;
import com.akesobio.common.utils.ServletUtils;
import com.akesobio.common.utils.poi.ExcelMergeUtil;
import com.akesobio.framework.web.service.TokenService;
import com.akesobio.report.basicData.service.IProjectCostSummaryService;
import com.akesobio.report.hr.domain.common.ImportArchiveData;
import com.akesobio.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 项目费用归集汇总Controller
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RestController
@RequestMapping("/basicData/projectCostSummary")
public class ProjectCostSummaryController extends BaseController {
    @Autowired
    private IProjectCostSummaryService projectCostSummaryService;

    @Autowired
    private TokenService tokenService;



    /**
     * 查询项目费用归集汇总列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectCostSummary projectCostSummary) {
        startPage();
        List<ProjectCostSummary> list = projectCostSummaryService.selectProjectCostSummaryList(projectCostSummary);
        return getDataTable(list);
    }

    /**
     * 导出项目费用归集汇总列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:export')")
    @Log(title = "项目费用归集汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectCostSummary projectCostSummary) {
        List<ProjectCostSummary> list = projectCostSummaryService.selectProjectCostSummaryList(projectCostSummary);
        ExcelUtil<ProjectCostSummary> util = new ExcelUtil<ProjectCostSummary>(ProjectCostSummary.class);
        util.exportExcel(response, list, "项目费用归集汇总数据");
    }

    /**
     * 获取项目费用归集汇总详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(projectCostSummaryService.selectProjectCostSummaryById(id));
    }

    /**
     * 新增项目费用归集汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:add')")
    @Log(title = "项目费用归集汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectCostSummary projectCostSummary) {
        return toAjax(projectCostSummaryService.insertProjectCostSummary(projectCostSummary));
    }

    /**
     * 修改项目费用归集汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:edit')")
    @Log(title = "项目费用归集汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectCostSummary projectCostSummary) {
        return toAjax(projectCostSummaryService.updateProjectCostSummary(projectCostSummary));
    }

    /**
     * 删除项目费用归集汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:remove')")
    @Log(title = "项目费用归集汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(projectCostSummaryService.deleteProjectCostSummaryByIds(ids));
    }

    /**
     * 导入项目费用归集汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:import')")
    @Log(title = "项目费用归集汇总", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProjectCostSummary> util = new ExcelUtil<ProjectCostSummary>(ProjectCostSummary.class);
        List<ProjectCostSummary> list = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = projectCostSummaryService.importProjectCostSummary(list, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectCostSummary:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectCostSummary> util = new ExcelUtil<ProjectCostSummary>(ProjectCostSummary.class);
        util.importTemplateExcel(response, "项目费用归集汇总数据");
    }
}
