package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.model.LoginUser;
import com.akesobio.common.utils.ServletUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.ProcurementList;
import com.akesobio.report.basicData.service.IProcurementListService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购大数据Controller
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/basicData/procurementList")
public class ProcurementListController extends BaseController {
    @Autowired
    private IProcurementListService procurementListService;

    /**
     * 查询采购大数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcurementList procurementList) {
        startPage();
        List<ProcurementList> list = procurementListService.selectProcurementListList(procurementList);
        return getDataTable(list);
    }

    /**
     * 导出采购大数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:export')")
    @Log(title = "采购大数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementList procurementList) {
        List<ProcurementList> list = procurementListService.selectProcurementListList(procurementList);
        ExcelUtil<ProcurementList> util = new ExcelUtil<ProcurementList>(ProcurementList.class);
        util.exportExcel(response, list, "采购大数据数据");
    }

    /**
     * 获取采购大数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(procurementListService.selectProcurementListById(id));
    }

    /**
     * 新增采购大数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:add')")
    @Log(title = "采购大数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementList procurementList) {
        return toAjax(procurementListService.insertProcurementList(procurementList));
    }

    /**
     * 修改采购大数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:edit')")
    @Log(title = "采购大数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementList procurementList) {
        return toAjax(procurementListService.updateProcurementList(procurementList));
    }

    /**
     * 删除采购大数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:remove')")
    @Log(title = "采购大数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(procurementListService.deleteProcurementListByIds(ids));
    }
    /**
     * 导入采购大数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:import')")
    @Log(title = "采购大表数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProcurementList> util = new ExcelUtil<ProcurementList>(ProcurementList.class);
        List<ProcurementList> list = util.importExcel(file.getInputStream());
        String message = procurementListService.importProcurementList(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:procurementList:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProcurementList> util = new ExcelUtil<ProcurementList>(ProcurementList.class);
        util.importTemplateExcel(response, "采购大表数据");
    }
}
