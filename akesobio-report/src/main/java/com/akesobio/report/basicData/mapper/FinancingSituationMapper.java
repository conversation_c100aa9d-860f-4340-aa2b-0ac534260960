package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.FinancingSituation;

/**
 * 融资情况Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface FinancingSituationMapper {
    /**
     * 查询融资情况
     *
     * @param id 融资情况主键
     * @return 融资情况
     */
    public FinancingSituation selectFinancingSituationById(Integer id);

    /**
     * 查询融资情况列表
     *
     * @param financingSituation 融资情况
     * @return 融资情况集合
     */
    public List<FinancingSituation> selectFinancingSituationList(FinancingSituation financingSituation);

    /**
     * 新增融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    public int insertFinancingSituation(FinancingSituation financingSituation);

    /**
     * 修改融资情况
     *
     * @param financingSituation 融资情况
     * @return 结果
     */
    public int updateFinancingSituation(FinancingSituation financingSituation);

    /**
     * 删除融资情况
     *
     * @param id 融资情况主键
     * @return 结果
     */
    public int deleteFinancingSituationById(Integer id);

    /**
     * 批量删除融资情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancingSituationByIds(Integer[] ids);
}
