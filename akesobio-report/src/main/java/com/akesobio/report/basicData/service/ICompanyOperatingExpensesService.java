package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;

/**
 * 公司运营费用数据Service接口
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface ICompanyOperatingExpensesService {
    /**
     * 查询公司运营费用数据
     *
     * @param id 公司运营费用数据主键
     * @return 公司运营费用数据
     */
    public CompanyOperatingExpenses selectCompanyOperatingExpensesById(Long id);

    /**
     * 查询公司运营费用数据列表
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 公司运营费用数据集合
     */
    public List<CompanyOperatingExpenses> selectCompanyOperatingExpensesList(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 新增公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    public int insertCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 修改公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    public int updateCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 批量删除公司运营费用数据
     *
     * @param ids 需要删除的公司运营费用数据主键集合
     * @return 结果
     */
    public int deleteCompanyOperatingExpensesByIds(Long[] ids);

    /**
     * 删除公司运营费用数据信息
     *
     * @param id 公司运营费用数据主键
     * @return 结果
     */
    public int deleteCompanyOperatingExpensesById(Long id);

    /**
     * 导入公司运营费用数据
     *
     * @param list 公司运营费用数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importCompanyOperatingExpenses(List<CompanyOperatingExpenses> list, Boolean isUpdateSupport);
}
