package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.FinancialIndicatorsBasicData;

/**
 * 财务指标基础数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface FinancialIndicatorsBasicDataMapper {
    /**
     * 查询财务指标基础数据
     *
     * @param id 财务指标基础数据主键
     * @return 财务指标基础数据
     */
    public FinancialIndicatorsBasicData selectFinancialIndicatorsBasicDataById(Integer id);

    /**
     * 查询财务指标基础数据列表
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 财务指标基础数据集合
     */
    public List<FinancialIndicatorsBasicData> selectFinancialIndicatorsBasicDataList(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 新增财务指标基础数据
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 结果
     */
    public int insertFinancialIndicatorsBasicData(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 修改财务指标基础数据
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 结果
     */
    public int updateFinancialIndicatorsBasicData(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 删除财务指标基础数据
     *
     * @param id 财务指标基础数据主键
     * @return 结果
     */
    public int deleteFinancialIndicatorsBasicDataById(Integer id);

    /**
     * 批量删除财务指标基础数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialIndicatorsBasicDataByIds(Integer[] ids);
}
