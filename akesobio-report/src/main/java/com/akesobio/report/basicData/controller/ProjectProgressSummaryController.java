package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.model.LoginUser;
import com.akesobio.common.utils.ServletUtils;
import com.akesobio.framework.web.service.TokenService;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.ProjectProgressSummary;
import com.akesobio.report.basicData.service.IProjectProgressSummaryService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 项目进度汇总Controller
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
@RestController
@RequestMapping("/basicData/projectProgressSummary")
public class ProjectProgressSummaryController extends BaseController {
    @Autowired
    private IProjectProgressSummaryService projectProgressSummaryService;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询项目进度汇总列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectProgressSummary projectProgressSummary)
    {
        startPage();
        List<ProjectProgressSummary> list = projectProgressSummaryService.selectProjectProgressSummaryList(projectProgressSummary);
        return getDataTable(list);
    }

    /**
     * 导出项目进度汇总列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:export')")
    @Log(title = "项目进度汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectProgressSummary projectProgressSummary)
    {
        List<ProjectProgressSummary> list = projectProgressSummaryService.selectProjectProgressSummaryList(projectProgressSummary);
        ExcelUtil<ProjectProgressSummary> util = new ExcelUtil<ProjectProgressSummary>(ProjectProgressSummary.class);
        util.exportExcel(response, list, "项目进度汇总数据");
    }

    /**
     * 获取项目进度汇总详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectProgressSummaryService.selectProjectProgressSummaryById(id));
    }

    /**
     * 新增项目进度汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:add')")
    @Log(title = "项目进度汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectProgressSummary projectProgressSummary)
    {
        return toAjax(projectProgressSummaryService.insertProjectProgressSummary(projectProgressSummary));
    }

    /**
     * 修改项目进度汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:edit')")
    @Log(title = "项目进度汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectProgressSummary projectProgressSummary)
    {
        return toAjax(projectProgressSummaryService.updateProjectProgressSummary(projectProgressSummary));
    }

    /**
     * 删除项目进度汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:remove')")
    @Log(title = "项目进度汇总", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectProgressSummaryService.deleteProjectProgressSummaryByIds(ids));
    }

    /**
     * 导入项目进度汇总
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:import')")
    @Log(title = "导入项目进度汇总", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ProjectProgressSummary> util = new ExcelUtil<ProjectProgressSummary>(ProjectProgressSummary.class);
        List<ProjectProgressSummary> list = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = projectProgressSummaryService.importProjectProgressSummary(list, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 项目进度汇总
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:projectProgressSummary:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectProgressSummary> util = new ExcelUtil<ProjectProgressSummary>(ProjectProgressSummary.class);
        util.importTemplateExcel(response, "项目进度汇总数据");
    }
}
