package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.LoanUsage;
import com.akesobio.report.basicData.domain.RfExpenses;

/**
 * 贷款使用情况Service接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface ILoanUsageService {
    /**
     * 查询贷款使用情况
     *
     * @param id 贷款使用情况主键
     * @return 贷款使用情况
     */
    public LoanUsage selectLoanUsageById(Integer id);

    /**
     * 查询贷款使用情况列表
     *
     * @param loanUsage 贷款使用情况
     * @return 贷款使用情况集合
     */
    public List<LoanUsage> selectLoanUsageList(LoanUsage loanUsage);

    /**
     * 新增贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    public int insertLoanUsage(LoanUsage loanUsage);

    /**
     * 修改贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    public int updateLoanUsage(LoanUsage loanUsage);

    /**
     * 批量删除贷款使用情况
     *
     * @param ids 需要删除的贷款使用情况主键集合
     * @return 结果
     */
    public int deleteLoanUsageByIds(Integer[] ids);

    /**
     * 删除贷款使用情况信息
     *
     * @param id 贷款使用情况主键
     * @return 结果
     */
    public int deleteLoanUsageById(Integer id);

    /**
     * 导入贷款使用情况数据
     *
     * @param list 贷款使用情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importLoanUsage(List<LoanUsage> list, Boolean isUpdateSupport);
}
