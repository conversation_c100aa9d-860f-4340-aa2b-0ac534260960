package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.report.basicData.domain.ProjectCostSummary;

/**
 * 项目费用归集汇总Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface IProjectCostSummaryService {
    /**
     * 查询项目费用归集汇总
     *
     * @param id 项目费用归集汇总主键
     * @return 项目费用归集汇总
     */
    public ProjectCostSummary selectProjectCostSummaryById(Long id);

    /**
     * 查询项目费用归集汇总列表
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 项目费用归集汇总集合
     */
    public List<ProjectCostSummary> selectProjectCostSummaryList(ProjectCostSummary projectCostSummary);

    /**
     * 新增项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    public int insertProjectCostSummary(ProjectCostSummary projectCostSummary);

    /**
     * 修改项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    public int updateProjectCostSummary(ProjectCostSummary projectCostSummary);

    /**
     * 批量删除项目费用归集汇总
     *
     * @param ids 需要删除的项目费用归集汇总主键集合
     * @return 结果
     */
    public int deleteProjectCostSummaryByIds(Long[] ids);

    /**
     * 删除项目费用归集汇总信息
     *
     * @param id 项目费用归集汇总主键
     * @return 结果
     */
    public int deleteProjectCostSummaryById(Long id);

    /**
     * 导入项目费用归集汇总数据
     *
     * @param list 项目费用归集汇总列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importProjectCostSummary(List<ProjectCostSummary> list, Boolean isUpdateSupport, String operName);
}
