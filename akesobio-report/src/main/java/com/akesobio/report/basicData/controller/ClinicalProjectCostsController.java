package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.model.LoginUser;
import com.akesobio.common.utils.ServletUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import com.akesobio.report.basicData.service.IClinicalProjectCostsService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 临床项目成本各数据Controller
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/basicData/clinicalProjectCosts")
public class ClinicalProjectCostsController extends BaseController {
    @Autowired
    private IClinicalProjectCostsService clinicalProjectCostsService;

    /**
     * 查询临床项目成本各数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalProjectCosts clinicalProjectCosts) {
        startPage();
        List<ClinicalProjectCosts> list = clinicalProjectCostsService.selectClinicalProjectCostsList(clinicalProjectCosts);
        return getDataTable(list);
    }

    /**
     * 导出临床项目成本各数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:export')")
    @Log(title = "临床项目成本各数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClinicalProjectCosts clinicalProjectCosts) {
        List<ClinicalProjectCosts> list = clinicalProjectCostsService.selectClinicalProjectCostsList(clinicalProjectCosts);
        ExcelUtil<ClinicalProjectCosts> util = new ExcelUtil<ClinicalProjectCosts>(ClinicalProjectCosts.class);
        util.exportExcel(response, list, "临床项目成本各数据数据");
    }

    /**
     * 获取临床项目成本各数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(clinicalProjectCostsService.selectClinicalProjectCostsById(id));
    }

    /**
     * 新增临床项目成本各数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:add')")
    @Log(title = "临床项目成本各数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ClinicalProjectCosts clinicalProjectCosts) {
        return toAjax(clinicalProjectCostsService.insertClinicalProjectCosts(clinicalProjectCosts));
    }

    /**
     * 修改临床项目成本各数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:edit')")
    @Log(title = "临床项目成本各数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ClinicalProjectCosts clinicalProjectCosts) {
        return toAjax(clinicalProjectCostsService.updateClinicalProjectCosts(clinicalProjectCosts));
    }

    /**
     * 删除临床项目成本各数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:remove')")
    @Log(title = "临床项目成本各数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(clinicalProjectCostsService.deleteClinicalProjectCostsByIds(ids));
    }

    /**
     * 导入临床项目成本各数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:import')")
    @Log(title = "临床项目成本各数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,boolean updateSupport) throws Exception{
        ExcelUtil<ClinicalProjectCosts> util = new ExcelUtil<ClinicalProjectCosts>(ClinicalProjectCosts.class);
        List<ClinicalProjectCosts> list = util.importExcel(file.getInputStream());
        String message = clinicalProjectCostsService.importClinicalProjectCosts(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:clinicalProjectCosts:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ClinicalProjectCosts> util = new ExcelUtil<ClinicalProjectCosts>(ClinicalProjectCosts.class);
        util.importTemplateExcel(response, "临床项目成本各数据");
    }
}
