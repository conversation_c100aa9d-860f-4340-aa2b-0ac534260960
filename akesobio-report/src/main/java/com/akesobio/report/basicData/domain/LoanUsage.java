package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 贷款使用情况对象 loan_usage
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public class LoanUsage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 贷款主体
     */
    @Excel(name = "贷款主体")
    private String companyName;

    /**
     * 贷款主体简称
     */
    @Excel(name = "贷款主体简称")
    private String companyAbbreviation;

    /**
     * 贷款主体代码
     */
    @Excel(name = "贷款主体代码")
    private String companyCode;

    /**
     * 启用年份
     */
    @Excel(name = "启用年份")
    private String yearTime;

    /**
     * 业务类型
     */
    @Excel(name = "业务类型")
    private String businessType;

    /**
     * 贷款期限
     */
    @Excel(name = "贷款期限")
    private String loanTerm;

    /**
     * 贷款银行
     */
    @Excel(name = "贷款银行")
    private String lendingBank;

    /**
     * 贷款额度
     */
    @Excel(name = "贷款额度")
    private BigDecimal loanLimit;

    /**
     * 贷款余额
     */
    @Excel(name = "贷款余额")
    private BigDecimal loanBalance;

    /**
     * 待启用额度
     */
    @Excel(name = "待启用额度")
    private BigDecimal enabledAmount;

    /**
     * 一年内到期贷款金额
     */
    @Excel(name = "一年内到期贷款金额")
    private BigDecimal dueLoanBalance;

    /**
     * 已偿还金额
     */
    @Excel(name = "已偿还金额")
    private BigDecimal amountRepaid;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String notes;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setYearTime(String yearTime) {
        this.yearTime = yearTime;
    }

    public String getYearTime() {
        return yearTime;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setLoanTerm(String loanTerm) {
        this.loanTerm = loanTerm;
    }

    public String getLoanTerm() {
        return loanTerm;
    }

    public void setLendingBank(String lendingBank) {
        this.lendingBank = lendingBank;
    }

    public String getLendingBank() {
        return lendingBank;
    }

    public void setLoanLimit(BigDecimal loanLimit) {
        this.loanLimit = loanLimit;
    }

    public BigDecimal getLoanLimit() {
        return loanLimit;
    }

    public void setLoanBalance(BigDecimal loanBalance) {
        this.loanBalance = loanBalance;
    }

    public BigDecimal getLoanBalance() {
        return loanBalance;
    }

    public void setEnabledAmount(BigDecimal enabledAmount) {
        this.enabledAmount = enabledAmount;
    }

    public BigDecimal getEnabledAmount() {
        return enabledAmount;
    }

    public void setDueLoanBalance(BigDecimal dueLoanBalance) {
        this.dueLoanBalance = dueLoanBalance;
    }

    public BigDecimal getDueLoanBalance() {
        return dueLoanBalance;
    }

    public void setAmountRepaid(BigDecimal amountRepaid) {
        this.amountRepaid = amountRepaid;
    }

    public BigDecimal getAmountRepaid() {
        return amountRepaid;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getNotes() {
        return notes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("companyAbbreviation", getCompanyAbbreviation())
                .append("companyCode", getCompanyCode())
                .append("yearTime", getYearTime())
                .append("businessType", getBusinessType())
                .append("loanTerm", getLoanTerm())
                .append("lendingBank", getLendingBank())
                .append("loanLimit", getLoanLimit())
                .append("loanBalance", getLoanBalance())
                .append("enabledAmount", getEnabledAmount())
                .append("dueLoanBalance", getDueLoanBalance())
                .append("amountRepaid", getAmountRepaid())
                .append("notes", getNotes())
                .toString();
    }
}
