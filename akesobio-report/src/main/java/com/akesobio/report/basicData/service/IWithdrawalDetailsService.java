package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.RfExpenses;
import com.akesobio.report.basicData.domain.WithdrawalDetails;

/**
 * 提款明细Service接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface IWithdrawalDetailsService {
    /**
     * 查询提款明细
     *
     * @param id 提款明细主键
     * @return 提款明细
     */
    public WithdrawalDetails selectWithdrawalDetailsById(Integer id);

    /**
     * 查询提款明细列表
     *
     * @param withdrawalDetails 提款明细
     * @return 提款明细集合
     */
    public List<WithdrawalDetails> selectWithdrawalDetailsList(WithdrawalDetails withdrawalDetails);

    /**
     * 新增提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    public int insertWithdrawalDetails(WithdrawalDetails withdrawalDetails);

    /**
     * 修改提款明细
     *
     * @param withdrawalDetails 提款明细
     * @return 结果
     */
    public int updateWithdrawalDetails(WithdrawalDetails withdrawalDetails);

    /**
     * 批量删除提款明细
     *
     * @param ids 需要删除的提款明细主键集合
     * @return 结果
     */
    public int deleteWithdrawalDetailsByIds(Integer[] ids);

    /**
     * 删除提款明细信息
     *
     * @param id 提款明细主键
     * @return 结果
     */
    public int deleteWithdrawalDetailsById(Integer id);


    /**
     * 导入提款明细数据
     *
     * @param list 提款明细数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importWithdrawalDetails(List<WithdrawalDetails> list, Boolean isUpdateSupport);
}
