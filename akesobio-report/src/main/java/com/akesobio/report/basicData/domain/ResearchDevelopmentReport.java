package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 研发报告数据对象 research_development_report
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public class ResearchDevelopmentReport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String dataSources;

    /**
     * 公司
     */
    @Excel(name = "公司")
    private String company;

    /**
     * 过账日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过账日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date accountDate;

    /**
     * 总账
     */
    @Excel(name = "总账")
    private String ledger;

    /**
     * 总账科目：短文本
     */
    @Excel(name = "总账科目：短文本")
    private String ledgerAccount;

    /**
     * 人民币金额
     */
    @Excel(name = "人民币金额")
    private BigDecimal rmbAmount;

    /**
     * 药品
     */
    @Excel(name = "药品")
    private String drug;

    /**
     * 项目号
     */
    @Excel(name = "项目号")
    private String projectNo;

    /**
     * 审计报告列示
     */
    @Excel(name = "审计报告列示")
    private String auditReportPresentation;

    /**
     * 成本分类
     */
    @Excel(name = "成本分类")
    private String costClassification;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remarks;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCompany() {
        return company;
    }

    public void setAccountDate(Date accountDate) {
        this.accountDate = accountDate;
    }

    public Date getAccountDate() {
        return accountDate;
    }

    public void setLedger(String ledger) {
        this.ledger = ledger;
    }

    public String getLedger() {
        return ledger;
    }

    public void setLedgerAccount(String ledgerAccount) {
        this.ledgerAccount = ledgerAccount;
    }

    public String getLedgerAccount() {
        return ledgerAccount;
    }

    public void setRmbAmount(BigDecimal rmbAmount) {
        this.rmbAmount = rmbAmount;
    }

    public BigDecimal getRmbAmount() {
        return rmbAmount;
    }

    public void setDrug(String drug) {
        this.drug = drug;
    }

    public String getDrug() {
        return drug;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setAuditReportPresentation(String auditReportPresentation) {
        this.auditReportPresentation = auditReportPresentation;
    }

    public String getAuditReportPresentation() {
        return auditReportPresentation;
    }

    public void setCostClassification(String costClassification) {
        this.costClassification = costClassification;
    }

    public String getCostClassification() {
        return costClassification;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dataSources", getDataSources())
                .append("company", getCompany())
                .append("accountDate", getAccountDate())
                .append("ledger", getLedger())
                .append("ledgerAccount", getLedgerAccount())
                .append("rmbAmount", getRmbAmount())
                .append("drug", getDrug())
                .append("projectNo", getProjectNo())
                .append("auditReportPresentation", getAuditReportPresentation())
                .append("costClassification", getCostClassification())
                .append("remarks", getRemarks())
                .toString();
    }
}
