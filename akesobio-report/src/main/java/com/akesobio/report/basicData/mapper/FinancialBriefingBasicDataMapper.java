package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.FinancialBriefingBasicData;

/**
 * 财务简报基础数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface FinancialBriefingBasicDataMapper {
    /**
     * 查询财务简报基础数据
     *
     * @param id 财务简报基础数据主键
     * @return 财务简报基础数据
     */
    public FinancialBriefingBasicData selectFinancialBriefingBasicDataById(Integer id);

    /**
     * 查询财务简报基础数据列表
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 财务简报基础数据集合
     */
    public List<FinancialBriefingBasicData> selectFinancialBriefingBasicDataList(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 新增财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    public int insertFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 修改财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    public int updateFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 删除财务简报基础数据
     *
     * @param id 财务简报基础数据主键
     * @return 结果
     */
    public int deleteFinancialBriefingBasicDataById(Integer id);

    /**
     * 批量删除财务简报基础数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialBriefingBasicDataByIds(Integer[] ids);
}
