package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.List;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 公司运营费用数据对象 company_operating_expenses
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public class CompanyOperatingExpenses extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目
     */
    @Excel(name = "项目")
    private String project;

    /**
     * 公司主体
     */
    @Excel(name = "公司主体")
    private String companyName;

    /**
     * 公司简称
     */
    @Excel(name = "公司简称")
    private String companyAbbreviation;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private Integer companyCode;

    /**
     * 1月
     */
    @Excel(name = "1月")
    private BigDecimal january;

    /**
     * 2月
     */
    @Excel(name = "2月")
    private BigDecimal february;

    /**
     * 3月
     */
    @Excel(name = "3月")
    private BigDecimal march;

    /**
     * 4月
     */
    @Excel(name = "4月")
    private BigDecimal april;

    /**
     * 5月
     */
    @Excel(name = "5月")
    private BigDecimal may;

    /**
     * 6月
     */
    @Excel(name = "6月")
    private BigDecimal june;

    /**
     * 7月
     */
    @Excel(name = "7月")
    private BigDecimal july;

    /**
     * 8月
     */
    @Excel(name = "8月")
    private BigDecimal august;

    /**
     * 9月
     */
    @Excel(name = "9月")
    private BigDecimal september;

    /**
     * 10月
     */
    @Excel(name = "10月")
    private BigDecimal october;

    /**
     * 11月
     */
    @Excel(name = "11月")
    private BigDecimal november;

    /**
     * 12月
     */
    @Excel(name = "12月")
    private BigDecimal december;

    /**
     * 全年
     */
    @Excel(name = "全年")
    private BigDecimal annual;

    /** 年份 */
    @Excel(name = "年份")
    private Integer yearTime;

    @TableField(exist = false)
    private List<String> companyList;

    @TableField(exist = false)
    private List<String> projectList;


    public List<String> getProjectList() {
        return projectList;
    }

    public void setProjectList(List<String> projectList) {
        this.projectList = projectList;
    }

    public Integer getYearTime() {
        return yearTime;
    }

    public void setYearTime(Integer yearTime) {
        this.yearTime = yearTime;
    }


    public List<String> getCompanyList() {
        return companyList;
    }

    public void setCompanyList(List<String> companyList) {
        this.companyList = companyList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getProject() {
        return project;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    public void setCompanyCode(Integer companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getCompanyCode() {
        return companyCode;
    }

    public void setJanuary(BigDecimal january) {
        this.january = january;
    }

    public BigDecimal getJanuary() {
        return january;
    }

    public void setFebruary(BigDecimal february) {
        this.february = february;
    }

    public BigDecimal getFebruary() {
        return february;
    }

    public void setMarch(BigDecimal march) {
        this.march = march;
    }

    public BigDecimal getMarch() {
        return march;
    }

    public void setApril(BigDecimal april) {
        this.april = april;
    }

    public BigDecimal getApril() {
        return april;
    }

    public void setMay(BigDecimal may) {
        this.may = may;
    }

    public BigDecimal getMay() {
        return may;
    }

    public void setJune(BigDecimal june) {
        this.june = june;
    }

    public BigDecimal getJune() {
        return june;
    }

    public void setJuly(BigDecimal july) {
        this.july = july;
    }

    public BigDecimal getJuly() {
        return july;
    }

    public void setAugust(BigDecimal august) {
        this.august = august;
    }

    public BigDecimal getAugust() {
        return august;
    }

    public void setSeptember(BigDecimal september) {
        this.september = september;
    }

    public BigDecimal getSeptember() {
        return september;
    }

    public void setOctober(BigDecimal october) {
        this.october = october;
    }

    public BigDecimal getOctober() {
        return october;
    }

    public void setNovember(BigDecimal november) {
        this.november = november;
    }

    public BigDecimal getNovember() {
        return november;
    }

    public void setDecember(BigDecimal december) {
        this.december = december;
    }

    public BigDecimal getDecember() {
        return december;
    }

    public void setAnnual(BigDecimal annual) {
        this.annual = annual;
    }

    public BigDecimal getAnnual() {
        return annual;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("project", getProject())
                .append("companyName", getCompanyName())
                .append("companyAbbreviation", getCompanyAbbreviation())
                .append("companyCode", getCompanyCode())
                .append("january", getJanuary())
                .append("february", getFebruary())
                .append("march", getMarch())
                .append("april", getApril())
                .append("may", getMay())
                .append("june", getJune())
                .append("july", getJuly())
                .append("august", getAugust())
                .append("september", getSeptember())
                .append("october", getOctober())
                .append("november", getNovember())
                .append("december", getDecember())
                .append("annual", getAnnual())
                .append("yearTime", getYearTime())
                .toString();
    }
}
