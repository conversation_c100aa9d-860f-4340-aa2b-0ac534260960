package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.RfExpenses;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.GroupFinancialSituation;
import com.akesobio.report.basicData.service.IGroupFinancialSituationService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 集团理财情况Controller
 * 
 * <AUTHOR>
 * @date 2024-01-30
 */
@RestController
@RequestMapping("/basicData/groupFinancialSituation")
public class GroupFinancialSituationController extends BaseController
{
    @Autowired
    private IGroupFinancialSituationService groupFinancialSituationService;

    /**
     * 查询集团理财情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:list')")
    @GetMapping("/list")
    public TableDataInfo list(GroupFinancialSituation groupFinancialSituation)
    {
        startPage();
        List<GroupFinancialSituation> list = groupFinancialSituationService.selectGroupFinancialSituationList(groupFinancialSituation);
        return getDataTable(list);
    }

    /**
     * 导出集团理财情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:export')")
    @Log(title = "集团理财情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GroupFinancialSituation groupFinancialSituation)
    {
        List<GroupFinancialSituation> list = groupFinancialSituationService.selectGroupFinancialSituationList(groupFinancialSituation);
        ExcelUtil<GroupFinancialSituation> util = new ExcelUtil<GroupFinancialSituation>(GroupFinancialSituation.class);
        util.exportExcel(response, list, "集团理财情况数据");
    }

    /**
     * 获取集团理财情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(groupFinancialSituationService.selectGroupFinancialSituationById(id));
    }

    /**
     * 新增集团理财情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:add')")
    @Log(title = "集团理财情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GroupFinancialSituation groupFinancialSituation)
    {
        return toAjax(groupFinancialSituationService.insertGroupFinancialSituation(groupFinancialSituation));
    }

    /**
     * 修改集团理财情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:edit')")
    @Log(title = "集团理财情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GroupFinancialSituation groupFinancialSituation)
    {
        return toAjax(groupFinancialSituationService.updateGroupFinancialSituation(groupFinancialSituation));
    }

    /**
     * 删除集团理财情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:remove')")
    @Log(title = "集团理财情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(groupFinancialSituationService.deleteGroupFinancialSituationByIds(ids));
    }

    /**
     * 导入集团理财情况数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:import')")
    @Log(title = "集团理财情况数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<GroupFinancialSituation> util = new ExcelUtil<GroupFinancialSituation>(GroupFinancialSituation.class);
        List<GroupFinancialSituation> list = util.importExcel(file.getInputStream());
        String message = groupFinancialSituationService.importGroupFinancialSituation(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:groupFinancialSituation:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<GroupFinancialSituation> util = new ExcelUtil<GroupFinancialSituation>(GroupFinancialSituation.class);
        util.importTemplateExcel(response, "集团理财情况数据");
    }
}
