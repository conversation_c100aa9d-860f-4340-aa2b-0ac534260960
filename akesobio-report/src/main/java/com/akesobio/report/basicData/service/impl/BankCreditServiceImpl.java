package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.RfExpenses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.BankCreditMapper;
import com.akesobio.report.basicData.domain.BankCredit;
import com.akesobio.report.basicData.service.IBankCreditService;

/**
 * 银行授信批复情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Service
public class BankCreditServiceImpl implements IBankCreditService {

    private static final Logger log= LoggerFactory.getLogger(BankCreditServiceImpl.class);
    @Autowired
    private BankCreditMapper bankCreditMapper;

    /**
     * 查询银行授信批复情况
     *
     * @param id 银行授信批复情况主键
     * @return 银行授信批复情况
     */
    @Override
    public BankCredit selectBankCreditById(Integer id) {
        return bankCreditMapper.selectBankCreditById(id);
    }

    /**
     * 查询银行授信批复情况列表
     *
     * @param bankCredit 银行授信批复情况
     * @return 银行授信批复情况
     */
    @Override
    public List<BankCredit> selectBankCreditList(BankCredit bankCredit) {
        return bankCreditMapper.selectBankCreditList(bankCredit);
    }

    /**
     * 新增银行授信批复情况
     *
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    @Override
    public int insertBankCredit(BankCredit bankCredit) {
        return bankCreditMapper.insertBankCredit(bankCredit);
    }

    /**
     * 修改银行授信批复情况
     *
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    @Override
    public int updateBankCredit(BankCredit bankCredit) {
        return bankCreditMapper.updateBankCredit(bankCredit);
    }

    /**
     * 批量删除银行授信批复情况
     *
     * @param ids 需要删除的银行授信批复情况主键
     * @return 结果
     */
    @Override
    public int deleteBankCreditByIds(Integer[] ids) {
        return bankCreditMapper.deleteBankCreditByIds(ids);
    }

    /**
     * 删除银行授信批复情况信息
     *
     * @param id 银行授信批复情况主键
     * @return 结果
     */
    @Override
    public int deleteBankCreditById(Integer id) {
        return bankCreditMapper.deleteBankCreditById(id);
    }

    /**
     * 导入行授信批复情况数据
     *
     * @param list            行授信批复情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importBankCredit(List<BankCredit> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BankCredit bankCredit : list) {
            try {
                bankCreditMapper.insertBankCredit(bankCredit);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司名称 " + bankCredit.getCompanyName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司名称 " + bankCredit.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
