package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 临床项目成本各数据对象 clinical_project_costs
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public class ClinicalProjectCosts extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 药品管线
     */
    @Excel(name = "药品管线")
    private String drugPipeline;

    /**
     * 适应症
     */
    @Excel(name = "适应症")
    private String indication;

    /**
     * 联用方案
     */
    @Excel(name = "联用方案")
    private String jointSolution;

    /**
     * 入组数
     */
    @Excel(name = "入组数")
    private BigDecimal enrollmentNumber;

    /**
     * 单例成本
     */
    @Excel(name = "单例成本")
    private BigDecimal singleInstanceCost;

    /**
     * 小计
     */
    @Excel(name = "小计")
    private BigDecimal subtotal;

    /**
     * 直接成本
     */
    @Excel(name = "直接成本")
    private BigDecimal directCosts;

    /**
     * 人工成本
     */
    @Excel(name = "人工成本")
    private BigDecimal laborCosts;

    /**
     * 材料成本
     */
    @Excel(name = "材料成本")
    private BigDecimal materialCost;

    /**
     * 运行成本
     */
    @Excel(name = "运行成本")
    private BigDecimal operatingCost;

    /**
     * 折旧及摊销
     */
    @Excel(name = "折旧及摊销")
    private BigDecimal depreciationAmortization;

    /**
     * RF费
     */
    @Excel(name = "RF费")
    private BigDecimal rfFee;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDrugPipeline(String drugPipeline) {
        this.drugPipeline = drugPipeline;
    }

    public String getDrugPipeline() {
        return drugPipeline;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public String getIndication() {
        return indication;
    }

    public void setJointSolution(String jointSolution) {
        this.jointSolution = jointSolution;
    }

    public String getJointSolution() {
        return jointSolution;
    }

    public void setEnrollmentNumber(BigDecimal enrollmentNumber) {
        this.enrollmentNumber = enrollmentNumber;
    }

    public BigDecimal getEnrollmentNumber() {
        return enrollmentNumber;
    }

    public void setSingleInstanceCost(BigDecimal singleInstanceCost) {
        this.singleInstanceCost = singleInstanceCost;
    }

    public BigDecimal getSingleInstanceCost() {
        return singleInstanceCost;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setDirectCosts(BigDecimal directCosts) {
        this.directCosts = directCosts;
    }

    public BigDecimal getDirectCosts() {
        return directCosts;
    }

    public void setLaborCosts(BigDecimal laborCosts) {
        this.laborCosts = laborCosts;
    }

    public BigDecimal getLaborCosts() {
        return laborCosts;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setOperatingCost(BigDecimal operatingCost) {
        this.operatingCost = operatingCost;
    }

    public BigDecimal getOperatingCost() {
        return operatingCost;
    }

    public void setDepreciationAmortization(BigDecimal depreciationAmortization) {
        this.depreciationAmortization = depreciationAmortization;
    }

    public BigDecimal getDepreciationAmortization() {
        return depreciationAmortization;
    }

    public void setRfFee(BigDecimal rfFee) {
        this.rfFee = rfFee;
    }

    public BigDecimal getRfFee() {
        return rfFee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("drugPipeline", getDrugPipeline())
                .append("indication", getIndication())
                .append("jointSolution", getJointSolution())
                .append("enrollmentNumber", getEnrollmentNumber())
                .append("singleInstanceCost", getSingleInstanceCost())
                .append("subtotal", getSubtotal())
                .append("directCosts", getDirectCosts())
                .append("laborCosts", getLaborCosts())
                .append("materialCost", getMaterialCost())
                .append("operatingCost", getOperatingCost())
                .append("depreciationAmortization", getDepreciationAmortization())
                .append("rfFee", getRfFee())
                .toString();
    }
}
