package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.BankCredit;
import com.akesobio.report.basicData.domain.FinancialBriefingBasicData;

/**
 * 财务简报基础数据Service接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface IFinancialBriefingBasicDataService {
    /**
     * 查询财务简报基础数据
     *
     * @param id 财务简报基础数据主键
     * @return 财务简报基础数据
     */
    public FinancialBriefingBasicData selectFinancialBriefingBasicDataById(Integer id);

    /**
     * 查询财务简报基础数据列表
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 财务简报基础数据集合
     */
    public List<FinancialBriefingBasicData> selectFinancialBriefingBasicDataList(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 新增财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    public int insertFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 修改财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    public int updateFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData);

    /**
     * 批量删除财务简报基础数据
     *
     * @param ids 需要删除的财务简报基础数据主键集合
     * @return 结果
     */
    public int deleteFinancialBriefingBasicDataByIds(Integer[] ids);

    /**
     * 删除财务简报基础数据信息
     *
     * @param id 财务简报基础数据主键
     * @return 结果
     */
    public int deleteFinancialBriefingBasicDataById(Integer id);

    /**
     * 导入财务简报基础数据
     *
     * @param list 财务简报基础数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importFinancialBriefingBasicData(List<FinancialBriefingBasicData> list, Boolean isUpdateSupport);
}
