package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;
import com.akesobio.report.basicData.domain.GroupFinancialSituation;

/**
 * 集团理财情况Service接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface IGroupFinancialSituationService {
    /**
     * 查询集团理财情况
     *
     * @param id 集团理财情况主键
     * @return 集团理财情况
     */
    public GroupFinancialSituation selectGroupFinancialSituationById(Integer id);

    /**
     * 查询集团理财情况列表
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 集团理财情况集合
     */
    public List<GroupFinancialSituation> selectGroupFinancialSituationList(GroupFinancialSituation groupFinancialSituation);

    /**
     * 新增集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    public int insertGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation);

    /**
     * 修改集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    public int updateGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation);

    /**
     * 批量删除集团理财情况
     *
     * @param ids 需要删除的集团理财情况主键集合
     * @return 结果
     */
    public int deleteGroupFinancialSituationByIds(Integer[] ids);

    /**
     * 删除集团理财情况信息
     *
     * @param id 集团理财情况主键
     * @return 结果
     */
    public int deleteGroupFinancialSituationById(Integer id);

    /**
     * 导入集团理财情况数据
     *
     * @param list            集团理财情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importGroupFinancialSituation(List<GroupFinancialSituation> list, Boolean isUpdateSupport);
}
