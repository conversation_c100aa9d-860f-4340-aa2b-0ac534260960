package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.GroupFinancialSituation;

/**
 * 集团理财情况Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface GroupFinancialSituationMapper {
    /**
     * 查询集团理财情况
     *
     * @param id 集团理财情况主键
     * @return 集团理财情况
     */
    public GroupFinancialSituation selectGroupFinancialSituationById(Integer id);

    /**
     * 查询集团理财情况列表
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 集团理财情况集合
     */
    public List<GroupFinancialSituation> selectGroupFinancialSituationList(GroupFinancialSituation groupFinancialSituation);

    /**
     * 新增集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    public int insertGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation);

    /**
     * 修改集团理财情况
     *
     * @param groupFinancialSituation 集团理财情况
     * @return 结果
     */
    public int updateGroupFinancialSituation(GroupFinancialSituation groupFinancialSituation);

    /**
     * 删除集团理财情况
     *
     * @param id 集团理财情况主键
     * @return 结果
     */
    public int deleteGroupFinancialSituationById(Integer id);

    /**
     * 批量删除集团理财情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGroupFinancialSituationByIds(Integer[] ids);
}
