package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.BankCredit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.FinancialBriefingBasicDataMapper;
import com.akesobio.report.basicData.domain.FinancialBriefingBasicData;
import com.akesobio.report.basicData.service.IFinancialBriefingBasicDataService;

/**
 * 财务简报基础数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Service
public class FinancialBriefingBasicDataServiceImpl implements IFinancialBriefingBasicDataService {

    private  static  final Logger log= LoggerFactory.getLogger(FinancialBriefingBasicDataServiceImpl.class);

    @Autowired
    private FinancialBriefingBasicDataMapper financialBriefingBasicDataMapper;

    /**
     * 查询财务简报基础数据
     *
     * @param id 财务简报基础数据主键
     * @return 财务简报基础数据
     */
    @Override
    public FinancialBriefingBasicData selectFinancialBriefingBasicDataById(Integer id) {
        return financialBriefingBasicDataMapper.selectFinancialBriefingBasicDataById(id);
    }

    /**
     * 查询财务简报基础数据列表
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 财务简报基础数据
     */
    @Override
    public List<FinancialBriefingBasicData> selectFinancialBriefingBasicDataList(FinancialBriefingBasicData financialBriefingBasicData) {
        return financialBriefingBasicDataMapper.selectFinancialBriefingBasicDataList(financialBriefingBasicData);
    }

    /**
     * 新增财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    @Override
    public int insertFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData) {
        return financialBriefingBasicDataMapper.insertFinancialBriefingBasicData(financialBriefingBasicData);
    }

    /**
     * 修改财务简报基础数据
     *
     * @param financialBriefingBasicData 财务简报基础数据
     * @return 结果
     */
    @Override
    public int updateFinancialBriefingBasicData(FinancialBriefingBasicData financialBriefingBasicData) {
        return financialBriefingBasicDataMapper.updateFinancialBriefingBasicData(financialBriefingBasicData);
    }

    /**
     * 批量删除财务简报基础数据
     *
     * @param ids 需要删除的财务简报基础数据主键
     * @return 结果
     */
    @Override
    public int deleteFinancialBriefingBasicDataByIds(Integer[] ids) {
        return financialBriefingBasicDataMapper.deleteFinancialBriefingBasicDataByIds(ids);
    }

    /**
     * 删除财务简报基础数据信息
     *
     * @param id 财务简报基础数据主键
     * @return 结果
     */
    @Override
    public int deleteFinancialBriefingBasicDataById(Integer id) {
        return financialBriefingBasicDataMapper.deleteFinancialBriefingBasicDataById(id);
    }

    /**
     * 导入财务简报基础数据
     *
     * @param list 财务简报基础数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importFinancialBriefingBasicData(List<FinancialBriefingBasicData> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (FinancialBriefingBasicData financialBriefingBasicData : list) {
            try {
                financialBriefingBasicDataMapper.insertFinancialBriefingBasicData(financialBriefingBasicData);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目 " + financialBriefingBasicData.getProject() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目 " + financialBriefingBasicData.getProject() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
