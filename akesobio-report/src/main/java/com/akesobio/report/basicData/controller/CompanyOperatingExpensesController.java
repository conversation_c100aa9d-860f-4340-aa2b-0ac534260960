package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;
import com.akesobio.report.basicData.service.ICompanyOperatingExpensesService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 公司运营费用数据Controller
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@RestController
@RequestMapping("/basicData/companyOperatingExpenses")
public class CompanyOperatingExpensesController extends BaseController {
    @Autowired
    private ICompanyOperatingExpensesService companyOperatingExpensesService;

    /**
     * 查询公司运营费用数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyOperatingExpenses companyOperatingExpenses) {
        startPage();
        List<CompanyOperatingExpenses> list = companyOperatingExpensesService.selectCompanyOperatingExpensesList(companyOperatingExpenses);
        return getDataTable(list);
    }

    /**
     * 导出公司运营费用数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:export')")
    @Log(title = "公司运营费用数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyOperatingExpenses companyOperatingExpenses) {
        List<CompanyOperatingExpenses> list = companyOperatingExpensesService.selectCompanyOperatingExpensesList(companyOperatingExpenses);
        ExcelUtil<CompanyOperatingExpenses> util = new ExcelUtil<CompanyOperatingExpenses>(CompanyOperatingExpenses.class);
        util.exportExcel(response, list, "公司运营费用数据数据");
    }

    /**
     * 获取公司运营费用数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(companyOperatingExpensesService.selectCompanyOperatingExpensesById(id));
    }

    /**
     * 新增公司运营费用数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:add')")
    @Log(title = "公司运营费用数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyOperatingExpenses companyOperatingExpenses) {
        return toAjax(companyOperatingExpensesService.insertCompanyOperatingExpenses(companyOperatingExpenses));
    }

    /**
     * 修改公司运营费用数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:edit')")
    @Log(title = "公司运营费用数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyOperatingExpenses companyOperatingExpenses) {
        return toAjax(companyOperatingExpensesService.updateCompanyOperatingExpenses(companyOperatingExpenses));
    }

    /**
     * 删除公司运营费用数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:remove')")
    @Log(title = "公司运营费用数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(companyOperatingExpensesService.deleteCompanyOperatingExpensesByIds(ids));
    }

    /**
     * 导入公司运营费用数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:import')")
    @Log(title = "公司运营费用数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception{
        ExcelUtil<CompanyOperatingExpenses> util = new ExcelUtil<CompanyOperatingExpenses>(CompanyOperatingExpenses.class);
        List<CompanyOperatingExpenses> list = util.importExcel(file.getInputStream());
        String message = companyOperatingExpensesService.importCompanyOperatingExpenses(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:companyOperatingExpenses:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CompanyOperatingExpenses> util = new ExcelUtil<CompanyOperatingExpenses>(CompanyOperatingExpenses.class);
        util.importTemplateExcel(response, "公司运营费用数据");
    }
}
