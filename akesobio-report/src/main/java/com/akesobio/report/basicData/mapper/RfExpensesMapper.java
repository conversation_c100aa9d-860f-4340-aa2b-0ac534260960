package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.RfExpenses;

/**
 * RF费用支出报告Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-12
 */
public interface RfExpensesMapper {
    /**
     * 查询RF费用支出报告
     *
     * @param id RF费用支出报告主键
     * @return RF费用支出报告
     */
    public RfExpenses selectRfExpensesById(Long id);

    /**
     * 查询RF费用支出报告列表
     *
     * @param rfExpenses RF费用支出报告
     * @return RF费用支出报告集合
     */
    public List<RfExpenses> selectRfExpensesList(RfExpenses rfExpenses);

    /**
     * 新增RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    public int insertRfExpenses(RfExpenses rfExpenses);

    /**
     * 修改RF费用支出报告
     *
     * @param rfExpenses RF费用支出报告
     * @return 结果
     */
    public int updateRfExpenses(RfExpenses rfExpenses);

    /**
     * 删除RF费用支出报告
     *
     * @param id RF费用支出报告主键
     * @return 结果
     */
    public int deleteRfExpensesById(Long id);

    /**
     * 批量删除RF费用支出报告
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRfExpensesByIds(Long[] ids);
}
