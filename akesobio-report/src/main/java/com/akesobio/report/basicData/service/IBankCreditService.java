package com.akesobio.report.basicData.service;

import java.util.List;
import com.akesobio.report.basicData.domain.BankCredit;
import com.akesobio.report.basicData.domain.RfExpenses;

/**
 * 银行授信批复情况Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface IBankCreditService 
{
    /**
     * 查询银行授信批复情况
     * 
     * @param id 银行授信批复情况主键
     * @return 银行授信批复情况
     */
    public BankCredit selectBankCreditById(Integer id);

    /**
     * 查询银行授信批复情况列表
     * 
     * @param bankCredit 银行授信批复情况
     * @return 银行授信批复情况集合
     */
    public List<BankCredit> selectBankCreditList(BankCredit bankCredit);

    /**
     * 新增银行授信批复情况
     * 
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    public int insertBankCredit(BankCredit bankCredit);

    /**
     * 修改银行授信批复情况
     * 
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    public int updateBankCredit(BankCredit bankCredit);

    /**
     * 批量删除银行授信批复情况
     * 
     * @param ids 需要删除的银行授信批复情况主键集合
     * @return 结果
     */
    public int deleteBankCreditByIds(Integer[] ids);

    /**
     * 删除银行授信批复情况信息
     * 
     * @param id 银行授信批复情况主键
     * @return 结果
     */
    public int deleteBankCreditById(Integer id);

    /**
     * 导入行授信批复情况数据
     *
     * @param list 行授信批复情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importBankCredit(List<BankCredit> list, Boolean isUpdateSupport);
}
