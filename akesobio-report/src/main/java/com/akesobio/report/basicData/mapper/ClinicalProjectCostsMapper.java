package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.ClinicalProjectCosts;

/**
 * 临床项目成本各数据Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface ClinicalProjectCostsMapper {
    /**
     * 查询临床项目成本各数据
     *
     * @param id 临床项目成本各数据主键
     * @return 临床项目成本各数据
     */
    public ClinicalProjectCosts selectClinicalProjectCostsById(Long id);

    /**
     * 查询临床项目成本各数据列表
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 临床项目成本各数据集合
     */
    public List<ClinicalProjectCosts> selectClinicalProjectCostsList(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 新增临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    public int insertClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 修改临床项目成本各数据
     *
     * @param clinicalProjectCosts 临床项目成本各数据
     * @return 结果
     */
    public int updateClinicalProjectCosts(ClinicalProjectCosts clinicalProjectCosts);

    /**
     * 删除临床项目成本各数据
     *
     * @param id 临床项目成本各数据主键
     * @return 结果
     */
    public int deleteClinicalProjectCostsById(Long id);

    /**
     * 批量删除临床项目成本各数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteClinicalProjectCostsByIds(Long[] ids);
}
