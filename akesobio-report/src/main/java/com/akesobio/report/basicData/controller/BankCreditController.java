package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.RfExpenses;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.BankCredit;
import com.akesobio.report.basicData.service.IBankCreditService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 银行授信批复情况Controller
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
@RestController
@RequestMapping("/basicData/bankCredit")
public class BankCreditController extends BaseController
{
    @Autowired
    private IBankCreditService bankCreditService;

    /**
     * 查询银行授信批复情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:list')")
    @GetMapping("/list")
    public TableDataInfo list(BankCredit bankCredit)
    {
        startPage();
        List<BankCredit> list = bankCreditService.selectBankCreditList(bankCredit);
        return getDataTable(list);
    }

    /**
     * 导出银行授信批复情况列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:export')")
    @Log(title = "银行授信批复情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BankCredit bankCredit)
    {
        List<BankCredit> list = bankCreditService.selectBankCreditList(bankCredit);
        ExcelUtil<BankCredit> util = new ExcelUtil<BankCredit>(BankCredit.class);
        util.exportExcel(response, list, "银行授信批复情况数据");
    }

    /**
     * 获取银行授信批复情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(bankCreditService.selectBankCreditById(id));
    }

    /**
     * 新增银行授信批复情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:add')")
    @Log(title = "银行授信批复情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BankCredit bankCredit)
    {
        return toAjax(bankCreditService.insertBankCredit(bankCredit));
    }

    /**
     * 修改银行授信批复情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:edit')")
    @Log(title = "银行授信批复情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BankCredit bankCredit)
    {
        return toAjax(bankCreditService.updateBankCredit(bankCredit));
    }

    /**
     * 删除银行授信批复情况
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:remove')")
    @Log(title = "银行授信批复情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(bankCreditService.deleteBankCreditByIds(ids));
    }

    /**
     * 导入银行授信批复情况数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:import')")
    @Log(title = "银行授信批复情况数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BankCredit> util = new ExcelUtil<BankCredit>(BankCredit.class);
        List<BankCredit> list = util.importExcel(file.getInputStream());
        String message = bankCreditService.importBankCredit(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:bankCredit:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BankCredit> util = new ExcelUtil<BankCredit>(BankCredit.class);
        util.importTemplateExcel(response, "银行授信批复情况数据");
    }
}
