package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 采购大数据对象 procurement_list
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public class ProcurementList extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remarks;

    /**
     * 公司主体名称
     */
    @Excel(name = "公司主体名称")
    private String companyName;

    /**
     * 费用归属年份
     */
    @Excel(name = "费用归属年份")
    private String feeAttributableYear;

    /**
     * 凭证号
     */
    @Excel(name = "凭证号")
    private String voucherNo;

    /**
     * 过账日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过账日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date accountDate;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /**
     * OA流程号
     */
    @Excel(name = "OA流程号")
    private String oaProcessNumber;

    /**
     * 摘要
     */
    @Excel(name = "摘要")
    private String abstractTo;

    /**
     * 付款金额
     */
    @Excel(name = "付款金额")
    private BigDecimal paymentAmount;

    /**
     * 发票类型
     */
    @Excel(name = "发票类型")
    private String invoiceType;

    /**
     * 税率
     */
    @Excel(name = "税率")
    private String taxRate;

    /**
     * 不含税金额
     */
    @Excel(name = "不含税金额")
    private BigDecimal excludingTaxAmount;

    /**
     * 供应商名称（全称）
     */
    @Excel(name = "供应商名称", readConverterExp = "全=称")
    private String supplierName;

    /**
     * 项目号
     */
    @Excel(name = "项目号")
    private String projectNo;

    /**
     * 对应付款里程碑
     */
    @Excel(name = "对应付款里程碑")
    private String paymentMilestones;

    /**
     * 合同编码
     */
    @Excel(name = "合同编码")
    private String contractCode;

    /**
     * 合同类型
     */
    @Excel(name = "合同类型")
    private String contractType;

    /**
     * 合同总金额
     */
    @Excel(name = "合同总金额")
    private BigDecimal totalContractAmount;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setFeeAttributableYear(String feeAttributableYear) {
        this.feeAttributableYear = feeAttributableYear;
    }

    public String getFeeAttributableYear() {
        return feeAttributableYear;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setAccountDate(Date accountDate) {
        this.accountDate = accountDate;
    }

    public Date getAccountDate() {
        return accountDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setOaProcessNumber(String oaProcessNumber) {
        this.oaProcessNumber = oaProcessNumber;
    }

    public String getOaProcessNumber() {
        return oaProcessNumber;
    }

    public void setAbstractTo(String abstractTo) {
        this.abstractTo = abstractTo;
    }

    public String getAbstractTo() {
        return abstractTo;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setPaymentMilestones(String paymentMilestones) {
        this.paymentMilestones = paymentMilestones;
    }

    public String getPaymentMilestones() {
        return paymentMilestones;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractType() {
        return contractType;
    }

    public void setTotalContractAmount(BigDecimal totalContractAmount) {
        this.totalContractAmount = totalContractAmount;
    }

    public BigDecimal getTotalContractAmount() {
        return totalContractAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("remarks", getRemarks())
                .append("companyName", getCompanyName())
                .append("feeAttributableYear", getFeeAttributableYear())
                .append("voucherNo", getVoucherNo())
                .append("accountDate", getAccountDate())
                .append("paymentDate", getPaymentDate())
                .append("oaProcessNumber", getOaProcessNumber())
                .append("abstractTo", getAbstractTo())
                .append("paymentAmount", getPaymentAmount())
                .append("invoiceType", getInvoiceType())
                .append("taxRate", getTaxRate())
                .append("excludingTaxAmount", getExcludingTaxAmount())
                .append("supplierName", getSupplierName())
                .append("projectNo", getProjectNo())
                .append("paymentMilestones", getPaymentMilestones())
                .append("contractCode", getContractCode())
                .append("contractType", getContractType())
                .append("totalContractAmount", getTotalContractAmount())
                .toString();
    }
}
