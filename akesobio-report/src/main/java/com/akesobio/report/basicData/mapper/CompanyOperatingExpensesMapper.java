package com.akesobio.report.basicData.mapper;

import java.util.List;

import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;

/**
 * 公司运营费用数据Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface CompanyOperatingExpensesMapper {
    /**
     * 查询公司运营费用数据
     *
     * @param id 公司运营费用数据主键
     * @return 公司运营费用数据
     */
    public CompanyOperatingExpenses selectCompanyOperatingExpensesById(Long id);

    /**
     * 查询公司运营费用数据列表
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 公司运营费用数据集合
     */
    public List<CompanyOperatingExpenses> selectCompanyOperatingExpensesList(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 新增公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    public int insertCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 修改公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    public int updateCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses);

    /**
     * 删除公司运营费用数据
     *
     * @param id 公司运营费用数据主键
     * @return 结果
     */
    public int deleteCompanyOperatingExpensesById(Long id);

    /**
     * 批量删除公司运营费用数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyOperatingExpensesByIds(Long[] ids);
}
