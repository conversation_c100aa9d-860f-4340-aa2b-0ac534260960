package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 集团理财情况对象 group_financial_situation
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public class GroupFinancialSituation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 公司简称
     */
    @Excel(name = "公司简称")
    private String companyAbbreviation;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String companyCode;

    /**
     * 年份
     */
    @Excel(name = "年份")
    private String yearTime;

    /**
     * 金融机构
     */
    @Excel(name = "金融机构")
    private String financialInstitution;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称")
    private String productName;

    /**
     * 境内/外
     */
    @Excel(name = "境内/外")
    private String domesticForeign;

    /**
     * 资金性质
     */
    @Excel(name = "资金性质")
    private String fundNature;

    /**
     * 币种
     */
    @Excel(name = "币种")
    private String currency;

    /**
     * 金额
     */
    @Excel(name = "金额")
    private BigDecimal amountMoney;

    /**
     * 年利率
     */
    @Excel(name = "年利率")
    private String annualInterestRate;

    /**
     * 期限
     */
    @Excel(name = "期限")
    private BigDecimal term;

    /**
     * 起息日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "起息日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date valueDate;

    /**
     * 到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dueDate;

    /**
     * 收益到账日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收益到账日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date incomeAccountDate;

    /**
     * 收益到账金额
     */
    @Excel(name = "收益到账金额")
    private BigDecimal incomeReceivedAmount;

    /**
     * 是否到期
     */
    @Excel(name = "是否到期")
    private String isDue;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String notes;

    /**
     * 人民币汇率
     */
    @Excel(name = "人民币汇率")
    private BigDecimal cnyExchangeRate;

    /**
     * 兑换成人民币金额
     */
    @Excel(name = "兑换成人民币金额")
    private BigDecimal cnyAmount;

    /**
     * 美元汇率
     */
    @Excel(name = "美元汇率")
    private BigDecimal usdExchangeRate;

    /**
     * 兑换成美元金额
     */
    @Excel(name = "兑换成美元金额")
    private BigDecimal usdAmount;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setYearTime(String yearTime) {
        this.yearTime = yearTime;
    }

    public String getYearTime() {
        return yearTime;
    }

    public void setFinancialInstitution(String financialInstitution) {
        this.financialInstitution = financialInstitution;
    }

    public String getFinancialInstitution() {
        return financialInstitution;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductName() {
        return productName;
    }

    public void setDomesticForeign(String domesticForeign) {
        this.domesticForeign = domesticForeign;
    }

    public String getDomesticForeign() {
        return domesticForeign;
    }

    public void setFundNature(String fundNature) {
        this.fundNature = fundNature;
    }

    public String getFundNature() {
        return fundNature;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

    public void setAmountMoney(BigDecimal amountMoney) {
        this.amountMoney = amountMoney;
    }

    public BigDecimal getAmountMoney() {
        return amountMoney;
    }

    public void setAnnualInterestRate(String annualInterestRate) {
        this.annualInterestRate = annualInterestRate;
    }

    public String getAnnualInterestRate() {
        return annualInterestRate;
    }

    public void setTerm(BigDecimal term) {
        this.term = term;
    }

    public BigDecimal getTerm() {
        return term;
    }

    public void setValueDate(Date valueDate) {
        this.valueDate = valueDate;
    }

    public Date getValueDate() {
        return valueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setIncomeAccountDate(Date incomeAccountDate) {
        this.incomeAccountDate = incomeAccountDate;
    }

    public Date getIncomeAccountDate() {
        return incomeAccountDate;
    }

    public void setIncomeReceivedAmount(BigDecimal incomeReceivedAmount) {
        this.incomeReceivedAmount = incomeReceivedAmount;
    }

    public BigDecimal getIncomeReceivedAmount() {
        return incomeReceivedAmount;
    }

    public void setIsDue(String isDue) {
        this.isDue = isDue;
    }

    public String getIsDue() {
        return isDue;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getNotes() {
        return notes;
    }

    public BigDecimal getCnyExchangeRate() {
        return cnyExchangeRate;
    }

    public void setCnyExchangeRate(BigDecimal cnyExchangeRate) {
        this.cnyExchangeRate = cnyExchangeRate;
    }

    public BigDecimal getCnyAmount() {
        return cnyAmount;
    }

    public void setCnyAmount(BigDecimal cnyAmount) {
        this.cnyAmount = cnyAmount;
    }

    public BigDecimal getUsdExchangeRate() {
        return usdExchangeRate;
    }

    public void setUsdExchangeRate(BigDecimal usdExchangeRate) {
        this.usdExchangeRate = usdExchangeRate;
    }

    public BigDecimal getUsdAmount() {
        return usdAmount;
    }

    public void setUsdAmount(BigDecimal usdAmount) {
        this.usdAmount = usdAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("companyAbbreviation", getCompanyAbbreviation())
                .append("companyCode", getCompanyCode())
                .append("yearTime", getYearTime())
                .append("financialInstitution", getFinancialInstitution())
                .append("productName", getProductName())
                .append("domesticForeign", getDomesticForeign())
                .append("fundNature", getFundNature())
                .append("currency", getCurrency())
                .append("amountMoney", getAmountMoney())
                .append("annualInterestRate", getAnnualInterestRate())
                .append("term", getTerm())
                .append("valueDate", getValueDate())
                .append("dueDate", getDueDate())
                .append("incomeAccountDate", getIncomeAccountDate())
                .append("incomeReceivedAmount", getIncomeReceivedAmount())
                .append("isDue", getIsDue())
                .append("notes", getNotes())
                .append("cnyExchangeRate", getCnyExchangeRate())
                .append("cnyAmount", getCnyAmount())
                .append("usdExchangeRate", getUsdExchangeRate())
                .append("usdAmount", getUsdAmount())
                .toString();
    }
}
