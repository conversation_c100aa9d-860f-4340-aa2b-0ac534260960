package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.akesobio.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.akesobio.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目进度汇总对象 project_progress_summary
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
public class ProjectProgressSummary extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目 */
    @Excel(name = "项目")
    private String project;

    /** 适应症 */
    @Excel(name = "适应症")
    private String indication;

    /** 项目启动时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目启动时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectStartTime;

    /** 项目进度时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目进度时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectScheduleTime;

    /** 项目进度 */
    @Excel(name = "项目进度")
    private String projectSchedule;

    /** 研究内容 */
    @Excel(name = "研究内容")
    private String researchContents;

    /** 病人数 */
    @Excel(name = "病人数")
    private Integer patientNumber;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal contractValue;

    /** 预估实际结算金额 */
    @Excel(name = "预估实际结算金额")
    private BigDecimal settlementAmount;

    /** 付款金额 */
    @Excel(name = "付款金额")
    private BigDecimal paymentAmount;

    /** 结算单价 */
    @Excel(name = "结算单价")
    private BigDecimal settlementUnitPrice;

    /** 合同履约进度 */
    @Excel(name = "合同履约进度")
    private String contractPerformanceProgress;

    /** 预计仍需支出 */
    @Excel(name = "预计仍需支出")
    private BigDecimal expenditureIsExpected;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProject(String project) 
    {
        this.project = project;
    }

    public String getProject() 
    {
        return project;
    }
    public void setIndication(String indication) 
    {
        this.indication = indication;
    }

    public String getIndication() 
    {
        return indication;
    }
    public void setProjectStartTime(Date projectStartTime) 
    {
        this.projectStartTime = projectStartTime;
    }

    public Date getProjectStartTime() 
    {
        return projectStartTime;
    }
    public void setProjectScheduleTime(Date projectScheduleTime) 
    {
        this.projectScheduleTime = projectScheduleTime;
    }

    public Date getProjectScheduleTime() 
    {
        return projectScheduleTime;
    }
    public void setProjectSchedule(String projectSchedule) 
    {
        this.projectSchedule = projectSchedule;
    }

    public String getProjectSchedule() 
    {
        return projectSchedule;
    }
    public void setResearchContents(String researchContents) 
    {
        this.researchContents = researchContents;
    }

    public String getResearchContents() 
    {
        return researchContents;
    }
    public void setPatientNumber(Integer patientNumber) 
    {
        this.patientNumber = patientNumber;
    }

    public Integer getPatientNumber() 
    {
        return patientNumber;
    }
    public void setContractValue(BigDecimal contractValue) 
    {
        this.contractValue = contractValue;
    }

    public BigDecimal getContractValue() 
    {
        return contractValue;
    }
    public void setSettlementAmount(BigDecimal settlementAmount) 
    {
        this.settlementAmount = settlementAmount;
    }

    public BigDecimal getSettlementAmount() 
    {
        return settlementAmount;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setSettlementUnitPrice(BigDecimal settlementUnitPrice) 
    {
        this.settlementUnitPrice = settlementUnitPrice;
    }

    public BigDecimal getSettlementUnitPrice() 
    {
        return settlementUnitPrice;
    }
    public void setContractPerformanceProgress(String contractPerformanceProgress) 
    {
        this.contractPerformanceProgress = contractPerformanceProgress;
    }

    public String getContractPerformanceProgress() 
    {
        return contractPerformanceProgress;
    }
    public void setExpenditureIsExpected(BigDecimal expenditureIsExpected) 
    {
        this.expenditureIsExpected = expenditureIsExpected;
    }

    public BigDecimal getExpenditureIsExpected() 
    {
        return expenditureIsExpected;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("project", getProject())
            .append("indication", getIndication())
            .append("projectStartTime", getProjectStartTime())
            .append("projectScheduleTime", getProjectScheduleTime())
            .append("projectSchedule", getProjectSchedule())
            .append("researchContents", getResearchContents())
            .append("patientNumber", getPatientNumber())
            .append("contractValue", getContractValue())
            .append("settlementAmount", getSettlementAmount())
            .append("paymentAmount", getPaymentAmount())
            .append("settlementUnitPrice", getSettlementUnitPrice())
            .append("contractPerformanceProgress", getContractPerformanceProgress())
            .append("expenditureIsExpected", getExpenditureIsExpected())
            .toString();
    }
}
