package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ClinicalProjectCosts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.CompanyOperatingExpensesMapper;
import com.akesobio.report.basicData.domain.CompanyOperatingExpenses;
import com.akesobio.report.basicData.service.ICompanyOperatingExpensesService;

/**
 * 公司运营费用数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Service
public class CompanyOperatingExpensesServiceImpl implements ICompanyOperatingExpensesService {

    private static final Logger log= LoggerFactory.getLogger(CompanyOperatingExpensesServiceImpl.class);
    @Autowired
    private CompanyOperatingExpensesMapper companyOperatingExpensesMapper;

    /**
     * 查询公司运营费用数据
     *
     * @param id 公司运营费用数据主键
     * @return 公司运营费用数据
     */
    @Override
    public CompanyOperatingExpenses selectCompanyOperatingExpensesById(Long id) {
        return companyOperatingExpensesMapper.selectCompanyOperatingExpensesById(id);
    }

    /**
     * 查询公司运营费用数据列表
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 公司运营费用数据
     */
    @Override
    public List<CompanyOperatingExpenses> selectCompanyOperatingExpensesList(CompanyOperatingExpenses companyOperatingExpenses) {
        return companyOperatingExpensesMapper.selectCompanyOperatingExpensesList(companyOperatingExpenses);
    }

    /**
     * 新增公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    @Override
    public int insertCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses) {
        return companyOperatingExpensesMapper.insertCompanyOperatingExpenses(companyOperatingExpenses);
    }

    /**
     * 修改公司运营费用数据
     *
     * @param companyOperatingExpenses 公司运营费用数据
     * @return 结果
     */
    @Override
    public int updateCompanyOperatingExpenses(CompanyOperatingExpenses companyOperatingExpenses) {
        return companyOperatingExpensesMapper.updateCompanyOperatingExpenses(companyOperatingExpenses);
    }

    /**
     * 批量删除公司运营费用数据
     *
     * @param ids 需要删除的公司运营费用数据主键
     * @return 结果
     */
    @Override
    public int deleteCompanyOperatingExpensesByIds(Long[] ids) {
        return companyOperatingExpensesMapper.deleteCompanyOperatingExpensesByIds(ids);
    }

    /**
     * 删除公司运营费用数据信息
     *
     * @param id 公司运营费用数据主键
     * @return 结果
     */
    @Override
    public int deleteCompanyOperatingExpensesById(Long id) {
        return companyOperatingExpensesMapper.deleteCompanyOperatingExpensesById(id);
    }

    /**
     * 导入公司运营费用数据
     *
     * @param list 公司运营费用数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importCompanyOperatingExpenses(List<CompanyOperatingExpenses> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CompanyOperatingExpenses companyOperatingExpenses : list)
        {
            try {
                companyOperatingExpensesMapper.insertCompanyOperatingExpenses(companyOperatingExpenses);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目 " + companyOperatingExpenses.getProject() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目 " + companyOperatingExpenses.getProject() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
