package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.RfExpenses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.RepaymentDetailsMapper;
import com.akesobio.report.basicData.domain.RepaymentDetails;
import com.akesobio.report.basicData.service.IRepaymentDetailsService;

/**
 * 还款明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class RepaymentDetailsServiceImpl implements IRepaymentDetailsService {

    private  static  final Logger log = LoggerFactory.getLogger(RepaymentDetailsServiceImpl.class);

    @Autowired
    private RepaymentDetailsMapper repaymentDetailsMapper;

    /**
     * 查询还款明细
     *
     * @param id 还款明细主键
     * @return 还款明细
     */
    @Override
    public RepaymentDetails selectRepaymentDetailsById(Integer id) {
        return repaymentDetailsMapper.selectRepaymentDetailsById(id);
    }

    /**
     * 查询还款明细列表
     *
     * @param repaymentDetails 还款明细
     * @return 还款明细
     */
    @Override
    public List<RepaymentDetails> selectRepaymentDetailsList(RepaymentDetails repaymentDetails) {
        return repaymentDetailsMapper.selectRepaymentDetailsList(repaymentDetails);
    }

    /**
     * 新增还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    @Override
    public int insertRepaymentDetails(RepaymentDetails repaymentDetails) {
        return repaymentDetailsMapper.insertRepaymentDetails(repaymentDetails);
    }

    /**
     * 修改还款明细
     *
     * @param repaymentDetails 还款明细
     * @return 结果
     */
    @Override
    public int updateRepaymentDetails(RepaymentDetails repaymentDetails) {
        return repaymentDetailsMapper.updateRepaymentDetails(repaymentDetails);
    }

    /**
     * 批量删除还款明细
     *
     * @param ids 需要删除的还款明细主键
     * @return 结果
     */
    @Override
    public int deleteRepaymentDetailsByIds(Integer[] ids) {
        return repaymentDetailsMapper.deleteRepaymentDetailsByIds(ids);
    }

    /**
     * 删除还款明细信息
     *
     * @param id 还款明细主键
     * @return 结果
     */
    @Override
    public int deleteRepaymentDetailsById(Integer id) {
        return repaymentDetailsMapper.deleteRepaymentDetailsById(id);
    }

    /**
     * 导入还款明细数据
     *
     * @param list            还款明细数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importRepaymentDetails(List<RepaymentDetails> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (RepaymentDetails repaymentDetails : list)
        {
            try {
                repaymentDetailsMapper.insertRepaymentDetails(repaymentDetails);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司名称 " + repaymentDetails.getCompanyName() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司名称 " + repaymentDetails.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
