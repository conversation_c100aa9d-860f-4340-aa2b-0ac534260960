package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.FinancialBriefingBasicData;
import com.akesobio.report.basicData.domain.FinancialIndicatorsBasicData;

/**
 * 财务指标基础数据Service接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface IFinancialIndicatorsBasicDataService {
    /**
     * 查询财务指标基础数据
     *
     * @param id 财务指标基础数据主键
     * @return 财务指标基础数据
     */
    public FinancialIndicatorsBasicData selectFinancialIndicatorsBasicDataById(Integer id);

    /**
     * 查询财务指标基础数据列表
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 财务指标基础数据集合
     */
    public List<FinancialIndicatorsBasicData> selectFinancialIndicatorsBasicDataList(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 新增财务指标基础数据
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 结果
     */
    public int insertFinancialIndicatorsBasicData(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 修改财务指标基础数据
     *
     * @param financialIndicatorsBasicData 财务指标基础数据
     * @return 结果
     */
    public int updateFinancialIndicatorsBasicData(FinancialIndicatorsBasicData financialIndicatorsBasicData);

    /**
     * 批量删除财务指标基础数据
     *
     * @param ids 需要删除的财务指标基础数据主键集合
     * @return 结果
     */
    public int deleteFinancialIndicatorsBasicDataByIds(Integer[] ids);

    /**
     * 删除财务指标基础数据信息
     *
     * @param id 财务指标基础数据主键
     * @return 结果
     */
    public int deleteFinancialIndicatorsBasicDataById(Integer id);


    /**
     * 导入财务指标基础数据
     *
     * @param list 财务指标基础数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importFinancialIndicatorsBasicData(List<FinancialIndicatorsBasicData> list, Boolean isUpdateSupport);
}
