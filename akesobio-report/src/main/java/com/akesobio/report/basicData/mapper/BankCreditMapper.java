package com.akesobio.report.basicData.mapper;

import java.util.List;
import com.akesobio.report.basicData.domain.BankCredit;

/**
 * 银行授信批复情况Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface BankCreditMapper 
{
    /**
     * 查询银行授信批复情况
     * 
     * @param id 银行授信批复情况主键
     * @return 银行授信批复情况
     */
    public BankCredit selectBankCreditById(Integer id);

    /**
     * 查询银行授信批复情况列表
     * 
     * @param bankCredit 银行授信批复情况
     * @return 银行授信批复情况集合
     */
    public List<BankCredit> selectBankCreditList(BankCredit bankCredit);

    /**
     * 新增银行授信批复情况
     * 
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    public int insertBankCredit(BankCredit bankCredit);

    /**
     * 修改银行授信批复情况
     * 
     * @param bankCredit 银行授信批复情况
     * @return 结果
     */
    public int updateBankCredit(BankCredit bankCredit);

    /**
     * 删除银行授信批复情况
     * 
     * @param id 银行授信批复情况主键
     * @return 结果
     */
    public int deleteBankCreditById(Integer id);

    /**
     * 批量删除银行授信批复情况
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBankCreditByIds(Integer[] ids);
}
