package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.ProcurementListMapper;
import com.akesobio.report.basicData.domain.ProcurementList;
import com.akesobio.report.basicData.service.IProcurementListService;

/**
 * 采购大数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
public class ProcurementListServiceImpl implements IProcurementListService {
    private  static final Logger log= LoggerFactory.getLogger(ProcurementListServiceImpl.class);
    @Autowired
    private ProcurementListMapper procurementListMapper;

    /**
     * 查询采购大数据
     *
     * @param id 采购大数据主键
     * @return 采购大数据
     */
    @Override
    public ProcurementList selectProcurementListById(Long id) {
        return procurementListMapper.selectProcurementListById(id);
    }

    /**
     * 查询采购大数据列表
     *
     * @param procurementList 采购大数据
     * @return 采购大数据
     */
    @Override
    public List<ProcurementList> selectProcurementListList(ProcurementList procurementList) {
        return procurementListMapper.selectProcurementListList(procurementList);
    }

    /**
     * 新增采购大数据
     *
     * @param procurementList 采购大数据
     * @return 结果
     */
    @Override
    public int insertProcurementList(ProcurementList procurementList) {
        return procurementListMapper.insertProcurementList(procurementList);
    }

    /**
     * 修改采购大数据
     *
     * @param procurementList 采购大数据
     * @return 结果
     */
    @Override
    public int updateProcurementList(ProcurementList procurementList) {
        return procurementListMapper.updateProcurementList(procurementList);
    }

    /**
     * 批量删除采购大数据
     *
     * @param ids 需要删除的采购大数据主键
     * @return 结果
     */
    @Override
    public int deleteProcurementListByIds(Long[] ids) {
        return procurementListMapper.deleteProcurementListByIds(ids);
    }

    /**
     * 删除采购大数据信息
     *
     * @param id 采购大数据主键
     * @return 结果
     */
    @Override
    public int deleteProcurementListById(Long id) {
        return procurementListMapper.deleteProcurementListById(id);
    }

    /**
     * 导入采购大数据
     *
     * @param list 采购大数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importProcurementList(List<ProcurementList> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProcurementList procurementList : list)
        {
            try {
                procurementListMapper.insertProcurementList(procurementList);
                successNum++;
                successMsg.append("<br/>" + successNum + "、公司主体名称 " + procurementList.getCompanyName() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公司主体名称 " + procurementList.getCompanyName()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
