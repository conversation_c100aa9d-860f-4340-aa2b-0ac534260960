package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import java.util.List;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * RF费用支出报告对象 rf_expenses
 *
 * <AUTHOR>
 * @date 2024-01-12
 */
public class RfExpenses extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 适应症项目号
     */
    @Excel(name = "适应症项目号")
    private String indicationProjectNo;

    /**
     * 适应症
     */
    @Excel(name = "适应症")
    private String indication;

    /**
     * 适应症名称
     */
    @Excel(name = "适应症名称")
    private String indicationName;

    /**
     * 是否肿瘤项目
     */
    @Excel(name = "是否肿瘤项目")
    private String isProject;

    /**
     * 例数
     */
    @Excel(name = "例数")
    private BigDecimal casesNumber;

    /**
     * 金额（万元）
     */
    @Excel(name = "金额(万元)")
    private BigDecimal amount;

    /**
     * 途径
     */
    @Excel(name = "途径 ")
    private String road;

    /**
     * 付款年度
     */
    @Excel(name = "付款年度 ")
    private String paymentYear;

    /**
     * 付款期间
     */
    @Excel(name = "付款期间")
    private String paymentPeriod;

    /**
     * 是否肿瘤项目集合
     */
    @TableField(exist = false)
    private List<String>  isProjectList;

    public List<String> getIsProjectList() {
        return isProjectList;
    }

    public void setIsProjectList(List<String> isProjectList) {
        this.isProjectList = isProjectList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setIndicationProjectNo(String indicationProjectNo) {
        this.indicationProjectNo = indicationProjectNo;
    }

    public String getIndicationProjectNo() {
        return indicationProjectNo;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public String getIndication() {
        return indication;
    }

    public void setIndicationName(String indicationName) {
        this.indicationName = indicationName;
    }

    public String getIndicationName() {
        return indicationName;
    }

    public void setIsProject(String isProject) {
        this.isProject = isProject;
    }

    public String getIsProject() {
        return isProject;
    }

    public void setCasesNumber(BigDecimal casesNumber) {
        this.casesNumber = casesNumber;
    }

    public BigDecimal getCasesNumber() {
        return casesNumber;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setRoad(String road) {
        this.road = road;
    }

    public String getRoad() {
        return road;
    }

    public void setPaymentYear(String paymentYear) {
        this.paymentYear = paymentYear;
    }

    public String getPaymentYear() {
        return paymentYear;
    }

    public void setPaymentPeriod(String paymentPeriod) {
        this.paymentPeriod = paymentPeriod;
    }

    public String getPaymentPeriod() {
        return paymentPeriod;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("indicationProjectNo", getIndicationProjectNo())
                .append("indication", getIndication())
                .append("indicationName", getIndicationName())
                .append("isProject", getIsProject())
                .append("casesNumber", getCasesNumber())
                .append("amount ", getAmount())
                .append("road", getRoad())
                .append("paymentYear", getPaymentYear())
                .append("paymentPeriod", getPaymentPeriod())
                .toString();
    }
}
