package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.common.utils.bean.BeanValidators;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.ProjectCostSummaryMapper;
import com.akesobio.report.basicData.domain.ProjectCostSummary;
import com.akesobio.report.basicData.service.IProjectCostSummaryService;

/**
 * 项目费用归集汇总Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Service
public class ProjectCostSummaryServiceImpl implements IProjectCostSummaryService {

    private  static final Logger log= LoggerFactory.getLogger(ProjectCostSummaryServiceImpl.class);

    @Autowired
    private ProjectCostSummaryMapper projectCostSummaryMapper;

    /**
     * 查询项目费用归集汇总
     *
     * @param id 项目费用归集汇总主键
     * @return 项目费用归集汇总
     */
    @Override
    public ProjectCostSummary selectProjectCostSummaryById(Long id) {
        return projectCostSummaryMapper.selectProjectCostSummaryById(id);
    }

    /**
     * 查询项目费用归集汇总列表
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 项目费用归集汇总
     */
    @Override
    public List<ProjectCostSummary> selectProjectCostSummaryList(ProjectCostSummary projectCostSummary) {
        return projectCostSummaryMapper.selectProjectCostSummaryList(projectCostSummary);
    }

    /**
     * 新增项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    @Override
    public int insertProjectCostSummary(ProjectCostSummary projectCostSummary) {
        return projectCostSummaryMapper.insertProjectCostSummary(projectCostSummary);
    }

    /**
     * 修改项目费用归集汇总
     *
     * @param projectCostSummary 项目费用归集汇总
     * @return 结果
     */
    @Override
    public int updateProjectCostSummary(ProjectCostSummary projectCostSummary) {
        return projectCostSummaryMapper.updateProjectCostSummary(projectCostSummary);
    }

    /**
     * 批量删除项目费用归集汇总
     *
     * @param ids 需要删除的项目费用归集汇总主键
     * @return 结果
     */
    @Override
    public int deleteProjectCostSummaryByIds(Long[] ids) {
        return projectCostSummaryMapper.deleteProjectCostSummaryByIds(ids);
    }

    /**
     * 删除项目费用归集汇总信息
     *
     * @param id 项目费用归集汇总主键
     * @return 结果
     */
    @Override
    public int deleteProjectCostSummaryById(Long id) {
        return projectCostSummaryMapper.deleteProjectCostSummaryById(id);
    }

    /**
     * 导入项目费用归集汇总数据
     *
     * @param list 项目费用归集汇总列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProjectCostSummary(List<ProjectCostSummary> list, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(list) || list.size() == 0){
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProjectCostSummary projectCostSummary : list)
        {
            try {
                projectCostSummaryMapper.insertProjectCostSummary(projectCostSummary);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目号 " + projectCostSummary.getItemNumber() + " 导入成功");

            } catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目号 " + projectCostSummary.getItemNumber()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
