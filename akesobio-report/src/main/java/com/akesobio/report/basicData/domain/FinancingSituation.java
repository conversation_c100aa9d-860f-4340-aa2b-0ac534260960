package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 融资情况对象 financing_situation
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
public class FinancingSituation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 公司简称
     */
    @Excel(name = "公司简称")
    private String companyAbbreviation;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String companyCode;

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer yearTime;

    /**
     * 业务类型
     */
    @Excel(name = "业务类型")
    private String businessType;

    /**
     * 授信敞口
     */
    @Excel(name = "授信敞口")
    private BigDecimal creditExposure;

    /**
     * 合同签订金额
     */
    @Excel(name = "合同签订金额")
    private BigDecimal contractSigningAmount;

    /**
     * 提款情况
     */
    @Excel(name = "提款情况")
    private BigDecimal withdrawalSituation;

    /**
     * 还款情况
     */
    @Excel(name = "还款情况")
    private BigDecimal repaymentSituation;

    /** 贷款余额 */
    @Excel(name = "贷款余额")
    private BigDecimal loanBalance;

    /**
     * 删除状态
     */
    private Integer deleteStatus;

    public BigDecimal getLoanBalance() {
        return loanBalance;
    }

    public void setLoanBalance(BigDecimal loanBalance) {
        this.loanBalance = loanBalance;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAbbreviation(String companyAbbreviation) {
        this.companyAbbreviation = companyAbbreviation;
    }

    public String getCompanyAbbreviation() {
        return companyAbbreviation;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setYearTime(Integer yearTime) {
        this.yearTime = yearTime;
    }

    public Integer getYearTime() {
        return yearTime;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setCreditExposure(BigDecimal creditExposure) {
        this.creditExposure = creditExposure;
    }

    public BigDecimal getCreditExposure() {
        return creditExposure;
    }

    public void setContractSigningAmount(BigDecimal contractSigningAmount) {
        this.contractSigningAmount = contractSigningAmount;
    }

    public BigDecimal getContractSigningAmount() {
        return contractSigningAmount;
    }

    public void setWithdrawalSituation(BigDecimal withdrawalSituation) {
        this.withdrawalSituation = withdrawalSituation;
    }

    public BigDecimal getWithdrawalSituation() {
        return withdrawalSituation;
    }

    public void setRepaymentSituation(BigDecimal repaymentSituation) {
        this.repaymentSituation = repaymentSituation;
    }

    public BigDecimal getRepaymentSituation() {
        return repaymentSituation;
    }

    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("companyAbbreviation", getCompanyAbbreviation())
                .append("companyCode", getCompanyCode())
                .append("yearTime", getYearTime())
                .append("businessType", getBusinessType())
                .append("creditExposure", getCreditExposure())
                .append("contractSigningAmount", getContractSigningAmount())
                .append("withdrawalSituation", getWithdrawalSituation())
                .append("repaymentSituation", getRepaymentSituation())
                .append("loanBalance", getLoanBalance())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
