package com.akesobio.report.basicData.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.basicData.domain.BankCredit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.basicData.domain.FinancialBriefingBasicData;
import com.akesobio.report.basicData.service.IFinancialBriefingBasicDataService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 财务简报基础数据Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@RestController
@RequestMapping("/basicData/financialBriefingBasicData")
public class FinancialBriefingBasicDataController extends BaseController {
    @Autowired
    private IFinancialBriefingBasicDataService financialBriefingBasicDataService;

    /**
     * 查询财务简报基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialBriefingBasicData financialBriefingBasicData) {
        startPage();
        List<FinancialBriefingBasicData> list = financialBriefingBasicDataService.selectFinancialBriefingBasicDataList(financialBriefingBasicData);
        return getDataTable(list);
    }

    /**
     * 导出财务简报基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:export')")
    @Log(title = "财务简报基础数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialBriefingBasicData financialBriefingBasicData) {
        List<FinancialBriefingBasicData> list = financialBriefingBasicDataService.selectFinancialBriefingBasicDataList(financialBriefingBasicData);
        ExcelUtil<FinancialBriefingBasicData> util = new ExcelUtil<FinancialBriefingBasicData>(FinancialBriefingBasicData.class);
        util.exportExcel(response, list, "财务简报基础数据数据");
    }

    /**
     * 获取财务简报基础数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(financialBriefingBasicDataService.selectFinancialBriefingBasicDataById(id));
    }

    /**
     * 新增财务简报基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:add')")
    @Log(title = "财务简报基础数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialBriefingBasicData financialBriefingBasicData) {
        return toAjax(financialBriefingBasicDataService.insertFinancialBriefingBasicData(financialBriefingBasicData));
    }

    /**
     * 修改财务简报基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:edit')")
    @Log(title = "财务简报基础数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancialBriefingBasicData financialBriefingBasicData) {
        return toAjax(financialBriefingBasicDataService.updateFinancialBriefingBasicData(financialBriefingBasicData));
    }

    /**
     * 删除财务简报基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:remove')")
    @Log(title = "财务简报基础数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(financialBriefingBasicDataService.deleteFinancialBriefingBasicDataByIds(ids));
    }

    /**
     * 导入财务简报基础数据
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:import')")
    @Log(title = "财务简报基础数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<FinancialBriefingBasicData> util = new ExcelUtil<FinancialBriefingBasicData>(FinancialBriefingBasicData.class);
        List<FinancialBriefingBasicData> list = util.importExcel(file.getInputStream());
        String message = financialBriefingBasicDataService.importFinancialBriefingBasicData(list,updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 模板下载
     * @param response
     */
    @PreAuthorize("@ss.hasPermi('basicData:financialBriefingBasicData:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FinancialBriefingBasicData> util = new ExcelUtil<FinancialBriefingBasicData>(FinancialBriefingBasicData.class);
        util.importTemplateExcel(response, "财务简报基础数据");
    }
}
