package com.akesobio.report.basicData.service.impl;

import java.util.List;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.basicData.domain.GroupFinancialSituation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.basicData.mapper.LoanUsageMapper;
import com.akesobio.report.basicData.domain.LoanUsage;
import com.akesobio.report.basicData.service.ILoanUsageService;

/**
 * 贷款使用情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@Service
public class LoanUsageServiceImpl implements ILoanUsageService {

    private static final Logger log = LoggerFactory.getLogger(LoanUsageServiceImpl.class);

    @Autowired
    private LoanUsageMapper loanUsageMapper;

    /**
     * 查询贷款使用情况
     *
     * @param id 贷款使用情况主键
     * @return 贷款使用情况
     */
    @Override
    public LoanUsage selectLoanUsageById(Integer id) {
        return loanUsageMapper.selectLoanUsageById(id);
    }

    /**
     * 查询贷款使用情况列表
     *
     * @param loanUsage 贷款使用情况
     * @return 贷款使用情况
     */
    @Override
    public List<LoanUsage> selectLoanUsageList(LoanUsage loanUsage) {
        return loanUsageMapper.selectLoanUsageList(loanUsage);
    }

    /**
     * 新增贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    @Override
    public int insertLoanUsage(LoanUsage loanUsage) {
        return loanUsageMapper.insertLoanUsage(loanUsage);
    }

    /**
     * 修改贷款使用情况
     *
     * @param loanUsage 贷款使用情况
     * @return 结果
     */
    @Override
    public int updateLoanUsage(LoanUsage loanUsage) {
        return loanUsageMapper.updateLoanUsage(loanUsage);
    }

    /**
     * 批量删除贷款使用情况
     *
     * @param ids 需要删除的贷款使用情况主键
     * @return 结果
     */
    @Override
    public int deleteLoanUsageByIds(Integer[] ids) {
        return loanUsageMapper.deleteLoanUsageByIds(ids);
    }

    /**
     * 删除贷款使用情况信息
     *
     * @param id 贷款使用情况主键
     * @return 结果
     */
    @Override
    public int deleteLoanUsageById(Integer id) {
        return loanUsageMapper.deleteLoanUsageById(id);
    }

    /**
     * 导入贷款使用情况数据
     *
     * @param list 贷款使用情况数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importLoanUsage(List<LoanUsage> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (LoanUsage loanUsage : list) {
            try {
                loanUsageMapper.insertLoanUsage(loanUsage);
                successNum++;
                successMsg.append("<br/>" + successNum + "、贷款主体  " + loanUsage.getCompanyName() + " 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、贷款主体  " + loanUsage.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
