package com.akesobio.report.basicData.service;

import java.util.List;

import com.akesobio.report.basicData.domain.ProjectCostSummary;
import com.akesobio.report.basicData.domain.ProjectProgressSummary;

/**
 * 项目进度汇总Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface IProjectProgressSummaryService {
    /**
     * 查询项目进度汇总
     * 
     * @param id 项目进度汇总主键
     * @return 项目进度汇总
     */
    public ProjectProgressSummary selectProjectProgressSummaryById(Long id);

    /**
     * 查询项目进度汇总列表
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 项目进度汇总集合
     */
    public List<ProjectProgressSummary> selectProjectProgressSummaryList(ProjectProgressSummary projectProgressSummary);

    /**
     * 新增项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    public int insertProjectProgressSummary(ProjectProgressSummary projectProgressSummary);

    /**
     * 修改项目进度汇总
     * 
     * @param projectProgressSummary 项目进度汇总
     * @return 结果
     */
    public int updateProjectProgressSummary(ProjectProgressSummary projectProgressSummary);

    /**
     * 批量删除项目进度汇总
     * 
     * @param ids 需要删除的项目进度汇总主键集合
     * @return 结果
     */
    public int deleteProjectProgressSummaryByIds(Long[] ids);

    /**
     * 删除项目进度汇总信息
     * 
     * @param id 项目进度汇总主键
     * @return 结果
     */
    public int deleteProjectProgressSummaryById(Long id);

    /**
     * 导入项目进度汇总数据
     *
     * @param list 项目进度汇总列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importProjectProgressSummary(List<ProjectProgressSummary> list, Boolean isUpdateSupport, String operName);


}
