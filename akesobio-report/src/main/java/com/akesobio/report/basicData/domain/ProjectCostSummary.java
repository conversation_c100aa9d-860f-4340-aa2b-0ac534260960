package com.akesobio.report.basicData.domain;

import java.math.BigDecimal;
import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 项目费用归集汇总对象 project_cost_summary
 * 
 * <AUTHOR>
 * @date 2023-12-07
 */
public class ProjectCostSummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目阶段 */
    @Excel(name = "项目阶段")
    private String projectStage;

    /** 药品管线 */
    @Excel(name = "药品管线")
    private String drugPipeline;

    /** 项目号 */
    @Excel(name = "项目号")
    private String itemNumber;

    /** 计划入组数 */
    @Excel(name = "计划入组数")
    private BigDecimal planNumber ;

    /** 入组病例 */
    @Excel(name = "入组病例")
    private BigDecimal casesEnrolled;

    /** 平均单例成本 */
    @Excel(name = "平均单例成本")
    private BigDecimal meanSingletonCosts;

    /** 小计 */
    @Excel(name = "小计")
    private BigDecimal subtotal;

    /** 直接成本 */
    @Excel(name = "直接成本")
    private BigDecimal directCost;

    /** RF */
    @Excel(name = "RF")
    private BigDecimal rf;

    /** 人工成本 */
    @Excel(name = "人工成本")
    private BigDecimal labourCost;

    /** 材料成本 */
    @Excel(name = "材料成本")
    private BigDecimal materialCost;

    /** 运行成本 */
    @Excel(name = "运行成本")
    private BigDecimal runningCost;

    /** 折旧及摊销 */
    @Excel(name = "折旧及摊销")
    private BigDecimal depreciationAndAmortization;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectStage(String projectStage) 
    {
        this.projectStage = projectStage;
    }

    public String getProjectStage() 
    {
        return projectStage;
    }
    public void setDrugPipeline(String drugPipeline) 
    {
        this.drugPipeline = drugPipeline;
    }

    public String getDrugPipeline() 
    {
        return drugPipeline;
    }
    public void setItemNumber(String itemNumber) 
    {
        this.itemNumber = itemNumber;
    }

    public String getItemNumber() 
    {
        return itemNumber;
    }
    public void setPlanNumber (BigDecimal planNumber ) 
    {
        this.planNumber  = planNumber ;
    }

    public BigDecimal getPlanNumber () 
    {
        return planNumber ;
    }
    public void setCasesEnrolled(BigDecimal casesEnrolled) 
    {
        this.casesEnrolled = casesEnrolled;
    }

    public BigDecimal getCasesEnrolled() 
    {
        return casesEnrolled;
    }
    public void setMeanSingletonCosts(BigDecimal meanSingletonCosts) 
    {
        this.meanSingletonCosts = meanSingletonCosts;
    }

    public BigDecimal getMeanSingletonCosts() 
    {
        return meanSingletonCosts;
    }
    public void setSubtotal(BigDecimal subtotal) 
    {
        this.subtotal = subtotal;
    }

    public BigDecimal getSubtotal() 
    {
        return subtotal;
    }
    public void setDirectCost(BigDecimal directCost) 
    {
        this.directCost = directCost;
    }

    public BigDecimal getDirectCost() 
    {
        return directCost;
    }
    public void setRf(BigDecimal rf) 
    {
        this.rf = rf;
    }

    public BigDecimal getRf() 
    {
        return rf;
    }
    public void setLabourCost(BigDecimal labourCost) 
    {
        this.labourCost = labourCost;
    }

    public BigDecimal getLabourCost() 
    {
        return labourCost;
    }
    public void setMaterialCost(BigDecimal materialCost) 
    {
        this.materialCost = materialCost;
    }

    public BigDecimal getMaterialCost() 
    {
        return materialCost;
    }
    public void setRunningCost(BigDecimal runningCost) 
    {
        this.runningCost = runningCost;
    }

    public BigDecimal getRunningCost() 
    {
        return runningCost;
    }
    public void setDepreciationAndAmortization(BigDecimal depreciationAndAmortization) 
    {
        this.depreciationAndAmortization = depreciationAndAmortization;
    }

    public BigDecimal getDepreciationAndAmortization() 
    {
        return depreciationAndAmortization;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectStage", getProjectStage())
            .append("drugPipeline", getDrugPipeline())
            .append("itemNumber", getItemNumber())
            .append("planNumber ", getPlanNumber ())
            .append("casesEnrolled", getCasesEnrolled())
            .append("meanSingletonCosts", getMeanSingletonCosts())
            .append("subtotal", getSubtotal())
            .append("directCost", getDirectCost())
            .append("rf", getRf())
            .append("labourCost", getLabourCost())
            .append("materialCost", getMaterialCost())
            .append("runningCost", getRunningCost())
            .append("depreciationAndAmortization", getDepreciationAndAmortization())
            .toString();
    }
}
