package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.clinical.domain.EkpLccgwzTrial;
import com.akesobio.report.clinical.service.IEkpLccgwzTrialService;
import com.akesobio.report.clinical.vo.excel.EkpLccgwzTrialExcelVo;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;

import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床试验物资2024Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@RestController
@RequestMapping("/clinical/trial")
public class EkpLccgwzTrialController extends BaseController
{
    @Resource
    private IEkpLccgwzTrialService ekpLccgwzTrialService;

    /**
     * 查询临床试验物资2024列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:trial:list')")
    @GetMapping("/list")
    public TableDataInfo list(EkpLccgwzTrial ekpLccgwzTrial)
    {
        startPage();
        List<EkpLccgwzTrial> list = ekpLccgwzTrialService.selectEkpLccgwzTrialList(ekpLccgwzTrial);
        return getDataTable(list);
    }

    /**
     * 导出临床试验物资2024列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:trial:export')")
    @Log(title = "临床试验物资2024", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EkpLccgwzTrial ekpLccgwzTrial)
    {
        List<EkpLccgwzTrial> list = ekpLccgwzTrialService.selectEkpLccgwzTrialList(ekpLccgwzTrial);
        List<EkpLccgwzTrialExcelVo> excelList = list.stream().filter(item -> StringUtils.hasText(item.getBasicInfo())).map(item -> {
            EkpLccgwzTrialExcelVo vo = new EkpLccgwzTrialExcelVo();
            BeanUtils.copyProperties(item, vo);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            if(item.getApplicationDate()!= null)
                vo.setApplicationDate(sdf.format(item.getApplicationDate()));
            if(item.getExpectedDeliveryDate()!= null)
                vo.setExpectedDeliveryDate(sdf.format(item.getExpectedDeliveryDate()));
            if(item.getProductionDate()!= null)
                vo.setProductionDate(sdf.format(item.getProductionDate()));
            if(item.getFlowEndDate()!= null)
                vo.setFlowEndDate(sdf.format(item.getFlowEndDate()));
            if (item.getMailingTime() != null)
                vo.setMailingTime(sdf.format(item.getMailingTime()));
            vo.setModesOfSupplyHandler("");
            vo.setMailingTime("");
            return vo;
        }).collect(Collectors.toList());
        ExcelUtil<EkpLccgwzTrialExcelVo> util = new ExcelUtil<EkpLccgwzTrialExcelVo>(EkpLccgwzTrialExcelVo.class);
        util.exportExcel(response, excelList, "临床试验物资2024数据");
    }

    /**
     * 获取临床试验物资2024详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:trial:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(ekpLccgwzTrialService.selectEkpLccgwzTrialById(id));
    }


}
