package com.akesobio.report.clinical.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.page.PageDomain;
import com.akesobio.common.core.page.TableSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.Fd72ClinicalExpenseReimbursement;
import com.akesobio.report.clinical.service.IFd72ClinicalExpenseReimbursementService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * FD72临床费用报销Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/clinical/fd72ClinicalExpenseReimbursement")
public class Fd72ClinicalExpenseReimbursementController extends BaseController {
    @Autowired
    private IFd72ClinicalExpenseReimbursementService fd72ClinicalExpenseReimbursementService;

    /**
     * 查询FD72临床费用报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<Fd72ClinicalExpenseReimbursement> list = fd72ClinicalExpenseReimbursementService.selectFd72ClinicalExpenseReimbursementList(fd72ClinicalExpenseReimbursement);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出FD72临床费用报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:export')")
    @Log(title = "FD72临床费用报销", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement) {
        List<Fd72ClinicalExpenseReimbursement> list = fd72ClinicalExpenseReimbursementService.selectFd72ClinicalExpenseReimbursementList(fd72ClinicalExpenseReimbursement);
        ExcelUtil<Fd72ClinicalExpenseReimbursement> util = new ExcelUtil<Fd72ClinicalExpenseReimbursement>(Fd72ClinicalExpenseReimbursement.class);
        util.exportExcel(response, list, "FD72临床费用报销数据");
    }

    /**
     * 获取FD72临床费用报销详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(fd72ClinicalExpenseReimbursementService.selectFd72ClinicalExpenseReimbursementById(id));
    }

    /**
     * 新增FD72临床费用报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:add')")
    @Log(title = "FD72临床费用报销", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement) {
        return toAjax(fd72ClinicalExpenseReimbursementService.insertFd72ClinicalExpenseReimbursement(fd72ClinicalExpenseReimbursement));
    }

    /**
     * 修改FD72临床费用报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:edit')")
    @Log(title = "FD72临床费用报销", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement) {
        return toAjax(fd72ClinicalExpenseReimbursementService.updateFd72ClinicalExpenseReimbursement(fd72ClinicalExpenseReimbursement));
    }

    /**
     * 删除FD72临床费用报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd72ClinicalExpenseReimbursement:remove')")
    @Log(title = "FD72临床费用报销", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(fd72ClinicalExpenseReimbursementService.deleteFd72ClinicalExpenseReimbursementByIds(ids));
    }
}
