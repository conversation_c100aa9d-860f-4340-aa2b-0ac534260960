package com.akesobio.report.clinical.controller.hr;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.hr.ClinicalDepartHr;
import com.akesobio.report.clinical.domain.hr.ClinicalEntryHr;
import com.akesobio.report.clinical.domain.hr.ClinicalTransferHr;
import com.akesobio.report.clinical.service.ClinicalDepartHrService;
import com.akesobio.report.clinical.service.ClinicalEntryHrService;
import com.akesobio.report.clinical.service.ClinicalTransferHrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Controller
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ClinicalHr")
public class ClinicalHrController extends BaseController {

    @Autowired
    private ClinicalEntryHrService clinicalEntryHrService;

    @Autowired
    private ClinicalDepartHrService clinicalDepartHrService;

    @Autowired
    private ClinicalTransferHrService clinicalTransferHrService;

    /**
     * 入职
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:entry:list')")
    @GetMapping("/entry/list")
    public TableDataInfo list(ClinicalEntryHr o) {
        startPage();
        List<ClinicalEntryHr> list = clinicalEntryHrService.queryClinicalEntryHrList(o);
        return getDataTable(list);
    }

    /**
     * 导出 入职
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:entry:export')")
    @Log(title = "入职申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/entry/export")
    public void export(HttpServletResponse response, ClinicalEntryHr o) throws Exception {
        List<ClinicalEntryHr> list = clinicalEntryHrService.queryClinicalEntryHrList(o);
        ExcelUtils.downloadAvoidStyle(response,list,"入职申请单");
    }
    /*==============================================================================================================*/

    /**
     * 离职
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:depart:list')")
    @GetMapping("/depart/list")
    public TableDataInfo list(ClinicalDepartHr o) {
        startPage();
        List<ClinicalDepartHr> list = clinicalDepartHrService.queryClinicalDepartHrList(o);
        return getDataTable(list);
    }

    /**
     * 导出 离职
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:depart:export')")
    @Log(title = "离职申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/depart/export")
    public void export(HttpServletResponse response, ClinicalDepartHr o) throws Exception {
        List<ClinicalDepartHr> list = clinicalDepartHrService.queryClinicalDepartHrList(o);
        ExcelUtils.downloadAvoidStyle(response,list,"离职申请单");
    }
    /*==============================================================================================================*/

    /**
     * 调动
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:transfer:list')")
    @GetMapping("/transfer/list")
    public TableDataInfo list(ClinicalTransferHr o) {
        startPage();
        List<ClinicalTransferHr> list = clinicalTransferHrService.queryClinicalTransferHrList(o);
        return getDataTable(list);
    }

    /**
     * 导出 调动
     */
    @PreAuthorize("@ss.hasPermi('ClinicalHr:transfer:export')")
    @Log(title = "调动申请单", businessType = BusinessType.EXPORT)
    @PostMapping("/transfer/export")
    public void export(HttpServletResponse response, ClinicalTransferHr o) throws Exception {
        List<ClinicalTransferHr> list = clinicalTransferHrService.queryClinicalTransferHrList(o);
        ExcelUtils.downloadAvoidStyle(response,list,"调动申请单");
    }




}