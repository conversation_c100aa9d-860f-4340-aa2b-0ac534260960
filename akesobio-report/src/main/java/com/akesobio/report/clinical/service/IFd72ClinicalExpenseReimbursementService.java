package com.akesobio.report.clinical.service;

import java.util.List;

import com.akesobio.report.clinical.domain.Fd72ClinicalExpenseReimbursement;

/**
 * FD72临床费用报销Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IFd72ClinicalExpenseReimbursementService {
    /**
     * 查询FD72临床费用报销
     *
     * @param id FD72临床费用报销主键
     * @return FD72临床费用报销
     */
    public Fd72ClinicalExpenseReimbursement selectFd72ClinicalExpenseReimbursementById(Integer id);

    /**
     * 查询FD72临床费用报销列表
     *
     * @param fd72ClinicalExpenseReimbursement FD72临床费用报销
     * @return FD72临床费用报销集合
     */
    public List<Fd72ClinicalExpenseReimbursement> selectFd72ClinicalExpenseReimbursementList(Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement);

    /**
     * 新增FD72临床费用报销
     *
     * @param fd72ClinicalExpenseReimbursement FD72临床费用报销
     * @return 结果
     */
    public int insertFd72ClinicalExpenseReimbursement(Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement);

    /**
     * 修改FD72临床费用报销
     *
     * @param fd72ClinicalExpenseReimbursement FD72临床费用报销
     * @return 结果
     */
    public int updateFd72ClinicalExpenseReimbursement(Fd72ClinicalExpenseReimbursement fd72ClinicalExpenseReimbursement);

    /**
     * 批量删除FD72临床费用报销
     *
     * @param ids 需要删除的FD72临床费用报销主键集合
     * @return 结果
     */
    public int deleteFd72ClinicalExpenseReimbursementByIds(Integer[] ids);

    /**
     * 删除FD72临床费用报销信息
     *
     * @param id FD72临床费用报销主键
     * @return 结果
     */
    public int deleteFd72ClinicalExpenseReimbursementById(Integer id);
}
