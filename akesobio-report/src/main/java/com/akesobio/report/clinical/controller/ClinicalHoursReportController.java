package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.ClinicalHoursReport;
import com.akesobio.report.clinical.service.IClinicalHoursReportService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床工时汇报明细Controller
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/clinical/clinicalHoursReport")
@Slf4j
public class ClinicalHoursReportController extends BaseController {
    @Autowired
    private IClinicalHoursReportService clinicalHoursReportService;

    /**
     * 查询临床工时汇报明细列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalHoursReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalHoursReport clinicalHoursReport) {
        startPage();
        long a = System.currentTimeMillis();
        log.info("A=" + a);
        List<ClinicalHoursReport> list = clinicalHoursReportService.selectClinicalHoursReportList(clinicalHoursReport);
        long b = System.currentTimeMillis();
        log.info("B=" + b);
        long c = Math.abs(b - a);
        log.info("C=" + c);
        return getDataTable(list);
    }

    /**
     * 导出临床工时汇报明细列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalHoursReport:export')")
    @Log(title = "临床工时汇报明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClinicalHoursReport clinicalHoursReport) {
        List<ClinicalHoursReport> list = clinicalHoursReportService.selectClinicalHoursReportList(clinicalHoursReport);
        ExcelUtil<ClinicalHoursReport> util = new ExcelUtil<ClinicalHoursReport>(ClinicalHoursReport.class);
        util.exportExcel(response, list, "临床工时汇报明细数据");
    }
}
