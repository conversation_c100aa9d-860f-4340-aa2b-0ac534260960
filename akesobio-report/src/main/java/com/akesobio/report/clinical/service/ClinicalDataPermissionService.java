package com.akesobio.report.clinical.service;

import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.clinical.domain.permission.ClinicalDataPermission;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Service
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface ClinicalDataPermissionService {
    List<ClinicalDataPermission> queryClinicalDataPermissionList(ClinicalDataPermission o);

    ClinicalDataPermission queryClinicalDataPermissionById(String id);

    AjaxResult insertClinicalDataPermission(ClinicalDataPermission o);

    AjaxResult insertClinicalDataPermissionList(MultipartFile file);

    AjaxResult updateClinicalDataPermissionById(ClinicalDataPermission o);

    AjaxResult updateByNameJobNumber(ClinicalDataPermission o);
}