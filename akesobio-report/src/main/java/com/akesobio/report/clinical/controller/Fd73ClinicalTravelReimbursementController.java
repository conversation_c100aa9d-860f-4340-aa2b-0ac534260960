package com.akesobio.report.clinical.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.page.PageDomain;
import com.akesobio.common.core.page.TableSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.Fd73ClinicalTravelReimbursement;
import com.akesobio.report.clinical.service.IFd73ClinicalTravelReimbursementService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * FD73临床差旅报销Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/clinical/fd73ClinicalTravelReimbursement")
public class Fd73ClinicalTravelReimbursementController extends BaseController {
    @Autowired
    private IFd73ClinicalTravelReimbursementService fd73ClinicalTravelReimbursementService;

    /**
     * 查询FD73临床差旅报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<Fd73ClinicalTravelReimbursement> list = fd73ClinicalTravelReimbursementService.selectFd73ClinicalTravelReimbursementList(fd73ClinicalTravelReimbursement);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出FD73临床差旅报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:export')")
    @Log(title = "FD73临床差旅报销", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement) {
        List<Fd73ClinicalTravelReimbursement> list = fd73ClinicalTravelReimbursementService.selectFd73ClinicalTravelReimbursementList(fd73ClinicalTravelReimbursement);
        ExcelUtil<Fd73ClinicalTravelReimbursement> util = new ExcelUtil<Fd73ClinicalTravelReimbursement>(Fd73ClinicalTravelReimbursement.class);
        util.exportExcel(response, list, "FD73临床差旅报销数据");
    }

    /**
     * 获取FD73临床差旅报销详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(fd73ClinicalTravelReimbursementService.selectFd73ClinicalTravelReimbursementById(id));
    }

    /**
     * 新增FD73临床差旅报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:add')")
    @Log(title = "FD73临床差旅报销", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement) {
        return toAjax(fd73ClinicalTravelReimbursementService.insertFd73ClinicalTravelReimbursement(fd73ClinicalTravelReimbursement));
    }

    /**
     * 修改FD73临床差旅报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:edit')")
    @Log(title = "FD73临床差旅报销", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement) {
        return toAjax(fd73ClinicalTravelReimbursementService.updateFd73ClinicalTravelReimbursement(fd73ClinicalTravelReimbursement));
    }

    /**
     * 删除FD73临床差旅报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd73ClinicalTravelReimbursement:remove')")
    @Log(title = "FD73临床差旅报销", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(fd73ClinicalTravelReimbursementService.deleteFd73ClinicalTravelReimbursementByIds(ids));
    }
}
