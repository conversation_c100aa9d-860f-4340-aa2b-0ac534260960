package com.akesobio.report.clinical.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.ExternalSystemPermissions;
import com.akesobio.report.clinical.service.ExternalSystemPermissionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.akesobio.common.utils.PageUtils.startPage;

/**
 * Controller 外部系统权限管理申请
 *
 * <AUTHOR>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@RestController
@RequestMapping("/ExternalSystemPermissions")
public class ExternalSystemPermissionsController extends BaseController {

    @Autowired
    private ExternalSystemPermissionsService externalSystemPermissionsService;

    @PreAuthorize("@ss.hasPermi('ExternalSystemPermissions:query:list')")
    @GetMapping("/query/list")
    public TableDataInfo list(ExternalSystemPermissions o) {
        startPage();
        List<ExternalSystemPermissions> list = externalSystemPermissionsService.queryExternalSystemPermissionsList(o);
        return getDataTable(list);
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('ExternalSystemPermissions:query:export')")
    @Log(title = "外部系统权限管理申请", businessType = BusinessType.EXPORT)
    @PostMapping("/query/export")
    public void export(HttpServletResponse response, ExternalSystemPermissions o) throws Exception {
        List<ExternalSystemPermissions> list = externalSystemPermissionsService.queryExternalSystemPermissionsList(o);
        ExcelUtils.downloadContainStyle(response,list,"外部系统权限管理申请");
    }
}