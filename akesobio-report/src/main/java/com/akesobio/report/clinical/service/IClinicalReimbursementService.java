package com.akesobio.report.clinical.service;

import java.util.List;

import com.akesobio.report.clinical.domain.ClinicalReimbursement;

/**
 * 临床报销Service接口
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
public interface IClinicalReimbursementService {
    /**
     * 查询临床报销
     *
     * @param id 临床报销主键
     * @return 临床报销
     */
    public ClinicalReimbursement selectClinicalReimbursementById(Integer id);

    /**
     * 查询临床报销列表
     *
     * @param clinicalReimbursement 临床报销
     * @return 临床报销集合
     */
    public List<ClinicalReimbursement> selectClinicalReimbursementList(ClinicalReimbursement clinicalReimbursement);

    /**
     * 新增临床报销
     *
     * @param clinicalReimbursement 临床报销
     * @return 结果
     */
    public int insertClinicalReimbursement(ClinicalReimbursement clinicalReimbursement);

    /**
     * 修改临床报销
     *
     * @param clinicalReimbursement 临床报销
     * @return 结果
     */
    public int updateClinicalReimbursement(ClinicalReimbursement clinicalReimbursement);

    /**
     * 批量删除临床报销
     *
     * @param ids 需要删除的临床报销主键集合
     * @return 结果
     */
    public int deleteClinicalReimbursementByIds(Integer[] ids);

    /**
     * 删除临床报销信息
     *
     * @param id 临床报销主键
     * @return 结果
     */
    public int deleteClinicalReimbursementById(Integer id);
}
