package com.akesobio.report.clinical.service;

import java.util.List;

import com.akesobio.report.clinical.domain.Fd74ClinicalConferenceReimbursement;

/**
 * FD74临床会议报销
 * <p>
 * Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IFd74ClinicalConferenceReimbursementService {
    /**
     * 查询FD74临床会议报销
     *
     * @param id FD74临床会议报销主键
     * @return FD74临床会议报销
     */
    public Fd74ClinicalConferenceReimbursement selectFd74ClinicalConferenceReimbursementById(Integer id);

    /**
     * 查询FD74临床会议报销列表
     *
     * @param fd74ClinicalConferenceReimbursement FD74临床会议报销
     * @return FD74临床会议报销
     * <p>
     * 集合
     */
    public List<Fd74ClinicalConferenceReimbursement> selectFd74ClinicalConferenceReimbursementList(Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement);

    /**
     * 新增FD74临床会议报销
     *
     * @param fd74ClinicalConferenceReimbursement FD74临床会议报销
     * @return 结果
     */
    public int insertFd74ClinicalConferenceReimbursement(Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement);

    /**
     * 修改FD74临床会议报销
     *
     * @param fd74ClinicalConferenceReimbursement FD74临床会议报销
     * @return 结果
     */
    public int updateFd74ClinicalConferenceReimbursement(Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement);

    /**
     * 批量删除FD74临床会议报销
     *
     * @param ids 需要删除的FD74临床会议报销主键集合
     * @return 结果
     */
    public int deleteFd74ClinicalConferenceReimbursementByIds(Integer[] ids);

    /**
     * 删除FD74临床会议报销信息
     * @param id FD74临床会议报销主键
     * @return 结果
     */
    public int deleteFd74ClinicalConferenceReimbursementById(Integer id);
}
