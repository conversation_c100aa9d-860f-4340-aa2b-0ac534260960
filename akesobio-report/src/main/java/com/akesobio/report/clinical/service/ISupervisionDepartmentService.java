package com.akesobio.report.clinical.service;

import java.util.List;
import com.akesobio.report.clinical.domain.SupervisionDepartment;

/**
 * 每周汇报-监查部Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-25
 */
public interface ISupervisionDepartmentService 
{


    /**
     * 查询每周汇报-监查部列表
     * 
     * @param supervisionDepartment 每周汇报-监查部
     * @return 每周汇报-监查部集合
     */
    public List<SupervisionDepartment> selectSupervisionDepartmentList(SupervisionDepartment supervisionDepartment);

}
