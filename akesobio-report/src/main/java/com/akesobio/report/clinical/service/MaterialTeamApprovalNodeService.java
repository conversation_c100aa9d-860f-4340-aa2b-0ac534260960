package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.MaterialTeamApprovalNode;

import java.util.List;

/**
 * Service 物资组表单审批节点合集
 *
 * @Package_Name
 * @<PERSON> <PERSON>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface MaterialTeamApprovalNodeService {
    List<MaterialTeamApprovalNode> queryMaterialTeamApprovalNodeList(MaterialTeamApprovalNode o);
    List<MaterialTeamApprovalNode> queryMaterialTeamApprovalNodeListALL(MaterialTeamApprovalNode o);
}