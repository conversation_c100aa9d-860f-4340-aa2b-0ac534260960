package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.SupervisionDepartment;
import com.akesobio.report.clinical.service.ISupervisionDepartmentService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 每周汇报-监查部Controller
 * 
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/clinical/supervisionDepartment")
public class SupervisionDepartmentController extends BaseController
{
    @Autowired
    private ISupervisionDepartmentService supervisionDepartmentService;

    /**
     * 查询每周汇报-监查部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:supervisionDepartment:list')")
    @GetMapping("/list")
    public TableDataInfo list(SupervisionDepartment supervisionDepartment)
    {
        startPage();
        List<SupervisionDepartment> list = supervisionDepartmentService.selectSupervisionDepartmentList(supervisionDepartment);
        return getDataTable(list);
    }

    /**
     * 导出每周汇报-监查部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:supervisionDepartment:export')")
    @Log(title = "每周汇报-监查部", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupervisionDepartment supervisionDepartment)
    {
        List<SupervisionDepartment> list = supervisionDepartmentService.selectSupervisionDepartmentList(supervisionDepartment);
        ExcelUtil<SupervisionDepartment> util = new ExcelUtil<SupervisionDepartment>(SupervisionDepartment.class);
        util.exportExcel(response, list, "每周汇报-监查部数据");
    }
}
