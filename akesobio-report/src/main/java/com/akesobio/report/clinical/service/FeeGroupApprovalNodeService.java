package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.FeeGroupApprovalNode;

import java.util.List;

/**
 * Service 费用组表单审批节点合集
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface FeeGroupApprovalNodeService {
    List<FeeGroupApprovalNode> queryFeeGroupApprovalNodeList(FeeGroupApprovalNode o);
    List<FeeGroupApprovalNode> queryFeeGroupApprovalNodeListALL(FeeGroupApprovalNode o);
}