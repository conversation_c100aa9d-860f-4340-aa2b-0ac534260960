package com.akesobio.report.clinical.service;

import java.util.List;

import com.akesobio.report.clinical.domain.ConfidentialityAgreementApproval;

/**
 * 保密协议审批Service接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IConfidentialityAgreementApprovalService {

    /**
     * 查询保密协议审批列表
     *
     * @param confidentialityAgreementApproval 保密协议审批
     * @return 保密协议审批集合
     */
    public List<ConfidentialityAgreementApproval> selectConfidentialityAgreementApprovalList(ConfidentialityAgreementApproval confidentialityAgreementApproval);

    /**
     * 查询保密协议审批列表
     *
     * @param confidentialityAgreementApproval 保密协议审批
     * @return 保密协议审批集合
     */
    public List<ConfidentialityAgreementApproval> selectConfidentialityAgreementApprovalList1(ConfidentialityAgreementApproval confidentialityAgreementApproval);
}
