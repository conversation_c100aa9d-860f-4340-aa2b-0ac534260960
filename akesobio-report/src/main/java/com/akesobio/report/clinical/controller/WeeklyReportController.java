package com.akesobio.report.clinical.controller;

import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysPermissionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.WeeklyReport;
import com.akesobio.report.clinical.service.IWeeklyReportService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 每周汇报-开发部Controller
 * 
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/clinical/weeklyReport")
public class WeeklyReportController extends BaseController
{
    @Autowired
    private IWeeklyReportService weeklyReportService;

    @Autowired
    private SysPermissionService sysPermissionService;


    @GetMapping("/getSelect")
    public Map select(){
        /**
         * map  返回给前端下拉框权限标识
         */
        Map<Object,Object> map=new HashMap<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应权限部门
         * 临床QC部:201,临床开发一部:202,
         * 临床开发二部:203,临床开发三部:204
         * 临床开发五部:205,生物统计学部:206
         * 外联事务部门:207，药物警戒部:208
         */
        for (String s : roles) {
            if (s.equals("201")) {
                map.put("201","临床QC部");
            }
            if (s.equals("202")) {
                map.put("202","临床开发一部");
            }
            if (s.equals("203")) {
                map.put("203","临床开发二部");
            }
            if (s.equals("204")) {
                map.put("204","临床开发三部");
            }
            if (s.equals("205")) {
                map.put("205","临床开发五部");
            }
            if (s.equals("206")) {
                map.put("206","生物统计学部");
            }
            if (s.equals("207")) {
                map.put("207","外联事务部门");
            }
            if (s.equals("208")) {
                map.put("208","药物警戒部");
            }
        }
        if(map.isEmpty()){
            map.put("201","临床QC部");
            map.put("202","临床开发一部");
            map.put("203","临床开发二部");
            map.put("204","临床开发三部");
            map.put("205","临床开发五部");
            map.put("206","生物统计学部");
            map.put("207","外联事务部门");
            map.put("208","药物警戒部");
        }
        return map;
    }

    /**
     * 查询每周汇报-开发部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:weeklyReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(WeeklyReport weeklyReport)
    {
        /**
         * map  返回给前端下拉框权限标识
         */
        Map<Object,Object> map=new HashMap<>();
        /**
         * departmentList  模糊查询匹配List集合
         */
        List<Object> departmentList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应权限部门
         * 临床QC部:201,临床开发一部:202,
         * 临床开发二部:203,临床开发三部:204
         * 临床开发五部:205,生物统计学部:206
         * 外联事务部门:207，药物警戒部:208
         */
        if(weeklyReport.getFirstDepartment()!=null && weeklyReport.getFirstDepartment()!=""){
            departmentList.add(weeklyReport.getFirstDepartment());
            for (String s : roles) {
                if (s.equals("201")) {
                    map.put("201","临床QC部");
                }
                if (s.equals("202")) {
                    map.put("202","临床开发一部");
                }
                if (s.equals("203")) {
                    map.put("203","临床开发二部");
                }
                if (s.equals("204")) {
                    map.put("204","临床开发三部");
                }
                if (s.equals("205")) {
                    map.put("205","临床开发五部");
                }
                if (s.equals("206")) {
                    map.put("206","生物统计学部");
                }
                if (s.equals("207")) {
                    map.put("207","外联事务部门");
                }
                if (s.equals("208")) {
                    map.put("208","药物警戒部");
                }
            }
        }else {
            for (String s : roles) {
                if (s.equals("201")) {
                    map.put("201","临床QC部");
                    departmentList.add("临床QC部");
                }
                if (s.equals("202")) {
                    map.put("202","临床开发一部");
                    departmentList.add("临床开发一部");
                }
                if (s.equals("203")) {
                    map.put("203","临床开发二部");
                    departmentList.add("临床开发二部");
                }
                if (s.equals("204")) {
                    map.put("204","临床开发三部");
                    departmentList.add("临床开发三部");
                }
                if (s.equals("205")) {
                    map.put("205","临床开发五部");
                    departmentList.add("临床开发五部");
                }
                if (s.equals("206")) {
                    map.put("206","生物统计学部");
                    departmentList.add("生物统计学部");
                }
                if (s.equals("207")) {
                    map.put("207","外联事务部门");
                    departmentList.add("外联事务部门");
                }
                if (s.equals("208")) {
                    map.put("208","药物警戒部");
                    departmentList.add("药物警戒部");
                }
            }
        }
        if(map.isEmpty()){
            map.put("201","临床QC部");
            map.put("202","临床开发一部");
            map.put("203","临床开发二部");
            map.put("204","临床开发三部");
            map.put("205","临床开发五部");
            map.put("206","生物统计学部");
            map.put("207","外联事务部门");
            map.put("208","药物警戒部");
        }
        weeklyReport.setObjectList(departmentList);
        startPage();
        List<WeeklyReport> list = weeklyReportService.selectWeeklyReportList(weeklyReport);
        return getDataTableMap(list,map);
    }

    /**
     * 导出每周汇报-开发部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:weeklyReport:export')")
    @Log(title = "每周汇报-开发部", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeeklyReport weeklyReport)
    {
        /**
         * departmentList  模糊查询匹配List集合
         */
        List<Object> departmentList=new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Set<String> roles = sysPermissionService.getRolePermission(user);
        /**角色对应权限部门
         * 临床QC部:201,临床开发一部:202,
         * 临床开发二部:203,临床开发三部:204
         * 临床开发五部:205,生物统计学部:206
         * 外联事务部门:207，药物警戒部:208
         */
        if(weeklyReport.getFirstDepartment()!=null && weeklyReport.getFirstDepartment()!=""){
            departmentList.add(weeklyReport.getFirstDepartment());
        }else {
            for (String s : roles) {
                if (s.equals("201")) {
                    departmentList.add("临床QC部");
                }
                if (s.equals("202")) {
                    departmentList.add("临床开发一部");
                }
                if (s.equals("203")) {
                    departmentList.add("临床开发二部");
                }
                if (s.equals("204")) {
                    departmentList.add("临床开发三部");
                }
                if (s.equals("205")) {
                    departmentList.add("临床开发五部");
                }
                if (s.equals("206")) {
                    departmentList.add("生物统计学部");
                }
                if (s.equals("207")) {
                    departmentList.add("外联事务部门");
                }
                if (s.equals("208")) {
                    departmentList.add("药物警戒部");
                }
            }
        }
        weeklyReport.setObjectList(departmentList);
        List<WeeklyReport> list = weeklyReportService.selectWeeklyReportList(weeklyReport);
        ExcelUtil<WeeklyReport> util = new ExcelUtil<WeeklyReport>(WeeklyReport.class);
        util.exportExcel(response, list, "每周汇报-开发部数据");
    }

}
