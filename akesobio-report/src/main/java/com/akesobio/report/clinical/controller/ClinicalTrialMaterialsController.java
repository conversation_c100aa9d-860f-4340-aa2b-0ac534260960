package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.ClinicalTrialMaterials;
import com.akesobio.report.clinical.service.IClinicalTrialMaterialsService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床试验物资Controller
 *
 * <AUTHOR>
 * @date 2023-06-28
 */
@RestController
@RequestMapping("/clinical/clinicalTrialMaterials")
public class ClinicalTrialMaterialsController extends BaseController {
    @Autowired
    private IClinicalTrialMaterialsService clinicalTrialMaterialsService;

    /**
     * 查询临床试验物资列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalTrialMaterials:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalTrialMaterials clinicalTrialMaterials) {
        startPage();
        long a = System.currentTimeMillis();
        System.out.println("A=" + a);
        List<ClinicalTrialMaterials> list = clinicalTrialMaterialsService.selectClinicalTrialMaterialsList(clinicalTrialMaterials);
        long b = System.currentTimeMillis();
        System.out.println("B=" + b);
        long c = Math.abs(b - a);
        System.out.println("C=" + c);
        return getDataTable(list);
    }

    /**
     * 导出临床试验物资列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalTrialMaterials:export')")
    @Log(title = "临床试验物资", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClinicalTrialMaterials clinicalTrialMaterials) {
        List<ClinicalTrialMaterials> list = clinicalTrialMaterialsService.selectClinicalTrialMaterialsList(clinicalTrialMaterials);
        ExcelUtil<ClinicalTrialMaterials> util = new ExcelUtil<ClinicalTrialMaterials>(ClinicalTrialMaterials.class);
        util.exportExcel(response, list, "临床试验物资数据");
    }

}
