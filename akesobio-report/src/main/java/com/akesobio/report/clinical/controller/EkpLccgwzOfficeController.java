package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.clinical.domain.EkpLccgwzOffice;
import com.akesobio.report.clinical.service.IEkpLccgwzOfficeService;
import com.akesobio.report.clinical.vo.excel.EkpLccgwzOfficeExcelVo;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床办公用品2024Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@RestController
@RequestMapping("/clinical/office")
public class EkpLccgwzOfficeController extends BaseController
{
    @Resource
    private IEkpLccgwzOfficeService ekpLccgwzOfficeService;

    /**
     * 查询临床办公用品2024列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:office:list')")
    @GetMapping("/list")
    public TableDataInfo list(EkpLccgwzOffice ekpLccgwzOffice)
    {
        startPage();
        List<EkpLccgwzOffice> list = ekpLccgwzOfficeService.selectEkpLccgwzOfficeList(ekpLccgwzOffice);
        return getDataTable(list);
    }

    /**
     * 导出临床办公用品2024列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:office:export')")
    @Log(title = "临床办公用品2024", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EkpLccgwzOffice ekpLccgwzOffice)
    {
        List<EkpLccgwzOffice> list = ekpLccgwzOfficeService.selectEkpLccgwzOfficeList(ekpLccgwzOffice);
//        list = list.stream().filter(item -> StringUtils.hasText(item.getBasicInfo())).collect(Collectors.toList());

        List<EkpLccgwzOfficeExcelVo> excelVoList = list.stream().map(item -> {
            EkpLccgwzOfficeExcelVo excelVo = new EkpLccgwzOfficeExcelVo();
            BeanUtils.copyProperties(item, excelVo);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            if (item.getFlowEndDate() != null) {
                excelVo.setFlowEndDate(sdf.format(item.getFlowEndDate()));
            }
            if (item.getApplicationDate() != null) {
                excelVo.setApplicationDate(sdf.format(item.getApplicationDate()));
            }
            return excelVo;
        }).collect(Collectors.toList());

        ExcelUtil<EkpLccgwzOfficeExcelVo> util = new ExcelUtil<EkpLccgwzOfficeExcelVo>(EkpLccgwzOfficeExcelVo.class);
        util.exportExcel(response, excelVoList, "临床办公用品2024数据");
    }

    /**
     * 获取临床办公用品2024详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:office:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(ekpLccgwzOfficeService.selectEkpLccgwzOfficeById(id));
    }

}
