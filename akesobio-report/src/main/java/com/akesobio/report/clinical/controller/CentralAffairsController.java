package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.CentralAffairs;
import com.akesobio.report.clinical.service.ICentralAffairsService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 关中心事务部确认Controller
 * 
 * <AUTHOR>
 * @date 2023-11-17
 */
@RestController
@RequestMapping("/clinical/centralAffairs")
public class CentralAffairsController extends BaseController
{
    @Autowired
    private ICentralAffairsService centralAffairsService;

    /**
     * 查询关中心事务部确认列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:centralAffairs:list')")
    @GetMapping("/list")
    public TableDataInfo list(CentralAffairs centralAffairs)
    {
        startPage();
        List<CentralAffairs> list = centralAffairsService.selectCentralAffairsList(centralAffairs);
        return getDataTable(list);
    }

    /**
     * 导出关中心事务部确认列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:centralAffairs:export')")
    @Log(title = "关中心事务部确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CentralAffairs centralAffairs)
    {
        List<CentralAffairs> list = centralAffairsService.selectCentralAffairsList(centralAffairs);
        ExcelUtil<CentralAffairs> util = new ExcelUtil<CentralAffairs>(CentralAffairs.class);
        util.exportExcel(response, list, "关中心事务部确认数据");
    }

}
