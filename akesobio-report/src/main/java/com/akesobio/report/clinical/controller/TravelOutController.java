package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.TravelOut;
import com.akesobio.report.clinical.service.ITravelOutService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 差旅/外出Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/clinical/travelOut")
public class TravelOutController extends BaseController {
    @Autowired
    private ITravelOutService travelOutService;

    /**
     * 查询差旅/外出列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:travelOut:list')")
    @GetMapping("/list")
    public TableDataInfo list(TravelOut travelOut) {
        startPage();
        List<TravelOut> list = travelOutService.selectTravelOutList(travelOut);
        return getDataTable(list);
    }

    /**
     * 导出差旅/外出列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:travelOut:export')")
    @Log(title = "差旅/外出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TravelOut travelOut) {
        List<TravelOut> list = travelOutService.selectTravelOutList(travelOut);
        ExcelUtil<TravelOut> util = new ExcelUtil<TravelOut>(TravelOut.class);
        util.exportExcel(response, list, "差旅外出数据");
    }
}
