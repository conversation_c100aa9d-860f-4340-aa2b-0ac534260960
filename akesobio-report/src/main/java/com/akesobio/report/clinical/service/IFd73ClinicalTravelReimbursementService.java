package com.akesobio.report.clinical.service;

import java.util.List;

import com.akesobio.report.clinical.domain.Fd73ClinicalTravelReimbursement;

/**
 * FD73临床差旅报销Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IFd73ClinicalTravelReimbursementService {
    /**
     * 查询FD73临床差旅报销
     *
     * @param id FD73临床差旅报销主键
     * @return FD73临床差旅报销
     */
    public Fd73ClinicalTravelReimbursement selectFd73ClinicalTravelReimbursementById(Integer id);

    /**
     * 查询FD73临床差旅报销列表
     *
     * @param fd73ClinicalTravelReimbursement FD73临床差旅报销
     * @return FD73临床差旅报销集合
     */
    public List<Fd73ClinicalTravelReimbursement> selectFd73ClinicalTravelReimbursementList(Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement);

    /**
     * 新增FD73临床差旅报销
     *
     * @param fd73ClinicalTravelReimbursement FD73临床差旅报销
     * @return 结果
     */
    public int insertFd73ClinicalTravelReimbursement(Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement);

    /**
     * 修改FD73临床差旅报销
     *
     * @param fd73ClinicalTravelReimbursement FD73临床差旅报销
     * @return 结果
     */
    public int updateFd73ClinicalTravelReimbursement(Fd73ClinicalTravelReimbursement fd73ClinicalTravelReimbursement);

    /**
     * 批量删除FD73临床差旅报销
     *
     * @param ids 需要删除的FD73临床差旅报销主键集合
     * @return 结果
     */
    public int deleteFd73ClinicalTravelReimbursementByIds(Integer[] ids);

    /**
     * 删除FD73临床差旅报销信息
     *
     * @param id FD73临床差旅报销主键
     * @return 结果
     */
    public int deleteFd73ClinicalTravelReimbursementById(Integer id);
}
