package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.CompanyAndCode;

import java.util.List;

/**
 * Service 临床相关 公司信息
 *
 * @Package_Name com.akesobio.report.clinical.service
 * <AUTHOR>
 * @TIME 24-2-5 14:19
 * @Version 1.0
 */
public interface CompanyAndCodeService {
    int insertCompanyAndCode(CompanyAndCode o);

    List<CompanyAndCode> queryCompanyAndCode(CompanyAndCode o);
}