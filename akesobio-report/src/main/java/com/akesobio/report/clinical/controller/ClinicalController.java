package com.akesobio.report.clinical.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.PageDomain;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.core.page.TableSupport;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.EasyExcelUtils;
import com.akesobio.report.clinical.domain.ClinicalAttendance;
import com.akesobio.report.clinical.domain.ClinicalAttendanceCheck;
import com.akesobio.report.clinical.domain.ClinicalAttendanceShare;
import com.akesobio.report.clinical.service.ClinicalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Package_Name com.akesobio.report.clinical.controller
 * <AUTHOR> Lee
 * @TIME 23-11-30 16:06
 * @Version 1.0
 */
@RestController
@RequestMapping("/clinical/comparison")
public class ClinicalController extends BaseController {
    @Autowired
    private ClinicalService clinicalService;

    /**
     * 工时明细
     *
     * @param o
     * @return
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalAttendance o) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<ClinicalAttendance> list = clinicalService.queryClinicalComparison(o);
        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
        return rspData;
    }

    /**
     * 导出 工时明细
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:export')")
    @Log(title = "工时明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void trainExport(HttpServletResponse response, ClinicalAttendance o) throws IOException {
        List<ClinicalAttendance> list = clinicalService.queryClinicalComparison(o);
        EasyExcelUtils.downloadFragmentation(response, list, "工时明细");
    }

    /**
     * 汇总 研发核对 汇总表1
     *
     * @param o
     * @return
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:clinicalAttendanceCheck:list')")
    @GetMapping("/clinicalAttendanceCheck/list")
    public TableDataInfo clinicalAttendanceChecklist(ClinicalAttendance o) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<ClinicalAttendanceCheck> list = clinicalService.queryClinicalAttendanceCheck3(o);
        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
        return rspData;
    }

    /**
     * 导出 工时汇总表1
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:clinicalAttendanceCheck:export')")
    @Log(title = "工时汇总表1", businessType = BusinessType.EXPORT)
    @PostMapping("/clinicalAttendanceCheck/export")
    public void dataExport(HttpServletResponse response, ClinicalAttendance o) throws IOException {
        List<ClinicalAttendanceCheck> list = clinicalService.queryClinicalAttendanceCheck3(o);
        EasyExcelUtils.downloadFragmentation(response, list, "工时汇总表1");
    }

    /**
     * 汇总 核算组分摊 汇总表2
     *
     * @param o
     * @return
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:clinicalAttendanceShare:list')")
    @GetMapping("/clinicalAttendanceShare/list")
    public TableDataInfo clinicalAttendanceShareList(ClinicalAttendance o) {
        startPage();
        List<ClinicalAttendanceShare> list = clinicalService.queryClinicalAttendanceShare(o);
        return getDataTable(list);
    }


    /**
     * 导出 工时汇总表2
     */
    @PreAuthorize("@ss.hasPermi('clinical:comparison:clinicalAttendanceShare:export')")
    @Log(title = "工时汇总表2", businessType = BusinessType.EXPORT)
    @PostMapping("/clinicalAttendanceShare/export")
    public void dataExportClinicalAttendanceShare(HttpServletResponse response, ClinicalAttendance o) throws IOException {
        List<ClinicalAttendanceShare> list = clinicalService.queryClinicalAttendanceShare(o);
        EasyExcelUtils.downloadFragmentation(response, list, "工时汇总表2");
    }

}
