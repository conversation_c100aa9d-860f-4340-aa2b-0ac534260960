package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.MaterialRecycling;
import com.akesobio.report.clinical.service.IMaterialRecyclingService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 物资回收Controller
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
@RestController
@RequestMapping("/clinical/materialRecycling")
public class MaterialRecyclingController extends BaseController
{
    @Autowired
    private IMaterialRecyclingService materialRecyclingService;

    /**
     * 查询物资回收列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:materialRecycling:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialRecycling materialRecycling)
    {
        startPage();
        List<MaterialRecycling> list = materialRecyclingService.selectMaterialRecyclingList(materialRecycling);
        return getDataTable(list);
    }

    /**
     * 导出物资回收列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:materialRecycling:export')")
    @Log(title = "物资回收", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialRecycling materialRecycling)
    {
        List<MaterialRecycling> list = materialRecyclingService.selectMaterialRecyclingList(materialRecycling);
        ExcelUtil<MaterialRecycling> util = new ExcelUtil<MaterialRecycling>(MaterialRecycling.class);
        util.exportExcel(response, list, "物资回收数据");
    }

}
