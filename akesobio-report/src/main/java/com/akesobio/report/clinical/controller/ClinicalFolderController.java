package com.akesobio.report.clinical.controller;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.ClinicalFolder;
import com.akesobio.report.clinical.service.ClinicalFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Controller   edoc文件夹权限开通申请（临床运行部门）
 *
 * <AUTHOR>
 * @version 2003/04/01
 * @date 1956/09/12
 */
@RestController
@RequestMapping("/ClinicalFolder")
public class ClinicalFolderController extends BaseController {

    @Autowired
    private ClinicalFolderService clinicalFolderService;

    @PreAuthorize("@ss.hasPermi('ClinicalFolder:query:list')")
    @GetMapping("/query/list")
    public TableDataInfo list(ClinicalFolder o) {
        startPage();
        List<ClinicalFolder> list = clinicalFolderService.queryClinicalFolderList(o);
        return getDataTable(list);
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('ClinicalFolder:query:export')")
    @Log(title = "edoc文件夹权限开通申请（临床运行部门）", businessType = BusinessType.EXPORT)
    @PostMapping("/query/export")
    public void export(HttpServletResponse response, ClinicalFolder o) throws Exception {
        List<ClinicalFolder> list = clinicalFolderService.queryClinicalFolderList(o);
        ExcelUtils.downloadContainStyle(response,list,"edoc文件夹权限开通申请（临床运行部门）");
    }
}