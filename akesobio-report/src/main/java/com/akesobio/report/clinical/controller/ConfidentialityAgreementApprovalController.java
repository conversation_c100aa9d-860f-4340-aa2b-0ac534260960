package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.utils.poi.ExcelUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.ConfidentialityAgreementApproval;
import com.akesobio.report.clinical.service.IConfidentialityAgreementApprovalService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 保密协议审批Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/clinical/confidentialityAgreementApproval")
public class ConfidentialityAgreementApprovalController extends BaseController {
    @Autowired
    private IConfidentialityAgreementApprovalService confidentialityAgreementApprovalService;

    /**
     * 查询保密协议审批列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:confidentialityAgreementApproval:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConfidentialityAgreementApproval confidentialityAgreementApproval) {
        startPage();
        List<ConfidentialityAgreementApproval> list = confidentialityAgreementApprovalService.selectConfidentialityAgreementApprovalList(confidentialityAgreementApproval);
        return getDataTable(list);
    }

    /**
     * 导出保密协议审批列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:confidentialityAgreementApproval:export')")
    @Log(title = "保密协议审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConfidentialityAgreementApproval confidentialityAgreementApproval) throws Exception {
        List<ConfidentialityAgreementApproval> list = confidentialityAgreementApprovalService.selectConfidentialityAgreementApprovalList1(confidentialityAgreementApproval);
        ExcelUtil<ConfidentialityAgreementApproval> util = new ExcelUtil<ConfidentialityAgreementApproval>(ConfidentialityAgreementApproval.class);
        util.exportExcel(response, list, "保密协议审批数据");
    }

}
