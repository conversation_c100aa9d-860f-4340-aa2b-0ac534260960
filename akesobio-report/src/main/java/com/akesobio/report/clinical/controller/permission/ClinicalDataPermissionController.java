package com.akesobio.report.clinical.controller.permission;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.permission.ClinicalDataPermission;
import com.akesobio.report.clinical.domain.permission.ClinicalDataPermissionTemplate;
import com.akesobio.report.clinical.service.ClinicalDataPermissionService;
import com.alibaba.excel.EasyExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * Controller
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ClinicalDataPermission")
public class ClinicalDataPermissionController extends BaseController {
    @Autowired
    private ClinicalDataPermissionService clinicalDataPermissionService;

    //查询
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:list')")
    @GetMapping("/permission/list")
    public TableDataInfo list(ClinicalDataPermission o) {
        startPage();
        List<ClinicalDataPermission> list = clinicalDataPermissionService.queryClinicalDataPermissionList(o);
        return getDataTable(list);
    }

    //查询单个
//    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:queryById')")
    @GetMapping("/permission/queryById/{id}")
    public ClinicalDataPermission queryById( @PathVariable("id") String id) {
        return clinicalDataPermissionService.queryClinicalDataPermissionById(id);
    }

    //添加
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:insert')")
    @PostMapping("/permission/insert")
    public AjaxResult insert(@RequestBody ClinicalDataPermission o) {
        return clinicalDataPermissionService.insertClinicalDataPermission(o);
    }

    //修改
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:update')")
    @PutMapping("/permission/update")
    public AjaxResult update(@RequestBody ClinicalDataPermission o) {
        return clinicalDataPermissionService.updateClinicalDataPermissionById(o);
    }

    //修改 替换项目负责人
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:updateByNameJobNumber')")
    @PutMapping("/permission/updateByNameJobNumber")
    public AjaxResult updateByNameJobNumber(@RequestBody ClinicalDataPermission o) {
        return clinicalDataPermissionService.updateByNameJobNumber(o);
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:export')")
    @Log(title = "临床权限管控", businessType = BusinessType.EXPORT)
    @PostMapping("/permission/export")
    public void export(HttpServletResponse response, ClinicalDataPermission o) throws Exception {
        List<ClinicalDataPermission> list = clinicalDataPermissionService.queryClinicalDataPermissionList(o);
        ExcelUtils.downloadAvoidStyle(response, list,"临床权限管控");
    }

    /**
     * 模版下载
     */
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:import')")
    @PostMapping("/permission/importTemplate")
    public void importTemplate(HttpServletResponse response, HttpServletRequest request) throws IOException {
        ExcelUtils.downloadExcelTemplate(request,response, ClinicalDataPermissionTemplate.class,"临床数据权限导入模版");
    }

    /**
     * 导入
     */
    @PreAuthorize("@ss.hasPermi('ClinicalDataPermission:permission:import')")
    @PostMapping("/permission/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        return clinicalDataPermissionService.insertClinicalDataPermissionList(file);
    }
}