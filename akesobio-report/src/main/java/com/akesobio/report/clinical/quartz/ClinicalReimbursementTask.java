package com.akesobio.report.clinical.quartz;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.akesobio.report.clinical.domain.*;
import com.akesobio.report.clinical.mapper.*;
import com.akesobio.report.ddi.util.GetComputerName;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component("ClinicalReimbursementTask")
@Slf4j
public class ClinicalReimbursementTask {

    @Autowired
    private ClinicalReimbursementMapper clinicalReimbursementMapper;

    @Autowired
    private ClinicalReimbursementOldMapper clinicalReimbursementOldMapper;

    @Autowired
    private ProcessManagementMapper processManagementMapper;

    @Autowired
    private ReimbursementApplicationMapper reimbursementApplicationMapper;

    @Autowired
    private TravelApplicationMapper travelApplicationMapper;

    /**
     * 定时更新临床报销表
     */
    public void clinicalReimbursementTask() {
        String computerName = GetComputerName.getComputerName();
        if (!computerName.equals("SERVER22")) {
            clinicalReimbursementMapper.deleteAllClinicalReimbursement();
            List<ClinicalReimbursement> listNew = new ArrayList<>();
            List<ClinicalReimbursementOld> listOld = clinicalReimbursementOldMapper.selectClinicalReimbursementOld(new ClinicalReimbursementOld());
            if (listOld.size() > 0 && listOld != null) {
                for (int i = 0; i < listOld.size(); i++) {
                    ClinicalReimbursement c = new ClinicalReimbursement();
                    ClinicalReimbursementOld cOld = listOld.get(i);
                    //OA报销编号
                    c.setOaReimbursementNumber(cOld.getOaReimbursementNumber());
                    //创建日期
                    c.setDateCreated(cOld.getDateCreated());
                    //报销类别
                    c.setReimbursementCategory(cOld.getReimbursementCategory());
                    //公司代码
                    c.setCompanyCode(cOld.getCompanyCode());
                    //公司名称
                    c.setCompanyName(cOld.getCompanyName());
                    //报销人
                    c.setReimbursementPerson(cOld.getReimbursementPerson());
                    //报销人工号
                    c.setReimbursementPersonNumber(cOld.getReimbursementPersonNumber());
                    //申请人
                    c.setApplicant(cOld.getApplicant());
                    //申请人工号
                    c.setApplicantNumber(cOld.getApplicantNumber());
                    //申请人职务
                    c.setApplicantPosition(cOld.getApplicantPosition());
                    //所属部门
                    c.setDepartment(cOld.getDepartment());
                    //成本中心代码
                    c.setCostCenterCode(cOld.getCostCenterCode());
                    //成本中心名称
                    c.setCostCenterName(cOld.getCostCenterName());
                    //关联审批单
                    c.setRelatedApprovalForm(cOld.getRelatedApprovalForm());
                    //业务发生期间
                    c.setBusinessPeriod(cOld.getBusinessPeriod());
                    //业务类型
                    c.setBusinessType(cOld.getBusinessType());
                    //类型细分
                    c.setTypeSegmentation(cOld.getTypeSegmentation());
                    //总裁办业务
                    c.setCeoBusiness(cOld.getCeoBusiness());
                    //会议开始日期
                    c.setMeetingStartDate(cOld.getMeetingStartDate());
                    //会议结束日期
                    c.setMeetingEndDate(cOld.getMeetingEndDate());
                    //会议名称
                    c.setMeetingName(cOld.getMeetingName());
                    //会议类型
                    c.setMeetingType(cOld.getMeetingType());
                    //会议类别
                    c.setMeetingCategory(cOld.getMeetingCategory());
                    //会议形式
                    c.setMeetingFormat(cOld.getMeetingFormat());
                    //公司角色
                    c.setCompanyRole(cOld.getCompanyRole());
                    //面向对象
                    c.setObjectOriented(cOld.getObjectOriented());
                    //省份
                    c.setProvince(cOld.getProvince());
                    //城市
                    c.setCity(cOld.getCity());
                    //覆盖人数
                    c.setPeopleCovered(cOld.getPeopleCovered());
                    //签到人数
                    c.setSignNumber(cOld.getSignNumber());
                    //专家人数
                    c.setExpertsNumber(cOld.getExpertsNumber());
                    //会议资料
                    c.setMeetingMaterials(cOld.getMeetingMaterials());
                    //邀请函
                    c.setInvitation(cOld.getInvitation());
                    //日程表
                    c.setSchedule(cOld.getSchedule());
                    //签到表
                    c.setAttendanceSheet(cOld.getAttendanceSheet());
                    //照片
                    c.setPhoto(cOld.getPhoto());
                    //支付方式
                    c.setPaymentMethod(cOld.getPaymentMethod());
                    //借款余额
                    c.setLoanBalance(cOld.getLoanBalance());
                    //是否冲抵借款
                    c.setIsWhetherLoan(cOld.getIsWhetherLoan());
                    //合计金额
                    c.setTotalAmount(cOld.getTotalAmount());
                    //关联审批单金额
                    c.setRelatedApprovalAmount(cOld.getRelatedApprovalAmount());
                    //差异金额
                    c.setDifferenceAmount(cOld.getDifferenceAmount());
                    //差异说明
                    c.setDifferenceExplanation(cOld.getDifferenceExplanation());
                    //费用科目代码
                    c.setExpenseAccountCode(cOld.getExpenseAccountCode());
                    //费用科目名称
                    c.setExpenseAccountName(cOld.getExpenseAccountName());
                    //业务描述
                    c.setServiceDescription(cOld.getServiceDescription());
                    //发票号码
                    c.setInvoiceNumber(cOld.getInvoiceNumber());
                    //不含税金额
                    c.setExcludingTaxAmount(cOld.getExcludingTaxAmount());
                    //专用发票税额
                    c.setSpecialInvoiceTaxAmount(cOld.getSpecialInvoiceTaxAmount());
                    //报销金额
                    c.setReimbursementAmount(cOld.getReimbursementAmount());
                    //币别
                    c.setCurrency(cOld.getCurrency());
                    //项目号
                    c.setProjectNo(cOld.getProjectNo());
                    //中心名称
                    c.setCenterName(cOld.getCenterName());
                    //流程状态
                    c.setDocumentStatus(cOld.getDocumentStatus());
                    //当前环节
                    c.setCurrentSession(cOld.getCurrentSession());
                    //SAP应付凭证状态
                    c.setSapPayNumber(cOld.getSapPayNumber());
                    //凭证推送日期
                    c.setVoucherPushDate(cOld.getVoucherPushDate());

                    //OA单号
                    String oaOddNumbers = listOld.get(i).getOaReimbursementNumber();
                    if (oaOddNumbers != null && !oaOddNumbers.contains("FD74")) {
                        //关联审批单
                        String relatedApprovalForm = listOld.get(i).getRelatedApprovalForm();
                        if (relatedApprovalForm != null && relatedApprovalForm != "") {
                            //费用报销
                            if (oaOddNumbers != null && oaOddNumbers.contains("FD72")) {
                                // 使用 org.json 库解析 JSON 字符串
                                JSONArray jsonArray = new JSONArray(relatedApprovalForm);
                                // 获取数组中的第一个元素
                                JSONObject jsonObject = jsonArray.getJSONObject(0);
                                // 获取 docId 的值
                                String docId = jsonObject.getStr("docId");
                                // 获取 subject 的值
                                String subject = jsonObject.getStr("subject");
                                c.setRelatedApprovalForm(subject);
                                if (docId != null && docId != "") {
                                    ProcessManagement processManagement = processManagementMapper.selectProcessManagement(docId);
                                    if (processManagement != null) {
                                        //流程管理单号
                                        String oddNumbers = processManagement.getOddNumbers();
                                        List<ReimbursementApplication> list = reimbursementApplicationMapper.selectReimbursementApplication(oddNumbers);
                                        if (list.size() > 0 && list != null) {
                                            ReimbursementApplication r = list.get(0);
                                            c.setExpenseApplicationNumber(r.getExpenseApplicationNumber());
                                            c.setApplicationSubject(r.getApplicationSubject());
                                            c.setApplicationType(r.getApplicationType());
                                            c.setSecondaryType(r.getSecondaryType());
                                            c.setApplicationsAmount(r.getApplicationsAmount());
                                            c.setDetailedDescription(r.getDetailedDescription());
                                        }
                                    }
                                }
                            }
                            //差旅报销
                            if (oaOddNumbers != null && oaOddNumbers.contains("FD73")) {
                                // 使用 org.json 库解析 JSON 字符串
                                JSONArray jsonArray = new JSONArray(relatedApprovalForm);
                                // 获取数组中的第一个元素
                                JSONObject jsonObject = jsonArray.getJSONObject(0);
                                // 获取 docId 的值
                                String docId = jsonObject.getStr("docId");
                                // 获取 subject 的值
                                String subject = jsonObject.getStr("subject");
                                c.setRelatedApprovalForm(subject);
                                if (docId != null && docId != "") {
                                    ProcessManagement processManagement = processManagementMapper.selectProcessManagement(docId);
                                    if (processManagement != null) {
                                        //流程管理单号
                                        String oddNumbers = processManagement.getOddNumbers();
                                        List<TravelApplication> list = travelApplicationMapper.selectTravelApplication(oddNumbers);
                                        if (list.size() > 0 && list != null) {
                                            TravelApplication t = list.get(0);
                                            c.setTravelDepartureDate(t.getTravelDepartureDate());
                                            c.setTravelReturnDate(t.getTravelReturnDate());
                                            c.setBusinessDay(t.getBusinessDay());
                                            c.setColleagues(t.getColleagues());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 获取当前日期
                    LocalDate today = LocalDate.now();
                    //执行日期
                    String executionDate = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    // 将格式化的字符串转换为 java.sql.Date 对象
                    Date sqlDate = Date.valueOf(executionDate);
                    c.setExecutionDate(sqlDate);
                    clinicalReimbursementMapper.insertClinicalReimbursement(c);
                }
            }
        }
    }
}
