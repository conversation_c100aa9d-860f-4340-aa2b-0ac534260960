package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.EkpLccgwzOffice;

import java.util.List;


/**
 * 临床办公用品2024Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
public interface IEkpLccgwzOfficeService 
{
    /**
     * 查询临床办公用品2024
     * 
     * @param id 临床办公用品2024主键
     * @return 临床办公用品2024
     */
    public EkpLccgwzOffice selectEkpLccgwzOfficeById(String id);

    /**
     * 查询临床办公用品2024列表
     * 
     * @param ekpLccgwzOffice 临床办公用品2024
     * @return 临床办公用品2024集合
     */
    public List<EkpLccgwzOffice> selectEkpLccgwzOfficeList(EkpLccgwzOffice ekpLccgwzOffice);

    /**
     * 新增临床办公用品2024
     * 
     * @param ekpLccgwzOffice 临床办公用品2024
     * @return 结果
     */
    public int insertEkpLccgwzOffice(EkpLccgwzOffice ekpLccgwzOffice);

    /**
     * 修改临床办公用品2024
     * 
     * @param ekpLccgwzOffice 临床办公用品2024
     * @return 结果
     */
    public int updateEkpLccgwzOffice(EkpLccgwzOffice ekpLccgwzOffice);

    /**
     * 批量删除临床办公用品2024
     * 
     * @param ids 需要删除的临床办公用品2024主键集合
     * @return 结果
     */
    public int deleteEkpLccgwzOfficeByIds(String[] ids);

    /**
     * 删除临床办公用品2024信息
     * 
     * @param id 临床办公用品2024主键
     * @return 结果
     */
    public int deleteEkpLccgwzOfficeById(String id);
}
