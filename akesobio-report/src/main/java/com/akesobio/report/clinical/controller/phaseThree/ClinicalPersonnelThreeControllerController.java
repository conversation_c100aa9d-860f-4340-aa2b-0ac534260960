package com.akesobio.report.clinical.controller.phaseThree;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.InformationGroupApprovalNode;
import com.akesobio.report.clinical.service.InformationGroupApprovalNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Controller 临床报表第三期 信息组审批节点合集
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ClinicalPersonnelThree")
public class ClinicalPersonnelThreeControllerController extends BaseController {

    @Autowired
    private InformationGroupApprovalNodeService informationGroupApprovalNodeService;

    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelThree:InformationGroupApprovalNode:list')")
    @GetMapping("/InformationGroupApprovalNode/list")
    public TableDataInfo list(InformationGroupApprovalNode o) {
        startPage();
        List<InformationGroupApprovalNode> list = informationGroupApprovalNodeService.queryInformationGroupApprovalNodeList(o);
        return getDataTable(list);
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelThree:InformationGroupApprovalNode:export')")
    @Log(title = "信息组审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/InformationGroupApprovalNode/export")
    public void export(HttpServletResponse response, InformationGroupApprovalNode o) throws Exception {
        List<InformationGroupApprovalNode> list = informationGroupApprovalNodeService.queryInformationGroupApprovalNodeList(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "信息组审批节点合集", "跳转OA表单");
    }
}