package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.MaterialTeamApprovalNode2;

import java.util.List;

/**
 * Service
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface MaterialTeamApprovalNode2Service {
    List<MaterialTeamApprovalNode2> queryMaterialTeamApprovalNode2List(MaterialTeamApprovalNode2 o);
    List<MaterialTeamApprovalNode2> queryMaterialTeamApprovalNode2ListALL(MaterialTeamApprovalNode2 o);
}