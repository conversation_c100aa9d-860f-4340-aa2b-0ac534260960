package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.CdmWorkingHours;
import com.akesobio.report.clinical.service.ICdmWorkingHoursService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * CDM工时Controller
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@RestController
@RequestMapping("/clinical/cdmWorkingHours")
public class CdmWorkingHoursController extends BaseController {
    @Autowired
    private ICdmWorkingHoursService cdmWorkingHoursService;

    /**
     * 查询CDM工时列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:cdmWorkingHours:list')")
    @GetMapping("/list")
    public TableDataInfo list(CdmWorkingHours cdmWorkingHours) {
        startPage();
        List<CdmWorkingHours> list = cdmWorkingHoursService.selectCdmWorkingHoursList(cdmWorkingHours);
        return getDataTable(list);
    }

    /**
     * 导出CDM工时列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:cdmWorkingHours:export')")
    @Log(title = "CDM工时", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CdmWorkingHours cdmWorkingHours) {
        List<CdmWorkingHours> list = cdmWorkingHoursService.selectCdmWorkingHoursList(cdmWorkingHours);
        ExcelUtil<CdmWorkingHours> util = new ExcelUtil<CdmWorkingHours>(CdmWorkingHours.class);
        util.exportExcel(response, list, "CDM工时数据");
    }
}
