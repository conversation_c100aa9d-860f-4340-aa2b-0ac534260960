package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.ClinicalOfficeSupplies;
import com.akesobio.report.clinical.service.IClinicalOfficeSuppliesService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床办公用品Controller
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
@RestController
@RequestMapping("/clinical/clinicalOfficeSupplies")
public class ClinicalOfficeSuppliesController extends BaseController
{
    @Autowired
    private IClinicalOfficeSuppliesService clinicalOfficeSuppliesService;

    /**
     * 查询临床办公用品列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalOfficeSupplies:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalOfficeSupplies clinicalOfficeSupplies)
    {
        startPage();
        List<ClinicalOfficeSupplies> list = clinicalOfficeSuppliesService.selectClinicalOfficeSuppliesList(clinicalOfficeSupplies);
        return getDataTable(list);
    }

    /**
     * 导出临床办公用品列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalOfficeSupplies:export')")
    @Log(title = "临床办公用品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClinicalOfficeSupplies clinicalOfficeSupplies)
    {
        List<ClinicalOfficeSupplies> list = clinicalOfficeSuppliesService.selectClinicalOfficeSuppliesList(clinicalOfficeSupplies);
        ExcelUtil<ClinicalOfficeSupplies> util = new ExcelUtil<ClinicalOfficeSupplies>(ClinicalOfficeSupplies.class);
        util.exportExcel(response, list, "临床办公用品数据");
    }

}
