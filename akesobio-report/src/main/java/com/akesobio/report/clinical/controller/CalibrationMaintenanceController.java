package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.CalibrationMaintenance;
import com.akesobio.report.clinical.service.ICalibrationMaintenanceService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 校准/维修申请Controller
 *
 * <AUTHOR>
 * @date 2024-01-02
 */
@RestController
@RequestMapping("/clinical/calibrationMaintenance")
public class CalibrationMaintenanceController extends BaseController {
    @Autowired
    private ICalibrationMaintenanceService calibrationMaintenanceService;

    /**
     * 查询校准/维修申请列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:calibrationMaintenance:list')")
    @GetMapping("/list")
    public TableDataInfo list(CalibrationMaintenance calibrationMaintenance) {
        startPage();
        List<CalibrationMaintenance> list = calibrationMaintenanceService.selectCalibrationMaintenanceList(calibrationMaintenance);
        return getDataTable(list);
    }

    /**
     * 导出校准/维修申请列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:calibrationMaintenance:export')")
    @Log(title = "校准维修申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CalibrationMaintenance calibrationMaintenance) {
        List<CalibrationMaintenance> list = calibrationMaintenanceService.selectCalibrationMaintenanceList(calibrationMaintenance);
        ExcelUtil<CalibrationMaintenance> util = new ExcelUtil<CalibrationMaintenance>(CalibrationMaintenance.class);
        util.exportExcel(response, list, "校准维修申请数据");
    }
}
