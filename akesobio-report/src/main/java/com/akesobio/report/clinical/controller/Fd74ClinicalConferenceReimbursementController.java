package com.akesobio.report.clinical.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.core.page.PageDomain;
import com.akesobio.common.core.page.TableSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.Fd74ClinicalConferenceReimbursement;
import com.akesobio.report.clinical.service.IFd74ClinicalConferenceReimbursementService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * FD74临床会议报销Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/clinical/fd74ClinicalConferenceReimbursement")
public class Fd74ClinicalConferenceReimbursementController extends BaseController {
    @Autowired
    private IFd74ClinicalConferenceReimbursementService fd74ClinicalConferenceReimbursementService;

    /**
     * 查询FD74临床会议报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<Fd74ClinicalConferenceReimbursement> list = fd74ClinicalConferenceReimbursementService.selectFd74ClinicalConferenceReimbursementList(fd74ClinicalConferenceReimbursement);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出FD74临床会议报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:export')")
    @Log(title = "FD74临床会议报销", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement) {
        List<Fd74ClinicalConferenceReimbursement> list = fd74ClinicalConferenceReimbursementService.selectFd74ClinicalConferenceReimbursementList(fd74ClinicalConferenceReimbursement);
        ExcelUtil<Fd74ClinicalConferenceReimbursement> util = new ExcelUtil<Fd74ClinicalConferenceReimbursement>(Fd74ClinicalConferenceReimbursement.class);
        util.exportExcel(response, list, "FD74临床会议报销数据");
    }

    /**
     * 获取FD74临床会议报销详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(fd74ClinicalConferenceReimbursementService.selectFd74ClinicalConferenceReimbursementById(id));
    }

    /**
     * 新增FD74临床会议报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:add')")
    @Log(title = "FD74临床会议报销 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement) {
        return toAjax(fd74ClinicalConferenceReimbursementService.insertFd74ClinicalConferenceReimbursement(fd74ClinicalConferenceReimbursement));
    }

    /**
     * 修改FD74临床会议报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:edit')")
    @Log(title = "FD74临床会议报销", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fd74ClinicalConferenceReimbursement fd74ClinicalConferenceReimbursement) {
        return toAjax(fd74ClinicalConferenceReimbursementService.updateFd74ClinicalConferenceReimbursement(fd74ClinicalConferenceReimbursement));
    }

    /**
     * 删除FD74临床会议报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:fd74ClinicalConferenceReimbursement:remove')")
    @Log(title = "FD74临床会议报销", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(fd74ClinicalConferenceReimbursementService.deleteFd74ClinicalConferenceReimbursementByIds(ids));
    }
}
