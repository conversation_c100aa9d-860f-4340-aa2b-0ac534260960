package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.EkpLccgwzTrial;

import java.util.List;


/**
 * 临床试验物资2024Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
public interface IEkpLccgwzTrialService 
{
    /**
     * 查询临床试验物资2024
     * 
     * @param id 临床试验物资2024主键
     * @return 临床试验物资2024
     */
    public EkpLccgwzTrial selectEkpLccgwzTrialById(String id);

    /**
     * 查询临床试验物资2024列表
     * 
     * @param ekpLccgwzTrial 临床试验物资2024
     * @return 临床试验物资2024集合
     */
    public List<EkpLccgwzTrial> selectEkpLccgwzTrialList(EkpLccgwzTrial ekpLccgwzTrial);

    /**
     * 新增临床试验物资2024
     * 
     * @param ekpLccgwzTrial 临床试验物资2024
     * @return 结果
     */
    public int insertEkpLccgwzTrial(EkpLccgwzTrial ekpLccgwzTrial);

    /**
     * 修改临床试验物资2024
     * 
     * @param ekpLccgwzTrial 临床试验物资2024
     * @return 结果
     */
    public int updateEkpLccgwzTrial(EkpLccgwzTrial ekpLccgwzTrial);

    /**
     * 批量删除临床试验物资2024
     * 
     * @param ids 需要删除的临床试验物资2024主键集合
     * @return 结果
     */
    public int deleteEkpLccgwzTrialByIds(String[] ids);

    /**
     * 删除临床试验物资2024信息
     * 
     * @param id 临床试验物资2024主键
     * @return 结果
     */
    public int deleteEkpLccgwzTrialById(String id);
}
