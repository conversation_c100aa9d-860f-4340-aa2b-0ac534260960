package com.akesobio.report.clinical.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.ClinicalReimbursement;
import com.akesobio.report.clinical.service.IClinicalReimbursementService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 临床报销Controller
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@RestController
@RequestMapping("/clinical/clinicalReimbursement")
public class ClinicalReimbursementController extends BaseController {
    @Autowired
    private IClinicalReimbursementService clinicalReimbursementService;

    /**
     * 查询临床报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClinicalReimbursement clinicalReimbursement) {
        startPage();
        List<ClinicalReimbursement> list = clinicalReimbursementService.selectClinicalReimbursementList(clinicalReimbursement);
        return getDataTable(list);
    }

    /**
     * 导出临床报销列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:export')")
    @Log(title = "临床报销", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClinicalReimbursement clinicalReimbursement) {
        List<ClinicalReimbursement> list = clinicalReimbursementService.selectClinicalReimbursementList(clinicalReimbursement);
        ExcelUtil<ClinicalReimbursement> util = new ExcelUtil<ClinicalReimbursement>(ClinicalReimbursement.class);
        util.exportExcel(response, list, "临床报销数据");
    }

    /**
     * 获取临床报销详细信息
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(clinicalReimbursementService.selectClinicalReimbursementById(id));
    }

    /**
     * 新增临床报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:add')")
    @Log(title = "临床报销", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ClinicalReimbursement clinicalReimbursement) {
        return toAjax(clinicalReimbursementService.insertClinicalReimbursement(clinicalReimbursement));
    }

    /**
     * 修改临床报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:edit')")
    @Log(title = "临床报销", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ClinicalReimbursement clinicalReimbursement) {
        return toAjax(clinicalReimbursementService.updateClinicalReimbursement(clinicalReimbursement));
    }

    /**
     * 删除临床报销
     */
    @PreAuthorize("@ss.hasPermi('clinical:clinicalReimbursement:remove')")
    @Log(title = "临床报销", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(clinicalReimbursementService.deleteClinicalReimbursementByIds(ids));
    }
}
