package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.FeeGroupApprovalNode2;

import java.util.List;

/**
 * Service
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface FeeGroupApprovalNode2Service {
    List<FeeGroupApprovalNode2> queryFeeGroupApprovalNode2List(FeeGroupApprovalNode2 o);
    List<FeeGroupApprovalNode2> queryFeeGroupApprovalNode2ListALL(FeeGroupApprovalNode2 o);
}