package com.akesobio.report.clinical.service;

import com.akesobio.report.clinical.domain.InformationGroupApprovalNode;
import com.akesobio.report.clinical.domain.staff.HrEmployeeInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * Service 临床报表第三期 信息组审批节点合集
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface InformationGroupApprovalNodeService {
    List<InformationGroupApprovalNode> queryInformationGroupApprovalNodeList(InformationGroupApprovalNode o);

    List<HrEmployeeInfo> queryData(HrEmployeeInfo o);
}