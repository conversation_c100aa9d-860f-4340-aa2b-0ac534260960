package com.akesobio.report.clinical.controller;

import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.report.clinical.domain.CompanyAndCode;
import com.akesobio.report.clinical.service.CompanyAndCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 临床相关 公司信息
 *
 * @Package_Name com.akesobio.report.clinical.controller
 * <AUTHOR> <PERSON>
 * @TIME 24-2-5 14:37
 * @Version 1.0
 */
@RestController
@RequestMapping("/clinical/company")
public class CompanyAndCodeController extends BaseController {
    @Autowired
    private CompanyAndCodeService companyAndCodeMapper;

    /**
     * 查询公司信息
     *
     * @param o
     * @return
     */
    @PreAuthorize("@ss.hasPermi('clinical:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyAndCode o) {
        startPage();
        List<CompanyAndCode> list = companyAndCodeMapper.queryCompanyAndCode(o);
        return getDataTable(list);
    }

    /**
     * 添加
     *
     * @param o
     * @return
     */
    @PreAuthorize("@ss.hasPermi('clinical:company:insert')")
    @PostMapping("/insert")
    public int insertCompanyAndCode(CompanyAndCode o) {
        return companyAndCodeMapper.insertCompanyAndCode(o);
    }


}
