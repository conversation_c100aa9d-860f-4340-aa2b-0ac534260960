package com.akesobio.report.clinical.controller.hr;

import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.report.clinical.domain.hr.EmploymentNoticeClinical;
import com.akesobio.report.clinical.service.EmploymentNoticeClinicalService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Controller
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/EmploymentNoticeClinicalController")
public class EmploymentNoticeClinicalController extends BaseController {
    @Autowired
    private EmploymentNoticeClinicalService employmentNoticeClinicalService;

    @PreAuthorize("@ss.hasPermi('EmploymentNoticeClinicalController:EmploymentNoticeClinical:list')")
    @GetMapping("/EmploymentNoticeClinical/list")
    public TableDataInfo list(EmploymentNoticeClinical o) {
        startPage();
        List<EmploymentNoticeClinical> list = employmentNoticeClinicalService.queryEmploymentNoticeClinicalList(o);
        return getDataTable(list);
    }

    /**
     * 导出
     */
    @PreAuthorize("@ss.hasPermi('EmploymentNoticeClinicalController:EmploymentNoticeClinical:export')")
    @Log(title = "入职通知单", businessType = BusinessType.EXPORT)
    @PostMapping("/EmploymentNoticeClinical/export")
    public void export(HttpServletResponse response, EmploymentNoticeClinical o) throws Exception {
        List<EmploymentNoticeClinical> list = employmentNoticeClinicalService.queryEmploymentNoticeClinicalList(o);
        ExcelUtils.downloadAvoidStyle(response, list, "入职通知单");
    }
}