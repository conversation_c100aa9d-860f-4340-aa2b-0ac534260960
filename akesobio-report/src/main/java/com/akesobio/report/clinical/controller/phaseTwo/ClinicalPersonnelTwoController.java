package com.akesobio.report.clinical.controller.phaseTwo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.page.PageDomain;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.common.core.page.TableSupport;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.common.utils.poi.ExcelUtils;
import com.akesobio.common.utils.time.QueryTimeProcessing;
import com.akesobio.report.clinical.domain.FeeGroupApprovalNode;
import com.akesobio.report.clinical.domain.FeeGroupApprovalNode2;
import com.akesobio.report.clinical.domain.MaterialTeamApprovalNode;
import com.akesobio.report.clinical.domain.MaterialTeamApprovalNode2;
import com.akesobio.report.clinical.service.FeeGroupApprovalNode2Service;
import com.akesobio.report.clinical.service.FeeGroupApprovalNodeService;
import com.akesobio.report.clinical.service.MaterialTeamApprovalNode2Service;
import com.akesobio.report.clinical.service.MaterialTeamApprovalNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller 临床第二批交付报表
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ClinicalPersonnelTwo")
public class ClinicalPersonnelTwoController extends BaseController {

    @Autowired
    private FeeGroupApprovalNodeService feeGroupApprovalNodeService;

    @Autowired
    private FeeGroupApprovalNode2Service feeGroupApprovalNode2Service;

    @Autowired
    private MaterialTeamApprovalNodeService materialTeamApprovalNodeService;

    @Autowired
    private MaterialTeamApprovalNode2Service materialTeamApprovalNode2Service;


    /**
     * 临床 费用组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode:list')")
    @GetMapping("/FeeGroupApprovalNode/list")
    public TableDataInfo list(FeeGroupApprovalNode o) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        if (!StrUtil.isEmpty(o.getStartDate()) && !StrUtil.isEmpty(o.getEndDate())) {
            o.setEndDate(o.getEndDate() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate1()) && !StrUtil.isEmpty(o.getEndDate1())) {
            o.setEndDate1(o.getEndDate1() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate2()) && !StrUtil.isEmpty(o.getEndDate2())) {
            o.setEndDate2(o.getEndDate2() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate3()) && !StrUtil.isEmpty(o.getEndDate3())) {
            o.setEndDate3(o.getEndDate3() + " 23:59:59");
        }
        List<FeeGroupApprovalNode> list = feeGroupApprovalNodeService.queryFeeGroupApprovalNodeList(o);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出 临床 费用组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode:export')")
    @Log(title = "费用组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/FeeGroupApprovalNode/export")
    public void export(HttpServletResponse response, FeeGroupApprovalNode o) throws Exception {
        if (StrUtil.isEmpty(o.getStartDate2()) || StrUtil.isEmpty(o.getEndDate2())) {
            Map<String, String> map = QueryTimeProcessing.returnTime();
            o.setStartDate2(map.get("startDate"));
            o.setEndDate2(map.get("endDate"));
        }
        o.setEndDate2(o.getEndDate2() + " 23:59:59");
        QueryTimeProcessing.timeProcessing(o.getStartDate2(), o.getEndDate2(),"请选择节点操作时间间隔半年内的数据进行导出",180);
        List<FeeGroupApprovalNode> list = feeGroupApprovalNodeService.queryFeeGroupApprovalNodeList(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "费用组表单审批节点合集","跳转OA表单");
    }

    /**
     * 导出 临床 费用组表单审批节点合集 全部
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode:exportAll')")
    @Log(title = "费用组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/FeeGroupApprovalNode/exportAll")
    public void exportAll(HttpServletResponse response, FeeGroupApprovalNode o) throws Exception {
        List<FeeGroupApprovalNode> list = feeGroupApprovalNodeService.queryFeeGroupApprovalNodeListALL(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "费用组表单审批节点合集","跳转OA表单");
    }

    /**
     * 临床 2费用组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode2:list')")
    @GetMapping("/FeeGroupApprovalNode2/list")
    public TableDataInfo list(FeeGroupApprovalNode2 o) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        if (!StrUtil.isEmpty(o.getStartDate2()) && !StrUtil.isEmpty(o.getEndDate2())) {
            o.setEndDate2(o.getEndDate2() + " 23:59:59");
        }
        List<FeeGroupApprovalNode2> list = feeGroupApprovalNode2Service.queryFeeGroupApprovalNode2List(o);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出 临床 2费用组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode2:export')")
    @Log(title = "2费用组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/FeeGroupApprovalNode2/export")
    public void export(HttpServletResponse response, FeeGroupApprovalNode2 o) throws Exception {
        if (StrUtil.isEmpty(o.getStartDate2()) || StrUtil.isEmpty(o.getEndDate2())) {
            Map<String, String> map = QueryTimeProcessing.returnTime();
            o.setStartDate2(map.get("startDate"));
            o.setEndDate2(map.get("endDate"));
        }
        o.setEndDate2(o.getEndDate2() + " 23:59:59");
        QueryTimeProcessing.timeProcessing(o.getStartDate2(), o.getEndDate2(),"请选择节点到达时间间隔半年内的数据进行导出",180);
        List<FeeGroupApprovalNode2> list = feeGroupApprovalNode2Service.queryFeeGroupApprovalNode2List(o);
        list.forEach(e->{
            e.setExportTime(LocalDate.now());
        });
        ExcelUtils.downloadAvoidStyleLink(response, list, "2费用组表单审批节点合集","跳转OA表单");
    }

    /**
     * 导出 临床 2费用组表单审批节点合集 全部
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:FeeGroupApprovalNode2:exportAll')")
    @Log(title = "2费用组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/FeeGroupApprovalNode2/exportAll")
    public void exportAll(HttpServletResponse response, FeeGroupApprovalNode2 o) throws Exception {
        List<FeeGroupApprovalNode2> list = feeGroupApprovalNode2Service.queryFeeGroupApprovalNode2ListALL(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "2费用组表单审批节点合集","跳转OA表单");
    }

    /*================================================================================================================================================*/

    /**
     * 临床 物资组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode:list')")
    @GetMapping("/MaterialTeamApprovalNode/list")
    public TableDataInfo list(MaterialTeamApprovalNode o) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        if (!StrUtil.isEmpty(o.getStartDate()) && !StrUtil.isEmpty(o.getEndDate())) {
            o.setEndDate(o.getEndDate() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate1()) && !StrUtil.isEmpty(o.getEndDate1())) {
            o.setEndDate1(o.getEndDate1() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate2()) && !StrUtil.isEmpty(o.getEndDate2())) {
            o.setEndDate2(o.getEndDate2() + " 23:59:59");
        }
        if (!StrUtil.isEmpty(o.getStartDate3()) && !StrUtil.isEmpty(o.getEndDate3())) {
            o.setEndDate3(o.getEndDate3() + " 23:59:59");
        }
        List<MaterialTeamApprovalNode> list = materialTeamApprovalNodeService.queryMaterialTeamApprovalNodeList(o);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出 临床 物资组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode:export')")
    @Log(title = "物资组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/MaterialTeamApprovalNode/export")
    public void export(HttpServletResponse response, MaterialTeamApprovalNode o) throws Exception {
        if (StrUtil.isEmpty(o.getStartDate2()) || StrUtil.isEmpty(o.getEndDate2())) {
            Map<String, String> map = QueryTimeProcessing.returnTime();
            o.setStartDate2(map.get("startDate"));
            o.setEndDate2(map.get("endDate"));
        }
        o.setEndDate1(o.getEndDate2() + " 23:59:59");
        QueryTimeProcessing.timeProcessing(o.getStartDate2(), o.getEndDate2(),"请选择节点操作时间间隔半年内的数据进行导出",180);
        List<MaterialTeamApprovalNode> list = materialTeamApprovalNodeService.queryMaterialTeamApprovalNodeList(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "物资组表单审批节点合集","跳转OA表单");
    }

    /**
     * 导出 临床 物资组表单审批节点合集 全部
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode:exportAll')")
    @Log(title = "物资组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/MaterialTeamApprovalNode/exportAll")
    public void exportAll(HttpServletResponse response, MaterialTeamApprovalNode o) throws Exception {
        List<MaterialTeamApprovalNode> list = materialTeamApprovalNodeService.queryMaterialTeamApprovalNodeListALL(o);
        ExcelUtils.downloadAvoidStyleLink(response, list, "物资组表单审批节点合集","跳转OA表单");
    }

    /**
     * 临床 2物资组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode2:list')")
    @GetMapping("/MaterialTeamApprovalNode2/list")
    public TableDataInfo list(MaterialTeamApprovalNode2 o) {
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        if (!StrUtil.isEmpty(o.getStartDate1()) && !StrUtil.isEmpty(o.getEndDate1())) {
            o.setEndDate1(o.getEndDate1() + " 23:59:59");
        }
        List<MaterialTeamApprovalNode2> list = materialTeamApprovalNode2Service.queryMaterialTeamApprovalNode2List(o);

        int num = list.size();
        list = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(num);
//        return getDataTable(list);
        return rspData;
    }

    /**
     * 导出 临床 2物资组表单审批节点合集
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode2:export')")
    @Log(title = "2物资组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/MaterialTeamApprovalNode2/export")
    public void export(HttpServletResponse response, MaterialTeamApprovalNode2 o) throws Exception {
        if (StrUtil.isEmpty(o.getStartDate2()) || StrUtil.isEmpty(o.getEndDate2())) {
            Map<String, String> map = QueryTimeProcessing.returnTime();
            o.setStartDate2(map.get("startDate"));
            o.setEndDate2(map.get("endDate"));
        }
        o.setEndDate2(o.getEndDate2() + " 23:59:59");
        QueryTimeProcessing.timeProcessing(o.getStartDate2(), o.getEndDate2(),"请选择节点到达时间间隔半年内的数据进行导出",180);
        List<MaterialTeamApprovalNode2> list = materialTeamApprovalNode2Service.queryMaterialTeamApprovalNode2List(o);
        list.forEach(e->{
            e.setExportTime(LocalDate.now());
        });
        ExcelUtils.downloadAvoidStyleLink(response, list, "2物资组表单审批节点合集","跳转OA表单");
    }

    /**
     * 导出 临床 2物资组表单审批节点合集 全部
     */
    @PreAuthorize("@ss.hasPermi('ClinicalPersonnelTwo:MaterialTeamApprovalNode2:exportAll')")
    @Log(title = "2物资组表单审批节点合集", businessType = BusinessType.EXPORT)
    @PostMapping("/MaterialTeamApprovalNode2/exportAll")
    public void exportAll(HttpServletResponse response, MaterialTeamApprovalNode2 o) throws Exception {
        List<MaterialTeamApprovalNode2> list = materialTeamApprovalNode2Service.queryMaterialTeamApprovalNode2ListALL(o);
        list.forEach(e->{
            e.setExportTime(LocalDate.now());
        });
        ExcelUtils.downloadAvoidStyleLink(response, list, "2物资组表单审批节点合集","跳转OA表单");
    }
}