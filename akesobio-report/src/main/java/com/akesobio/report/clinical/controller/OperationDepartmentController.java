package com.akesobio.report.clinical.controller;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.clinical.domain.OperationDepartment;
import com.akesobio.report.clinical.service.IOperationDepartmentService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;

/**
 * 每日汇报-运行部Controller
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/clinical/operationDepartment")
public class OperationDepartmentController extends BaseController
{
    @Autowired
    private IOperationDepartmentService operationDepartmentService;

    /**
     * 查询每日汇报-运行部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:operationDepartment:list')")
    @GetMapping("/list")
    public TableDataInfo list(OperationDepartment operationDepartment)
    {
        startPage();
        List<OperationDepartment> list = operationDepartmentService.selectOperationDepartmentList(operationDepartment);
        return getDataTable(list);
    }

    /**
     * 导出每日汇报-运行部列表
     */
    @PreAuthorize("@ss.hasPermi('clinical:operationDepartment:export')")
    @Log(title = "每日汇报-运行部", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OperationDepartment operationDepartment)
    {
        List<OperationDepartment> list = operationDepartmentService.selectOperationDepartmentList(operationDepartment);
        ExcelUtil<OperationDepartment> util = new ExcelUtil<OperationDepartment>(OperationDepartment.class);
        util.exportExcel(response, list, "每日汇报-运行部数据");
    }

}
