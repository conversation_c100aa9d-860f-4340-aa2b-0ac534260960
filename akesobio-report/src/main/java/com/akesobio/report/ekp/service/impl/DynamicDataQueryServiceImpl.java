package com.akesobio.report.ekp.service.impl;

import com.akesobio.report.ekp.mapper.ModelingAppModelMapper;
import com.akesobio.report.ekp.service.DynamicDataQueryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态数据查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
@Slf4j
public class DynamicDataQueryServiceImpl implements DynamicDataQueryService {
    
    @Autowired
    private ModelingAppModelMapper modelingAppModelMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String getDynamicDataAsJson(String documentNumber, String documentNumberField, String mainTableName, List<String> childTableNames) {
        // 参数验证
        validateParameters(documentNumber, documentNumberField, mainTableName, childTableNames);
        
        try {
            log.info("开始动态查询数据 - 单号: {}, 单号字段: {}, 主表: {}, 子表: {}", documentNumber, documentNumberField, mainTableName, childTableNames);

            // 1. 查询主表数据
            List<Map<String, Object>> parentData = queryParentTable(documentNumber, documentNumberField, mainTableName);
            if (parentData.isEmpty()) {
                log.warn("未找到主表数据 - 单号: {}, 单号字段: {}, 主表: {}", documentNumber, documentNumberField, mainTableName);
                return objectMapper.writeValueAsString(new ArrayList<>());
            }
            
            // 2. 提取主表记录的ID列表
            List<String> parentIds = parentData.stream()
                    .map(record -> String.valueOf(record.get("fd_id")))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (parentIds.isEmpty()) {
                log.warn("主表数据中未找到有效的fd_id字段");
                return objectMapper.writeValueAsString(parentData);
            }
            
            // 3. 查询所有子表数据并按表名分组
            Map<String, List<Map<String, Object>>> childDataByTable = new HashMap<>();
            for (String childTableName : childTableNames) {
                List<Map<String, Object>> childData = queryChildTable(childTableName, parentIds);
                
                // 按fd_parent_id分组子表数据
                Map<String, List<Map<String, Object>>> groupedChildData = childData.stream()
                        .collect(Collectors.groupingBy(record -> String.valueOf(record.get("fd_parent_id"))));
                
                childDataByTable.put(childTableName, new ArrayList<>());
                for (List<Map<String, Object>> group : groupedChildData.values()) {
                    childDataByTable.get(childTableName).addAll(group);
                }
            }
            
            // 4. 将子表数据嵌入主表记录中
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> parentRecord : parentData) {
                Map<String, Object> resultRecord = new HashMap<>(parentRecord);
                String parentId = String.valueOf(parentRecord.get("fd_id"));
                
                // 为每个子表添加数据
                for (int i = 0; i < childTableNames.size(); i++) {
                    String childTableName = childTableNames.get(i);
                    List<Map<String, Object>> allChildData = childDataByTable.get(childTableName);
                    
                    // 筛选属于当前父记录的子表数据
                    List<Map<String, Object>> parentChildData = allChildData.stream()
                            .filter(childRecord -> parentId.equals(String.valueOf(childRecord.get("fd_parent_id"))))
                            .collect(Collectors.toList());
                    
                    // 使用动态key名称，基于现有模式
                    String childKey = generateChildKey(i);
                    resultRecord.put(childKey, parentChildData);
                }
                
                // 添加kf_fdId字段（参考现有实现）
                resultRecord.put("kf_fdId", resultRecord.get("fd_id"));
                resultList.add(resultRecord);
            }
            
            // 5. 转换为JSON字符串
            String jsonResult = objectMapper.writeValueAsString(resultList);
            log.info("动态查询完成，返回 {} 条主表记录", resultList.size());
            return jsonResult;
            
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败", e);
            throw new RuntimeException("JSON序列化失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("动态数据查询失败", e);
            throw new RuntimeException("动态数据查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 参数验证
     */
    private void validateParameters(String documentNumber, String documentNumberField, String mainTableName, List<String> childTableNames) {
        if (!StringUtils.hasText(documentNumber)) {
            throw new IllegalArgumentException("单号不能为空");
        }
        if (!StringUtils.hasText(documentNumberField)) {
            throw new IllegalArgumentException("单号字段名不能为空");
        }
        if (!StringUtils.hasText(mainTableName)) {
            throw new IllegalArgumentException("主表名称不能为空");
        }
        if (childTableNames == null || childTableNames.isEmpty()) {
            throw new IllegalArgumentException("子表名称列表不能为空");
        }

        // 验证表名和字段名格式（基本的SQL注入防护）
        validateTableName(mainTableName);
        validateFieldName(documentNumberField);
        for (String childTableName : childTableNames) {
            if (!StringUtils.hasText(childTableName)) {
                throw new IllegalArgumentException("子表名称不能为空");
            }
            validateTableName(childTableName);
        }
    }
    
    /**
     * 验证表名格式，防止SQL注入
     */
    private void validateTableName(String tableName) {
        if (!tableName.matches("^[a-zA-Z0-9_]+$")) {
            throw new IllegalArgumentException("表名格式无效: " + tableName);
        }
    }

    /**
     * 验证字段名格式，防止SQL注入
     */
    private void validateFieldName(String fieldName) {
        if (!fieldName.matches("^[a-zA-Z0-9_]+$")) {
            throw new IllegalArgumentException("字段名格式无效: " + fieldName);
        }
    }

    /**
     * 查询主表数据
     */
    private List<Map<String, Object>> queryParentTable(String documentNumber, String documentNumberField, String mainTableName) {
        Map<String, Object> params = new HashMap<>();
        params.put("mainTableName", mainTableName);
        params.put("documentNumber", documentNumber);
        params.put("documentNumberField", documentNumberField);
        return modelingAppModelMapper.selectDynamicParentTable(params);
    }
    
    /**
     * 查询子表数据
     */
    private List<Map<String, Object>> queryChildTable(String childTableName, List<String> parentIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("childTableName", childTableName);
        params.put("parentIds", parentIds);
        return modelingAppModelMapper.selectDynamicChildTable(params);
    }
    
    /**
     * 生成子表数据的key名称（参考现有实现模式）
     */
    private String generateChildKey(int index) {
        // 参考现有实现中的key命名模式
        switch (index) {
            case 0:
                return "fd_39f277be2bc29a";  // 第一个子表使用现有的key
            case 1:
                return "fd_3b52a4e5a433e2";  // 第二个子表使用现有的key
            default:
                return "child_table_" + index;  // 更多子表使用通用命名
        }
    }
}
