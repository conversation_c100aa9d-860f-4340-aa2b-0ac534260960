package com.akesobio.report.ekp.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * 流程模板类别对象 sys_category_main
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Data
public class SysCategoryMain {
    /**
     * id
     */
    private String id;

    /**
     * 目录名称
     */
    private String name;

    /**
     * 描述
     */
    private String desc;

    /**
     * 层级ID
     */
    private String hierarchyId;

    /**
     * 排序号
     */
    private Integer order;

    /**
     * 模板类名
     */
    private String modelName;

    /**
     * 创建时间
     */
    private Date docCreateTime;

    /**
     * 修改时间
     */
    private Date docAlterTime;

    /**
     * 继承可维护者
     */
    private Integer isInheritMaintainer;

    /**
     * 继承可使用者
     */
    private Integer isInheritUser;

    /**
     * 可阅读者标记
     */
    private Integer authReaderFlag;

    /**
     * 创建人
     */
    private String docCreatorId;

    /**
     * 修改人
     */
    private String docAlterorId;

    /**
     * 父类别名称
     */
    private String parentId;

    /**
     * 所属场所
     */
    private String authAreaId;

    /**
     * 中文
     */
    private String nameCn;

    /**
     * 英文
     */
    private String nameUs;
}
