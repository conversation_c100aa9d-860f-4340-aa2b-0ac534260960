package com.akesobio.report.ekp.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * HR流程的转换处理类
 *
 * @name: ProcessHrDTO
 * @author: shenglin.qin
 * @date: 2023-09-26 09:44
 **/
@Data
public class ProcessHrDTO {
    /**
     * id
     */
    private String id;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 主题
     */
    private String docSubject;
    /**
     * 当前处理人
     */
    private String currentProcessor;
    /**
     * 类名
     */
    private String modelClass;
    /**
     * 流程管理模板名称
     */
    private String kmTemplateName;
    /**
     * 业务建模模板名称
     */
    private String modelingTemplateName;
    /**
     * 人事流程模板名称
     */
    private String hrTemplateName;
    /**
     * 流程管理单号
     */
    private String kmNumber;
    /**
     * 业务建模单号
     */
    private String modelingNumber;
    /**
     * 入职单号
     */
    private String hrRatifyNumber;
}
