package com.akesobio.report.ekp.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 业务模块对象 modeling_app_model
 *
 * <AUTHOR>
 * @date 2023-09-25
 */
@Data
public class ModelingAppModel {

    /**
     * id
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 基础模块id
     */
    private String baseModelId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 主题自动生成规则实际值
     */
    private String subjectRegulationValue;

    /**
     * 主题自动生成规则文本
     */
    private String subjectRegulationText;

    /**
     * 机制部署配置
     */
    private String mechanismConfig;

    /**
     * 是否开启流程
     */
    private Integer enableFlow;

    /**
     * 是否开启校验
     */
    private Integer valid;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docCreateTime;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 录入者
     */
    private String docCreatorId;

    /**
     * 所有人可阅读标记
     */
    private Integer authReaderFlag;

    /**
     * 在阅读状态所有人不可下载
     */
    private Integer authTmpAttNodownload;

    /**
     * 在阅读状态所有人不可拷贝
     */
    private Integer authTmpAttNocopy;

    /**
     * 在阅读状态所有人不可打印
     */
    private Integer authTmpAttNoprint;

    /**
     * 起草人不可修改可阅读者
     */
    private Integer changeReaderFlag;

    /**
     * 起草人不可修改可编辑者
     */
    private Integer changeEditorFlag;

    /**
     * 权限生效时间
     */
    private Integer rbpFlag;

    /**
     * 起草人不可修改附件权限
     */
    private Integer changeAtt;

    /**
     * 树结构
     */
    private Integer tree;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 所有人不可阅读标记
     */
    private Integer authNotReaderFlag;

    /**
     * 修改人
     */
    private String docAlterorId;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docAlterTime;
}
