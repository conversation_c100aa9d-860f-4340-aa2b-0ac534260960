package com.akesobio.report.ekp.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 流程工具类
 *
 * @author: shenglin.qin
 **/
public class ProcessUtils {
    private static final Map<String, String> LINK_TEMPLATE_MAP;

    static {
        LINK_TEMPLATE_MAP = new HashMap<>();
        String prefix = "https://oa.akesobio.com";
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.summary.model.KmSummaryMain", prefix + "/km/summary/km_summary_main/kmSummaryMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.imeeting.model.KmImeetingSummary", prefix + "/km/imeeting/km_imeeting_summary/kmImeetingSummary.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.hr.ratify.model.HrRatifyLeave", prefix + "/hr/ratify/hr_ratify_leave/hrRatifyLeave.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.kms.multidoc.model.KmsMultidocKnowledge", prefix + "/kms/multidoc/kms_multidoc_knowledge/kmsMultidocKnowledge.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.hr.ratify.model.HrRatifyTransfer", prefix + "/hr/ratify/hr_ratify_transfer/hrRatifyTransfer.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.carmng.model.KmCarmngApplication", prefix + "/km/carmng/km_carmng_application/kmCarmngApplication.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.imeeting.model.KmImeetingMain", prefix + "/km/imeeting/km_imeeting_main/kmImeetingMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.hr.ratify.model.HrRatifyEntry", prefix + "/hr/ratify/hr_ratify_entry/hrRatifyEntry.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.hr.ratify.model.HrRatifyPositive", prefix + "/hr/ratify/hr_ratify_positive/hrRatifyPositive.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.oitems.model.KmOitemsBudgerApplication", prefix + "/km/oitems/km_oitems_budger_application/kmOitemsBudgerApplication.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.kms.kmaps.model.KmsKmapsMain", prefix + "/kms/kmaps/kms_kmaps_main/kmsKmapsMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.sys.news.model.SysNewsMain", prefix + "/sys/news/sys_news_main/sysNewsMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.archives.model.KmArchivesMain", prefix + "/km/archives/km_archives_main/kmArchivesMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.km.review.model.KmReviewMain", prefix + "/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${fdId}");
        LINK_TEMPLATE_MAP.put("com.landray.kmss.sys.modeling.main.model.ModelingAppModelMain", prefix + "/sys/modeling/main/modelingAppModelMain.do?method=view&fdId=${fdId}");
    }

    public static String getProcessLink(String modelClass, String processId) {
        if (LINK_TEMPLATE_MAP.containsKey(modelClass)) {
            return LINK_TEMPLATE_MAP.get(modelClass).replace("${fdId}", processId);
        }else {
            return null;
        }
    }
}
