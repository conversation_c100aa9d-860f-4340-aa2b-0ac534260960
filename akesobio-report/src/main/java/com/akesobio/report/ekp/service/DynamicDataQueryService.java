package com.akesobio.report.ekp.service;

import java.util.List;

/**
 * 动态数据查询服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface DynamicDataQueryService {
    
    /**
     * 根据用户提供的参数动态查询主表和子表数据，并组装成JSON格式
     *
     * @param documentNumber 单号（如：'YXHT-20250409001'）
     * @param documentNumberField 主表中单号字段名称（如：'fd_3996ac8c37d5a0'）
     * @param mainTableName 主表名称（如：'ekp_flwhtsp'）
     * @param childTableNames 子表名称列表（如：['ekp_kp_flwhtsp_77be2bc29a', 'ekp_kp_flwhtsp_a4e5a433e2']）
     * @return JSON字符串，包含主表数据及其关联的子表数据
     * @throws IllegalArgumentException 当参数为空或无效时抛出
     * @throws RuntimeException 当数据查询或JSON序列化失败时抛出
     */
    String getDynamicDataAsJson(String documentNumber, String documentNumberField, String mainTableName, List<String> childTableNames);
}
