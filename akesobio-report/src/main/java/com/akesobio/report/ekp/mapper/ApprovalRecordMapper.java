package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.dto.ApprovalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


//todo 生产环境下记得换回来
@Mapper
@DataSource(DataSourceType.EKP)
public interface ApprovalRecordMapper {
    /**
     * 根据流程ID查询审批记录
     *
     * @param processId 流程ID
     * @param dynamicTableName 动态表名
     * @return 审批记录列表
     */
    List<ApprovalRecord> findApprovalRecordsByProcessId(
            @Param("dynamicTableName") String dynamicTableName,     // 动态表名// 动态表中用于过滤processId的列
            @Param("processId") String processId
    );

    String findFdIdByDocumentNumber(@Param("dynamicTableName") String dynamicTableName,
                                   @Param("filedName") String filedName,
                                   @Param("documentNumber") String documentNumber);
//    List<ApprovalRecord> findApprovalRecordsByProcessId(@Param("processId") String processId);


}
