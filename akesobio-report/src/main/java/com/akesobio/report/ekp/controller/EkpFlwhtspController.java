package com.akesobio.report.ekp.controller;

import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.ekp.service.EkpFlwhtspService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * EKP流程相关Controller
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/ekp/flwhtsp")
public class EkpFlwhtspController extends BaseController {
    
    @Autowired
    private EkpFlwhtspService ekpFlwhtspService;
    
    /**
     * 获取父表和子表数据的JSON格式
     * @return 包含父表及关联子表数据的JSON响应
     */
    @GetMapping("/data")
    public AjaxResult getFlwhtspData() {
        try {
            String jsonData = ekpFlwhtspService.getFlwhtspDataAsJson();
            return AjaxResult.success("数据查询成功", jsonData);
        } catch (Exception e) {
            logger.error("查询EKP流程数据失败", e);
            return AjaxResult.error("数据查询失败: " + e.getMessage());
        }
    }
} 