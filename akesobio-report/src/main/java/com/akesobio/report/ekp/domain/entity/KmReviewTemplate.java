package com.akesobio.report.ekp.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 流程管理模板对象 km_review_template
 *
 * <AUTHOR>
 * @date 2023-09-25
 */

@Data
public class KmReviewTemplate {

    /**
     * id
     */
    private String id;

    /**
     * 同步时机
     */
    private String syncDataToCalendarTime;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String desc;

    /**
     * 模板开启/关闭状态
     */
    private Integer isAvailable;

    /**
     * 是否允许传阅
     */
    private Integer canCircularize;

    /**
     * 审批记录可见
     */
    private Integer lableVisiable;

    /**
     * 允许申请人修改
     */
    private Integer feedbackModify;

    /**
     * 排序号
     */
    private Integer order;

    /**
     * 编号前缀
     */
    private String numberPrefix;

    /**
     * 内容
     */
    private String docContent;

    /**
     * 启用表单
     */
    private Integer useForm;

    /**
     * 启用Word
     */
    private Integer useWord;

    /**
     * 不支持移动端中该自定义表单的操作
     */
    private Integer disableMobileForm;

    /**
     * 创建人
     */
    private String docCreatorId;

    /**
     * 适用类别
     */
    private String categoryId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docCreateTime;

    /**
     * 阅读标记
     */
    private Integer authReaderFlag;

    /**
     * 模板不可下载标记
     */
    private Integer authTmpAttNodownload;

    /**
     * 模板不可拷贝标记
     */
    private Integer authTmpAttNocopy;

    /**
     * 模板不可打印标记
     */
    private Integer authTmpAttNoprint;

    /**
     * 外部流程
     */
    private Integer isExternal;

    /**
     * 外部链接
     */
    private String externalUrl;

    /**
     * 导入流程
     */
    private Integer isImport;

    /**
     * 导入属性ids
     */
    private String unImportFieldIds;

    /**
     * 导入属性名称
     */
    private String unImportFieldNames;

    /**
     * 启动签名
     */
    private Integer signEnable;

    /**
     * 修改人
     */
    private String docAlterorId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docAlterTime;

    /**
     * 是否可修改阅读者
     */
    private Integer changeReaderFlag;

    /**
     * 权限生效时间
     */
    private Integer rbpFlag;

    /**
     * 是否可修改附件
     */
    private Integer changeAtt;

    /**
     * 所属场所
     */
    private String authAreaId;

    /**
     * 主题自动生成规则
     */
    private String titleRegulation;

    /**
     * 主题自动生成规则名称
     */
    private String titleRegulationName;

    /**
     * 支持移动端新建
     */
    private Integer isMobileCreate;

    /**
     * 支持移动端审批
     */
    private Integer isMobileApprove;

    /**
     * 支持移动端查阅
     */
    private Integer isMobileView;

    /**
     * 是否允许复制流程
     */
    private Integer isCopyDoc;

    /**
     * 中文名称
     */
    private String nameCn;

    /**
     * 英文名称
     */
    private String nameUs;

    /**
     * 模板编码
     */
    private String code;

}
