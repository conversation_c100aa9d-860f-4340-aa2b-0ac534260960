package com.akesobio.report.ekp.domain.entity;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 待办事宜对象 ekp_sys_notify_todo
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
public class SysNotify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 待办来源 */
    @Excel(name = "待办来源")
    private String appName;

    /** 模块 */
    @Excel(name = "模块")
    private String modelName;

    /** 模块ID */
    @Excel(name = "模块ID")
    private String modelId;

    /** 关键字 */
    @Excel(name = "关键字")
    private String key;
    private String parameter1;
    private String parameter2;

    /** 标题 */
    @Excel(name = "标题")
    private String subject;

    /** 类型 */
    @Excel(name = "类型")
    private Integer type;

    /** 链接 */
    @Excel(name = "链接")
    private String link;

    /** 手机链接 */
    @Excel(name = "手机链接")
    private String mobileLink;

    /** pad链接 */
    @Excel(name = "pad链接")
    private String padLink;

    private String bundle;
    private String replaceText;
    private String md5;
    private String delFlag;

    /** 优先级 */
    @Excel(name = "优先级")
    private Integer level;

    /** 发起人 */
    @Excel(name = "发起人")
    private String creatorId;

    /** 发起人名称 */
    private String creatorName;

    /** 是否已读 */
    private Integer docRead;
    private String extendContent;
    private String lang;
    private String cateName;
    private String cateId;
    private String templateName;
    private String templateId;
    private String hierarchyId;
    private String subjectCn;
    private String extendContentCn;
    private String subjectUs;
    private String extendContentUs;


    public Integer getDocRead() {
        return docRead;
    }

    public void setDocRead(Integer docRead) {
        this.docRead = docRead;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setAppName(String appName) 
    {
        this.appName = appName;
    }

    public String getAppName() 
    {
        return appName;
    }
    public void setModelName(String modelName) 
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }
    public void setModelId(String modelId) 
    {
        this.modelId = modelId;
    }

    public String getModelId() 
    {
        return modelId;
    }
    public void setKey(String key) 
    {
        this.key = key;
    }

    public String getKey() 
    {
        return key;
    }
    public void setParameter1(String parameter1) 
    {
        this.parameter1 = parameter1;
    }

    public String getParameter1() 
    {
        return parameter1;
    }
    public void setParameter2(String parameter2) 
    {
        this.parameter2 = parameter2;
    }

    public String getParameter2() 
    {
        return parameter2;
    }
    public void setSubject(String subject) 
    {
        this.subject = subject;
    }

    public String getSubject() 
    {
        return subject;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setLink(String link) 
    {
        this.link = link;
    }

    public String getLink() 
    {
        return link;
    }
    public void setMobileLink(String mobileLink) 
    {
        this.mobileLink = mobileLink;
    }

    public String getMobileLink() 
    {
        return mobileLink;
    }
    public void setPadLink(String padLink) 
    {
        this.padLink = padLink;
    }

    public String getPadLink() 
    {
        return padLink;
    }
    public void setBundle(String bundle) 
    {
        this.bundle = bundle;
    }

    public String getBundle() 
    {
        return bundle;
    }
    public void setReplaceText(String replaceText) 
    {
        this.replaceText = replaceText;
    }

    public String getReplaceText() 
    {
        return replaceText;
    }
    public void setMd5(String md5) 
    {
        this.md5 = md5;
    }

    public String getMd5() 
    {
        return md5;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setLevel(Integer level) 
    {
        this.level = level;
    }

    public Integer getLevel() 
    {
        return level;
    }
    public void setCreatorId(String creatorId) 
    {
        this.creatorId = creatorId;
    }

    public String getCreatorId() 
    {
        return creatorId;
    }
    public void setExtendContent(String extendContent) 
    {
        this.extendContent = extendContent;
    }

    public String getExtendContent() 
    {
        return extendContent;
    }
    public void setLang(String lang) 
    {
        this.lang = lang;
    }

    public String getLang() 
    {
        return lang;
    }
    public void setCateName(String cateName) 
    {
        this.cateName = cateName;
    }

    public String getCateName() 
    {
        return cateName;
    }
    public void setCateId(String cateId) 
    {
        this.cateId = cateId;
    }

    public String getCateId() 
    {
        return cateId;
    }
    public void setTemplateName(String templateName) 
    {
        this.templateName = templateName;
    }

    public String getTemplateName() 
    {
        return templateName;
    }
    public void setTemplateId(String templateId) 
    {
        this.templateId = templateId;
    }

    public String getTemplateId() 
    {
        return templateId;
    }
    public void setHierarchyId(String hierarchyId) 
    {
        this.hierarchyId = hierarchyId;
    }

    public String getHierarchyId() 
    {
        return hierarchyId;
    }
    public void setSubjectCn(String subjectCn) 
    {
        this.subjectCn = subjectCn;
    }

    public String getSubjectCn() 
    {
        return subjectCn;
    }
    public void setExtendContentCn(String extendContentCn) 
    {
        this.extendContentCn = extendContentCn;
    }

    public String getExtendContentCn() 
    {
        return extendContentCn;
    }
    public void setSubjectUs(String subjectUs) 
    {
        this.subjectUs = subjectUs;
    }

    public String getSubjectUs() 
    {
        return subjectUs;
    }
    public void setExtendContentUs(String extendContentUs) 
    {
        this.extendContentUs = extendContentUs;
    }

    public String getExtendContentUs() 
    {
        return extendContentUs;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appName", getAppName())
            .append("modelName", getModelName())
            .append("modelId", getModelId())
            .append("key", getKey())
            .append("parameter1", getParameter1())
            .append("parameter2", getParameter2())
            .append("createTime", getCreateTime())
            .append("subject", getSubject())
            .append("type", getType())
            .append("link", getLink())
            .append("mobileLink", getMobileLink())
            .append("padLink", getPadLink())
            .append("bundle", getBundle())
            .append("replaceText", getReplaceText())
            .append("md5", getMd5())
            .append("delFlag", getDelFlag())
            .append("level", getLevel())
            .append("creatorId", getCreatorId())
            .append("extendContent", getExtendContent())
            .append("lang", getLang())
            .append("cateName", getCateName())
            .append("cateId", getCateId())
            .append("templateName", getTemplateName())
            .append("templateId", getTemplateId())
            .append("hierarchyId", getHierarchyId())
            .append("subjectCn", getSubjectCn())
            .append("extendContentCn", getExtendContentCn())
            .append("subjectUs", getSubjectUs())
            .append("extendContentUs", getExtendContentUs())
            .append("creatorName", getCreatorName())
            .append("docRead", getDocRead())
            .toString();
    }
}
