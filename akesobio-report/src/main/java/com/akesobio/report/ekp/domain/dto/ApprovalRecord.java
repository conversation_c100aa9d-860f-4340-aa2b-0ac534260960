package com.akesobio.report.ekp.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 审批记录实体类
 */
@Data
@TableName("ekp_flwhtsp")
public class ApprovalRecord {
    /**
     * 节点名称
     */
    @TableField("fd_fact_node_name")
    private String nodeName;

    /**
     * 审批人
     */
    @TableField("fd_staff_no")
    private String staffNo;

    /**
     * 创建时间
     */
    @TableField("fd_create_time")

    private Date createTime;
    /**
     * 审批时间
     */
    @TableField("approve_time")
    private Date approveTime;
}