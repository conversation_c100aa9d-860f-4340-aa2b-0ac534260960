package com.akesobio.report.ekp.service.impl;

import com.akesobio.report.ekp.mapper.ModelingAppModelMapper;
import com.akesobio.report.ekp.service.EkpFlwhtspService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * EKP流程相关Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class EkpFlwhtspServiceImpl implements EkpFlwhtspService {
    
    @Autowired
    private ModelingAppModelMapper modelingAppModelMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String getFlwhtspDataAsJson() {
        try {
            // 1. 查询父表数据
            List<Map<String, Object>> parentData = modelingAppModelMapper.selectParentTable();
            
            // 2. 查询两个子表数据
            List<Map<String, Object>> childTable1Data = modelingAppModelMapper.selectChildTable1();
            List<Map<String, Object>> childTable2Data = modelingAppModelMapper.selectChildTable2();
            
            // 3. 按fd_parent_id分组子表数据
            Map<String, List<Map<String, Object>>> childTable1Grouped = childTable1Data.stream()
                    .collect(Collectors.groupingBy(record -> String.valueOf(record.get("fd_parent_id"))));
            
            Map<String, List<Map<String, Object>>> childTable2Grouped = childTable2Data.stream()
                    .collect(Collectors.groupingBy(record -> String.valueOf(record.get("fd_parent_id"))));

            // 4. 将子表数据嵌入父表记录中
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (Map<String, Object> parentRecord : parentData) {
                Map<String, Object> resultRecord = new HashMap<>(parentRecord);
                String parentId = String.valueOf(parentRecord.get("fd_id"));
                
                // 添加第一个子表数据（使用fd_39f277be2bc29a作为key）
                List<Map<String, Object>> child1List = childTable1Grouped.getOrDefault(parentId, new ArrayList<>());
                resultRecord.put("fd_39f277be2bc29a", child1List);
                
                // 添加第二个子表数据（使用fd_3b52a4e5a433e2作为key）
                List<Map<String, Object>> child2List = childTable2Grouped.getOrDefault(parentId, new ArrayList<>());
                resultRecord.put("fd_3b52a4e5a433e2", child2List);
                resultRecord.put("kf_fdId",resultRecord.get("fd_id"));
                resultList.add(resultRecord);
            }
            // 5. 转换为JSON字符串
            return objectMapper.writeValueAsString(resultList);

            
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON序列化失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("数据查询聚合失败: " + e.getMessage(), e);
        }
    }
} 