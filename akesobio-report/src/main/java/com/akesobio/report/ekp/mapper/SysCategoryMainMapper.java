package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.entity.SysCategoryMain;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 流程模板类别Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface SysCategoryMainMapper {
    public List<SysCategoryMain> selectCategoryList();
}
