package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.dto.CategoryTemplate;
import com.akesobio.report.ekp.domain.dto.ProcessHrDTO;
import com.akesobio.report.ekp.domain.entity.LbpmProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * OA流程实例Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-18
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface LbpmProcessMapper
{
    /**
     * 查询指定创建人发起的流程
     * 
     * @param creatorId 创建人ID
     * @return OA流程实例
     */
    public List<LbpmProcess> selectProcessByFdCreatorId(String creatorId);

    /**
     * 根据模板查询在途流程，分页非常慢
     * @param kmReviewIds 流程管理的模板id
     * @param modelingIds 业务建模的模板id
     */
    public List<LbpmProcess> selectRunningProcessByTemplateIds(@Param("kmReviews")List<String> kmReviewIds, @Param("modelings") List<String> modelingIds);

    public List<ProcessHrDTO> selectHrRunningProcess(@Param("template") CategoryTemplate template);
}
