package com.akesobio.report.ekp.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 模板类别下的所有模板id
 *
 * @name: CategoryTemplate
 * @author: shenglin.qin
 * @date: 2023-09-27 09:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CategoryTemplate {
    private List<String> kmTemplateIds;
    private List<String> modelingTemplateIds;
    private List<String> hrRatifyModelIds;
    private Map<String, String> templates;
}
