package com.akesobio.report.ekp.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
@DataSource(DataSourceType.EKP)
public interface EkpCommonMapper {

    public List<Map<String, Object>> ekpBmcbzx();

    public List<Map<String, Object>> ekpUser();

    public List<Map<String, Object>> ekpDept();

    public List<Map<String, Object>> sysNotifyTodo();

    public List<Map<String, Object>> selectAllHrStaffPersonInfo();

}
