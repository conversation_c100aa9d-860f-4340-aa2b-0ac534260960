package com.akesobio.report.ekp.mapper;

import com.akesobio.report.ekp.domain.entity.TotalContractAmount;
import com.akesobio.report.ekp.domain.entity.IITProjectAmount;
import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * IIT项目相关数据库操作接口
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface IITProjectMapper {

    /**
     * 根据项目编号查询IIT立项申请的有效总金额及其限额
     *
     * @param projectCode 项目编号
     * @return IIT项目金额信息
     */
    IITProjectAmount getProjectAmountByCode(@Param("projectCode") String projectCode);

    /**
     * 根据项目编号查询合同金额信息
     *
     * @param projectCode 项目编号
     * @return 合同金额信息
     */
    TotalContractAmount getTotalContractAmountByProjectCode(@Param("projectCode") String projectCode);
}