package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.entity.ModelingAppModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 业务模块Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-25
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface ModelingAppModelMapper 
{
    public List<ModelingAppModel> selectAll();

    public Map<String,Object> ekpFlwhtsp();

    /**
     * 查询父表ekp_flwhtsp
     * @return 父表数据列表
     */
    public List<Map<String,Object>> selectParentTable();
    
    /**
     * 查询子表ekp_kp_flwhtsp_77be2bc29a
     * @return 子表1数据列表
     */
    public List<Map<String,Object>> selectChildTable1();
    
    /**
     * 查询子表ekp_kp_flwhtsp_a4e5a433e2
     * @return 子表2数据列表
     */
    public List<Map<String,Object>> selectChildTable2();

    /**
     * 动态查询主表数据
     * @param params 查询参数，包含mainTableName、documentNumber和documentNumberField
     * @return 主表数据列表
     */
    public List<Map<String,Object>> selectDynamicParentTable(Map<String, Object> params);

    /**
     * 动态查询子表数据
     * @param params 查询参数，包含childTableName和parentIds
     * @return 子表数据列表
     */
    public List<Map<String,Object>> selectDynamicChildTable(Map<String, Object> params);
}
