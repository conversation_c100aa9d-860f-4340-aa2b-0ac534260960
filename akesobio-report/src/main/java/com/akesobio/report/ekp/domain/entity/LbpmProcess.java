package com.akesobio.report.ekp.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * OA流程实例对象 ekp_lbpm_process
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Data
public class LbpmProcess {

    /**
     * ID
     */
    private String fdId;

    /**
     * 实例名
     */
    private String fdName;

    /**
     * 流程标题
     */
    private String docSubject;

    /**
     * 流程定义Id
     */
    private String fdTemplateId;

    /**
     * 状态
     */
    private String fdStatus;

    /**
     * 主文档的model类名
     */
    private String fdModelName;

    /**
     * 主文档ID
     */
    private String fdModelId;

    /**
     * 业务模板ID
     */
    private String fdTemplateModelId;

    /**
     * 主文档标识
     */
    private String fdKey;

    /**
     * 详细流程定义
     */
    private String fdDetail;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fdCreateTime;

    /**
     * 年份
     */
    private String fdCreateYear;

    /**
     * 月份
     */
    private String fdCreateMonth;

    /**
     * 最后处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fdLastHandleTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fdEndedTime;

    /**
     * 申请人id
     */
    private String fdCreatorId;

    /**
     * 加载方式
     */
    private Integer fdLoadType;

    /**
     * 流程耗时
     */
    private String fdCostTime;

    /**
     * 当前节点时间
     */
    private String fdWorkTime;

    /**
     * 耗时排名
     */
    private Double fdEfficiency;

    /**
     * 提交人身份
     */
    private String fdIdentityId;

    /**
     * 层级Id
     */
    private String fdHierarchyId;

    /**
     * 串联流程（源流程实例id）
     */
    private String fdSourceId;

    /**
     * 父流程节点fdId
     */
    private String fdParentNodeFdid;

    /**
     * 父流程节点Id
     */
    private String fdParentNodeid;

    /**
     * 子流程状态
     */
    private Integer fdSubStatus;

    /**
     * 是否已删除
     */
    private Integer docDeleteFlag;

    /**
     * 父流程
     */
    private String fdParentid;

    /**
     * 状态
     */
    private String docStatus;

    /**
     * 当前处理人
     */
    private String currentProcessor;
}
