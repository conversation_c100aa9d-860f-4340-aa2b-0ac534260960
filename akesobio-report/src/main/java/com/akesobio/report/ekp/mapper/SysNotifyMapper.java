package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.entity.SysNotify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 待办事宜Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface SysNotifyMapper
{
    /**
     * 获取指定id处理人的当前待办
     * @param handlerId 当前处理人id
     * @return 待办事宜集合
     */
    public List<SysNotify> selectTodoByHandlerId(String handlerId);


    /**
     * 获取指定id处理人的已完成的待办
     * @param handlerId 当前处理人id
     * @return 已完成待办事宜集合
     */
    public List<SysNotify> selectDoneByHandlerId(String handlerId);


    /**
     * 获取指定目标id的待办通知
     * @param targetId 目标id
     * @return 系统待办事宜（通知）集合
     */
    public List<SysNotify> selectNoticeByTargetId(String targetId);

    /**
     * 获取指定id的待办
     * @param id 待办id
     * @return 待办
     */
    public SysNotify selectNotifyById(@Param("id")String id);


    /**
     * 获取指定id的系统通知
     * @param id 系统通知id
     * @return 系统通知
     */
    public SysNotify selectSystemNotifyById(@Param("id")String id);



    /**
     * 查询员工对应待办的行数
     * @param targetId 员工id
     * @param todoId 待办id
     * @return 行数
     */
    public int selectTargetTodo(@Param("targetId") String targetId, @Param("todoId")String todoId);

    /**
     * 查询员工对应通知的行数
     * @param targetId 员工id
     * @param todoId 通知id
     * @return 行数
     */
    public int selectSystemTargetTodo(@Param("targetId") String targetId, @Param("todoId")String todoId);
}
