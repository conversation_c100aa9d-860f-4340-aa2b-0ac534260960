package com.akesobio.report.ekp.mapper;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.ekp.domain.entity.KmReviewTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程管理模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-25
 */
@Mapper
@DataSource(DataSourceType.EKP)
public interface KmReviewTemplateMapper
{
    public List<KmReviewTemplate> selectAllTemplate();

    public List<KmReviewTemplate> selectTemplateByIds(@Param("list") List<String> ids);
}
