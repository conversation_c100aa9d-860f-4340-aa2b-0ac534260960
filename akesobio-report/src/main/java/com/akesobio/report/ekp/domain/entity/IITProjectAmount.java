package com.akesobio.report.ekp.domain.entity;


import java.math.BigDecimal;
import lombok.Data;

/**
 * IIT项目金额信息实体类
 */
@Data
public class IITProjectAmount {
    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 有效总金额（排除特定行类型后的金额）
     */
    private BigDecimal validAmount;

    /**
     * 有效总金额限额（有效总金额的102.5%）
     */
    private BigDecimal validAmountLimit;
}