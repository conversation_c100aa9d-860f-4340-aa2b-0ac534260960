package com.akesobio.report.caseCollect.client;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.time.Instant;
@Component
@Slf4j
public class IdentifyClient {
    private static final String IDENTIFY_URL = "https://api.regenai.com/v1/general/fetch";
    private static final String IDENTIFY_KEY = "3cnttit13jbgrt6g";
    private static final String IDENTIFY_SECRET = "os7vkhb03f74fnh5hfgitx02fb5h2d1r9mwe317n";

    public String uploadIdentify(MultipartFile file) throws IOException {
        // 记录方法开始时间
        long startTime = System.currentTimeMillis();
        log.info("识别开始时间：{}",startTime);

        StringBuilder resultString = new StringBuilder();
        byte[] fileBytes = file.getBytes();
        String fileName = file.getOriginalFilename();
        long epochSecond = Instant.now().getEpochSecond();
        String combine = IDENTIFY_KEY + "+" + epochSecond + "+" + IDENTIFY_SECRET;
        String token = DigestUtil.md5Hex(combine);
        // 创建HTTP客户端
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 创建HTTP POST请求
        HttpPost httpPost = new HttpPost(IDENTIFY_URL);

        // 创建MultipartEntityBuilder实例
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();

        // 添加byte[]类型的变量
        builder.addTextBody("app_key",IDENTIFY_KEY);
        builder.addTextBody("token",token);
        builder.addTextBody("timestamp", String.valueOf(epochSecond));
        builder.addBinaryBody("image_file", file.getInputStream(), ContentType.MULTIPART_FORM_DATA, fileName);
        // 创建MultipartEntity实例
        HttpEntity entity = builder.build();

        // 设置HTTP请求的请求体
        httpPost.setEntity(entity);

        // 发送HTTP请求
        HttpResponse response = httpClient.execute(httpPost);

        // 处理响应
        if (response.getStatusLine().getStatusCode() == 200) {
            // 获取响应实体
            HttpEntity responseEntity = response.getEntity();
            // 读取响应数据
            String responseJson = EntityUtils.toString(responseEntity);
            // 处理成功
            JSONObject jsonObject = JSONUtil.parseObj(responseJson);
            JSONObject responseData = jsonObject.getJSONObject("response").getJSONObject("data");
            JSONArray identifyResults = responseData.getJSONArray("identify_results");
            for (Object identifyResult : identifyResults) {
                JSONObject identifyJsonObject = (JSONObject) identifyResult;
                JSONArray print = identifyJsonObject.getJSONObject("details").getJSONArray("print");
                for (Object object : print) {
                    JSONObject resultObject = (JSONObject) object;
                    // 获取result对象的值
                    String value = resultObject.getStr("result");
                    // 对每个result进行操作，例如打印或其他处理
                    resultString.append(value);
                    System.out.println(value);
                }
                System.out.println("----------------------------------");
            }
        } else {
            // 处理失败
            System.out.println("请求失败");
        }
        String identifyString = resultString.toString();
        // 记录方法结束时间
        long endTime = System.currentTimeMillis();
        // 计算方法运行时间
        long duration = endTime - startTime;
        log.info("识别总运行时间：{} 毫秒",duration);
        return identifyString;
    }
}
