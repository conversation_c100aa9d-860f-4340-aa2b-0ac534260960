package com.akesobio.report.caseCollect.mapper;

import java.util.List;
import com.akesobio.report.caseCollect.domain.CaseCollectTable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 病例收集Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface CaseCollectTableMapper extends BaseMapper<CaseCollectTable>
{
    /**
     * 查询病例收集
     * 
     * @param id 病例收集主键
     * @return 病例收集
     */
    public CaseCollectTable selectCaseCollectTableById(String id);

    /**
     * 查询病例收集列表
     * 
     * @param caseCollectTable 病例收集
     * @return 病例收集集合
     */
    public List<CaseCollectTable> selectCaseCollectTableList(CaseCollectTable caseCollectTable);

    /**
     * 新增病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    public int insertCaseCollectTable(CaseCollectTable caseCollectTable);

    /**
     * 修改病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    public int updateCaseCollectTable(CaseCollectTable caseCollectTable);

    /**
     * 删除病例收集
     * 
     * @param id 病例收集主键
     * @return 结果
     */
    public int deleteCaseCollectTableById(String id);

    /**
     * 批量删除病例收集
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaseCollectTableByIds(String[] ids);
}
