package com.akesobio.report.caseCollect.utils;

import com.akesobio.common.config.RuoYiConfig;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.file.FileUploadUtils;
import com.akesobio.common.utils.file.FileUtils;
import com.akesobio.framework.config.ServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
@Component
public class CaseImageUploadUtil {

    @Autowired
    private ServerConfig serverConfig;
    /**
     * 上传请求（单个）
     */
    public AjaxResult uploadCaseImage(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getCasePath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }
}
