package com.akesobio.report.caseCollect.service;

import java.util.List;
import com.akesobio.report.caseCollect.domain.CaseCollectTable;
import com.akesobio.report.caseCollect.vo.CaseCollectVo;

/**
 * 病例收集Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface ICaseCollectTableService 
{
    /**
     * 查询病例收集
     * 
     * @param id 病例收集主键
     * @return 病例收集
     */
    public CaseCollectVo selectCaseCollectTableById(String id);

    /**
     * 查询病例收集列表
     * 
     * @param caseCollectTable 病例收集
     * @return 病例收集集合
     */
    public List<CaseCollectTable> selectCaseCollectTableList(CaseCollectTable caseCollectTable);

    /**
     * 新增病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    public int insertCaseCollectTable(CaseCollectTable caseCollectTable);

    /**
     * 修改病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    public int updateCaseCollectTable(CaseCollectTable caseCollectTable);

    /**
     * 批量删除病例收集
     * 
     * @param ids 需要删除的病例收集主键集合
     * @return 结果
     */
    public int deleteCaseCollectTableByIds(String[] ids);

    /**
     * 删除病例收集信息
     * 
     * @param id 病例收集主键
     * @return 结果
     */
    public int deleteCaseCollectTableById(String id);
}
