package com.akesobio.report.caseCollect.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.lang.UUID;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.report.caseCollect.vo.CaseCollectVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.akesobio.report.caseCollect.mapper.CaseCollectTableMapper;
import com.akesobio.report.caseCollect.domain.CaseCollectTable;
import com.akesobio.report.caseCollect.service.ICaseCollectTableService;

import javax.annotation.Resource;

/**
 * 病例收集Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class CaseCollectTableServiceImpl implements ICaseCollectTableService 
{
    @Resource
    private CaseCollectTableMapper caseCollectTableMapper;

    /**
     * 查询病例收集
     * 
     * @param id 病例收集主键
     * @return 病例收集
     */
    @Override
    public CaseCollectVo selectCaseCollectTableById(String id)
    {
        CaseCollectTable caseCollectTable = caseCollectTableMapper.selectCaseCollectTableById(id);
        CaseCollectVo caseCollectVo = new CaseCollectVo();
        BeanUtils.copyProperties(caseCollectTable,caseCollectVo);
        return caseCollectVo;
    }

    /**
     * 查询病例收集列表
     * 
     * @param caseCollectTable 病例收集
     * @return 病例收集
     */
    @Override
    public List<CaseCollectTable> selectCaseCollectTableList(CaseCollectTable caseCollectTable)
    {
        return caseCollectTableMapper.selectCaseCollectTableList(caseCollectTable);
    }

    /**
     * 新增病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    @Override
    public int insertCaseCollectTable(CaseCollectTable caseCollectTable)
    {
        caseCollectTable.setId(UUID.randomUUID().toString());
        Date date = new Date();
        String username = SecurityUtils.getUsername();
        caseCollectTable.setCreateTime(date);
        caseCollectTable.setCreateBy(username);
        return caseCollectTableMapper.insertCaseCollectTable(caseCollectTable);
    }

    /**
     * 修改病例收集
     * 
     * @param caseCollectTable 病例收集
     * @return 结果
     */
    @Override
    public int updateCaseCollectTable(CaseCollectTable caseCollectTable)
    {
        Date date = new Date();
        String username = SecurityUtils.getUsername();
        caseCollectTable.setUpdateTime(date);
        caseCollectTable.setUpdateBy(username);
        return caseCollectTableMapper.updateCaseCollectTable(caseCollectTable);
    }

    /**
     * 批量删除病例收集
     * 
     * @param ids 需要删除的病例收集主键
     * @return 结果
     */
    @Override
    public int deleteCaseCollectTableByIds(String[] ids)
    {
        return caseCollectTableMapper.deleteCaseCollectTableByIds(ids);
    }

    /**
     * 删除病例收集信息
     * 
     * @param id 病例收集主键
     * @return 结果
     */
    @Override
    public int deleteCaseCollectTableById(String id)
    {
        return caseCollectTableMapper.deleteCaseCollectTableById(id);
    }
}
