package com.akesobio.report.caseCollect.vo;

import com.akesobio.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class CaseCollectVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 主诉 */
    @Excel(name = "主诉")
    private String chiefComplaint;

    /** 家族史、既往史 */
    @Excel(name = "家族史、既往史")
    private String familyMedicalHistory;

    /** 现病史 */
    @Excel(name = "现病史")
    private String presentIllnessHistory;

    /** 体格检查 */
    @Excel(name = "体格检查")
    private String physicalExamination;

    /** 血常规检查 */
    @Excel(name = "血常规检查")
    private String bloodRoutineExamination;

    /** 生化检查 */
    @Excel(name = "生化检查")
    private String biochemicalExamination;

    /** 肿瘤标记物 */
    @Excel(name = "肿瘤标记物")
    private String tumorMarker;

    /** 其他实验室检查 */
    @Excel(name = "其他实验室检查")
    private String laboratoryExaminationElse;

    /** PETCT影像 */
    @Excel(name = "PETCT影像")
    private String petctImage;

    /** PETCT报告 */
    @Excel(name = "PETCT报告")
    private String petctReport;

    /** CT影像 */
    @Excel(name = "CT影像")
    private String ctImage;

    /** CT报告 */
    @Excel(name = "CT报告")
    private String ctReport;

    /** MRI影像 */
    @Excel(name = "MRI影像")
    private String mriImage;

    /** MRI报告 */
    @Excel(name = "MRI报告")
    private String mriReport;

    /** 其他影像 */
    @Excel(name = "其他影像")
    private String elseImage;

    /** 其他报告 */
    @Excel(name = "其他报告")
    private String elseReport;

    /** PD-1检查 */
    @Excel(name = "PD-1检查")
    private String pd1;

    /** 基因检测 */
    @Excel(name = "基因检测")
    private String geneticTest;

    /** 免疫组化 */
    @Excel(name = "免疫组化")
    private String immunohistochemical;

    /** 其他检查 */
    @Excel(name = "其他检查")
    private String elseExamination;

    /** 病理诊断 */
    @Excel(name = "病理诊断")
    private String pathologicDiagnosis;

    /** 新辅助治疗方案 */
    @Excel(name = "新辅助治疗方案")
    private String neoadjuvantTreatmentOptions;

    /** 新辅助治疗周期 */
    @Excel(name = "新辅助治疗周期")
    private String neoadjuvantTreatmentCycle;

    /** 新辅助治疗疗效评估 */
    @Excel(name = "新辅助治疗疗效评估")
    private String ntEvaluation;

    /** 手术治疗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "手术治疗时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date surgicalTreatmentTime;

    /** 手术治疗结果 */
    @Excel(name = "手术治疗结果")
    private String surgicalTreatmentResult;

    /** 术后辅助治疗方案 */
    @Excel(name = "术后辅助治疗方案")
    private String patPlan;

    /** 术后辅助治疗周期 */
    @Excel(name = "术后辅助治疗周期")
    private String patCycle;

    /** 术后辅助治疗疗效评估 */
    @Excel(name = "术后辅助治疗疗效评估")
    private String patEvaluation;

    /** 局部治疗方案 */
    @Excel(name = "局部治疗方案")
    private String topicalTreatmentPlan;

    /** 局部治疗周期 */
    @Excel(name = "局部治疗周期")
    private String topicalTreatmentCycle;

    /** 局部治疗影像 */
    @Excel(name = "局部治疗影像")
    private String topicalTreatmentImage;

    /** 局部治疗影像报告 */
    @Excel(name = "局部治疗影像报告")
    private String topicalTreatmentImagReport;

    /** 局部治疗肿瘤标志物检查 */
    @Excel(name = "局部治疗肿瘤标志物检查")
    private String ttTumorMarkerExamination;

    /** 局部治疗疗效评估 */
    @Excel(name = "局部治疗疗效评估")
    private String ttEfficacyAssessment;

    /** 局部治疗其他内容 */
    @Excel(name = "局部治疗其他内容")
    private String topicalTreatmentElse;

    /** 一线治疗方案 */
    @Excel(name = "一线治疗方案")
    private String firstLineTreatmentPlan;

    /** 一线治疗周期 */
    @Excel(name = "一线治疗周期")
    private String firstLineTreatmentCycle;

    /** 一线治疗影像 */
    @Excel(name = "一线治疗影像")
    private String firstLineTreatmentImage;

    /** 一线治疗影像报告 */
    @Excel(name = "一线治疗影像报告")
    private String fltImageReport;

    /** 一线治疗肿瘤标志物检查 */
    @Excel(name = "一线治疗肿瘤标志物检查")
    private String fltTumorMarkerExamination;

    /** 一线治疗疗效评估 */
    @Excel(name = "一线治疗疗效评估")
    private String fltEfficacyAssessment;

    /** 一线治疗其他内容 */
    @Excel(name = "一线治疗其他内容")
    private String firstLineTreatmentElse;

    /** 二线治疗方案 */
    @Excel(name = "二线治疗方案")
    private String secondLineTreatmentPlan;

    /** 二线治疗周期 */
    @Excel(name = "二线治疗周期")
    private String secondLineTreatmentCycle;

    /** 二线治疗影像 */
    @Excel(name = "二线治疗影像")
    private String secondLineTreatmentImage;

    /** 二线治疗影像报告 */
    @Excel(name = "二线治疗影像报告")
    private String sltImageReport;

    /** 二线治疗肿瘤标志物检查 */
    @Excel(name = "二线治疗肿瘤标志物检查")
    private String sltTumorMarkerExamination;

    /** 二线治疗疗效评估 */
    @Excel(name = "二线治疗疗效评估")
    private String sltEfficacyAssessment;

    /** 二线治疗其他内容 */
    @Excel(name = "二线治疗其他内容")
    private String secondLineTreatmentElse;

    /** 三线治疗方案 */
    @Excel(name = "三线治疗方案")
    private String thirdLineTreatmentPlan;

    /** 三线治疗周期 */
    @Excel(name = "三线治疗周期")
    private String thirdLineTreatmentCycle;

    /** 三线治疗影像 */
    @Excel(name = "三线治疗影像")
    private String thirdLineTreatmentImage;

    /** 三线治疗影像报告 */
    @Excel(name = "三线治疗影像报告")
    private String tltImageReport;

    /** 三线治疗肿瘤标志物检查 */
    @Excel(name = "三线治疗肿瘤标志物检查")
    private String tltTumorMarkerExamination;

    /** 三线治疗疗效评估 */
    @Excel(name = "三线治疗疗效评估")
    private String tltEfficacyAssessment;

    /** 三线治疗其他内容 */
    @Excel(name = "三线治疗其他内容")
    private String thirdLineTreatmentElse;

    /** 不良反应处理 */
    @Excel(name = "不良反应处理")
    private String adverseReactionHandle;

    /** 病例总结与思考 */
    @Excel(name = "病例总结与思考")
    private String caseSummaryAndReflection;
}
