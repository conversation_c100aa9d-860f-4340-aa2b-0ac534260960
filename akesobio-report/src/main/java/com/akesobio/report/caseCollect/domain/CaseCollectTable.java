package com.akesobio.report.caseCollect.domain;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 病例收集表对象 case_collect_table
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public class CaseCollectTable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 主诉 */
    @Excel(name = "主诉")
    private String chiefComplaint;

    /** 家族史、既往史 */
    @Excel(name = "家族史、既往史")
    private String familyMedicalHistory;

    /** 现病史 */
    @Excel(name = "现病史")
    private String presentIllnessHistory;

    /** 体格检查 */
    @Excel(name = "体格检查")
    private String physicalExamination;

    /** 血常规检查 */
    @Excel(name = "血常规检查")
    private String bloodRoutineExamination;

    /** 生化检查 */
    @Excel(name = "生化检查")
    private String biochemicalExamination;

    /** 肿瘤标记物 */
    @Excel(name = "肿瘤标记物")
    private String tumorMarker;

    /** 其他实验室检查 */
    @Excel(name = "其他实验室检查")
    private String laboratoryExaminationElse;

    /** PETCT影像 */
    @Excel(name = "PETCT影像")
    private String petctImage;

    /** PETCT报告 */
    @Excel(name = "PETCT报告")
    private String petctReport;

    /** CT影像 */
    @Excel(name = "CT影像")
    private String ctImage;

    /** CT报告 */
    @Excel(name = "CT报告")
    private String ctReport;

    /** MRI影像 */
    @Excel(name = "MRI影像")
    private String mriImage;

    /** MRI报告 */
    @Excel(name = "MRI报告")
    private String mriReport;

    /** 其他影像 */
    @Excel(name = "其他影像")
    private String elseImage;

    /** 其他报告 */
    @Excel(name = "其他报告")
    private String elseReport;

    /** PD-1检查 */
    @Excel(name = "PD-1检查")
    private String pd1;

    /** 基因检测 */
    @Excel(name = "基因检测")
    private String geneticTest;

    /** 免疫组化 */
    @Excel(name = "免疫组化")
    private String immunohistochemical;

    /** 其他检查 */
    @Excel(name = "其他检查")
    private String elseExamination;

    /** 病理诊断 */
    @Excel(name = "病理诊断")
    private String pathologicDiagnosis;

    /** 新辅助治疗方案 */
    @Excel(name = "新辅助治疗方案")
    private String neoadjuvantTreatmentOptions;

    /** 新辅助治疗周期 */
    @Excel(name = "新辅助治疗周期")
    private String neoadjuvantTreatmentCycle;

    /** 新辅助治疗疗效评估 */
    @Excel(name = "新辅助治疗疗效评估")
    private String ntEvaluation;

    /** 手术治疗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "手术治疗时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date surgicalTreatmentTime;

    /** 手术治疗结果 */
    @Excel(name = "手术治疗结果")
    private String surgicalTreatmentResult;

    /** 术后辅助治疗方案 */
    @Excel(name = "术后辅助治疗方案")
    private String patPlan;

    /** 术后辅助治疗周期 */
    @Excel(name = "术后辅助治疗周期")
    private String patCycle;

    /** 术后辅助治疗疗效评估 */
    @Excel(name = "术后辅助治疗疗效评估")
    private String patEvaluation;

    /** 局部治疗方案 */
    @Excel(name = "局部治疗方案")
    private String topicalTreatmentPlan;

    /** 局部治疗周期 */
    @Excel(name = "局部治疗周期")
    private String topicalTreatmentCycle;

    /** 局部治疗影像 */
    @Excel(name = "局部治疗影像")
    private String topicalTreatmentImage;

    /** 局部治疗影像报告 */
    @Excel(name = "局部治疗影像报告")
    private String topicalTreatmentImagReport;

    /** 局部治疗肿瘤标志物检查 */
    @Excel(name = "局部治疗肿瘤标志物检查")
    private String ttTumorMarkerExamination;

    /** 局部治疗疗效评估 */
    @Excel(name = "局部治疗疗效评估")
    private String ttEfficacyAssessment;

    /** 局部治疗其他内容 */
    @Excel(name = "局部治疗其他内容")
    private String topicalTreatmentElse;

    /** 一线治疗方案 */
    @Excel(name = "一线治疗方案")
    private String firstLineTreatmentPlan;

    /** 一线治疗周期 */
    @Excel(name = "一线治疗周期")
    private String firstLineTreatmentCycle;

    /** 一线治疗影像 */
    @Excel(name = "一线治疗影像")
    private String firstLineTreatmentImage;

    /** 一线治疗影像报告 */
    @Excel(name = "一线治疗影像报告")
    private String fltImageReport;

    /** 一线治疗肿瘤标志物检查 */
    @Excel(name = "一线治疗肿瘤标志物检查")
    private String fltTumorMarkerExamination;

    /** 一线治疗疗效评估 */
    @Excel(name = "一线治疗疗效评估")
    private String fltEfficacyAssessment;

    /** 一线治疗其他内容 */
    @Excel(name = "一线治疗其他内容")
    private String firstLineTreatmentElse;

    /** 二线治疗方案 */
    @Excel(name = "二线治疗方案")
    private String secondLineTreatmentPlan;

    /** 二线治疗周期 */
    @Excel(name = "二线治疗周期")
    private String secondLineTreatmentCycle;

    /** 二线治疗影像 */
    @Excel(name = "二线治疗影像")
    private String secondLineTreatmentImage;

    /** 二线治疗影像报告 */
    @Excel(name = "二线治疗影像报告")
    private String sltImageReport;

    /** 二线治疗肿瘤标志物检查 */
    @Excel(name = "二线治疗肿瘤标志物检查")
    private String sltTumorMarkerExamination;

    /** 二线治疗疗效评估 */
    @Excel(name = "二线治疗疗效评估")
    private String sltEfficacyAssessment;

    /** 二线治疗其他内容 */
    @Excel(name = "二线治疗其他内容")
    private String secondLineTreatmentElse;

    /** 三线治疗方案 */
    @Excel(name = "三线治疗方案")
    private String thirdLineTreatmentPlan;

    /** 三线治疗周期 */
    @Excel(name = "三线治疗周期")
    private String thirdLineTreatmentCycle;

    /** 三线治疗影像 */
    @Excel(name = "三线治疗影像")
    private String thirdLineTreatmentImage;

    /** 三线治疗影像报告 */
    @Excel(name = "三线治疗影像报告")
    private String tltImageReport;

    /** 三线治疗肿瘤标志物检查 */
    @Excel(name = "三线治疗肿瘤标志物检查")
    private String tltTumorMarkerExamination;

    /** 三线治疗疗效评估 */
    @Excel(name = "三线治疗疗效评估")
    private String tltEfficacyAssessment;

    /** 三线治疗其他内容 */
    @Excel(name = "三线治疗其他内容")
    private String thirdLineTreatmentElse;

    /** 不良反应处理 */
    @Excel(name = "不良反应处理")
    private String adverseReactionHandle;

    /** 病例总结与思考 */
    @Excel(name = "病例总结与思考")
    private String caseSummaryAndReflection;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 备注 */
    private String remark;

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getSex()
    {
        return sex;
    }
    public void setAge(String age)
    {
        this.age = age;
    }

    public String getAge()
    {
        return age;
    }
    public void setChiefComplaint(String chiefComplaint)
    {
        this.chiefComplaint = chiefComplaint;
    }

    public String getChiefComplaint()
    {
        return chiefComplaint;
    }
    public void setFamilyMedicalHistory(String familyMedicalHistory)
    {
        this.familyMedicalHistory = familyMedicalHistory;
    }

    public String getFamilyMedicalHistory()
    {
        return familyMedicalHistory;
    }
    public void setPresentIllnessHistory(String presentIllnessHistory)
    {
        this.presentIllnessHistory = presentIllnessHistory;
    }

    public String getPresentIllnessHistory()
    {
        return presentIllnessHistory;
    }
    public void setPhysicalExamination(String physicalExamination)
    {
        this.physicalExamination = physicalExamination;
    }

    public String getPhysicalExamination()
    {
        return physicalExamination;
    }
    public void setBloodRoutineExamination(String bloodRoutineExamination)
    {
        this.bloodRoutineExamination = bloodRoutineExamination;
    }

    public String getBloodRoutineExamination()
    {
        return bloodRoutineExamination;
    }
    public void setBiochemicalExamination(String biochemicalExamination)
    {
        this.biochemicalExamination = biochemicalExamination;
    }

    public String getBiochemicalExamination()
    {
        return biochemicalExamination;
    }
    public void setTumorMarker(String tumorMarker)
    {
        this.tumorMarker = tumorMarker;
    }

    public String getTumorMarker()
    {
        return tumorMarker;
    }
    public void setLaboratoryExaminationElse(String laboratoryExaminationElse)
    {
        this.laboratoryExaminationElse = laboratoryExaminationElse;
    }

    public String getLaboratoryExaminationElse()
    {
        return laboratoryExaminationElse;
    }
    public void setPetctImage(String petctImage)
    {
        this.petctImage = petctImage;
    }

    public String getPetctImage()
    {
        return petctImage;
    }
    public void setPetctReport(String petctReport)
    {
        this.petctReport = petctReport;
    }

    public String getPetctReport()
    {
        return petctReport;
    }
    public void setCtImage(String ctImage)
    {
        this.ctImage = ctImage;
    }

    public String getCtImage()
    {
        return ctImage;
    }
    public void setCtReport(String ctReport)
    {
        this.ctReport = ctReport;
    }

    public String getCtReport()
    {
        return ctReport;
    }
    public void setMriImage(String mriImage)
    {
        this.mriImage = mriImage;
    }

    public String getMriImage()
    {
        return mriImage;
    }
    public void setMriReport(String mriReport)
    {
        this.mriReport = mriReport;
    }

    public String getMriReport()
    {
        return mriReport;
    }
    public void setElseImage(String elseImage)
    {
        this.elseImage = elseImage;
    }

    public String getElseImage()
    {
        return elseImage;
    }
    public void setElseReport(String elseReport)
    {
        this.elseReport = elseReport;
    }

    public String getElseReport()
    {
        return elseReport;
    }
    public void setPd1(String pd1)
    {
        this.pd1 = pd1;
    }

    public String getPd1()
    {
        return pd1;
    }
    public void setGeneticTest(String geneticTest)
    {
        this.geneticTest = geneticTest;
    }

    public String getGeneticTest()
    {
        return geneticTest;
    }
    public void setImmunohistochemical(String immunohistochemical)
    {
        this.immunohistochemical = immunohistochemical;
    }

    public String getImmunohistochemical()
    {
        return immunohistochemical;
    }
    public void setElseExamination(String elseExamination)
    {
        this.elseExamination = elseExamination;
    }

    public String getElseExamination()
    {
        return elseExamination;
    }
    public void setPathologicDiagnosis(String pathologicDiagnosis)
    {
        this.pathologicDiagnosis = pathologicDiagnosis;
    }

    public String getPathologicDiagnosis()
    {
        return pathologicDiagnosis;
    }
    public void setNeoadjuvantTreatmentOptions(String neoadjuvantTreatmentOptions)
    {
        this.neoadjuvantTreatmentOptions = neoadjuvantTreatmentOptions;
    }

    public String getNeoadjuvantTreatmentOptions()
    {
        return neoadjuvantTreatmentOptions;
    }
    public void setNeoadjuvantTreatmentCycle(String neoadjuvantTreatmentCycle)
    {
        this.neoadjuvantTreatmentCycle = neoadjuvantTreatmentCycle;
    }

    public String getNeoadjuvantTreatmentCycle()
    {
        return neoadjuvantTreatmentCycle;
    }
    public void setNtEvaluation(String ntEvaluation)
    {
        this.ntEvaluation = ntEvaluation;
    }

    public String getNtEvaluation()
    {
        return ntEvaluation;
    }
    public void setSurgicalTreatmentTime(Date surgicalTreatmentTime)
    {
        this.surgicalTreatmentTime = surgicalTreatmentTime;
    }

    public Date getSurgicalTreatmentTime()
    {
        return surgicalTreatmentTime;
    }
    public void setSurgicalTreatmentResult(String surgicalTreatmentResult)
    {
        this.surgicalTreatmentResult = surgicalTreatmentResult;
    }

    public String getSurgicalTreatmentResult()
    {
        return surgicalTreatmentResult;
    }
    public void setPatPlan(String patPlan)
    {
        this.patPlan = patPlan;
    }

    public String getPatPlan()
    {
        return patPlan;
    }
    public void setPatCycle(String patCycle)
    {
        this.patCycle = patCycle;
    }

    public String getPatCycle()
    {
        return patCycle;
    }
    public void setPatEvaluation(String patEvaluation)
    {
        this.patEvaluation = patEvaluation;
    }

    public String getPatEvaluation()
    {
        return patEvaluation;
    }
    public void setTopicalTreatmentPlan(String topicalTreatmentPlan)
    {
        this.topicalTreatmentPlan = topicalTreatmentPlan;
    }

    public String getTopicalTreatmentPlan()
    {
        return topicalTreatmentPlan;
    }
    public void setTopicalTreatmentCycle(String topicalTreatmentCycle)
    {
        this.topicalTreatmentCycle = topicalTreatmentCycle;
    }

    public String getTopicalTreatmentCycle()
    {
        return topicalTreatmentCycle;
    }
    public void setTopicalTreatmentImage(String topicalTreatmentImage)
    {
        this.topicalTreatmentImage = topicalTreatmentImage;
    }

    public String getTopicalTreatmentImage()
    {
        return topicalTreatmentImage;
    }
    public void setTopicalTreatmentImagReport(String topicalTreatmentImagReport)
    {
        this.topicalTreatmentImagReport = topicalTreatmentImagReport;
    }

    public String getTopicalTreatmentImagReport()
    {
        return topicalTreatmentImagReport;
    }
    public void setTtTumorMarkerExamination(String ttTumorMarkerExamination)
    {
        this.ttTumorMarkerExamination = ttTumorMarkerExamination;
    }

    public String getTtTumorMarkerExamination()
    {
        return ttTumorMarkerExamination;
    }
    public void setTtEfficacyAssessment(String ttEfficacyAssessment)
    {
        this.ttEfficacyAssessment = ttEfficacyAssessment;
    }

    public String getTtEfficacyAssessment()
    {
        return ttEfficacyAssessment;
    }
    public void setTopicalTreatmentElse(String topicalTreatmentElse)
    {
        this.topicalTreatmentElse = topicalTreatmentElse;
    }

    public String getTopicalTreatmentElse()
    {
        return topicalTreatmentElse;
    }
    public void setFirstLineTreatmentPlan(String firstLineTreatmentPlan)
    {
        this.firstLineTreatmentPlan = firstLineTreatmentPlan;
    }

    public String getFirstLineTreatmentPlan()
    {
        return firstLineTreatmentPlan;
    }
    public void setFirstLineTreatmentCycle(String firstLineTreatmentCycle)
    {
        this.firstLineTreatmentCycle = firstLineTreatmentCycle;
    }

    public String getFirstLineTreatmentCycle()
    {
        return firstLineTreatmentCycle;
    }
    public void setFirstLineTreatmentImage(String firstLineTreatmentImage)
    {
        this.firstLineTreatmentImage = firstLineTreatmentImage;
    }

    public String getFirstLineTreatmentImage()
    {
        return firstLineTreatmentImage;
    }
    public void setFltImageReport(String fltImageReport)
    {
        this.fltImageReport = fltImageReport;
    }

    public String getFltImageReport()
    {
        return fltImageReport;
    }
    public void setFltTumorMarkerExamination(String fltTumorMarkerExamination)
    {
        this.fltTumorMarkerExamination = fltTumorMarkerExamination;
    }

    public String getFltTumorMarkerExamination()
    {
        return fltTumorMarkerExamination;
    }
    public void setFltEfficacyAssessment(String fltEfficacyAssessment)
    {
        this.fltEfficacyAssessment = fltEfficacyAssessment;
    }

    public String getFltEfficacyAssessment()
    {
        return fltEfficacyAssessment;
    }
    public void setFirstLineTreatmentElse(String firstLineTreatmentElse)
    {
        this.firstLineTreatmentElse = firstLineTreatmentElse;
    }

    public String getFirstLineTreatmentElse()
    {
        return firstLineTreatmentElse;
    }
    public void setSecondLineTreatmentPlan(String secondLineTreatmentPlan)
    {
        this.secondLineTreatmentPlan = secondLineTreatmentPlan;
    }

    public String getSecondLineTreatmentPlan()
    {
        return secondLineTreatmentPlan;
    }
    public void setSecondLineTreatmentCycle(String secondLineTreatmentCycle)
    {
        this.secondLineTreatmentCycle = secondLineTreatmentCycle;
    }

    public String getSecondLineTreatmentCycle()
    {
        return secondLineTreatmentCycle;
    }
    public void setSecondLineTreatmentImage(String secondLineTreatmentImage)
    {
        this.secondLineTreatmentImage = secondLineTreatmentImage;
    }

    public String getSecondLineTreatmentImage()
    {
        return secondLineTreatmentImage;
    }
    public void setSltImageReport(String sltImageReport)
    {
        this.sltImageReport = sltImageReport;
    }

    public String getSltImageReport()
    {
        return sltImageReport;
    }
    public void setSltTumorMarkerExamination(String sltTumorMarkerExamination)
    {
        this.sltTumorMarkerExamination = sltTumorMarkerExamination;
    }

    public String getSltTumorMarkerExamination()
    {
        return sltTumorMarkerExamination;
    }
    public void setSltEfficacyAssessment(String sltEfficacyAssessment)
    {
        this.sltEfficacyAssessment = sltEfficacyAssessment;
    }

    public String getSltEfficacyAssessment()
    {
        return sltEfficacyAssessment;
    }
    public void setSecondLineTreatmentElse(String secondLineTreatmentElse)
    {
        this.secondLineTreatmentElse = secondLineTreatmentElse;
    }

    public String getSecondLineTreatmentElse()
    {
        return secondLineTreatmentElse;
    }
    public void setThirdLineTreatmentPlan(String thirdLineTreatmentPlan)
    {
        this.thirdLineTreatmentPlan = thirdLineTreatmentPlan;
    }

    public String getThirdLineTreatmentPlan()
    {
        return thirdLineTreatmentPlan;
    }
    public void setThirdLineTreatmentCycle(String thirdLineTreatmentCycle)
    {
        this.thirdLineTreatmentCycle = thirdLineTreatmentCycle;
    }

    public String getThirdLineTreatmentCycle()
    {
        return thirdLineTreatmentCycle;
    }
    public void setThirdLineTreatmentImage(String thirdLineTreatmentImage)
    {
        this.thirdLineTreatmentImage = thirdLineTreatmentImage;
    }

    public String getThirdLineTreatmentImage()
    {
        return thirdLineTreatmentImage;
    }
    public void setTltImageReport(String tltImageReport)
    {
        this.tltImageReport = tltImageReport;
    }

    public String getTltImageReport()
    {
        return tltImageReport;
    }
    public void setTltTumorMarkerExamination(String tltTumorMarkerExamination)
    {
        this.tltTumorMarkerExamination = tltTumorMarkerExamination;
    }

    public String getTltTumorMarkerExamination()
    {
        return tltTumorMarkerExamination;
    }
    public void setTltEfficacyAssessment(String tltEfficacyAssessment)
    {
        this.tltEfficacyAssessment = tltEfficacyAssessment;
    }

    public String getTltEfficacyAssessment()
    {
        return tltEfficacyAssessment;
    }
    public void setThirdLineTreatmentElse(String thirdLineTreatmentElse)
    {
        this.thirdLineTreatmentElse = thirdLineTreatmentElse;
    }

    public String getThirdLineTreatmentElse()
    {
        return thirdLineTreatmentElse;
    }
    public void setAdverseReactionHandle(String adverseReactionHandle)
    {
        this.adverseReactionHandle = adverseReactionHandle;
    }

    public String getAdverseReactionHandle()
    {
        return adverseReactionHandle;
    }
    public void setCaseSummaryAndReflection(String caseSummaryAndReflection)
    {
        this.caseSummaryAndReflection = caseSummaryAndReflection;
    }

    public String getCaseSummaryAndReflection()
    {
        return caseSummaryAndReflection;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("sex", getSex())
                .append("age", getAge())
                .append("chiefComplaint", getChiefComplaint())
                .append("familyMedicalHistory", getFamilyMedicalHistory())
                .append("presentIllnessHistory", getPresentIllnessHistory())
                .append("physicalExamination", getPhysicalExamination())
                .append("bloodRoutineExamination", getBloodRoutineExamination())
                .append("biochemicalExamination", getBiochemicalExamination())
                .append("tumorMarker", getTumorMarker())
                .append("laboratoryExaminationElse", getLaboratoryExaminationElse())
                .append("petctImage", getPetctImage())
                .append("petctReport", getPetctReport())
                .append("ctImage", getCtImage())
                .append("ctReport", getCtReport())
                .append("mriImage", getMriImage())
                .append("mriReport", getMriReport())
                .append("elseImage", getElseImage())
                .append("elseReport", getElseReport())
                .append("pd1", getPd1())
                .append("geneticTest", getGeneticTest())
                .append("immunohistochemical", getImmunohistochemical())
                .append("elseExamination", getElseExamination())
                .append("pathologicDiagnosis", getPathologicDiagnosis())
                .append("neoadjuvantTreatmentOptions", getNeoadjuvantTreatmentOptions())
                .append("neoadjuvantTreatmentCycle", getNeoadjuvantTreatmentCycle())
                .append("ntEvaluation", getNtEvaluation())
                .append("surgicalTreatmentTime", getSurgicalTreatmentTime())
                .append("surgicalTreatmentResult", getSurgicalTreatmentResult())
                .append("patPlan", getPatPlan())
                .append("patCycle", getPatCycle())
                .append("patEvaluation", getPatEvaluation())
                .append("topicalTreatmentPlan", getTopicalTreatmentPlan())
                .append("topicalTreatmentCycle", getTopicalTreatmentCycle())
                .append("topicalTreatmentImage", getTopicalTreatmentImage())
                .append("topicalTreatmentImagReport", getTopicalTreatmentImagReport())
                .append("ttTumorMarkerExamination", getTtTumorMarkerExamination())
                .append("ttEfficacyAssessment", getTtEfficacyAssessment())
                .append("topicalTreatmentElse", getTopicalTreatmentElse())
                .append("firstLineTreatmentPlan", getFirstLineTreatmentPlan())
                .append("firstLineTreatmentCycle", getFirstLineTreatmentCycle())
                .append("firstLineTreatmentImage", getFirstLineTreatmentImage())
                .append("fltImageReport", getFltImageReport())
                .append("fltTumorMarkerExamination", getFltTumorMarkerExamination())
                .append("fltEfficacyAssessment", getFltEfficacyAssessment())
                .append("firstLineTreatmentElse", getFirstLineTreatmentElse())
                .append("secondLineTreatmentPlan", getSecondLineTreatmentPlan())
                .append("secondLineTreatmentCycle", getSecondLineTreatmentCycle())
                .append("secondLineTreatmentImage", getSecondLineTreatmentImage())
                .append("sltImageReport", getSltImageReport())
                .append("sltTumorMarkerExamination", getSltTumorMarkerExamination())
                .append("sltEfficacyAssessment", getSltEfficacyAssessment())
                .append("secondLineTreatmentElse", getSecondLineTreatmentElse())
                .append("thirdLineTreatmentPlan", getThirdLineTreatmentPlan())
                .append("thirdLineTreatmentCycle", getThirdLineTreatmentCycle())
                .append("thirdLineTreatmentImage", getThirdLineTreatmentImage())
                .append("tltImageReport", getTltImageReport())
                .append("tltTumorMarkerExamination", getTltTumorMarkerExamination())
                .append("tltEfficacyAssessment", getTltEfficacyAssessment())
                .append("thirdLineTreatmentElse", getThirdLineTreatmentElse())
                .append("adverseReactionHandle", getAdverseReactionHandle())
                .append("caseSummaryAndReflection", getCaseSummaryAndReflection())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("remark", getRemark())
                .toString();
    }
}
