package com.akesobio.report.caseCollect.controller;

import java.io.File;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.report.caseCollect.client.IdentifyClient;
import com.akesobio.report.caseCollect.utils.CaseImageUploadUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;
import com.akesobio.report.caseCollect.domain.CaseCollectTable;
import com.akesobio.report.caseCollect.service.ICaseCollectTableService;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 病例收集Controller
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/caseCollect/caseCollectTable")
public class CaseCollectTableController extends BaseController
{
    @Resource
    private ICaseCollectTableService caseCollectTableService;
    @Resource
    private CaseImageUploadUtil caseImageUploadUtil;
    @Resource
    private IdentifyClient identifyClient;

    /**
     * 查询病例收集列表
     */
//    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaseCollectTable caseCollectTable)
    {
        startPage();
        List<CaseCollectTable> list = caseCollectTableService.selectCaseCollectTableList(caseCollectTable);
        return getDataTable(list);
    }

    /**
     * 导出病例收集列表
     */
    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:export')")
    @Log(title = "病例收集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaseCollectTable caseCollectTable)
    {
        List<CaseCollectTable> list = caseCollectTableService.selectCaseCollectTableList(caseCollectTable);
        ExcelUtil<CaseCollectTable> util = new ExcelUtil<CaseCollectTable>(CaseCollectTable.class);
        util.exportExcel(response, list, "病例收集数据");
    }

    /**
     * 获取病例收集详细信息
     */
//    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(caseCollectTableService.selectCaseCollectTableById(id));
    }

    /**
     * 新增病例收集
     */
//    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:add')")
    @Log(title = "病例收集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaseCollectTable caseCollectTable)
    {
        return toAjax(caseCollectTableService.insertCaseCollectTable(caseCollectTable));
    }

    /**
     * 修改病例收集
     */
//    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:edit')")
    @Log(title = "病例收集", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody CaseCollectTable caseCollectTable)
    {
        return toAjax(caseCollectTableService.updateCaseCollectTable(caseCollectTable));
    }

    /**
     * 删除病例收集
     */
    @PreAuthorize("@ss.hasPermi('caseCollect:caseCollectTable:remove')")
    @Log(title = "病例收集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(caseCollectTableService.deleteCaseCollectTableByIds(ids));
    }

    /**
     * 图片上传
     */
    @PostMapping("/uploadCaseImage")
    public AjaxResult caseImageUpload(MultipartFile file) throws Exception {
        return caseImageUploadUtil.uploadCaseImage(file);
    }


    /**
     * 文字识别
     */
    @PostMapping("/identifyUpload")
    public String identifyUpload(MultipartFile file) throws IOException {
        return identifyClient.uploadIdentify(file);
    }
}
