package com.akesobio.report.costControl.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 单据类型字段依据
 *
 * <AUTHOR>
 * @since 2025/6/19  16:54
 */
@Getter
@AllArgsConstructor
public enum HeaderColumnTypeEnum {
    FMAA(155913, "FMAA", "FMA", 1, "FMAA-差旅申请-准入", "column34", "申请类"),
    FMAB(155914, "FMAB", "FMA", 3, "FMAB-差旅报销-准入", "column34", "报销类"),
    FMBA(155935, "FMBA", "FMB", 1, "FMBA-费用申请-准入", "cost_center_id", "申请类"),
    FMBB(155936, "FMBB", "FMB", 3, "FMBB-费用报销-准入", "cost_center_id", "报销类"),
    FMCA(155943, "FMCA", "FMC", 1, "FMCA-会议申请-准入（CRM发起）", "cost_center_id", "申请类"),
    //    FMCB(155942, "FMCB","FMC","","FMCB-会议总结-准入（CRM发起）", "),
    FMCC(155934, "FMCC", "FMC", 3, "FMCC-会议核报-准入（含对公）", "cost_center_id", "报销类"),
    FSAA(154341, "FSAA", "FSA", 1, "FSAA-差旅申请", "", "申请类"),
    FSAB(154316, "FSAB", "FSA", 3, "FSAB-差旅报销", "", "报销类"),
    FSBA(155915, "FSBA", "FSB", 1, "FSBA-费用申请", "cost_center_id", "申请类"),
    FSBB(155920, "FSBB", "FSB", 3, "FSBB-费用报销", "cost_center_id", "报销类"),
    FSCA(155921, "FSCA", "FSC", 0, "FSCA -系列会立项申请", "", "申请类"),
    FSCB(155922, "FSCB", "FSC", 1, "FSCB-会议申请（CRM发起）", "cost_center_id", "申请类"),
    FSCC(155941, "FSCC", "FSC", 0, "FSCC-会议总结（CRM发起）", "", "其他"),
    FSCD(155923, "FSCD", "FSC", 3, "FSCD-会议核报（含对公）", "cost_center_id", "报销类"),
    //    FSDA(155917, "FSDA","FSD","","FSDA-平台项目立项申请", "),
    FSFA(155911, "FSFA", "FSF", 1, "FSFA-终端准入申请", "cost_center_id", "申请类"),
    FSFB(155912, "FSFB", "FSF", 3, "FSFB-终端准入核报", "cost_center_id", "报销类"),
    //    FSDB(155918, "FSDB","FSD","FSDB-平台执行计划"),
//    FSDC(155919, "FSDC","FSD","FSDC-平台执行计划汇总"),
    FSEA(154312, "FSEA", "FSE", 0, "FSEA-IIT立项申请", "", "申请类"),
    //    FSEB(155924, "FSEB","FSE","FSEB-实际领药申请","申请类"),
    FSGA(155946, "FSG", "", 1, "FSGA-京东采购申请", "", "申请类"),
    //    FSGB(155947, "FSG","FSGB-京东采购验收（验收后系统自动生成）"),
    FSHA(154443, "FSH", "FSHA-采购合同（OA发起）", 0, "", "", "其他"),
    FSHB(154314, "FSH", "FSHB-对公预付（含会议）", 3, "", "", "报销类"),
    FSHC(154318, "FSH", "FSHC-对公应付（除会议外）", 3, "", "", "报销类"),
    //    FSIA(155928, "FSI","FSIA-费用分摊（含奖金预提）"),
//    FSIB(155930, "FSI","FSIB-月度薪酬"),
//    FSJA(154315, "FSJ","FSJA-员工借款申请"),
//    FSJB(154317, "FSJ","FSJB-员工还款"),
//    FSKB(155927, "","FSKB-预算结果调整"),
    FSKA(155925, "", "FSKA-预算导入及调整", 0, "", "", "其他"),
    FSKC(155945, "", "FSKC-预算调拨单", 0, "", "", "其他"),
    FSLA(155937, "", "FSLA -取消/关闭申请", 0, "", "", "其他"),
    //    FSMB(155940, "","FSMB-意见反馈"),
    DGZFD(154444, "", "对公支付单", 0, "", "", "其他"),
    DSZFD(154442, "", "对私支付单", 0, "", "", "其他"),
    HHZFD(155948, "", "混合支付单", 0, "", "", "其他"),

    ;
    private final Integer id;
    private final String code;
    private final String type;
    /**
     * 1提交、2核报、3报销
     */
    private final Integer action;
    private final String name;
    private final String chargeDepartmentColumn;

    //单据分类
    private final String typeClass;


    public static HeaderColumnTypeEnum getHeaderTypeEnumById(Integer id) {
        for (HeaderColumnTypeEnum headerColumnTypeEnum : HeaderColumnTypeEnum.values()) {
            if (headerColumnTypeEnum.getId().equals(id)) {
                return headerColumnTypeEnum;
            }
        }
        return null;
    }

    public static String getHeaderTypeClassById(Integer id) {
        for (HeaderColumnTypeEnum headerColumnTypeEnum : HeaderColumnTypeEnum.values()) {
            if (headerColumnTypeEnum.getId().equals(id)) {
                return headerColumnTypeEnum.getTypeClass();
            }
        }
        return null;
    }

    public static List<String> getHeaderTypeCodesByAction(Integer action) {
        List<String> list = new ArrayList<>();
        for (HeaderColumnTypeEnum headerColumnTypeEnum : HeaderColumnTypeEnum.values()) {
            if (headerColumnTypeEnum.getAction().equals(action)) {
                list.add(headerColumnTypeEnum.getCode());
            }
        }
        return list;
    }

    public static List<Integer> getTypeIdsByAction(Integer action) {
        List<Integer> list = new ArrayList<>();
        for (HeaderColumnTypeEnum headerColumnTypeEnum : HeaderColumnTypeEnum.values()) {
            if (headerColumnTypeEnum.getAction().equals(action)) {
                list.add(headerColumnTypeEnum.getId());
            }
        }
        return list;
    }

}
