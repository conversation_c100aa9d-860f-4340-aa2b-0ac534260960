@康方  是的，新增一个岗位编码position_code,把触发时间event_code改为wfp:开始审批
@康方  expsubmitted是单据提交后一定会触发的，如果单据提交后，审批节点是虚拟岗、业务审批人或财务审批人，wfp是会触发的；如果单据提交后，直接是财务审核岗，那就是wfa。如果没有配置审批流，那单据提交后直接是审批通过状态，expapproved也会触发

INSERT INTO mq_config (
    company_id, exchange, rooting_key, platform, 
    interface_name, description, event_code, header_type_code, 
    position_code, workflow_type, created_by, creation_date, 
    last_updated_by, last_update_date 
) VALUES (
    23049, 'wf_claim', 'mq-aliprod-customize-claim-push', 'cloudpense',
    'addBusinessTrip', NULL, 'wfp', 'YXCL01',
    'KF09', NULL, NULL, NOW(),
    NULL, NOW()
);

rooting_key这个字段应该的aliqa，aliprod是生产环境的路由

INSERT INTO `cpkfsw`.`fnd_lov_value` ( `lov_id`, `company_id`, `value_code`, `enabled_flag`, `column10`, `created_by`, `creation_date`, `last_updated_by`, `last_update_date` )
VALUES
	(
		( SELECT `lov_id` FROM `cpkfsw`.`fnd_lov` WHERE `lov_name` = 'HTTP_CONFIG_PARAM' AND `company_id` = 23049 ),
		23049,
		'addBusinessTrip',
		'Y',
		'{\"url\": \"https://*********/kangfang/*********/addBusinessTrip\",\r\n	\"jsonText\": \"{\\\"headerColumns\\\":[\\\"header_id\\\",\\\"workflow_paths\\\",\\\"document_num\\\",\\\"column_json\\\",\\\"column10\\\",\\\"column17\\\",\\\"end_datetime\\\",\\\"start_datetime\\\",\\\"column28\\\",\\\"description\\\",\\\"destination_cities\\\",\\\"column45\\\",\\\"column46\\\",\\\"status\\\",\\\"header_type_id\\\",\\\"submit_user\\\",\\\"charge_user\\\",\\\"submit_date\\\"],\\\"headerLinkColumns\\\":[\\\"header_id\\\",\\\"document_num\\\",\\\"column22\\\",\\\"header_type_id\\\"],\\\"lineColumns\\\":[\\\"line_id\\\",\\\"type_id\\\",\\\"column_json\\\"]}\"}',
		NULL,
		NOW(),
		NULL,
		NOW() 
	);

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`,`value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049), '云简业财系统的差旅申请单需对接考勤系统', NULL, 'en_US', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`, `value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049), '云简业财系统的差旅申请单需对接考勤系统', NULL, 'ja_JP', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`,`value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049),'云简业财系统的差旅申请单需对接考勤系统', NULL, 'zh_CN', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`, `value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049),'云简业财系统的差旅申请单需对接考勤系统', NULL, 'zh_TW', now());