package com.akesobio.report.costControl.common;

import cn.hutool.core.util.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.util.Strings;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @Date 2023-12-26 13:57
 **/
@Slf4j
public class SsoUtils {

    public static String encrypt(String sSrc, String sKey, String entryMode) {
        entryMode = !isEmpty(entryMode) ? entryMode : "AES/ECB/PKCS5Padding";
        try {
            if (sKey == null) {
                log.info("Key为空null");
                return null;
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) {
                log.info("Key长度不是16位");
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance(entryMode);//"算法/模式/补码方式"
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
            byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
            //此处使用BASE64做转码功能，同时能起到2次加密的作用。
            return URLEncoder.encode(new Base64().encodeToString(encrypted), "utf-8");
        } catch (Exception e) {
            log.error("加密出错", e);
        }
        return null;
    }

    static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String && Strings.isEmpty(obj.toString())) {
            return true;
        }
        return false;
    }

}
