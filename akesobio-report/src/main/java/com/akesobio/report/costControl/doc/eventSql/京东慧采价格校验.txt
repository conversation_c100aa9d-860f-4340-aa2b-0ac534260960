-- 校验京东采购申请慧采	/jd/purchaseApprovalCheck
/* #校验京东采购申请慧采*//* 校验规则 - val_rule_param   校验规则入参表：可以配置当前校验规则的入参，有当前单据头字段header_param、行字段line_param；前序管理单据头字段link_header_param，行字段link_line_param；后续管理单据：头字段back_header_param、行字段back_line_param */
INSERT INTO ` cpkfsw `.` val_rule_param ` ( ` rule_name `, ` header_param `, ` line_param `, ` creation_date `, ` last_update_date `, ` company_id `, ` last_updated_by `, ` link_header_param `, ` link_line_param `, ` back_header_param `, ` back_line_param ` )
VALUES
	( 'c_jd_purchase_approval', 'header_id,header_type_id,company_id,document_num,total_amount,status,column37', 'line_id,type_id,quantity,price,amount', now ( ), now ( ), 23049, 0, 'header_id,header_type_id,company_id,document_num,total_amount,status', 'line_id,type_id,quantity,price,amount', '', '' );
/* 校验规则 - fnd_common_column   校验规则类型表：NEXP:单据头校验、NEXPL:单据行校验 */
INSERT INTO ` cpkfsw `.` fnd_common_column ` ( ` source `, ` column_seq_num `, ` application_column_name `, ` column_type `, ` lov_id `, ` bi_flag `, ` val_flag `, ` wf_flag `, ` parameter_flag `, ` parameter `, ` end_user_column_name `, ` last_update_date ` )
VALUES
	( 'NEXP', 900, 'c_jd_purchase_approval', 'integer', NULL, NULL, 'Y', 'Y', NULL, NULL, '校验京东采购申请慧采', now ( ) );
/* 校验规则 - 规则条件 多语言   校验规则多语言描述表 */
INSERT INTO ` cpkfsw `.` fnd_common_column_tl ` ( ` source `, ` application_column_name `, ` end_user_column_name `, ` LANGUAGE `, ` last_update_date ` )
VALUES
	( 'NEXP', 'c_jd_purchase_approval', '校验京东采购申请慧采', 'en_US', now ( ) ),
	( 'NEXP', 'c_jd_purchase_approval', '校验京东采购申请慧采', 'zh_CN', now ( ) ),
	( 'NEXP', 'c_jd_purchase_approval', '校验京东采购申请慧采', 'ja_JP', now ( ) ),
	( 'NEXP', 'c_jd_purchase_approval', '校验京东采购申请慧采', 'zh_TW', now ( ) );
/* 校验规则 - project_api_url    校验规则触发接口表：project_url配置触发地址*/
INSERT INTO ` cpkfsw `.` project_api_url ` ( ` company_id `, ` project_url `, ` event_code `, ` header_type_code `, ` position_code `, ` sequence_num `, ` pre_incomplete `, ` after_incomplete `, ` pre_approve `, ` after_approve `, ` login `, ` description ` )
VALUES
	( 23049, 'http://*********/*********/jd/purchaseApprovalCheck', 'c_jd_purchase_approval', 'all', NULL, 1, NULL, NULL, NULL, NULL, NULL, '校验京东采购申请慧采' );
-- PS:四张表由c_jd_purchase_approval关联：val_rule_param.rule_name、fnd_common_column.application_column_name、fnd_common_column_tl.application_column_name、project_api_url.event_code