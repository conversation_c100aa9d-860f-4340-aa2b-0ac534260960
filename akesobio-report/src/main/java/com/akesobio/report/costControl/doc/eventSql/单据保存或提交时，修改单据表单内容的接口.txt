单据保存或提交时，修改单据表单内容的接口，配置的数据表是project_api_url，相关字段有project_url:请求接口，event_code：事件(固定为allowance)，header_type_code：单据类型，sequence_num：执行顺序（如果针对同一种单据有多个类型接口），pre_incomplete：保存前触发，description：文字描述

INSERT INTO `cpkfsw`.`project_api_url` (`project_url_id`, `company_id`, `project_url`, `event_code`, `header_type_code`, `position_code`, `sequence_num`, `pre_incomplete`, `after_incomplete`, `pre_approve`, `after_approve`, `login`, `description`) VALUES (4, 23049, 'http://*********/*********/IITController/KF-SAP-01/IITProjectNumberFind/YXDG02', 'allowance', 'YXDG02', NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL);
