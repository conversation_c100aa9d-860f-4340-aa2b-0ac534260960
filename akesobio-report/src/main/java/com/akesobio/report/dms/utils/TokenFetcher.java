package com.akesobio.report.dms.utils;

import org.springframework.beans.factory.annotation.Value;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class TokenFetcher {

    @Value("${bt.auth.url}")
    private String url;

    @Value("${bt.auth.sysCode}")
    private String sysCode;

    @Value("${bt.auth.secret}")
    private String secret;

  /*  // 测试环境URL
    private static final String TOKEN_URL = "https://akesobio-uat.sinoeyes.com:48888/webapi/api/akesobio/getToken";

    // 账号信息
    private static final String USERNAME = "akesobioOA";
    private static final String PASSWORD = "akesobioOA@Test250527";*/

    /**
     * 获取Token
     *
     * @param sysCode 第三方应用代码
     * @param secret  登录密钥
     * @return TokenResponse对象，包含token和过期时间
     * @throws Exception 如果获取失败抛出异常
     */
    public static String getToken(String sysCode, String secret, String url) throws Exception {
        // 构建请求参数
        String params = "sysCode=" + sysCode + "&secret=" + secret;

        // 创建HTTP连接
        URL url1 = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) url1.openConnection();

        try {
            // 设置请求属性
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

            // 添加Basic Auth认证头
            String auth = sysCode + ":" + secret;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            // 发送请求参数
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = params.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }

                    // 解析JSON响应
                    String jsonResponse = response.toString();
                    if (jsonResponse.contains("errorCode")) {
                        // 处理错误响应
                        throw new RuntimeException("获取Token失败: " + jsonResponse);
                    } else {
                        // 提取token和过期时间
                       /* String token = jsonResponse.split("\"token\":\"")[1].split("\"")[0];
                        String expiredTimeStr = jsonResponse.split("\"expiredTime\":")[1].split("}")[0];
                        long expiredTime = Long.parseLong(expiredTimeStr);
                        return new TokenResponse(token, expiredTime);*/
                        String token = jsonResponse.split("\"token\":\"")[1].split("\"")[0];
                        return token;
                    }
                }
            } else {
                throw new RuntimeException("HTTP请求失败，状态码: " + responseCode);
            }
        } finally {
            connection.disconnect();
        }
    }

    /**
     * Token响应数据类
     */
    public static class TokenResponse {
        private final String token;
        private final long expiredTime; // 单位:秒

        public TokenResponse(String token, long expiredTime) {
            this.token = token;
            this.expiredTime = expiredTime;
        }

        public String getToken() {
            return token;
        }

        public long getExpiredTime() {
            return expiredTime;
        }

        @Override
        public String toString() {
            return "TokenResponse{" +
                    "token='" + token + '\'' +
                    ", expiredTime=" + expiredTime +
                    '}';
        }
    }

    // 示例用法
    public static void main(String[] args) {
        try {
            // 替换为平台提供的实际sysCode和secret
            String sysCode = "your_sys_code";
            String secret = "your_secret";

            String response = TokenFetcher.getToken(sysCode, secret, secret);
            System.out.println("获取Token成功: " + response);
            //System.out.println("Token: " + response.getToken());
           // System.out.println("过期时间(秒): " + response.getExpiredTime());
        } catch (Exception e) {
            System.err.println("获取Token失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
