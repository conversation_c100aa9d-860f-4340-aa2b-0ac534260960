package com.akesobio.report.dms.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.dms.service.IAnnualDistributionContractService;
import com.akesobio.report.dms.utils.TokenUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AnnualDistributionContractServiceImpl implements IAnnualDistributionContractService {

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.grant_type}")
    private String grant_type;
    @Value("${client.auth.username}")
    private String username;
    @Value("${client.auth.password}")
    private String password;
    @Value("${client.auth.dataBaseurl}")
    private String DATABASEURL;
    @Value("${client.auth.ekpUser}")
    private String EKPUSER;
    @Value("${client.auth.ekpPassword}")
    private String EKPPASSWORD;

    @Value("${client.auth.annualDistributionContractStatus}")
    private String annualDistributionContractStatus;
    @Value("${client.auth.annualDistributionContractUploadFile}")
    private String annualDistributionContractUploadFile;

    private static String token = "";

    /**
     * 获取token
     */
    public void getToken() {
        JSONObject map = new JSONObject();
        map.put("grant_type", grant_type);
        map.put("username", username);
        map.put("password", password);
        String post;
        try {
            post = HttpUtils.sendPostJson(IP + GetToken, map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String annualDistributionContractApproved(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA-DMS单号  */
        String code = jsonObject.getString("dms_oddNumbers");
        /** 签核状态  */ //制单：10;审批中：20;结束：30;废弃：40; 驳回：50;
        Integer auditStatus = 30;

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM年度经销合同审批状态数据为：" + objectMap.toString());
        String url = IP + annualDistributionContractStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String annualDistributionContractReject(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA-DMS单号  */
        String code = jsonObject.getString("dms_oddNumbers");
        /** 签核状态  */ //制单：10;审批中：20;结束：30;废弃：40; 驳回：50;
        Integer auditStatus = 50;

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM年度经销合同审批状态数据为：" + objectMap.toString());
        String url = IP + annualDistributionContractStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String annualDistributionContractDiscard(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA-DMS单号  */
        String code = jsonObject.getString("dms_oddNumbers");
        /** 签核状态  */ //制单：10;审批中：20;结束：30;废弃：40; 驳回：50;
        Integer auditStatus = 40;

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM年度经销合同审批状态数据为：" + objectMap.toString());
        String url = IP + annualDistributionContractStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    /* OA年度经销合同，文件同步CRM */
    @Override
    public AjaxResult annualDistributionContractFile(JSONObject jsonObject) {
        String id = jsonObject.getString("kf_fdId");//id
        String code = jsonObject.getString("dms_oddNumbers");//DMS单号
        FileInputStream in = null;
        FileOutputStream out = null;
        List<File> files = null;
        try {
            List<Map<String, String>> pathList = TokenUtils.getPathList(DATABASEURL, EKPUSER, EKPPASSWORD, id, "fd_bdpdf_fj");
            if (pathList.isEmpty()) {
                return AjaxResult.success();
            }
            log.info("文件数据：{}", pathList);
            String token = TokenUtils.getToken(IP, GetToken, username, password, grant_type);
//            String token = TokenUtils.getToken("http://localhost:9080/t/akesobio_devop", "/token", "administrator", "P@ssw0rd1", grant_type);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);

            HttpRequest request = HttpUtil.createPost(IP + annualDistributionContractUploadFile);
//            HttpRequest request = HttpUtil.createPost("http://localhost:9080/t/akesobio_devop/api/akesobiocsd/openApiAnnualAgreement/updateFile");
            request.addHeaders(headers);
            request.form("code", code);//订单号

            files = new ArrayList<>();

            for (Map<String, String> stringStringMap : pathList) {
                File newFile;
                File originalFile = new File(stringStringMap.get("filePath"));
                in = new FileInputStream(originalFile);

                newFile = new File(stringStringMap.get("fileName"));
                newFile.setWritable(true);

                out = new FileOutputStream(newFile);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) > 0) {
                    out.write(buffer, 0, bytesRead);
                }
                files.add(newFile);
            }
            for (File file : files) {
                request.form("multipartFile", file);
            }

            String reuslt = request.execute().body();
            System.out.println(reuslt);
            JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
            if (!data.getBoolean("success"))
                return AjaxResult.error(data.getString("errorInfo"));
            return AjaxResult.success();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if (files != null) {
                for (File file : files) {
                    if (file != null && file.exists()) {
                        file.delete();
                    }
                }
            }

        }


    }

    @Override
    public AjaxResult annualDistributionContractFile(String code, List<MultipartFile> files) throws IOException {
        String token = TokenUtils.getToken(IP, GetToken, username, password, grant_type);
//            String token = TokenUtils.getToken("http://localhost:9080/t/akesobio_devop", "/token", "administrator", "P@ssw0rd1", grant_type);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + annualDistributionContractUploadFile);
//            HttpRequest request = HttpUtil.createPost("http://localhost:9080/t/akesobio_devop/api/akesobiocsd/openApiAnnualAgreement/updateFile");
        request.addHeaders(headers);
        request.form("code", code);//订单号

        for (MultipartFile file : files) {
            String originalFilename = file.getOriginalFilename();
            File tempFile = FileUtil.createTempFile();
            FileUtil.writeFromStream(file.getInputStream(), tempFile);
            request.form("multipartFile", tempFile, originalFilename);
//            request.form("multipartFile", FileUtil.writeFromStream(file.getInputStream(), Files.createTempFile(originalFilename.substring(0, originalFilename.indexOf(".")), originalFilename.substring(originalFilename.indexOf("."))).toFile()));
        }

        String reuslt = request.execute().body();

        log.info("【年度协议-同步协议-响应参数】：{}", reuslt);
        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
        if (!data.getBoolean("success"))
            return AjaxResult.error(data.getString("errorInfo"));
        return AjaxResult.success();
    }
}
