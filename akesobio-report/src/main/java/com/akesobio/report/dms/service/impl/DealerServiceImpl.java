package com.akesobio.report.dms.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.entity.Dealer;
import com.akesobio.report.dms.service.DealerService;
import com.akesobio.report.dms.utils.TokenUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ServiceImpl 经销商开户
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@Service
@Slf4j
public class DealerServiceImpl implements DealerService {

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GETTOKEN;
    @Value("${client.auth.grant_type}")
    private String GRANT_TYPE;
    @Value("${client.auth.username}")
    private String USERNAME;
    @Value("${client.auth.password}")
    private String PASSWORD;

    @Value("${client.auth.updateDealer}")
    private String UPDATEDEALER;

    @Override
    public AjaxResult updateDealer(Dealer dealer, List<MultipartFile> files) throws Exception {
        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + UPDATEDEALER);
        request.addHeaders(headers);

        request.form("code", dealer.getCode());//订单号
        request.form("uploadExpressNumberMethod", dealer.getUploadExpressNumberMethod());//上传快递单号信息，10：单号 20：附件
        request.form("expressNumber", dealer.getExpressNumber());//快递单号

        if (files != null && !files.isEmpty()) {
            for (MultipartFile file : files) {
                String originalFilename = file.getOriginalFilename();
                File tempFile = FileUtil.createTempFile();
                FileUtil.writeFromStream(file.getInputStream(), tempFile);
                request.form("multipartFile", tempFile, originalFilename);
//            request.form("multipartFile", FileUtil.writeFromStream(file.getInputStream(), Files.createTempFile(originalFilename.substring(0, originalFilename.indexOf(".")), originalFilename.substring(originalFilename.indexOf("."))).toFile()));
            }
        }
        String reuslt = request.execute().body();

        log.info("【经销商开户-更新-响应参数】：{}", reuslt);
        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
        if (!data.getBoolean("success"))
            return AjaxResult.error(data.getString("errorInfo"));
        return AjaxResult.success();
    }

}