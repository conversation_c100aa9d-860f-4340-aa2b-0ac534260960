package com.akesobio.report.dms.service;

import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.entity.ParamsAgreement;
import com.akesobio.report.dms.entity.ReturnOrder;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Service
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface ReturnOrderService {

    /* 新建 */
    AjaxResult createReturnOrder(JSONObject jsonObject) throws Exception;

    AjaxResult createReturnOrder(ReturnOrder returnOrder, MultipartFile file) throws Exception;
    AjaxResult createReturnOrder2(ReturnOrder returnOrder, List<MultipartFile> files) throws Exception;

    /* 更新 */
    AjaxResult updateReturnOrder(@RequestBody JSONObject json);

    AjaxResult syncAgreement(ParamsAgreement paramsAgreement, MultipartFile file) throws Exception;

    /* 单据状态-待商务经理审批 */
    AjaxResult approveReturnOrder20(@RequestBody JSONObject json);

    /* 单据状态-待OA后续审批（待审） */
    AjaxResult approveReturnOrder30(@RequestBody JSONObject json);

    /* 单据状态-已审批（结束） */
    AjaxResult approveReturnOrder40(@RequestBody JSONObject json);

    /* 单据状态-驳回 */
    AjaxResult approveReturnOrder50(@RequestBody JSONObject json);

    /* 单据状态-作废 */
    AjaxResult approveReturnOrder60(@RequestBody JSONObject json);
}