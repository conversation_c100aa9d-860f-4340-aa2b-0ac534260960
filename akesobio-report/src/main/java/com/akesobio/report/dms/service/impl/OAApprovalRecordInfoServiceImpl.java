package com.akesobio.report.dms.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.dms.domain.CRMApprovalRecord;
import com.akesobio.report.dms.domain.CRMApprovalRecordDetailsInfo;
import com.akesobio.report.dms.domain.OAApprovalRecordInfo;
import com.akesobio.report.dms.mapper.OAApprovalRecordInfoMapper;
import com.akesobio.report.dms.service.IOAApprovalRecordInfoService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * OA审批记录信息Service业务层处理
 */
@Service
@Slf4j
public class OAApprovalRecordInfoServiceImpl implements IOAApprovalRecordInfoService {

    @Autowired
    private OAApprovalRecordInfoMapper mapper;

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.grant_type}")
    private String grant_type;
    @Value("${client.auth.username}")
    private String username;
    @Value("${client.auth.password}")
    private String password;

    @Value("${client.auth.approvalRecordInfo}")
    private String approvalRecordInfo;

    private static String token = "";

    /**
     * 获取token
     */
    public void getToken() {
        JSONObject map = new JSONObject();
        map.put("grant_type", grant_type);
        map.put("username", username);
        map.put("password", password);
        String post;
        try {
            post = HttpUtils.sendPostJson(IP + GetToken, map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 查询OA审批记录信息
     *
     * @return OA审批记录信息
     */
    @Override
    public String selectOAApprovalRecordInfo(String id, Integer type, String code) {
        getToken();

        OAApprovalRecordInfo info = new OAApprovalRecordInfo();
        info.setId(id);
        List<OAApprovalRecordInfo> list = mapper.selectOAApprovalRecordInfoList(info);
        List<CRMApprovalRecordDetailsInfo> crmList = new ArrayList<>();
        String currentHandler = "";
        if (list != null && !list.isEmpty()) {
            currentHandler = list.get(0).getCurrentProcessor();
            for (OAApprovalRecordInfo oaRecord : list) {
                CRMApprovalRecordDetailsInfo crmRecord = new CRMApprovalRecordDetailsInfo();
                crmRecord.setOperator(oaRecord.getOperator());
                crmRecord.setNodeName(oaRecord.getNodeName());
                crmRecord.setReceptionTime(oaRecord.getReceptionTime());
                crmList.add(crmRecord);
            }
        }

        List<CRMApprovalRecord> crmApprovalRecordList=new ArrayList<>();
        CRMApprovalRecord crmApprovalRecord=new CRMApprovalRecord();
        crmApprovalRecord.setCode(code);
        crmApprovalRecord.setType(type);
        crmApprovalRecord.setCurrentHandler(currentHandler);
        crmApprovalRecord.setDetails(crmList);

        crmApprovalRecordList.add(crmApprovalRecord);




        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        String jsonArray = JSONArray.toJSONString(crmApprovalRecordList);

        log.info("发送CRMOA审批记录信息数据为：" + jsonArray.toString());
        String url = IP + approvalRecordInfo;

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }
}
