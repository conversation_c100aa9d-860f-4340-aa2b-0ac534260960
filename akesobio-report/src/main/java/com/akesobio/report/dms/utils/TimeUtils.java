package com.akesobio.report.dms.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class TimeUtils {
   public static String parseString(String date) throws ParseException {
        return date.split(" ")[0];
    }

    public static String parseLong(Long date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date(date));
    }

    public static String parse(String date){
       if(StringUtils.isBlank(date)){
          return "";
       }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date(Long.parseLong(String.valueOf(date))));
    }

    public static String parseTime(String date){
        SimpleDateFormat sdf=new SimpleDateFormat("HH:mm:ss");
        return sdf.format(new Date(Long.parseLong(String.valueOf(date))));
    }

    public static String parseTime1(String date){
        SimpleDateFormat sdf=new SimpleDateFormat("HH:mm");
        return sdf.format(new Date(Long.parseLong(String.valueOf(date))));
    }

    public static String parseDateTime(String date){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(Long.parseLong(String.valueOf(date))));
    }

    public static Date parseDate(String date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return  sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return new Date();
        }
    }
    public static String parseDate1(String date){
        if(StringUtils.isBlank(date)){
            return "";
        }
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.format(sdf.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }
}
