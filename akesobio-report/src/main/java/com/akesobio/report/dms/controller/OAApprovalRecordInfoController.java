package com.akesobio.report.dms.controller;


import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.service.IOAApprovalRecordInfoService;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * OA审批记录Controller
 */
@RestController
@RequestMapping("/approvalRecordInfo")
@Slf4j
public class OAApprovalRecordInfoController {

    @Autowired
    private IOAApprovalRecordInfoService service;

    /**
     * OA采购订单流程审批记录更新到CRM
     */
    @RequestMapping("/purchaseOrder")
    public AjaxResult purchaseOrder(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单发送数据分析系统为：" + jsonObject);
        /** OA fdid  */
        String id = jsonObject.getString("kf_fdId");
        //单据类型
        Integer type = 20;
        //OA单据编号
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        if (id != null && id != "") {
            String response = service.selectOAApprovalRecordInfo(id, type, code);
            log.info("CRM新增OA采购订单签核流程接口返回信息为：" + response);
            // 解析JSON响应
            JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
            Integer count = jsonResponse.getAsJsonObject("Data").get("count").getAsInt();
            String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
            String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
            //构建返回参数
            Map<String, Object> mapOA = new HashMap<>();
            if (count > 0  && success.equals("true")) {
                return AjaxResult.success()
                        .put("success", true).put("data", mapOA);
            } else {
                return AjaxResult.error().put("error", true).put("data", errorInfo);
            }
        } else {
            return AjaxResult.error().put("error", true).put("data", "OA单据ID为空！！！");
        }
    }

    /**
     * OA退货订单流程审批记录更新到CRM
     */
    @RequestMapping("/returnOrder")
    public AjaxResult returnOrder(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA退货订单发送数据分析系统为：" + jsonObject);
        /** OA fdid  */
        String id = jsonObject.getString("kf_fdId");
        //单据类型
        Integer type = 30;
        //OA单据编号
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        if (id != null && id != "") {
            String response = service.selectOAApprovalRecordInfo(id, type, code);
            log.info("CRM新增OA退货订单签核流程接口返回信息为：" + response);
            // 解析JSON响应
            JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
            Integer count = jsonResponse.getAsJsonObject("Data").get("count").getAsInt();
            String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
            String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
            //构建返回参数
            Map<String, Object> mapOA = new HashMap<>();
            if (count > 0  && success.equals("true")) {
                return AjaxResult.success()
                        .put("success", true).put("data", mapOA);
            } else {
                return AjaxResult.error().put("error", true).put("data", errorInfo);
            }
        } else {
            return AjaxResult.error().put("error", true).put("data", "OA单据ID为空！！！");
        }
    }

    /**
     * OA业务伙伴流程审批记录更新到CRM
     */
    @RequestMapping("/businessPartner")
    public AjaxResult businessPartner(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA业务伙伴发送数据分析系统为：" + jsonObject);
        /** OA fdid  */
        String id = jsonObject.getString("kf_fdId");
        //单据类型
        Integer type = 10;
        //DMS单据编号
        String code = jsonObject.getString("dms_oddNumbers");
        if (id != null && id != "") {
            String response = service.selectOAApprovalRecordInfo(id, type, code);
            log.info("CRM新增OA业务伙伴签核流程接口返回信息为：" + response);
            // 解析JSON响应
            JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
            Integer count = jsonResponse.getAsJsonObject("Data").get("count").getAsInt();
            String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
            String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
            //构建返回参数
            Map<String, Object> mapOA = new HashMap<>();
            if (count > 0  && success.equals("true")) {
                return AjaxResult.success()
                        .put("success", true).put("data", mapOA);
            } else {
                return AjaxResult.error().put("error", true).put("data", errorInfo);
            }
        } else {
            return AjaxResult.error().put("error", true).put("data", "OA单据ID为空！！！");
        }
    }

    /**
     * OA年度经销合同流程审批记录更新到CRM
     */
    @RequestMapping("/annualDistributionContract")
    public AjaxResult annualDistributionContract(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA年度经销合同发送数据分析系统为：" + jsonObject);
        /** OA fdid  */
        String id = jsonObject.getString("kf_fdId");
        //单据类型
        Integer type = 40;
        //DMS单据编号
        String code = jsonObject.getString("dms_oddNumbers");
        if (id != null && id != "") {
            String response = service.selectOAApprovalRecordInfo(id, type, code);
            log.info("CRM新增OA年度经销合同签核流程接口返回信息为：" + response);
            // 解析JSON响应
            JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
            Integer count = jsonResponse.getAsJsonObject("Data").get("count").getAsInt();
            String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
            String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
            //构建返回参数
            Map<String, Object> mapOA = new HashMap<>();
            if (count > 0 && success.equals("true")) {
                return AjaxResult.success()
                        .put("success", true).put("data", mapOA);
            } else {
                return AjaxResult.error().put("error", true).put("data", errorInfo);
            }
        } else {
            return AjaxResult.error().put("error", true).put("data", "OA单据ID为空！！！");
        }
    }
}
