package com.akesobio.report.dms.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.domain.SapSupplierData;
import com.akesobio.report.dms.entity.ParamsAgreement;
import com.akesobio.report.dms.entity.ReturnOrder;
import com.akesobio.report.dms.mapper.SapSupplierDataMapper;
import com.akesobio.report.dms.service.ReturnOrderService;
import com.akesobio.report.dms.utils.TokenUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * ServiceImpl
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@Service
@Slf4j
public class ReturnOrderServiceImpl implements ReturnOrderService {

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GETTOKEN;
    @Value("${client.auth.grant_type}")
    private String GRANT_TYPE;
    @Value("${client.auth.username}")
    private String USERNAME;
    @Value("${client.auth.password}")
    private String PASSWORD;
    @Value("${client.auth.createReturnOrder}")
    private String CREATERETURNORDER;
    @Value("${client.auth.updateReturnOrder}")
    private String UPDATERETURNORDER;
    @Value("${client.auth.purchaseOrderUpdate}")
    private String PURCHASEORDERUPDATE;
    @Value("${client.auth.approveReturnOrder}")
    private String APPROVERETURNORDER;
    @Value("${client.auth.dataBaseurl}")
    private String DATABASEURL;
    @Value("${client.auth.ekpUser}")
    private String EKPUSER;
    @Value("${client.auth.ekpPassword}")
    private String EKPPASSWORD;

    @Autowired
    private SapSupplierDataMapper sapSupplierDataMapper;

    /**
     * 新建
     *
     * @param jsonObject
     * @return
     */
    @Override
    public AjaxResult createReturnOrder(JSONObject jsonObject) throws Exception {
        String id = jsonObject.getString("kf_fdId");//id
        String code = jsonObject.getString("fd_3abf84e024cbfe");//订单号
        String accountCode = jsonObject.getString("fd_3ad8db9129ed14_text");//经销商编码
        String businessManagerCode = jsonObject.getString("work_number");//商务经理编码
        String salesOrganizationCode = jsonObject.getString("fd_3ad013e53bcf88");//销售组织编码
        String reason = jsonObject.getString("fd_3bdaa7ff05ebd8");//原因
        String compensation = jsonObject.getString("tax_rate_makeup_1");//3%税率补差
        String totalAllowance = jsonObject.getString("fd_3ac92706221f44");//折回折让合计
        String orderTime = jsonObject.getString("fd_3abf84ee12b8e8");//下单日期
        Date date = new Date(Long.parseLong(orderTime));
        String orderDate = new SimpleDateFormat("yyyy/MM/dd").format(date);
        String way = jsonObject.getString("fd_3ac21896757e14");//退货方式
        String returnMethod = jsonObject.getString("fd_3ac07ba8a87cac");//退货处理方式
        JSONArray jsonArray = JSON.parseArray(jsonObject.getString("fd_3ac07b0eb5f7ee"));
        ArrayList<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            JSONObject e = jsonArray.getJSONObject(i);
            map.put("number", i + 1);//序号
            map.put("sapSalesOrder", e.getString("fd_3ad7ece6481aea"));//销售单号
            map.put("productCode", e.getString("fd_3ac07bded777c0"));//产品编码
            map.put("price", e.getFloat("fd_3ac07bf205322e"));//单价
            map.put("sapLot", e.getString("sap_batch"));//SAP批次
            map.put("lot", e.getString("fd_3ba3915913e238"));//批次
            map.put("quantity", e.getInteger("fd_3ac07bf3563cb8"));//数量
            map.put("totalOrder", e.getFloat("fd_3ad811de718254"));//销售总价
            map.put("returnQuantity", e.getInteger("fd_3ac07bf804cdc0"));//退货数量
            map.put("returnPrice", e.getFloat("fd_3ad0619fb4d69e"));//退货单价
            map.put("totalReturn", e.getFloat("fd_3ad061f6b5d0c2"));//退货总价
            map.put("totalActualReturn", e.getFloat("fd_3ad8aef8c9d0b6"));//实际退货总价
            map.put("allowance", e.getFloat("fd_3ad2bbcd59b474"));//合同折让折回
            map.put("compensation", e.getFloat("fd_3ce75a632621d4"));//3%税率补差-明细表
            map.put("deduct", e.getFloat("fd_3ad061a14a017e"));//补差扣回
            list.add(map);
        }
        String detailsJson = JSON.toJSONString(list);//订单明细
        log.info("【新建-退货订单-明细表数据】：{}", detailsJson);

        List<Map<String, String>> pathList = TokenUtils.getPathList(DATABASEURL, EKPUSER, EKPPASSWORD, id, "fd_3ac2195414ebb6");
        FileInputStream in = null;
        FileOutputStream out = null;
        File newFile = null;
        if (!pathList.isEmpty()) {
            log.info("文件数据：{}", pathList);
            Map<String, String> stringMap = pathList.get(0);
            File originalFile = new File(stringMap.get("filePath"));

            in = new FileInputStream(originalFile);

            newFile = new File(stringMap.get("fileName"));
            newFile.setWritable(true);

            out = new FileOutputStream(newFile);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) > 0) {
                out.write(buffer, 0, bytesRead);
            }
        }

        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + CREATERETURNORDER);
        request.addHeaders(headers);

        request.form("auditStatus", "30");//DMS表单状态，待OA其他审批
        request.form("code", code);//订单号
        request.form("accountCode", accountCode);//经销商编码
        request.form("businessManagerCode", businessManagerCode);//商务经理编码
        request.form("salesOrganizationCode", salesOrganizationCode);//销售组织编码
        request.form("reasonReturn", TokenUtils.map.getOrDefault(reason, ""));//原因
        request.form("compensation", compensation);//3%税率补差
        request.form("totalAllowance", totalAllowance); //折回折让合计
        request.form("orderDate", orderDate);//下单日期
        request.form("way", way);//退货方式
        request.form("returnMethod", returnMethod);//退货处理方式
        request.form("detailsJson", detailsJson);//订单明细
        if (newFile != null)
            request.form("multipartFile", newFile);

        String reuslt = request.execute().body();

        if (in != null)
            in.close();
        if (out != null)
            out.close();
        if (newFile != null && newFile.exists())
            newFile.delete();

        log.info("【新建-退货订单-返回参数】：{}", reuslt);

        Integer errorCode = JSON.parseObject(reuslt).getInteger("ErrorCode");
        if (-1 == errorCode) {
            String message = JSON.parseObject(reuslt).getString("Message");
            return AjaxResult.error(message);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult createReturnOrder(ReturnOrder returnOrder, MultipartFile file) throws IOException {
        log.info("【新建-退货订单-请求数据】：{}", returnOrder);
        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + CREATERETURNORDER);
        request.addHeaders(headers);

        String accountCode = returnOrder.getAccountCode();
        if (accountCode != null && accountCode != "") {
            SapSupplierData sapSupplierData = sapSupplierDataMapper.selectSapSupplierData(accountCode);
            if (sapSupplierData != null && sapSupplierData.getSecondaryMerchantCode() != null
                    && !sapSupplierData.getSecondaryMerchantCode().trim().isEmpty()) {
                log.info("订单号:" + returnOrder.getCode() + ",经销商编码为：" + accountCode + ",一级商编码为：" + sapSupplierData.getFirstLevelMerchantCode() + ",二级商编码为：" + sapSupplierData.getSecondaryMerchantCode());
                accountCode = sapSupplierData.getSecondaryMerchantCode();
            }
        }

        request.form("auditStatus", "30");//DMS表单状态，待OA其他审批
        request.form("code", returnOrder.getCode());//订单号
        request.form("accountCode", accountCode);//经销商编码
        request.form("businessManagerCode", returnOrder.getBusinessManagerCode());//商务经理编码
        request.form("salesOrganizationCode", returnOrder.getSalesOrganizationCode());//销售组织编码
        request.form("reasonReturn", returnOrder.getReasonReturn());//原因
        request.form("compensation", returnOrder.getCompensation());//3%税率补差
        request.form("totalAllowance", returnOrder.getTotalAllowance()); //折回折让合计
        String orderDate = new SimpleDateFormat("yyyy/MM/dd").format(returnOrder.getOrderDate());
        request.form("orderDate", orderDate);//下单日期
        request.form("way", returnOrder.getWay());//退货方式
        request.form("returnMethod", returnOrder.getReturnMethod());//退货处理方式
        request.form("detailsJson", returnOrder.getDetailsJson());//订单明细
        if (file != null) {
            String originalFilename = file.getOriginalFilename();
            request.form("multipartFile", FileUtil.writeFromStream(file.getInputStream(), Files.createTempFile(originalFilename.substring(0, originalFilename.indexOf(".")), originalFilename.substring(originalFilename.indexOf("."))).toFile()));
        }
        String reuslt = request.execute().body();
        log.info("【新建-退货订单-返回参数】：{}", reuslt);

        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
        if (!data.getBoolean("success"))
            return AjaxResult.error(data.getString("errorInfo"));
        return AjaxResult.success();
    }

    @Override
    public AjaxResult createReturnOrder2(ReturnOrder returnOrder, List<MultipartFile> files) throws IOException {
        log.info("【新建-退货订单-请求数据】：{}", returnOrder);
        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + CREATERETURNORDER);
        request.addHeaders(headers);

        String accountCode = returnOrder.getAccountCode();
        if (accountCode != null && accountCode != "") {
            SapSupplierData sapSupplierData = sapSupplierDataMapper.selectSapSupplierData(accountCode);
            if (sapSupplierData != null && sapSupplierData.getSecondaryMerchantCode() != null
                    && !sapSupplierData.getSecondaryMerchantCode().trim().isEmpty()) {
                log.info("订单号:" + returnOrder.getCode() + ",经销商编码为：" + accountCode + ",一级商编码为：" + sapSupplierData.getFirstLevelMerchantCode() + ",二级商编码为：" + sapSupplierData.getSecondaryMerchantCode());
                accountCode = sapSupplierData.getSecondaryMerchantCode();
            }
        }

        request.form("auditStatus", "30");//DMS表单状态，待OA其他审批
        request.form("code", returnOrder.getCode());//订单号
        request.form("accountCode", accountCode);//经销商编码
        request.form("businessManagerCode", returnOrder.getBusinessManagerCode());//商务经理编码
        request.form("salesOrganizationCode", returnOrder.getSalesOrganizationCode());//销售组织编码
        request.form("reasonReturn", returnOrder.getReasonReturn());//原因
        request.form("compensation", returnOrder.getCompensation());//3%税率补差
        request.form("totalAllowance", returnOrder.getTotalAllowance()); //折回折让合计
        String orderDate = new SimpleDateFormat("yyyy/MM/dd").format(returnOrder.getOrderDate());
        request.form("orderDate", orderDate);//下单日期
        request.form("way", returnOrder.getWay());//退货方式
        request.form("returnMethod", returnOrder.getReturnMethod());//退货处理方式
        request.form("detailsJson", returnOrder.getDetailsJson());//订单明细
        if (files != null && !files.isEmpty()) {
            for (MultipartFile file : files) {
                String originalFilename = file.getOriginalFilename();
                File tempFile = FileUtil.createTempFile();
                FileUtil.writeFromStream(file.getInputStream(), tempFile);
                request.form("multipartFile", tempFile, originalFilename);
//            request.form("multipartFile", FileUtil.writeFromStream(file.getInputStream(), Files.createTempFile(originalFilename.substring(0, originalFilename.indexOf(".")), originalFilename.substring(originalFilename.indexOf("."))).toFile()));
            }
        }
        String reuslt = request.execute().body();
        log.info("【新建-退货订单-返回参数】：{}", reuslt);

        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
        if (!data.getBoolean("success"))
            return AjaxResult.error(data.getString("errorInfo"));
        return AjaxResult.success();
    }

    // 更新
    @Override
    public AjaxResult updateReturnOrder(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        String invoiceNumber = json.getString("fd_3be7088628d1a4");//红字发票信息表编号
        String invoiceTypes = json.getString("fd_3c5051a9f5035c");//发票类型
        Integer invoiceType = null;
        String invoiceAddress = "";
        if (invoiceTypes.contains("电子发票")) {
            invoiceType = 10;
            invoiceAddress = json.getString("fd_3c50518bf9f7e4_text");//收票地址邮箱
        } else if (invoiceTypes.contains("纸质发票")) {
            invoiceType = 20;
            invoiceAddress = json.getString("fd_3c5052256fef48_text");//收票地址地点
        }
        String invoiceWay = json.getString("fd_3c505181a88db4");//开票方式
        String invoiceExpressInfo = json.getString("fd_3c50517a15d4c4");//发票快递信息
        String invoiceDates = json.getString("fd_3b2aa5a5d58606");//开票时间
        String invoiceDate = "";
        if (!StrUtil.isEmpty(invoiceDates)) {
            LocalDate parse = LocalDate.parse(invoiceDates);
            Date from = Date.from(parse.atStartOfDay(ZoneId.systemDefault()).toInstant());
            invoiceDate = new SimpleDateFormat("yyyy/MM/dd").format(from);
        }

        String number = json.getString("proof");//发票号码
        String invoiceSigns = json.getString("fd_3c5052075e6cfa");//发票签收
        int invoiceSign;
        if (invoiceSigns != null && invoiceSigns.contains("已签收"))
            invoiceSign = 10;
        else
            invoiceSign = 20;
        String demand = json.getString("fd_3c50521a393404");//开票要求
        String sapCode = json.getString("fd_3ad062abbb3798");//SAP订单号
        String sapStatus = json.getString("fd_3ad2b85d4bf7d8");//SAP订单状态
        String message = json.getString("return_information");//回传信息
//        String sapInvoice = json.getString("fd_3abf84e024cbfe");//SAP系统发票号
        String deliveryCode = json.getString("delivery_no");//交货单号
        String address = json.getString("fd_3c5052256fef48");//退货地址
        String people = json.getString("fd_return_name");//退货收件人

        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("code", code);
        requestMap.put("invoiceNumber", invoiceNumber);
        requestMap.put("invoiceType", invoiceType);
        requestMap.put("invoiceAddress", invoiceAddress);
        requestMap.put("invoiceWay", invoiceWay);
        requestMap.put("invoiceExpressInfo", invoiceExpressInfo);
        requestMap.put("invoiceDate", invoiceDate);
        requestMap.put("number", number);
        requestMap.put("invoiceSign", invoiceSign);
        requestMap.put("demand", demand);
        requestMap.put("sapCode", sapCode);
        requestMap.put("sapStatus", sapStatus);//SAP订单状态
        requestMap.put("message", message);//回传信息
        requestMap.put("address", address);//退货地址
        requestMap.put("people", people);//退货收件人
        requestMap.put("deliveryCode", deliveryCode);
        String requestDataJsonString = JSON.toJSONString(requestMap);

        List<String> requestData = new ArrayList<>();
        requestData.add(requestDataJsonString);

        log.info("【修改-退货订单-请求参数】：{}", requestData);

        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + UPDATERETURNORDER);
        request.addHeaders(headers);
        request.body(requestData.toString());
        String reuslt = request.execute().body();

        log.info("【修改-退货订单-响应参数】：{}", reuslt);


        Integer errorCode = JSON.parseObject(reuslt).getInteger("ErrorCode");
        if (-1 == errorCode) {
            String messages = JSON.parseObject(reuslt).getString("Message");
            return AjaxResult.error(messages);
        }
        return AjaxResult.success();
    }

    //更新退货订单，含文件
    @Override
    public AjaxResult syncAgreement(ParamsAgreement paramsAgreement, MultipartFile file) throws Exception {
        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);
        String suffix;
        if ("订货".equals(paramsAgreement.getAgreementType())) {
            suffix = PURCHASEORDERUPDATE;
        } else if ("退货".equals(paramsAgreement.getAgreementType())) {
            suffix = UPDATERETURNORDER;
        } else {
            log.info("【同步-经销商执行合同文件-不属于采购或退货之一】");
            return AjaxResult.success();
        }
        HttpRequest request = HttpUtil.createPost(IP + suffix);
        request.addHeaders(headers);

        request.form("code", paramsAgreement.getCode());
        if (file != null) {
            String originalFilename = file.getOriginalFilename();
            request.form("multipartFile", FileUtil.writeFromStream(file.getInputStream(), Files.createTempFile(originalFilename.substring(0, originalFilename.indexOf(".")), originalFilename.substring(originalFilename.indexOf("."))).toFile()));
        }
        String reuslt = request.execute().body();
        log.info("【同步-经销商执行合同文件】：{}", reuslt);

        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));
        if (!data.getBoolean("success"))
            return AjaxResult.error(data.getString("errorInfo"));
        return AjaxResult.success();
    }

    /**
     * 待商务经理审批
     *
     * @param json
     * @return
     */
    @Override
    public AjaxResult approveReturnOrder20(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        return this.approveReturnOrder(code, 20, "");
    }

    /**
     * 待OA后续审批（待审）
     *
     * @param json
     * @return
     */
    @Override
    public AjaxResult approveReturnOrder30(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        return this.approveReturnOrder(code, 30, "");
    }

    /**
     * 已审批（结束）
     *
     * @param json
     * @return
     */
    @Override
    public AjaxResult approveReturnOrder40(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        return this.approveReturnOrder(code, 40, "");
    }

    /**
     * 驳回
     *
     * @param json
     * @return
     */
    @Override
    public AjaxResult approveReturnOrder50(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        return this.approveReturnOrder(code, 50, "");
    }

    /**
     * 作废
     *
     * @param json
     * @return
     */
    @Override
    public AjaxResult approveReturnOrder60(JSONObject json) {
        String code = json.getString("fd_3abf84e024cbfe");//订单号
        return this.approveReturnOrder(code, 60, "");
    }


    /**
     * 更新审批状态
     *
     * @param code
     * @param auditStatus
     * @param idea
     * @return
     */
    private AjaxResult approveReturnOrder(String code, Integer auditStatus, String idea) {
        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("code", code);
        requestMap.put("auditStatus", auditStatus);//10：制单 20：待商务经理审批 30：待OA后续审批 40：已审批 50：已驳回 60：作废
        requestMap.put("idea", idea);

        String requestDataJsonString = JSON.toJSONString(requestMap);
        List<String> requestData = new ArrayList<>();
        requestData.add(requestDataJsonString);
        log.info("【更新签核状态-退货订单-请求参数】：{}", requestData);

        String token = TokenUtils.getToken(IP, GETTOKEN, USERNAME, PASSWORD, GRANT_TYPE);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        HttpRequest request = HttpUtil.createPost(IP + APPROVERETURNORDER);
        request.addHeaders(headers);
        request.body(requestData.toString());
        String reuslt = request.execute().body();

        log.info("【更新签核状态-退货订单-响应参数】：{}", reuslt);

        JSONObject data = JSON.parseObject(JSON.parseObject(reuslt).getString("Data"));

        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();

        if (!data.getBoolean("success"))
            return AjaxResult.error().put("error", true).put("data", data.getString("errorInfo"));
        return AjaxResult.success().put("success", true).put("data", mapOA);
    }

}