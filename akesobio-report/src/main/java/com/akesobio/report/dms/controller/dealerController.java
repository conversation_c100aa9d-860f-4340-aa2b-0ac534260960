package com.akesobio.report.dms.controller;

import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.entity.Dealer;
import com.akesobio.report.dms.service.DealerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 经销商开户
 *
 * @Package_Name com.akesobio.report.dms.controller
 * <AUTHOR>
 * @TIME
 * @Version
 */
@RestController
@RequestMapping("/dealerController")
@Slf4j
public class dealerController {

    @Autowired
    private DealerService dealerService;

    /**
     * 更新经销商开户
     */
    @RequestMapping("/updateDealer")
    public AjaxResult updateDealer(@RequestPart("dealer") Dealer dealer, @RequestPart(value = "files", required = false) List<MultipartFile> files) throws Exception {
        return dealerService.updateDealer(dealer, files);
    }

}
