package com.akesobio.report.dms.service;


import com.akesobio.report.dms.domain.PurchaseOrder;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;

/**
 * OA采购订单Service接口
 */
public interface IPurchaseOrderService {

    /**
     * OA采购订单新增发送-CRM
     */
    String sendPurchaseOrderAddToCRM(JSONObject jsonObject);

    /**
     * OA采购订单修改发送-CRM
     */
    String sendPurchaseOrderUpdateToCRM(JSONObject jsonObject);

    /**
     * OA采购订单从CRM获取信用额度
     */
    String obtainPurchaseOrderLimitToCRM(JSONObject jsonObject);

    /**
     * OA采购订单从CRM获取资质效期
     */
    String qualificationValidityPeriod(JSONObject jsonObject);

    /**
     * OA采购订单审批状态更新到CRM
     * 审批通过（单据结束）
     */
    String purchaseOrderApproved(JSONObject jsonObject);

    /**
     * OA采购订单审批状态更新到CRM
     * 驳回（单据驳回）
     */
    String purchaseOrderReject(JSONObject jsonObject);

    /**
     * OA采购订单审批状态更新到CRM
     * 驳回（单据驳回到商务经理）
     */
    String purchaseOrderRejectBusinessManager(JSONObject jsonObject);

    /**
     * OA采购订单审批状态更新到CRM
     * 废弃（单据废弃）
     */
    String purchaseOrderDiscard(JSONObject jsonObject);

    /**
     * 查询OA采购订单
     */
    List<PurchaseOrder> selectPurchaseOrderList(PurchaseOrder purchaseOrder);

}
