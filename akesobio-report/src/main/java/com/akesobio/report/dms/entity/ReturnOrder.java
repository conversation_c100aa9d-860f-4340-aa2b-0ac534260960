package com.akesobio.report.dms.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Package_Name com.akesobio.report.dms.entity
 * <AUTHOR>
 * @TIME
 * @Version
 */
@Data
@Getter
@Setter
public class ReturnOrder {

    private String code;

    private String accountCode;

    private String businessManagerCode;

    private String salesOrganizationCode;

    private String reasonReturn;

    private String compensation;

    private String totalAllowance;

    @JsonFormat(pattern = "yyyy/MM/dd")
    private Date orderDate;

    private String way;

    private String returnMethod;

    private String detailsJson;

}
