package com.akesobio.report.dms.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DmsOpenApiAuthService {

    @Value("${dms.openapi.host}")
    private String dmsApiHost;

    @Value("${dms.openapi.tenant-code}")
    private String dmsTenantCode;

    @Value("${dms.openapi.username}")
    private String dmsApiUsername;

    @Value("${dms.openapi.password}")
    private String dmsApiPassword;

    private String dmsTokenUrl;

    private final OkHttpClient client;

    public DmsOpenApiAuthService() {
        this.client = new OkHttpClient.Builder().build();
    }

    @PostConstruct
    private void init() {
        this.dmsTokenUrl = dmsApiHost + "/t/" + dmsTenantCode + "/token";
        log.info("DMS OpenAPI Token URL initialized to: {}", this.dmsTokenUrl);
    }

    /**
     * Retrieves an authentication token from the DMS OpenAPI.
     *
     * @return The access token string.
     * @throws IOException if the request fails or the response cannot be processed.
     * @throws RuntimeException if the token is not found in the response.
     */
    public String getDmsAuthToken() throws IOException {
        log.info("Attempting to get DMS OpenAPI AuthToken for tenant: {}", dmsTenantCode);

        Map<String, String> requestBodyMap = new HashMap<>();
        requestBodyMap.put("grant_type", "password");
        requestBodyMap.put("username", dmsApiUsername);
        requestBodyMap.put("password", dmsApiPassword);

        String jsonRequestBody = JSONObject.toJSONString(requestBodyMap);
        // log.debug("DMS token request body: {}", jsonRequestBody); // Avoid logging sensitive info in production

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, jsonRequestBody);

        Request request = new Request.Builder()
                .url(this.dmsTokenUrl)
                .post(body)
                .header("Accept", "application/json")
                .header("Content-Type", "application/json")
                .build();

        log.debug("Requesting DMS token from URL: {}", this.dmsTokenUrl);

        try (Response response = client.newCall(request).execute()) {
            String responseBodyString = response.body() != null ? response.body().string() : null;

            if (!response.isSuccessful()) {
                log.error("Failed to get DMS token: Status={}, Body={}", response.code(), responseBodyString);
                throw new IOException("Failed to get DMS token: " + response.code() + " " + responseBodyString);
            }

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                log.error("Failed to get DMS token: Empty response body from {}", this.dmsTokenUrl);
                throw new IOException("Failed to get DMS token: Empty response body.");
            }

            Map<String, Object> responseMap = JSONObject.parseObject(responseBodyString);
            String accessToken = (String) responseMap.get("access_token");

            if (accessToken == null || accessToken.isEmpty()) {
                log.error("Failed to parse access_token from DMS response: {}", responseBodyString);
                throw new RuntimeException("Failed to get DMS token: 'access_token' field not found or empty in response.");
            }

            log.info("DMS AuthToken received successfully for tenant: {}", dmsTenantCode);
            return accessToken;
        } catch (IOException e) {
            log.error("IOException while requesting DMS token for tenant {}: {}", dmsTenantCode, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while requesting DMS token for tenant {}: {}", dmsTenantCode, e.getMessage(), e);
            throw new RuntimeException("Unexpected error fetching DMS token: " + e.getMessage(), e);
        }
    }
} 