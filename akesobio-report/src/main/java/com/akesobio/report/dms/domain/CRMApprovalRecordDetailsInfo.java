package com.akesobio.report.dms.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * OA传CRM审批记录明细信息
 */
@Data
public class CRMApprovalRecordDetailsInfo {

    /**
     * 操作人
     */
    private String operator;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date receptionTime;


}
