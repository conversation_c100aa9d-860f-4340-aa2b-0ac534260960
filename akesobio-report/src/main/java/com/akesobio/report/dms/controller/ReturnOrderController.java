package com.akesobio.report.dms.controller;

import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.entity.ParamsAgreement;
import com.akesobio.report.dms.entity.ReturnOrder;
import com.akesobio.report.dms.service.ReturnOrderService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Controller
 *
 * @Package_Name
 * <AUTHOR> Lee
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
@RestController
@RequestMapping("/ReturnOrder")
@Slf4j
public class ReturnOrderController {

    @Autowired
    private ReturnOrderService returnOrderService;

//    /**
//     * 新增退货订单
//     * @param jsonObject
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping("/createReturnOrder")
//    public AjaxResult createReturnOrder(@RequestBody JSONObject jsonObject) throws Exception {
//        return returnOrderService.createReturnOrder(jsonObject);
//    }

    @RequestMapping("/createReturnOrder")
    public AjaxResult createReturnOrder(@RequestPart("returnOrder") ReturnOrder returnOrder, @RequestPart(value = "file", required = false) MultipartFile file) throws Exception {
        return returnOrderService.createReturnOrder(returnOrder, file);
    }

    @RequestMapping("/createReturnOrder2")
    public AjaxResult createReturnOrder2(@RequestPart("returnOrder") ReturnOrder returnOrder, @RequestPart(value = "files", required = false) List<MultipartFile> files) throws Exception {
        return returnOrderService.createReturnOrder2(returnOrder, files);
    }

    /**
     * 更新退货订单数据（批量）
     *
     * @param json
     * @return
     */
    @RequestMapping("/updateReturnOrder")
    public AjaxResult updateReturnOrder(@RequestBody JSONObject json) {
        return returnOrderService.updateReturnOrder(json);
    }

    // 经销商执行合同触发，同步盖章合同到DMS
    @RequestMapping("/syncAgreement")
    public AjaxResult syncAgreement(@RequestPart("params") ParamsAgreement paramsAgreement, @RequestPart("file") MultipartFile file) throws Exception {
        return returnOrderService.syncAgreement(paramsAgreement, file);
    }

    /**
     * 更新退货订单签核状态（批量）待商务经理审批
     *
     * @param json
     * @return
     */
    @RequestMapping("/approveReturnOrder20")
    public AjaxResult approveReturnOrder20(@RequestBody JSONObject json) {
        return returnOrderService.approveReturnOrder20(json);
    }

    /**
     * 更新退货订单签核状态（批量）待OA后续审批（待审）
     *
     * @param json
     * @return
     */
    @RequestMapping("/approveReturnOrder30")
    public AjaxResult approveReturnOrder30(@RequestBody JSONObject json) {
        return returnOrderService.approveReturnOrder30(json);
    }

    /**
     * 更新退货订单签核状态（批量）已审批（结束）
     *
     * @param json
     * @return
     */
    @RequestMapping("/approveReturnOrder40")
    public AjaxResult approveReturnOrder40(@RequestBody JSONObject json) {
        return returnOrderService.approveReturnOrder40(json);
    }

    /**
     * 更新退货订单签核状态（批量）驳回
     *
     * @param json
     * @return
     */
    @RequestMapping("/approveReturnOrder50")
    public AjaxResult approveReturnOrder50(@RequestBody JSONObject json) {
        return returnOrderService.approveReturnOrder50(json);
    }

    /**
     * 更新退货订单签核状态（批量）作废
     *
     * @param json
     * @return
     */
    @RequestMapping("/approveReturnOrder60")
    public AjaxResult approveReturnOrder60(@RequestBody JSONObject json) {
        return returnOrderService.approveReturnOrder60(json);
    }


}