package com.akesobio.report.dms.service;


import com.akesobio.common.core.domain.AjaxResult;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * OA年度经销合同Service接口
 */
public interface IAnnualDistributionContractService {

    /**
     * OA年度经销合同审批状态更新到CRM
     * 审批通过（单据结束）
     */
    String annualDistributionContractApproved(JSONObject jsonObject);

    /**
     * OA年度经销合同审批状态更新到CRM
     * 驳回（单据驳回）
     */
    String annualDistributionContractReject(JSONObject jsonObject);

    /**
     * OA年度经销合同审批状态更新到CRM
     * 废弃（单据废弃）
     */
    String annualDistributionContractDiscard(JSONObject jsonObject);

    /* OA年度经销合同，文件同步CRM */
    AjaxResult annualDistributionContractFile(JSONObject jsonObject);

    AjaxResult annualDistributionContractFile(String code, List<MultipartFile> files) throws IOException;
}
