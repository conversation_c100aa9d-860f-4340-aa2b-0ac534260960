package com.akesobio.report.dms.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.dms.domain.SapSupplierData;
import io.lettuce.core.dynamic.annotation.Param;



/**
 * SAP供应商主数据Mapper接口
 */
@DataSource(value = DataSourceType.SAP)
public interface SapSupplierDataMapper {

    SapSupplierData selectSapSupplierData(@Param("firstLevelMerchantCode") String firstLevelMerchantCode);
}
