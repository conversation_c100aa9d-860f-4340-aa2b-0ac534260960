package com.akesobio.report.dms.service;

import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.entity.Dealer;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Service 经销商开户
 *
 * @Package_Name
 * <AUTHOR>
 * @TIME 1956/09/12
 * @Version 2003/04/01
 */
public interface DealerService {

    AjaxResult updateDealer(Dealer dealer, List<MultipartFile> files) throws Exception;
}