package com.akesobio.report.dms.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * OA审批记录信息
 */
@Data
public class OAApprovalRecordInfo {



    /**
     * id
     */
    private String id;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date receptionTime;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date operatingTime;

    /**
     * 单据编号
     */
    private String code;

    /**
     * 当前处理人
     */
    private String currentProcessor;


}
