package com.akesobio.report.dms.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.ObjectMapper;
import com.akesobio.common.annotation.Anonymous;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.dms.service.IPurchaseOrderService;
import com.akesobio.report.dms.service.impl.PurchaseOrderServiceImpl;
import com.akesobio.report.dms.utils.TimeUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;



/**
 * 采购订单Controller
 *
 */
@RestController
@RequestMapping("/purchaseOrderController")
@Slf4j
public class PurchaseOrderController {

    @Autowired
    private IPurchaseOrderService purchaseOrderService;

    //UniversalInterface;http://**********:9966/dev-api/purchaseOrderController/qualificationValidityPeriod

    /**
     * OA采购订单新建同步CRM
     */
    @RequestMapping("/purchaseOrderAdd")
    public AjaxResult purchaseOrderAdd(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单新增发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.sendPurchaseOrderAddToCRM(jsonObject);
        log.info("CRM采购订单新增接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA采购订单修改同步CRM
     */
    @RequestMapping("/purchaseOrderUpdate")
    public AjaxResult purchaseOrderUpdate(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单修改发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.sendPurchaseOrderUpdateToCRM(jsonObject);
        log.info("CRM采购订单修改接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }


    /**
     * OA采购订单从CRM获取信用额度
     */
    @RequestMapping("/purchaseOrderLimit")
    public AjaxResult purchaseOrderLimit(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单从CRM获取信用额度发送数据分析系统数据为：" + jsonObject);
        String response = purchaseOrderService.obtainPurchaseOrderLimitToCRM(jsonObject);
        log.info("CRM获取信用额度接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        Object data = jsonResponse.get("Data").isJsonNull() ? 0 : jsonResponse.get("Data").getAsDouble();
        // 提取 Message（字符串）
        String message = jsonResponse.get("Message").getAsString();
        // 提取 ErrorCode（整数）
        int errorCode = jsonResponse.get("ErrorCode").getAsInt();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        mapOA.put("fd_3ac07a9b93cdce", data);
        if (errorCode == 0) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", message);
        }
    }

    /**
     * OA采购订单从CRM获取资质效期
     */
    @RequestMapping("/qualificationValidityPeriod")
    public AjaxResult qualificationValidityPeriod(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单从CRM获取资质效期发送数据分析系统数据为：" + jsonObject);
        String response = purchaseOrderService.qualificationValidityPeriod(jsonObject);
        log.info("CRM获取资质效期接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String dateTimeStr = jsonResponse.get("Data").isJsonNull() ? null : jsonResponse.get("Data").getAsString();
        LocalDateTime dateTime = null;
        if (dateTimeStr != null && dateTimeStr != "") {
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 转换为 LocalDateTime
            dateTime = LocalDateTime.parse(dateTimeStr, formatter);
        }
        // 提取 Message（字符串）
        String message = jsonResponse.get("Message").getAsString();
        // 提取 ErrorCode（整数）
        int errorCode = jsonResponse.get("ErrorCode").getAsInt();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (dateTime != null) {
            mapOA.put("qualificationValidityPeriod", dateTime);
        }
        if (errorCode == 0) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", message);
        }
    }

    /**
     * OA采购订单审批状态更新到CRM
     * 审批通过（单据结束）
     */
    @RequestMapping("/purchaseOrderApproved")
    public AjaxResult purchaseOrderApproved(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单审批通过发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.purchaseOrderApproved(jsonObject);
        log.info("CRM采购订单审批通过接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     *
     * 驳回（单据驳回）
     */
    @RequestMapping("/purchaseOrderReject")
    public AjaxResult purchaseOrderReject(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单驳回发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.purchaseOrderReject(jsonObject);
        log.info("CRM采购订单驳回接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     *
     * 驳回（单据驳回到商务经理）
     */
    @RequestMapping("/purchaseOrderRejectBusinessManager")
    public AjaxResult purchaseOrderRejectBusinessManager(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单驳回到商务经理发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.purchaseOrderRejectBusinessManager(jsonObject);
        log.info("CRM采购订单驳回到商务经理接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA采购订单审批状态更新到CRM
     * 废弃（单据废弃）
     */
    @RequestMapping("/purchaseOrderDiscard")
    public AjaxResult purchaseOrderDiscard(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA采购订单废弃发送数据分析系统为：" + jsonObject);
        String response = purchaseOrderService.purchaseOrderDiscard(jsonObject);
        log.info("CRM采购订单废弃接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }
}
