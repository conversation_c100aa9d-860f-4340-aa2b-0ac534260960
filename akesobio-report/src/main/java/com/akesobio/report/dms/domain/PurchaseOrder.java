package com.akesobio.report.dms.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * OA采购订单
 */
@Data
public class PurchaseOrder {
    /**
     * 订单号
     */
    private String code;
    /**
     * 经销商编码
     */
    private String accountCode;
    /**
     * 总价
     */
    private BigDecimal totalOrder;
    /**
     * 应收款合计
     */
    private BigDecimal totalReceivable;
    /**
     * 商务代表编码
     */
    private String businessRepresentativeCode;
    /**
     * 商务经理编码
     */
    private String businessManagerCode;
    /**
     * 销售组织编码
     */
    private String salesOrganizationCode;
    /**
     * 付款条件编码
     */
    private String paymentTermsCode;
    /**
     * SAP订单状态
     */
    private String sapStatus;
    /**
     *  本次折让合计
     */
    private BigDecimal totalAllowance;
    /**
     *  可折让金额
     */
    private BigDecimal  allowanceAmount;
    /**
     * 3%税率补差
     */
    private BigDecimal compensation;
    /**
     *  下单日期
     */
    @JsonFormat(pattern = "yyyy/MM/dd")
    private Date orderDate;
    /**
     * 收货地址编码
     */
    private String deliveryAddressCode;
    /**
     * 省份
     */
    private String provinceName;
    /**
     * 城市
     */
    private String cityName;
    /**
     * 区县
     */
    private String countyName;
    /**
     * 周六日可收货时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date weekendTime;
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 收票地址
     */
    private String invoiceAddress;
    /**
     * 开票方式
     */
    private String invoiceWay;
    /**
     * 开票要求
     */
    private String invoiceDemand;
    /**
     * 单据id
     */
    private String fdid;
    /**
     * 指定发货仓库
     */
    private String stash;

    /**
     * 应收款对账单
     */
    private String check;
    /**
     *  现款客户应补税金额
     */
    private BigDecimal taxAmount;

    /**
     *  出纳确认到款金额
     */
    private BigDecimal payment;
    /**
     * 回传信息
     */
    private String message;
    /**
     * 物流公司
     */
    private String logisticsCompany;
    /**
     * 预约发货时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date shipmentsTime;
    /**
     * 物流单委托号
     */
    private String logisticsBill;
    /**
     * 是否投保
     */
    private String insureName;
    /**
     * SAP订单号
     */
    private String sapCode;
    /**
     * SAP系统发票号
     */
    private String sapInvoice;
    /**
     * 交货单号
     */
    private String deliveryCode;
    /**
     * 发票快递信息
     */
    private String invoiceExpressInfo;
    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd")
    private Date invoiceDate;
    /**
     * 发票号码
     */
    private String invoiceNumber;
    /**
     * 发票签收
     */
    private Integer invoiceSign;
    /**
     * 签核状态
     */
    private Integer auditStatus;
    /**
     * 当前处理人
     */
    private String currentHandler;

    /**
     * 审批意见
     */
    private String idea;
    /**
     * 订单明细
     */
    private List<PurchaseOrderDetails> details;

}
