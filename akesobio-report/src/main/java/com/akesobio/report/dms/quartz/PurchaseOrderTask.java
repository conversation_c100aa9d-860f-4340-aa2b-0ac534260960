package com.akesobio.report.dms.quartz;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.common.utils.uuid.UUID;
import com.akesobio.report.dms.domain.PurchaseOrder;
import com.akesobio.report.dms.service.IPurchaseOrderService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("PurchaseOrderTask")
@Slf4j
public class PurchaseOrderTask {

    @Autowired
    private IPurchaseOrderService service;

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.grant_type}")
    private String grant_type;
    @Value("${client.auth.username}")
    private String username;
    @Value("${client.auth.password}")
    private String password;

    @Value("${client.auth.purchaseOrderList}")
    private String purchaseOrderList;

    private static String token = "";

    /**
     * 获取token
     */
    public void getToken() {
        JSONObject map = new JSONObject();
        map.put("grant_type", grant_type);
        map.put("username", username);
        map.put("password", password);
        String post;
        try {
            post = HttpUtils.sendPostJson(IP + GetToken, map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 同步OA历史采购订单到DMS
     */
    public void purchaseOrderTask() {
        List<PurchaseOrder> list = service.selectPurchaseOrderList(new PurchaseOrder());
        if (list.size() > 0 && list != null) {
            getToken();
            int batchSize = 1000;
            int totalBatches = (list.size() + batchSize - 1) / batchSize; // 计算需要同步的批次数量
            for (int i = 0; i < totalBatches; i++) {
                int num = i + 1;
                int start = i * batchSize;
                int end = Math.min(start + batchSize, list.size());
                List<PurchaseOrder> batchList = list.subList(start, end);
                String jsonArray = JSONArray.toJSONString(batchList);
                log.info("发送DMS采购订单数据为："+batchList);
                log.info("发送DMS采购订单JSON数据为："+jsonArray);
                Map<String, String> header = new HashMap<>();
                header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
                header.put("Connection", "keep-alive");

                //创建multipart/form-data请求
                HttpRequest request = HttpUtil.createPost(IP + purchaseOrderList)
                        .addHeaders(header)
                        .body(jsonArray);

                // 发送请求并获取响应
                String response = request.execute().body();
                log.info("第"+num+"次数据同步信息："+response);
                System.out.println(response);
                try {
                    Thread.sleep(10000); // 暂停10秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Thread interrupted during sleep", e);
                }
            }
        } else {

        }
    }
}
