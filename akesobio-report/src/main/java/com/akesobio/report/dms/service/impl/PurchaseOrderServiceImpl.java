package com.akesobio.report.dms.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.dms.domain.PurchaseOrder;
import com.akesobio.report.dms.domain.SapSupplierData;
import com.akesobio.report.dms.mapper.PurchaseOrderMapper;
import com.akesobio.report.dms.mapper.SapSupplierDataMapper;
import com.akesobio.report.dms.service.IPurchaseOrderService;
import com.akesobio.report.dms.utils.TimeUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class PurchaseOrderServiceImpl implements IPurchaseOrderService {

    @Value("${client.auth.IP}")
    private String IP;
    @Value("${client.auth.GetToken}")
    private String GetToken;
    @Value("${client.auth.grant_type}")
    private String grant_type;
    @Value("${client.auth.username}")
    private String username;
    @Value("${client.auth.password}")
    private String password;

    @Value("${client.auth.purchaseOrderAdd}")
    private String purchaseOrderAdd;
    @Value("${client.auth.purchaseOrderLimit}")
    private String purchaseOrderLimit;
    @Value("${client.auth.qualificationValidityPeriod}")
    private String qualificationValidityPeriod;
    @Value("${client.auth.purchaseOrderStatus}")
    private String purchaseOrderStatus;
    @Value("${client.auth.purchaseOrderUpdate}")
    private String purchaseOrderUpdate;

    private static String token = "";

    @Autowired
    private PurchaseOrderMapper purchaseOrderMapper;

    @Autowired
    private SapSupplierDataMapper sapSupplierDataMapper;

    /**
     * 获取token
     */
    public void getToken() {
        JSONObject map = new JSONObject();
        map.put("grant_type", grant_type);
        map.put("username", username);
        map.put("password", password);
        String post;
        try {
            post = HttpUtils.sendPostJson(IP + GetToken, map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public String sendPurchaseOrderAddToCRM(JSONObject jsonObject) {
        getToken();

        Map<String, Object> map = new HashMap<>();

        /** OA fdid  */
        String fdid = jsonObject.getString("kf_fdId");
        map.put("fdid", fdid);

        /** 订单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        map.put("code", code);

        /** 经销商编码  */
        String accountCode = jsonObject.getString("fd_3ad00c8a3ee33a");
        if (accountCode != null && accountCode != "") {
            SapSupplierData sapSupplierData = sapSupplierDataMapper.selectSapSupplierData(accountCode);
            if (sapSupplierData != null && sapSupplierData.getSecondaryMerchantCode() != null
                    && !sapSupplierData.getSecondaryMerchantCode().trim().isEmpty()) {
                log.info("订单号:" + code + ",经销商编码为：" + accountCode + ",一级商编码为：" + sapSupplierData.getFirstLevelMerchantCode() + ",二级商编码为：" + sapSupplierData.getSecondaryMerchantCode());
                accountCode = sapSupplierData.getSecondaryMerchantCode();
            }
        }
        map.put("accountCode", accountCode);

        /** 总价  */
        double totalOrder = jsonObject.getDouble("fd_3ac07a41a0936e");
        map.put("totalOrder", totalOrder);

        /** 应收款合计  */
        double totalReceivable = jsonObject.getDouble("fd_3ad8af6c80c3ec");
        map.put("totalReceivable", totalReceivable);

        /** 商务代表编码  */
        String businessRepresentativeCode = jsonObject.getString("fd_3bd5eff7b30fba");
        map.put("businessRepresentativeCode", businessRepresentativeCode);

        /** 商务经理编码  */
        String businessManagerCode = jsonObject.getString("fd_3ba92db45aeab8");
        map.put("businessManagerCode", businessManagerCode);

        /** 销售组织编码  */
        String salesOrganizationCode = jsonObject.getString("fd_3ad013e53bcf88");
        map.put("salesOrganizationCode", salesOrganizationCode);

        /** 付款条件编码  */
        String paymentTermsCode = jsonObject.getString("fd_3d9dc9491ec9f6");
        map.put("paymentTermsCode", paymentTermsCode);

        /** SAP 订单状态  */
        String sapStatus = jsonObject.getString("fd_3ad2b85d4bf7d8");
        map.put("sapStatus", sapStatus);

        /**  本次折让合计 */
        double totalAllowance = 0;
        if (jsonObject.getDouble("fd_3ac92706221f44") != null) {
            totalAllowance = jsonObject.getDouble("fd_3ac92706221f44");
        }
        map.put("totalAllowance", totalAllowance);

        /** 可折让金额  */
        double allowanceAmount = 0;
        if (jsonObject.getDouble("fd_3ad4d5fc75f066") != null) {
            allowanceAmount = jsonObject.getDouble("fd_3ad4d5fc75f066");
        }
        map.put("allowanceAmount", allowanceAmount);


        /** 3%税率补差  */
        double compensation = 0;
        if (jsonObject.getDouble("tax_rate_makeup_1") != null) {
            compensation = jsonObject.getDouble("tax_rate_makeup_1");
        }
        map.put("compensation", compensation);

        /**  下单日期 */
        String orderDate = TimeUtils.parse(jsonObject.getString("fd_3abf84ee12b8e8"));
        map.put("orderDate", orderDate.replace("-", "/"));

        /** 收货地址编码  */
        String deliveryAddress = jsonObject.getString("fd_3ad2b3ea4afcec_text");
        map.put("deliveryAddress", deliveryAddress);

        /**  省份 */
        String provinceName = jsonObject.getString("province");
        switch (provinceName) {
            case "上海":
                provinceName = "上海市";
                break;
            case "北京":
                provinceName = "北京市";
                break;
            case "天津":
                provinceName = "天津市";
                break;
            case "重庆":
                provinceName = "重庆市";
                break;
        }
        map.put("provinceName", provinceName);

        /** 城市  */
        String cityName = jsonObject.getString("city");
        map.put("cityName", cityName);

        /** 区县  */
        String countyName = jsonObject.getString("district");
        map.put("countyName", countyName);

        /** 周六日可收货时间  */
        String weekendTime = TimeUtils.parse(jsonObject.getString("fd_3bc3493ce57946"));
        map.put("weekendTime", weekendTime);

        /** 发票类型  */
        Integer invoiceType = null;
        String invoiceTypeTo = jsonObject.getString("fd_3bb0fc3a8f419e");
        if (invoiceTypeTo.equals("电子发票")) {
            invoiceType = 10;
        }
        if (invoiceTypeTo.equals("纸质发票")) {
            invoiceType = 20;
        }
        map.put("invoiceType", invoiceType);

        /** 收票地址  */
        String invoiceAddress = jsonObject.getString("fd_3ad2c27addff84_text");
        map.put("invoiceAddress", invoiceAddress);

        /**  开票方式 */
        String invoiceWay = jsonObject.getString("fd_3ac07acd70f50a");
        map.put("invoiceWay", invoiceWay);

        /**  开票要求 */
        String invoiceDemand = jsonObject.getString("fd_3ac20f46db6f96");
        map.put("invoiceDemand", invoiceDemand);

        /**  订单明细  */
        JSONArray jsonArray = jsonObject.getJSONArray("fd_3ac079239994d4");
        List<Map<String, Object>> items = new ArrayList<>();
        Map<String, Object> item = new HashMap<>();
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                com.alibaba.fastjson2.JSONObject object = jsonArray.getJSONObject(i);
                item.put("number", i + 1);
                item.put("productCode", object.get("fd_3ad056ad385a8e_text"));//产品编码
                item.put("price", object.getDouble("fd_3ac079dd52666a"));//单价
                item.put("taxRate", object.getString("fd_3ac079ddd91d84").replace("%", ""));//税率
                item.put("quantity", object.getDouble("fd_3ac079dee74a7e"));//数量
                item.put("totalOrder", object.getDouble("fd_3ac079e7365382"));//总价
                item.put("allowance", object.getDouble("fd_3ad2adc2fd2ab2"));//合同折让
                item.put("compensation", object.getDouble("fd_3cf87769bd936e"));//3%税率补差
                item.put("receivable", object.getDouble("fd_3ad8af5f33843a"));//应收款
                items.add(item);
            }
        }

        map.put("detailsJson", items);
        Gson gson = new Gson();
        String detailsJson = gson.toJson(items);

        String data = JSON.toJSONString(map);

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Connection", "keep-alive");

        log.info("发送CRM采购订单新增数据为：" + map);
        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(IP + purchaseOrderAdd)
                .addHeaders(header)
                .form("fdid", fdid)
                .form("code", code)// 使用form方法创建multipart请求
                .form("accountCode", accountCode)
                .form("totalOrder", totalOrder)
                .form("totalReceivable", totalReceivable)
                .form("businessRepresentativeCode", businessRepresentativeCode)
                .form("businessManagerCode", businessManagerCode)
                .form("salesOrganizationCode", salesOrganizationCode)
                .form("paymentTermsCode", paymentTermsCode)
                .form("sapStatus", sapStatus)
                .form("totalAllowance", totalAllowance)
                .form("allowanceAmount", allowanceAmount)
                .form("compensation", compensation)
                .form("orderDate", orderDate.replace("-", "/"))
                .form("deliveryAddress", deliveryAddress)
                .form("provinceName", provinceName)
                .form("cityName", cityName)
                .form("countyName", countyName)
                .form("weekendTime", weekendTime)
                .form("invoiceType", invoiceType)
                .form("invoiceAddress", invoiceAddress)
                .form("invoiceWay", invoiceWay)
                .form("invoiceDemand", invoiceDemand)
                .form("detailsJson", detailsJson);

        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String sendPurchaseOrderUpdateToCRM(JSONObject jsonObject) {
        getToken();

        Map<String, Object> map = new HashMap<>();

        /** 订单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        map.put("code", code);

        /** 指定发货仓库  */
        String stash = Objects.toString(jsonObject.getString("warehouse"), "");
        if (!stash.isEmpty()) {
            switch (stash) {
                case "1000":
                    stash = "1000（中山康方）";
                    break;
                case "1020":
                    stash = "1020（康方天成）";
                    break;
                case "1030":
                    stash = "1030（康方赛诺）";
                    break;
                case "1050":
                    stash = "1050（康方药业）";
                    break;
                case "1060":
                    stash = "1060（康融东方）";
                    break;
                case "1070":
                    stash = "1070（康融广州）";
                    break;
                case "1080":
                    stash = "1080（康方隆跃）";
                    break;
                case "1200":
                    stash = "1200（康方添成）";
                    break;
                case "1230":
                    stash = "1230（康方汇科）";
                    break;
                case "1250":
                    stash = "1250（汇科广州）";
                    break;
                case "1610":
                    stash = "1610（广东泽晟）";
                    break;
            }
        }
        map.put("stash", stash);

        /** 应收款对账单  */
        String check = Objects.toString(jsonObject.getString("fd_3ac07a99c02d8a"), "");
        map.put("check", check);

        /** 现款客户应补税金额  */
        double taxAmount = jsonObject.getDouble("fd_3c5c4305c76b40");
        map.put("taxAmount", taxAmount);

        /** 出纳确认到款金额  */
        double payment = jsonObject.getDouble("fd_3c5c42a867013c");
        map.put("payment", payment);

        /** 回传信息  */
        String message = Objects.toString(jsonObject.getString("return_information"), "");
        map.put("message", message);

        /** 物流公司  */
        String logisticsCompany = Objects.toString(jsonObject.getString("fd_3b9ee91d72980c"), "");
        map.put("logisticsCompany", logisticsCompany);

        /** 预约发货时间  */
        String shipmentsTime = "";
        if (jsonObject.getString("fd_rqr_pkp_time") != null) {
            String inputDate = jsonObject.getString("fd_rqr_pkp_time");
            long timestamp = Long.parseLong(inputDate);
            // 转换为 Instant（UTC 时间）
            Instant instant = Instant.ofEpochMilli(timestamp);
            // 转换为目标时区（如东八区）
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern("yyyy/MM/dd HH:mm:ss")
                    .withZone(ZoneId.of("Asia/Shanghai")); // 东八区
            shipmentsTime = formatter.format(instant);
        }
        map.put("shipmentsTime", shipmentsTime);

        /** 物流单委托号  */
        String logisticsBill = Objects.toString(jsonObject.getString("fd_entrust_no"), "");
        map.put("logisticsBill", logisticsBill);

        /**  是否投保 */
        String insureName = Objects.toString(jsonObject.getString("safe_item"), "");
        if (insureName.equals("投保")) {
            insureName = "是";
        } else {
            insureName = "否";
        }
        map.put("insureName", insureName);

        /** SAP订单号  */
        String sapCode = Objects.toString(jsonObject.getString("fd_3ad062abbb3798"), "");
        map.put("sapCode", sapCode);

        /** SAP系统发票号  */
        String sapInvoice = Objects.toString(jsonObject.getString("invoice_no"), "");
        map.put("sapInvoice", sapInvoice);

        /**  交货单号 */
        String deliveryCode = Objects.toString(jsonObject.getString("delivery_no"), "");
        map.put("deliveryCode", deliveryCode);

        /** 发票快递信息  */
        String invoiceExpressInfo = Objects.toString(jsonObject.getString("fd_3ac07adb27c0c4"), "");
        map.put("invoiceExpressInfo", invoiceExpressInfo);

        /** 开票时间  */
        String invoiceDate = "";
        if (jsonObject.getString("fd_3b2aa5a5d58606") != null && jsonObject.getString("fd_3b2aa5a5d58606") != "") {
            String inputDate = jsonObject.getString("fd_3b2aa5a5d58606");
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDate date = LocalDate.parse(inputDate, inputFormatter);
            invoiceDate = date.atStartOfDay().format(outputFormatter);
        }
        map.put("invoiceDate", invoiceDate);

        /** 发票号码  */
        String invoiceNumber = Objects.toString(jsonObject.getString("proof"), "");
        map.put("invoiceNumber", invoiceNumber);

        /** 发票签收  */ //已签收：10；未签收：20
        String invoiceSign = Objects.toString(jsonObject.getString("fd_3ad9aa40081e5c"), "");
        if (invoiceSign.equals("已签收")) {
            invoiceSign = "10";
        } else {
            invoiceSign = "20";
        }
        map.put("invoiceSign", invoiceSign);


        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Connection", "keep-alive");
        // header.put("Content-Type", "application/json");
        // header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(map);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM采购订单修改数据为：" + jsonArray.toString());
        String url = IP + purchaseOrderUpdate;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .form("code", code)
                .form("stash", stash)
                .form("check", check)
                .form("taxAmount", taxAmount)
                .form("payment", payment)
                .form("message", message)
                .form("logisticsCompany", logisticsCompany)
                .form("shipmentsTime", shipmentsTime)
                .form("logisticsBill", logisticsBill)
                .form("insureName", insureName)
                .form("sapCode", sapCode)
                .form("sapInvoice", sapInvoice)
                .form("deliveryCode", deliveryCode)
                .form("invoiceExpressInfo", invoiceExpressInfo)
                .form("invoiceDate", invoiceDate)
                .form("invoiceNumber", invoiceNumber)
                .form("invoiceSign", invoiceSign);

        // 发送请求并获取响应
        String response = request.execute().body();

        return response;
    }

    @Override
    public String obtainPurchaseOrderLimitToCRM(JSONObject jsonObject) {
        getToken();
        /** 经销商编码  */
        String accountCode = jsonObject.getString("fd_3ad00c8a3ee33a");
        if (accountCode != null && accountCode != "") {
            SapSupplierData sapSupplierData = sapSupplierDataMapper.selectSapSupplierData(accountCode);
            if (sapSupplierData != null && sapSupplierData.getSecondaryMerchantCode() != null
                    && !sapSupplierData.getSecondaryMerchantCode().trim().isEmpty()) {
                log.info("经销商编码为：" + accountCode + ",一级商编码为：" + sapSupplierData.getFirstLevelMerchantCode() + ",二级商编码为：" + sapSupplierData.getSecondaryMerchantCode());
                accountCode = sapSupplierData.getSecondaryMerchantCode();
            }
        }

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Connection", "keep-alive");

        log.info("发送CRM获取信用额度客户编码为：" + accountCode);
        String url = IP + purchaseOrderLimit + "?accountCode=" + accountCode;
        System.out.println("url:" + url);
        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createGet(url)
                .addHeaders(header);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String qualificationValidityPeriod(JSONObject jsonObject) {
        getToken();
        /** 经销商编码  */
        String accountCode = jsonObject.getString("fd_3ad00c8a3ee33a");
        if (accountCode != null && accountCode != "") {
            SapSupplierData sapSupplierData = sapSupplierDataMapper.selectSapSupplierData(accountCode);
            if (sapSupplierData != null && sapSupplierData.getSecondaryMerchantCode() != null
                    && !sapSupplierData.getSecondaryMerchantCode().trim().isEmpty()) {
                log.info("s经销商编码为：" + accountCode + ",一级商编码为：" + sapSupplierData.getFirstLevelMerchantCode() + ",二级商编码为：" + sapSupplierData.getSecondaryMerchantCode());
                accountCode = sapSupplierData.getSecondaryMerchantCode();
            }
        }

        Integer type = 10;

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Connection", "keep-alive");

        log.info("发送CRM获取资质效期客户编码为：" + accountCode);
        String url = IP + qualificationValidityPeriod + "?accountCode=" + accountCode + "&type=" + type;
        System.out.println("url:" + url);
        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createGet(url)
                .addHeaders(header);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String purchaseOrderApproved(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        /** 签核状态  */ //制单：10;待商务经理审批：20;待OA后续审批：30;已审批：40; 已驳回：50;作废：60
        Integer auditStatus = 40;

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM采购订单审批通过数据为：" + objectMap.toString());
        String url = IP + purchaseOrderStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String purchaseOrderReject(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        map.put("code", code);

        /** 签核状态  */ //制单：10;待商务经理审批：20;待OA后续审批：30;已审批：40; 已驳回：50;作废：60
        Integer auditStatus = 50;
        map.put("auditStatus", auditStatus);

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM采购订单驳回数据为：" + objectMap.toString());
        String url = IP + purchaseOrderStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String purchaseOrderRejectBusinessManager(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        map.put("code", code);

        /** 签核状态  */ //制单：10;待商务经理审批：20;待OA后续审批：30;已审批：40; 已驳回：50;作废：60
        Integer auditStatus = 20;
        map.put("auditStatus", auditStatus);

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM采购订单驳回到商务经理数据为：" + objectMap.toString());
        String url = IP + purchaseOrderStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public String purchaseOrderDiscard(JSONObject jsonObject) {
        getToken();
        Map<String, Object> map = new HashMap<>();
        /** OA单号  */
        String code = jsonObject.getString("fd_3abf84e024cbfe");
        map.put("code", code);

        /** 签核状态  */ //制单：10;待商务经理审批：20;待OA后续审批：30;已审批：40; 已驳回：50;作废：60
        Integer auditStatus = 60;
        map.put("auditStatus", auditStatus);

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + token);  // 注意添加Bearer前缀
        header.put("Content-Type", "application/json");
        header.put("Connection", "keep-alive");

        List<Map<String, Object>> list = new ArrayList<>();

        // 构建请求体JSON
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("code", code);
        objectMap.put("auditStatus", auditStatus); //
        list.add(objectMap);

        String jsonArray = JSONArray.toJSONString(list);

        log.info("发送CRM采购订单废弃数据为：" + objectMap.toString());
        String url = IP + purchaseOrderStatus;
        System.out.println("url:" + url);

        //创建multipart/form-data请求
        HttpRequest request = HttpUtil.createPost(url)
                .addHeaders(header)
                .body(jsonArray);
        // 发送请求并获取响应
        String response = request.execute().body();
        return response;
    }

    @Override
    public List<PurchaseOrder> selectPurchaseOrderList(PurchaseOrder purchaseOrder) {
        return purchaseOrderMapper.selectPurchaseOrderList(purchaseOrder);
    }
}
