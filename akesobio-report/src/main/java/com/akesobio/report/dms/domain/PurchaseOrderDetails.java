package com.akesobio.report.dms.domain;


import lombok.Data;

import java.math.BigDecimal;

/**
 * OA采购订单明细
 */
@Data
public class PurchaseOrderDetails {
    /**
     * 序号
     */
    private String number;
    /**
     *  产品编码
     */
    private String  productCode;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 总价
     */
    private BigDecimal totalOrder;
    /**
     * 合同折让
     */
    private BigDecimal allowance;
    /**
     * 3%税率补差
     */
    private BigDecimal compensation;
    /**
     * 应收款
     */
    private BigDecimal receivable;
}
