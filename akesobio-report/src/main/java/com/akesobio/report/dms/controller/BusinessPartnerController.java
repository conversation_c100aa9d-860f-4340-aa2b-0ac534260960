package com.akesobio.report.dms.controller;


import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.dms.service.IBusinessPartnerService;
import com.akesobio.report.dms.utils.TokenFetcher;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务伙伴Controller
 */
@RestController
@RequestMapping("/businessPartner")
@Slf4j
public class BusinessPartnerController {

    @Autowired
    private IBusinessPartnerService businessPartnerService;

    private static String token = "";

    // UniversalInterface;http://10.10.2.44:9966/dev-api/businessPartner/approved
    //UniversalInterface;http://10.10.2.44:9966/dev-api/purchaseOrderController/purchaseOrderDiscard
    // UniversalInterface;http://10.10.2.44:9966/dev-api/purchaseOrderController/purchaseOrderAdd

    /**
     * OA业务伙伴审批状态更新到CRM
     * 审批通过（单据结束）
     */
    @RequestMapping("/approved")
    public AjaxResult approved(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA业务伙伴审批通过发送数据分析系统为：" + jsonObject);
        String response = businessPartnerService.businessPartnerApproved(jsonObject);
        log.info("CRM业务伙伴审批通过接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA业务伙伴审批状态更新到CRM
     * 驳回（单据驳回）
     */
    @RequestMapping("/reject")
    public AjaxResult reject(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA业务伙伴审批驳回发送数据分析系统为：" + jsonObject);
        String response = businessPartnerService.businessPartnerReject(jsonObject);
        log.info("CRM业务伙伴审批驳回接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA业务伙伴审批状态更新到CRM
     * 废弃（单据废弃）
     */
    @RequestMapping("/discard")
    public AjaxResult discard(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA业务伙伴审批废弃发送数据分析系统为：" + jsonObject);
        String response = businessPartnerService.businessPartnerDiscard(jsonObject);
        log.info("CRM业务伙伴审批废弃接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA业务伙伴同步倍通主数据新增,更新接口
     */
    @RequestMapping("/masterData")
    public AjaxResult masterData(@RequestBody JSONObject jsonObject) throws Exception {
        log.info(" OA业务伙伴同步倍通主数据发送数据分析系统为：" + jsonObject);
        String response = businessPartnerService.masterData(jsonObject);
        log.info("倍通主数据新增,更新接口接口返回信息为：" + response);

        if (response == null || response.isEmpty()) {
            log.error("倍通主数据接口返回为空");
            return AjaxResult.error("倍通主数据接口返回为空");
        }

        JsonObject jsonResponse;
        try {
            jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        } catch (Exception e) {
            log.error("解析倍通主数据接口响应JSON失败: " + response, e);
            return AjaxResult.error("解析倍通主数据接口响应JSON失败");
        }

        // 检查API是否返回错误码
        if (jsonResponse.has("errorCode")) {
            String errorCode = jsonResponse.get("errorCode").getAsString();
            String errorText = jsonResponse.has("errorText") && jsonResponse.get("errorText").isJsonPrimitive() ? jsonResponse.get("errorText").getAsString() : "接口返回错误，但未提供详细信息。";
            log.error("倍通主数据接口返回错误 - errorCode: {}, errorText: {}", errorCode, errorText);
            return AjaxResult.error("接口错误: " + errorText + " (代码: " + errorCode + ")");
        }

        // 处理成功响应
        if (jsonResponse.has("Success") && jsonResponse.get("Success").isJsonPrimitive() && jsonResponse.get("Success").getAsJsonPrimitive().isBoolean() && jsonResponse.get("Success").getAsBoolean()) {
            String errText = jsonResponse.has("errText") && jsonResponse.get("errText").isJsonPrimitive() ? jsonResponse.get("errText").getAsString() : "操作成功";

            Map<String, Object> mapOA = new HashMap<>();
            // 如果需要，可以从 jsonResponse.getAsJsonArray("rows") 中提取数据放入 mapOA
            // com.google.gson.JsonArray rowsArray = jsonResponse.has("rows") && jsonResponse.get("rows").isJsonArray() ? jsonResponse.getAsJsonArray("rows") : null;
            // if (rowsArray != null) {
            //     mapOA.put("details", rowsArray.toString());
            // }
            log.info("倍通主数据接口调用成功: {}", errText);
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            // Success 标志不存在或为false
            String errText = jsonResponse.has("errText") && jsonResponse.get("errText").isJsonPrimitive() ? jsonResponse.get("errText").getAsString() : "操作失败或响应格式不正确";
            log.warn("倍通主数据接口调用未成功或响应格式不符合预期. Success flag missing, false, or not boolean. Response: {}", response);
            return AjaxResult.error(errText);
        }
    }

}
