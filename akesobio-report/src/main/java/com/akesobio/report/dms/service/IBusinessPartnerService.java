package com.akesobio.report.dms.service;


import com.alibaba.fastjson2.JSONObject;

/**
 * OA业务伙伴Service接口
 */
public interface IBusinessPartnerService {

    /**
     * OA业务伙伴审批状态更新到CRM
     * 审批通过（单据结束）
     */
    String businessPartnerApproved(JSONObject jsonObject);

    /**
     * OA业务伙伴审批状态更新到CRM
     * 驳回（单据驳回）
     */
    String businessPartnerReject(JSONObject jsonObject);

    /**
     * OA业务伙伴审批状态更新到CRM
     * 废弃（单据废弃）
     */
    String businessPartnerDiscard(JSONObject jsonObject);


    /**
     * OA业务伙伴同步倍通主数据新增,更新接口
     */
    String masterData(JSONObject jsonObject) throws Exception;

}
