package com.akesobio.report.dms.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 退货订单修改实体
 *
 * @Package_Name com.akesobio.report.dms.entity
 * <AUTHOR>
 * @TIME
 * @Version
 */
@Data
@Getter
@Setter
public class ParamsAgreement {
    private String code;//订单号

    private String agreementType;//合同类型，区分采购还是退货，采购：订货、退货：退货
}
