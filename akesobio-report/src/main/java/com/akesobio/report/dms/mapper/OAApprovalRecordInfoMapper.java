package com.akesobio.report.dms.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.dms.domain.OAApprovalRecordInfo;

import java.util.List;

/**
 * OA审批记录信息Mapper接口
 */
@DataSource(value = DataSourceType.EKP)
public interface OAApprovalRecordInfoMapper {

    /**
     * 查询OA审批记录信息
     */
    public List<OAApprovalRecordInfo> selectOAApprovalRecordInfoList(OAApprovalRecordInfo info);
}
