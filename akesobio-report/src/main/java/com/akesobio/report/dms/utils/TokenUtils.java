package com.akesobio.report.dms.utils;

import com.akesobio.common.utils.http.HttpUtils;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取DMS Token
 *
 * @Package_Name com.akesobio.report.dms.utils
 * <AUTHOR> <PERSON>
 * @TIME
 * @Version
 */
@Component
public class TokenUtils implements CommandLineRunner {
    private static final Logger LOGGER = LoggerFactory.getLogger(TokenUtils.class);

    public static Map<String, String> map;

    public static String getToken(String ip, String getToken, String username, String password, String grantType) {
        JSONObject map = new JSONObject();
        map.put("grant_type", grantType);
        map.put("username", username);
        map.put("password", password);
        String token = "";
        try {
            String post = HttpUtils.sendPostJson(ip + getToken, map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            token = jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }

    /**
     * 获取文件信息 =====================================================================================================
     *
     * @param id                表单id
     * @param fileControlId     文件控件id
     * @return
     * @throws Exception
     */
    public static List<Map<String, String>> getPathList(String DATABASEURL,String EKPUSER,String EKPPASSWORD,String id, String fileControlId) throws Exception {
        String driverName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        String sql = " select sys_att_file.fd_file_path,sys_att_main.fd_file_name  from  sys_att_file LEFT JOIN sys_att_main ON sys_att_file.fd_id = sys_att_main.fd_file_id  "
                + " where sys_att_main.fd_model_id = '" + id
                + "' and sys_att_main.fd_model_name = 'com.landray.kmss.sys.modeling.main.model.ModelingAppModelMain' and sys_att_main.fd_key = '" + fileControlId + "'";
        List<Map<String, String>> pathList = new ArrayList<>();
        Connection conn = null;
        Statement stmt = null;
        try {
            Class.forName(driverName);
            conn = DriverManager.getConnection(DATABASEURL, EKPUSER, EKPPASSWORD);

            stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            // 遍历结果集
            while (rs.next()) {
                Map<String, String> map = new HashMap<>();
                String filePath = "c:/landray/kmss/resource" + rs.getString("fd_file_path");
                String fileName = rs.getString("fd_file_name");
                map.put("filePath", filePath);
                map.put("fileName", fileName);
                pathList.add(map);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (conn != null) {
                conn.close();
            }
            if (stmt != null) {
                stmt.close();
            }
        }
        return pathList;
    }

    @Override
    public void run(String... args) {
        LOGGER.info("***************开始加载【原因】数据***************");
        map = new HashMap<>();
        map.put("Z00", "价格差异：价格太高");
        map.put("Z01", "价格差异：价格太低");
        map.put("Z02", "转运中受损");
        map.put("Z03", "数量不符");
        map.put("Z04", "物料其它损坏");
        map.put("Z05", "免费样品");
        map.put("Z06", "质量问题");
        map.put("Z07", "临床发货");
        map.put("Z08", "价格调差");
        map.put("Z09", "客户间调货处理");
        LOGGER.info("***************结束加载数据***************");
    }

}
