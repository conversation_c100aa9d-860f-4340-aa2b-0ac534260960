package com.akesobio.report.dms.mapper;


import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.dms.domain.PurchaseOrder;

import java.util.List;

/**
 * OA采购订单Mapper接口
 */
@DataSource(value = DataSourceType.EKP)
public interface PurchaseOrderMapper {

    List<PurchaseOrder> selectPurchaseOrderList(PurchaseOrder purchaseOrder);
}
