package com.akesobio.report.dms.controller;


import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.report.dms.service.IAnnualDistributionContractService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 年度经销合同Controller
 *
 */
@RestController
@RequestMapping("/annualDistributionContract")
@Slf4j
public class AnnualDistributionContractController {

    @Autowired
    private IAnnualDistributionContractService annualDistributionContractService;
    /**
     * OA年度经销合同审批状态更新到CRM
     * 审批通过（单据结束）
     */
    @RequestMapping("/approved")
    public AjaxResult approved(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA年度经销合同审批通过发送数据分析系统为：" + jsonObject);
        String response = annualDistributionContractService.annualDistributionContractApproved(jsonObject);
        log.info("CRM年度经销合同审批通过接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     *
     * 驳回（单据驳回）
     */
    @RequestMapping("/reject")
    public AjaxResult reject(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA年度经销合同审批驳回发送数据分析系统为：" + jsonObject);
        String response = annualDistributionContractService.annualDistributionContractReject(jsonObject);
        log.info("CRM年度经销合同审批驳回接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

    /**
     * OA采购订单审批状态更新到CRM
     * 废弃（单据废弃）
     */
    @RequestMapping("/discard")
    public AjaxResult discard(@RequestBody JSONObject jsonObject) throws Exception {
        log.info("OA年度经销合同审批废弃发送数据分析系统为：" + jsonObject);
        String response = annualDistributionContractService.annualDistributionContractDiscard(jsonObject);
        log.info("CRM年度经销合同审批废弃接口返回信息为：" + response);
        // 解析JSON响应
        JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();
        String count = jsonResponse.getAsJsonObject("Data").get("count").getAsString();
        String success = jsonResponse.getAsJsonObject("Data").get("success").getAsString();
        String errorInfo = jsonResponse.getAsJsonObject("Data").get("errorInfo").getAsJsonObject().toString();
        //构建返回参数
        Map<String, Object> mapOA = new HashMap<>();
        if (count.equals("1") && success.equals("true")) {
            return AjaxResult.success()
                    .put("success", true).put("data", mapOA);
        } else {
            return AjaxResult.error().put("error", true).put("data", errorInfo);
        }
    }

//    /**
//     * 年度协议更新文件
//     *
//     */
//    @RequestMapping("/uploadFile2")
//    public AjaxResult uploadFile(@RequestBody JSONObject jsonObject) {
//        return annualDistributionContractService.annualDistributionContractFile(jsonObject);
//    }

    @RequestMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestPart("code") String code,@RequestPart("files") List<MultipartFile> files) throws IOException {
        return annualDistributionContractService.annualDistributionContractFile(code, files);

    }

}
