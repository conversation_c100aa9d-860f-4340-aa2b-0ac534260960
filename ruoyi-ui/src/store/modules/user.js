import {login, logout, getInfo} from '@/api/login'
import {getToken, setToken, removeToken} from '@/utils/auth'
import {getCache} from "@/api/autoacct/config";
const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    ekpId: '',
    roles: [],
    permissions: [],
    nickName:'',
    autoacctFactory:[]
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_EKP_ID: (state, ekpId) => {
      state.ekpId = ekpId
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_NICKNAME:(state,nickName)=>{
     state.nickName=nickName;
    },
    SET_AUTOACCT_FACTORY: (state,autoacctFactory) =>{
      state.autoacctFactory = autoacctFactory
    }
  },

  actions: {
    // 登录
    Login({commit}, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 第三方登录
    thirdPartyLogin({commit}, token) {
      return new Promise((resolve, reject) => {
        setToken(token)
            commit('SET_TOKEN', token)
            resolve()
      })
    },

    // 获取用户信息
    GetInfo({commit, state}) {
      return new Promise((resolve, reject) => {
        getCache().then(res =>{
          commit('SET_AUTOACCT_FACTORY',res.autoacctFactory)
        })
        getInfo().then(res => {
          const user = res.user
          const avatarLink = "https://oahr.akesobio.com/prod-api/common/avatar/" + user.ekpId;
          const avatar = (user.ekpId === "" || user.ekpId == null) ? require("@/assets/images/profile.png") : avatarLink;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_EKP_ID', user.ekpId)
          commit('SET_AVATAR', avatar)
          commit('SET_NICKNAME',user.nickName)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({commit, state}) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({commit}) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
