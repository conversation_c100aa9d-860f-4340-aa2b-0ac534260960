<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="完成日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:desktopOperations:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="desktopOperationsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" width="100"/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="100"/>
      <el-table-column label="软件运维" align="center" prop="softwareMaintenance" width="100"/>
      <el-table-column label="网络运维" align="center" prop="networkMaintenance" width="100"/>
      <el-table-column label="硬件运维" align="center" prop="hardwareMaintenance" width="100"/>
      <el-table-column label="弱电工程" align="center" prop="weakCurrentEngineering" width="100"/>
      <el-table-column label="其他运维" align="center" prop="otherMaintenance" width="100"/>
      <el-table-column label="重装系统" align="center" prop="reinstallTheSystem" width="100"/>
      <el-table-column label="应用软件安装" align="center" prop="applicationSoftwareInstallatio" width="100"/>
      <el-table-column label="软件技术支持" align="center" prop="softwareTechnicalSupport" width="100"/>
      <el-table-column label="软件更新" align="center" prop="softwareUpdates" width="100"/>
      <el-table-column label="软件故障维修" align="center" prop="softwareFaultRepair" width="100"/>
      <el-table-column label="交换机" align="center" prop="exchangeBoard" width="100"/>
      <el-table-column label="信息安全设备" align="center" prop="informationSecurityEquipment" width="100"/>
      <el-table-column label="线路故障维修" align="center" prop="lineFaultMaintenance" width="100"/>
      <el-table-column label="组网配置" align="center" prop="networkingConfiguration" width="100"/>
      <el-table-column label="WiFi维护" align="center" prop="wiFiMaintenance" width="100"/>
      <el-table-column label="外部网络" align="center" prop="externalNetwork" width="100"/>
      <el-table-column label="服务器" align="center" prop="server" width="100"/>
      <el-table-column label="显示器" align="center" prop="monitor" width="100"/>
      <el-table-column label="小型打印机" align="center" prop="miniPrinter" width="100"/>
      <el-table-column label="复印件" align="center" prop="copy" width="100"/>
      <el-table-column label="终端盒" align="center" prop="terminalBox" width="100"/>
      <el-table-column label="音响" align="center" prop="sound" width="100"/>
      <el-table-column label="笔记本" align="center" prop="laptop" width="100"/>
      <el-table-column label="摄像机" align="center" prop="camera" width="100"/>
      <el-table-column label="终端门禁" align="center" prop="terminalAccessControl" width="100"/>
      <el-table-column label="电话机" align="center" prop="telephone" width="100"/>
      <el-table-column label="刻录机" align="center" prop="burner" width="100"/>
      <el-table-column label="投影仪" align="center" prop="projector" width="100"/>
      <el-table-column label="投影幕布" align="center" prop="projectionScreen" width="100"/>
      <el-table-column label="MAXHUB" align="center" prop="maxhub" width="100"/>
      <el-table-column label="弱电规划" align="center" prop="weakCurrentPlanning" width="100"/>
      <el-table-column label="弱电施工" align="center" prop="weakCurrentConstruction" width="100"/>
      <el-table-column label="弱电会议" align="center" prop="weakCurrentConference" width="100"/>
      <el-table-column label="基础数据" align="center" prop="basicData" width="100"/>
      <el-table-column label="账号权限" align="center" prop="accountPermissions" width="100"/>
      <el-table-column label="运维协助" align="center" prop="maintenanceAssistance" width="100"/>
      <el-table-column label="问题答疑" align="center" prop="problem" width="100"/>
      <el-table-column label="机房检查" align="center" prop="machineRoomInspection" width="100"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listDesktopOperations } from "@/api/it/desktopOperations";

export default {
  name: "DesktopOperations",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 桌面运维工时汇总表格数据
      desktopOperationsList: [],
      // 工时完成时间范围
      daterangeDateCreated: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        softwareMaintenance: null,
        networkMaintenance: null,
        hardwareMaintenance: null,
        weakCurrentEngineering: null,
        otherMaintenance: null,
        reinstallTheSystem: null,
        applicationSoftwareInstallatio: null,
        softwareTechnicalSupport: null,
        softwareUpdates: null,
        softwareFaultRepair: null,
        exchangeBoard: null,
        informationSecurityEquipment: null,
        lineFaultMaintenance: null,
        networkingConfiguration: null,
        wiFiMaintenance: null,
        externalNetwork: null,
        server: null,
        monitor: null,
        miniPrinter: null,
        copy: null,
        terminalBox: null,
        sound: null,
        laptop: null,
        camera: null,
        terminalAccessControl: null,
        telephone: null,
        burner: null,
        projector: null,
        projectionScreen: null,
        maxhub: null,
        weakCurrentPlanning: null,
        weakCurrentConstruction: null,
        weakCurrentConference: null,
        basicData: null,
        accountPermissions: null,
        maintenanceAssistance: null,
        problem: null,
        machineRoomInspection: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询桌面运维工时汇总列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listDesktopOperations(this.queryParams).then(response => {
        this.desktopOperationsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        softwareMaintenance: null,
        networkMaintenance: null,
        hardwareMaintenance: null,
        weakCurrentEngineering: null,
        otherMaintenance: null,
        reinstallTheSystem: null,
        applicationSoftwareInstallatio: null,
        softwareTechnicalSupport: null,
        softwareUpdates: null,
        softwareFaultRepair: null,
        exchangeBoard: null,
        informationSecurityEquipment: null,
        lineFaultMaintenance: null,
        networkingConfiguration: null,
        wiFiMaintenance: null,
        externalNetwork: null,
        server: null,
        monitor: null,
        miniPrinter: null,
        copy: null,
        terminalBox: null,
        sound: null,
        laptop: null,
        camera: null,
        terminalAccessControl: null,
        telephone: null,
        burner: null,
        projector: null,
        projectionScreen: null,
        maxhub: null,
        weakCurrentPlanning: null,
        weakCurrentConstruction: null,
        weakCurrentConference: null,
        basicData: null,
        accountPermissions: null,
        maintenanceAssistance: null,
        problem: null,
        machineRoomInspection: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDateCreated = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/desktopOperations/export', {
        ...this.queryParams
      }, `desktopOperations_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
