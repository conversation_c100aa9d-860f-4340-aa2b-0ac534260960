<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="完成日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:systemMaintenance:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="systemMaintenanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" width="100"/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="100"/>
      <el-table-column label="OA" align="center" prop="oa" width="100"/>
      <el-table-column label="SAP" align="center" prop="sap" width="100"/>
      <el-table-column label="SHR" align="center" prop="shr" width="100"/>
      <el-table-column label="发票识别" align="center" prop="invoiceIdentification" width="100"/>
      <el-table-column label="电子印章" align="center" prop="electronicSeal" width="100"/>
      <el-table-column label="邮箱系统" align="center" prop="mailboxSystem" width="100"/>
      <el-table-column label="RF费用" align="center" prop="rfFees" width="100"/>
      <el-table-column label="考试系统" align="center" prop="examinationSystem" width="100"/>
      <el-table-column label="表单" align="center" prop="form" width="100"/>
      <el-table-column label="接口" align="center" prop="interfaceDevelopment" width="100"/>
      <el-table-column label="功能" align="center" prop="function" width="100"/>
      <el-table-column label="报表" align="center" prop="reportForms" width="100"/>
      <el-table-column label="数据库" align="center" prop="database" width="100"/>
      <el-table-column label="测试	" align="center" prop="test" width="100"/>
      <el-table-column label="问题答疑" align="center" prop="problem" width="100"/>
      <el-table-column label="异常排查处理" align="center" prop="exceptionInvestigation" width="100"/>
      <el-table-column label="流程表单异常" align="center" prop="processFormException" width="100"/>
      <el-table-column label="接口异常" align="center" prop="interfaceException" width="100"/>
      <el-table-column label="系统更新" align="center" prop="systemUpdates" width="100"/>
      <el-table-column label="权限角色" align="center" prop="permissionRole" width="100"/>
      <el-table-column label="系统配置更改" align="center" prop="systemConfigurationChanges" width="100"/>
      <el-table-column label="用户操作指引" align="center" prop="userOperationGuidelines" width="100"/>
      <el-table-column label="用户培训	" align="center" prop="userTraining" width="100"/>
      <el-table-column label="用户操作文档编写" align="center" prop="userOperationDocumentWriting" width="150"/>
      <el-table-column label="简易程序修改" align="center" prop="simplifiedProgramModification" width="100"/>
      <el-table-column label="资料整理" align="center" prop="dataCompilation" width="100"/>
      <el-table-column label="邮箱运维" align="center" prop="email" width="100"/>
      <el-table-column label="问题单跟进" align="center" prop="issueTracking" width="100"/>
      <el-table-column label="协助配合" align="center" prop="assistanceCooperation" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listSystemMaintenance } from "@/api/it/systemMaintenance";

export default {
  name: "SystemMaintenance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统运维工时汇总表格数据
      systemMaintenanceList: [],
      // 工时完成时间范围
      daterangeDateCreated: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        oa: null,
        sap: null,
        shr: null,
        invoiceIdentification: null,
        electronicSeal: null,
        mailboxSystem: null,
        rfFees: null,
        examinationSystem: null,
        form: null,
        interfaceDevelopment: null,
        function: null,
        reportForms: null,
        database: null,
        test: null,
        problem: null,
        exceptionInvestigation: null,
        processFormException: null,
        interfaceException: null,
        systemUpdates: null,
        permissionRole: null,
        systemConfigurationChanges: null,
        userOperationGuidelines: null,
        userTraining: null,
        userOperationDocumentWriting: null,
        simplifiedProgramModification: null,
        dataCompilation: null,
        email : null,
        issueTracking: null,
        assistanceCooperation: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询系统运维工时汇总列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listSystemMaintenance(this.queryParams).then(response => {
        this.systemMaintenanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        oa: null,
        sap: null,
        shr: null,
        invoiceIdentification: null,
        electronicSeal: null,
        mailboxSystem: null,
        rfFees: null,
        examinationSystem: null,
        form: null,
        interfaceDevelopment: null,
        function: null,
        reportForms: null,
        database: null,
        test: null,
        problem: null,
        exceptionInvestigation: null,
        processFormException: null,
        interfaceException: null,
        systemUpdates: null,
        permissionRole: null,
        systemConfigurationChanges: null,
        userOperationGuidelines: null,
        userTraining: null,
        userOperationDocumentWriting: null,
        simplifiedProgramModification: null,
        dataCompilation: null,
        email : null,
        issueTracking: null,
        assistanceCooperation: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDateCreated = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/systemMaintenance/export', {
        ...this.queryParams
      }, `systemMaintenance_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
