<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="完成日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:targetDepartment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="targetDepartmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" width="100"/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="100"/>
      <el-table-column label="集团财务" align="center" prop="groupFinance" width="100"/>
      <el-table-column label="营销财务" align="center" prop="marketingFinance" width="100"/>
      <el-table-column label="商业运营" align="center" prop="commercialOperations" width="100"/>
      <el-table-column label="法务部" align="center" prop="legalDepartment" width="100"/>
      <el-table-column label="设备部" align="center" prop="equipment" width="100"/>
      <el-table-column label="生产部" align="center" prop="productionDepartment" width="100"/>
      <el-table-column label="质量控制部" align="center" prop="qualityControl" width="100"/>
      <el-table-column label="质量保证部" align="center" prop="qualityAssurance" width="100"/>
      <el-table-column label="人力资源部" align="center" prop="humanResourcesDepartment" width="100"/>
      <el-table-column label="物料管理部" align="center" prop="materialManagementDepartment" width="100"/>
      <el-table-column label="生产自动化" align="center" prop="productionAutomation" width="100"/>
      <el-table-column label="药政事务部" align="center" prop="pharmaceuticalAffairsDepartmen" width="100"/>
      <el-table-column label="外联事务部" align="center" prop="externalAffairsDepartment" width="100"/>
      <el-table-column label="临床科学部" align="center" prop="clinicalScienceDepartment" width="100"/>
      <el-table-column label="临床部门" align="center" prop="clinicalDepartment" width="100"/>
      <el-table-column label="运营部门	" align="center" prop="operationDepartment" width="100"/>
      <el-table-column label="生物统计部" align="center" prop="biostatisticsDepartment" width="100"/>
      <el-table-column label="PV药物警戒部" align="center" prop="pharmacovigilanceDepartment" width="150"/>
      <el-table-column label="MST生产科学与技术" align="center" prop="productionScienceTechnology" width="150"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listTargetDepartment } from "@/api/it/targetDepartment";

export default {
  name: "TargetDepartment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 对象部门工时合计表格数据
      targetDepartmentList: [],
      // 工时完成时间范围
      daterangeDateCreated: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        groupFinance: null,
        marketingFinance: null,
        commercialOperations: null,
        legalDepartment: null,
        equipment: null,
        productionDepartment: null,
        qualityControl: null,
        qualityAssurance: null,
        humanResourcesDepartment: null,
        materialManagementDepartment: null,
        productionAutomation: null,
        pharmaceuticalAffairsDepartmen: null,
        externalAffairsDepartment: null,
        clinicalScienceDepartment: null,
        clinicalDepartment: null,
        operationDepartment: null,
        biostatisticsDepartment: null,
        pharmacovigilanceDepartment: null,
        productionScienceTechnology: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询对象部门工时合计列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listTargetDepartment(this.queryParams).then(response => {
        this.targetDepartmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        groupFinance: null,
        marketingFinance: null,
        commercialOperations: null,
        legalDepartment: null,
        equipment: null,
        productionDepartment: null,
        qualityControl: null,
        qualityAssurance: null,
        humanResourcesDepartment: null,
        materialManagementDepartment: null,
        productionAutomation: null,
        pharmaceuticalAffairsDepartmen: null,
        externalAffairsDepartment: null,
        clinicalScienceDepartment: null,
        clinicalDepartment: null,
        operationDepartment: null,
        biostatisticsDepartment: null,
        pharmacovigilanceDepartment: null,
        productionScienceTechnology: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDateCreated = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/targetDepartment/export', {
        ...this.queryParams
      }, `targetDepartment_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
