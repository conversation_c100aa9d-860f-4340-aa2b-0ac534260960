<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="完成日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:gmpWorkingHours:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gmpWorkingHoursList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name"  width="100"/>
      <el-table-column label="工号" align="center" prop="jobNumber"  width="100"/>
      <el-table-column label="GMP项目" align="center" prop="gmpProject"  width="100"/>
      <el-table-column label="账号权限开通/禁用" align="center" prop="accountPermissionActivation"  width="150"/>
      <el-table-column label="灾难恢复验证" align="center" prop="disasterRecoveryVerification"  width="100"/>
      <el-table-column label="数据可读性验证" align="center" prop="dataReadabilityVerification"  width="150"/>
      <el-table-column label="设备CSV验证" align="center" prop="deviceVerification"  width="100"/>
      <el-table-column label="部门验证方案协助" align="center" prop="departmentValidation"  width="150"/>
      <el-table-column label="3Q验证" align="center" prop="verification"  width="100"/>
      <el-table-column label="部门SOP文件起草" align="center" prop="departmentDocumentDrafting"  width="150"/>
      <el-table-column label="设备数据备份" align="center" prop="deviceDataBackup"  width="100"/>
      <el-table-column label="设备管控" align="center" prop="equipmentControl"  width="100"/>
      <el-table-column label="偏差调查" align="center" prop="deviationSurvey"  width="100"/>
      <el-table-column label="文件审阅" align="center" prop="documentReview"  width="100"/>
      <el-table-column label="数据拷贝" align="center" prop="dataCopying"  width="100"/>
      <el-table-column label="GMP会议" align="center" prop="gmpMeeting"  width="100"/>
      <el-table-column label="GMP自检" align="center" prop="gmpSelfInspection"  width="100"/>
      <el-table-column label="CAPA执行" align="center" prop="capaExecution"  width="100"/>
      <el-table-column label="部门文件起草" align="center" prop="draftingDepartmentDocuments"  width="100"/>
      <el-table-column label="文件签批流程" align="center" prop="fileApprovalProcess"  width="100"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listGmpWorkingHours} from "@/api/it/gmpWorkingHours";

export default {
  name: "GmpWorkingHours",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // GMP工作工时汇总表格数据
      gmpWorkingHoursList: [],
      // 工时完成时间范围
      daterangeDateCreated: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        gmpProject: null,
        accountPermissionActivation: null,
        disasterRecoveryVerification: null,
        dataReadabilityVerification: null,
        deviceVerification: null,
        departmentValidation: null,
        verification: null,
        departmentDocumentDrafting: null,
        deviceDataBackup: null,
        equipmentControl: null,
        deviationSurvey: null,
        documentReview: null,
        dataCopying: null,
        gmpMeeting: null,
        gmpSelfInspection: null,
        capaExecution: null,
        draftingDepartmentDocuments: null,
        fileApprovalProcess: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询GMP工作工时汇总列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listGmpWorkingHours(this.queryParams).then(response => {
        this.gmpWorkingHoursList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        gmpProject: null,
        accountPermissionActivation: null,
        disasterRecoveryVerification: null,
        dataReadabilityVerification: null,
        deviceVerification: null,
        departmentValidation: null,
        verification: null,
        departmentDocumentDrafting: null,
        deviceDataBackup: null,
        equipmentControl: null,
        deviationSurvey: null,
        documentReview: null,
        dataCopying: null,
        gmpMeeting: null,
        gmpSelfInspection: null,
        capaExecution: null,
        draftingDepartmentDocuments: null,
        fileApprovalProcess: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDateCreated = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/gmpWorkingHours/export', {
        ...this.queryParams
      }, `gmpWorkingHours_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
