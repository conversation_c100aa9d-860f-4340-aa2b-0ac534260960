<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="完成日期">
        <el-date-picker
          v-model="daterangeDateCreated"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="姓名	" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名	"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:systemDevelopment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="systemDevelopmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名	" align="center" prop="name" width="100"/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="100"/>
      <el-table-column label="OA" align="center" prop="oa" width="100"/>
      <el-table-column label="BI" align="center" prop="bi" width="100"/>
      <el-table-column label="SAP" align="center" prop="sap" width="100"/>
      <el-table-column label="SHR" align="center" prop="shr" width="100"/>
      <el-table-column label="DMS" align="center" prop="dms" width="100"/>
      <el-table-column label="TMS" align="center" prop="tms" width="100"/>
      <el-table-column label="QMS" align="center" prop="qms" width="100"/>
      <el-table-column label="LIMS" align="center" prop="lims" width="100"/>
      <el-table-column label="SRM" align="center" prop="srm" width="100"/>
      <el-table-column label="WMS" align="center" prop="wms" width="100"/>
      <el-table-column label="CRM" align="center" prop="cam" width="100"/>
      <el-table-column label="发票识别" align="center" prop="invoiceIdentification" width="100"/>
      <el-table-column label="电子印章" align="center" prop="electronicSeal" width="100"/>
      <el-table-column label="合同系统" align="center" prop="contractSystem" width="100"/>
      <el-table-column label="报表系统	" align="center" prop="reportingSystem" width="100"/>
      <el-table-column label="考试系统" align="center" prop="examinationSystem" width="100"/>
      <el-table-column label="表单" align="center" prop="form" width="100"/>
      <el-table-column label="接口" align="center" prop="interfaceDevelopment" width="100"/>
      <el-table-column label="功能" align="center" prop="function" width="100"/>
      <el-table-column label="报表" align="center" prop="reportForms" width="100"/>
      <el-table-column label="测试" align="center" prop="test" width="100"/>
      <el-table-column label="数据库" align="center" prop="database" width="100"/>
      <el-table-column label="需求分析" align="center" prop="requirementAnalysis" width="100"/>
      <el-table-column label="需求分析" align="center" prop="requirementAnalysisTwo" width="100"/>
      <el-table-column label="权限角色" align="center" prop="permissionRole" width="100"/>
      <el-table-column label="开发文档编写" align="center" prop="developmentDocumentWriting" width="100"/>
      <el-table-column label="单元需求分析" align="center" prop="unitRequirementsAnalysis" width="100"/>
      <el-table-column label="单元测试" align="center" prop="unitTesting" width="100"/>
      <el-table-column label="集成测试" align="center" prop="integrationTesting" width="100"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listSystemDevelopment} from "@/api/it/systemDevelopment";

export default {
  name: "SystemDevelopment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统开发工时汇总表格数据
      systemDevelopmentList: [],
      // 工时完成时间范围
      daterangeDateCreated: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        oa: null,
        bi: null,
        sap: null,
        shr: null,
        dms: null,
        tms: null,
        qms: null,
        lims: null,
        srm: null,
        wms: null,
        cam: null,
        invoiceIdentification: null,
        electronicSeal: null,
        contractSystem: null,
        reportingSystem: null,
        examinationSystem: null,
        form: null,
        interfaceDevelopment: null,
        function: null,
        reportForms: null,
        test: null,
        database: null,
        requirementAnalysis: null,
        requirementAnalysisTwo: null,
        permissionRole: null,
        developmentDocumentWriting: null,
        unitRequirementsAnalysis: null,
        unitTesting: null,
        integrationTesting: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询系统开发工时汇总列表 */
    getList() {
      this.loading = true;
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDateCreated && '' != this.daterangeDateCreated) {
        this.queryParams.params["beginDateCreated"] = this.daterangeDateCreated[0];
        this.queryParams.params["endDateCreated"] = this.daterangeDateCreated[1];
      }
      listSystemDevelopment(this.queryParams).then(response => {
        this.systemDevelopmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        jobNumber: null,
        oa: null,
        bi: null,
        sap: null,
        shr: null,
        dms: null,
        tms: null,
        qms: null,
        lims: null,
        srm: null,
        wms: null,
        cam: null,
        invoiceIdentification: null,
        electronicSeal: null,
        contractSystem: null,
        reportingSystem: null,
        examinationSystem: null,
        form: null,
        interfaceDevelopment: null,
        function: null,
        reportForms: null,
        test: null,
        database: null,
        requirementAnalysis: null,
        requirementAnalysisTwo: null,
        permissionRole: null,
        developmentDocumentWriting: null,
        unitRequirementsAnalysis: null,
        unitTesting: null,
        integrationTesting: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.daterangeDateCreated = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.name)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/systemDevelopment/export', {
        ...this.queryParams
      }, `systemDevelopment_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
