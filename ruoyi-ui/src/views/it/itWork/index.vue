<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="daterangePlannedDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['it:itWork:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itWorkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="表单编号" align="center" prop="oddNumbers" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="姓名" align="center" prop="name" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="工号" align="center" prop="jobNumber" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="任务组" align="center" prop="taskGroup" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="申请日期" align="center" prop="plannedDate" width="150" :show-overflow-tooltip="true">
      </el-table-column>
<!--      <el-table-column label="确认人	" align="center" prop="confirmedBy" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="服务对象" align="center" prop="serviceObject" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="服务部门" align="center" prop="targetDep" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="计划内容" align="center" prop="content" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="工作类型" align="center" prop="worktype1" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="项目类型" align="center" prop="worktype2" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="项目细则" align="center" prop="worktype3" width="150" :show-overflow-tooltip="true"/>-->
<!--      <el-table-column label="标准时间" align="center" prop="hour" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="实际用时" align="center" prop="realityHour" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="计划开始时间" align="center" prop="plannedStartTime" width="200" :show-overflow-tooltip="true">-->
<!--      </el-table-column>-->
<!--      <el-table-column label="计划完成时间" align="center" prop="plannedEndTime" width="200" :show-overflow-tooltip="true"/>-->
      <el-table-column label="实际完成时间" align="center" prop="completionTime" width="200" :show-overflow-tooltip="true">
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="taskState" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" width="150" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="备注" align="center" prop="fdRemarks" width="150" :show-overflow-tooltip="true"/>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listItWork } from "@/api/it/itWork";

export default {
  name: "ItWork",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // IT工作计划表格数据
      itWorkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 工号时间范围
      daterangePlannedDate: [],
      // 工号时间范围
      daterangePlannedStartTime: [],
      // 工号时间范围
      daterangeCompletionTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oddNumbers: null,
        name: null,
        taskGroup: null,
        plannedDate: null,
        confirmedBy: null,
        serviceObject: null,
        targetDep: null,
        content: null,
        worktype1: null,
        worktype2: null,
        worktype3: null,
        hour: null,
        realityHour: null,
        plannedStartTime: null,
        plannedEndTime: null,
        completionTime: null,
        taskState: null,
        documentStatus: null,
        fdRemarks: null,
        jobNumber: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    // 初始化默认时间
    this.defaultDate();
    this.getList();
  },
/*  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.daterangePlannedDate'(newVal) {
      if (newVal == null) {
        this.queryParams.daterangePlannedDate = ''
        this.queryParams.beginPlannedDate = ''
        this.queryParams.endPlannedDate = ''
      }
    }
  },*/
  methods: {
    // 初始化默认时间
    defaultDate() {
      // var date = new Date()
      // var year = date.getFullYear().toString()
      // var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      // // var da = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      // var da = new Date(date.getFullYear(), month, 0).getDate()
      // da < 10 ? '0' + da.toString() : da.toString()
      // var beg = year + '-' + month + '-01'
      // var end = year + '-' + month + '-' + da
      // this.daterangePlannedDate = [beg, end] //将值设置给插件绑定的数据
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = date.getTime() - 7 * 24 * 3600 * 1000 // 转化为时间戳
      let defalutEndTime = date.getTime()
      let startDateNs = new Date(defalutStartTime)
      let endDateNs = new Date(defalutEndTime)
      // 月，日 不够10补0
      defalutStartTime = startDateNs.getFullYear() + '-' + ((startDateNs.getMonth() + 1) >= 10 ? (startDateNs.getMonth() + 1) : '0' + (startDateNs.getMonth() + 1)) + '-' + (startDateNs.getDate() >= 10 ? startDateNs.getDate() : '0' + startDateNs.getDate())
      defalutEndTime = endDateNs.getFullYear() + '-' + ((endDateNs.getMonth() + 1) >= 10 ? (endDateNs.getMonth() + 1) : '0' + (endDateNs.getMonth() + 1)) + '-' + (endDateNs.getDate() >= 10 ? endDateNs.getDate() : '0' + endDateNs.getDate())
      this.daterangePlannedDate = [defalutStartTime, defalutEndTime] //将值设置给插件绑定的数据
    },
    /** 查询IT工作计划列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePlannedDate && '' != this.daterangePlannedDate) {
        this.queryParams.params["beginPlannedDate"] = this.daterangePlannedDate[0];
        this.queryParams.params["endPlannedDate"] = this.daterangePlannedDate[1];
      }
      if (null != this.daterangePlannedStartTime && '' != this.daterangePlannedStartTime) {
        this.queryParams.params["beginPlannedStartTime"] = this.daterangePlannedStartTime[0];
        this.queryParams.params["endPlannedStartTime"] = this.daterangePlannedStartTime[1];
      }
      if (null != this.daterangeCompletionTime && '' != this.daterangeCompletionTime) {
        this.queryParams.params["beginCompletionTime"] = this.daterangeCompletionTime[0];
        this.queryParams.params["endCompletionTime"] = this.daterangeCompletionTime[1];
      }
      listItWork(this.queryParams).then(response => {
        this.itWorkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        oddNumbers: null,
        name: null,
        taskGroup: null,
        plannedDate: null,
        confirmedBy: null,
        serviceObject: null,
        targetDep: null,
        content: null,
        worktype1: null,
        worktype2: null,
        worktype3: null,
        hour: null,
        realityHour: null,
        plannedStartTime: null,
        plannedEndTime: null,
        completionTime: null,
        taskState: null,
        documentStatus: null,
        fdRemarks: null,
        jobNumber: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePlannedDate = [];
      this.daterangePlannedStartTime = [];
      this.daterangeCompletionTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('it/itWork/export', {
        ...this.queryParams
      }, `itWork_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
