<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label-width="auto" label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="学校或公司" prop="schoolPerhapsCompany">
        <el-input
          v-model="queryParams.schoolPerhapsCompany"
          placeholder="请输入学校或公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="专业或职位" prop="majorOrPosition">
        <el-input
          v-model="queryParams.majorOrPosition"
          placeholder="请输入专业或职位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:educationWork:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="educationWorkList" max-height="490" border
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" align="center" prop="name" fixed :show-overflow-tooltip="true"/>
      <el-table-column label="学校或公司" align="center" prop="schoolPerhapsCompany" :show-overflow-tooltip="true"/>
      <el-table-column label="专业或职位" align="center" prop="majorOrPosition" :show-overflow-tooltip="true"/>
      <el-table-column label="开始日期" align="center" prop="beginTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import {queryEducationWork} from "@/api/hr/educationWork/educationWork";

  export default {
    name: "EducationWork",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        //
        educationWorkList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          signDate: '',
          pageNum: 1,
          pageSize: 10,
          name: null,
          schoolPerhapsCompany: null,
          majorOrPosition: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {},
        pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }]
        },
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询结婚生育礼金申请数据 */
      getList() {
        this.loading = true;
        this.queryParams.params = {};

        // this.queryParams.applyForStartDate = this.queryParams.signDate[0];
        // this.queryParams.applyForEndDate = this.queryParams.signDate[1];
        // this.queryParams.cashGiftType = this.selectedValue;
        queryEducationWork(this.queryParams).then(response => {
          console.log(response.rows)
          this.educationWorkList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          applyForStartDate: null,
          applyForEndDate: null,
          applicant: null,
          jobNumber: null,
          cashGiftType: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams.signDate = ''
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.tableName)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 导出按钮操作 */
      handleExport() {
        var date = new Date()
        var year = date.getFullYear().toString()
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
        var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
        var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
        var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
        var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
        console.log(dateTime)
        this.download('hr/educationWork/export', {
          ...this.queryParams
        }, `工作、教育经历数据_${dateTime}.xlsx`)
      },
      defaultDate() {
        var date = new Date()
        var year = date.getFullYear().toString()
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
        var da = new Date(date.getFullYear(), month, 0).getDate()
        da < 10 ? '0' + da.toString() : da.toString()
        var beg = year + '-01' + '-01'
        var end = year + '-12' + '-' + da
        this.queryParams.signDate = [beg, end] //将值设置给插件绑定的数据
      }
    },
    watch: {
      // 监听日期清理后数据为null进行处理否则会报错
      'queryParams.signDate'(newVal) {
        if (newVal == null) {
          this.queryParams.signDate = ''
        }
      }
    }


  }
</script>
