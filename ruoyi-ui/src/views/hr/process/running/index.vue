<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="申请人" prop="creator">
        <el-input
          v-model="queryParams.creator"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="当前处理人" prop="currentProcessor">
        <el-input
          v-model="queryParams.currentProcessor"
          placeholder="请输入当前处理人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模块名称" prop="year">
        <el-select v-model="queryParams.moduleKeys" clearable multiple filterable collapse-tags placeholder="请选择" style="width: 200px">
          <el-option
            v-for="item in moduleOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="processList" :max-height="tableMaxHeight" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="申请人" fixed align="center" prop="creator" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="模板名称" align="center" prop="modelName"/>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="主题" align="center" prop="docSubject" width="300" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <a target="_blank" style="color:#007bff;" v-if="scope.row.docSubject!=null"
             v-bind:href="scope.row.link">
            {{ scope.row.docSubject }}
          </a>
          <span v-else> {{ scope.row.docSubject }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请单编号" align="center" prop="processNumber" :show-overflow-tooltip='true'/>
      <el-table-column label="当前处理人" align="center" prop="currentProcessor" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {runningProcessList, runningProcessSelector} from "@/api/hr/process";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "processRunning",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外出/出差明细表格数据
      processList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 模块选项
      moduleOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moduleKeys: null,
        creator: null,
        currentProcessor: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getSelector();
    this.getList();
  },
  computed:{
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight -230;
    }
  },
  methods: {
    getSelector() {
      runningProcessSelector().then(response => {
        this.moduleOptions = response.rows
        console.log(this.moduleOptions)
      })
    },
    /** 查询外出/出差明细列表 */
    getList() {
      this.loading = true;
      runningProcessList(this.queryParams).then(response => {
        this.processList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        startTime: null,
        endTime: null,
        duration: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/business/export', {
        ...this.queryParams
      }, `外出、出差明细_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>

<style scoped>

</style>
