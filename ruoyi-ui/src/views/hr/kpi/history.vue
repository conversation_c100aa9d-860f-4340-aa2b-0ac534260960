<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 120px"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 120px"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:kpi-history:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="dataList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="index" label="序号" width="50" fixed align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" fixed="left"/>
      <el-table-column label="所属公司" align="center" prop="company" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="所属高管" align="center" prop="vpName" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" width="80" :show-overflow-tooltip='true'/>
      <template v-for="(item, index) in tableHeaders">
        <el-table-column align="center" :label="item.title" :key="item.key_str" width="70">
          <template slot-scope="scope">
            {{ scope.row.performanceRatingList[index].rating }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="上一次晋升时间" align="center" prop="lastPromotionDate" width="120" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import {getKpiHistoryList} from "@/api/hr/kpi";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "history",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效考核数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        department: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 子表头
      tableHeaders: [],
    };
  },
  created() {
    this.initTableHeaders();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 查询绩效考核列表 */
    getList() {
      this.loading = true;
      getKpiHistoryList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getKpiResetDate() {
      let currentYear = new Date().getFullYear();
      return new Date(currentYear, 10, 1); // 注意：月份是从0开始的，所以10代表11月
    },
    getKpiYear() {
      let resetDate = this.getKpiResetDate();
      let now = new Date();
      let currentYear = now.getFullYear();

      if (now < resetDate) {
        return currentYear - 1;
      } else {
        return currentYear;
      }
    },
    /** 初始化表头 */
    initTableHeaders() {
      this.tableHeaders = []
      for (let i = 2012; i <= this.getKpiYear(); i++) {
        this.tableHeaders.push({
          title: i + "年终考评",
          key_str: "key" + i
        })
      }
      this.tableHeaders.reverse()
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jobNum: null,
        username: null,
        company: null,
        department: null,
        year: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/kpi-history/export', {
        ...this.queryParams
      }, `历年绩效汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
