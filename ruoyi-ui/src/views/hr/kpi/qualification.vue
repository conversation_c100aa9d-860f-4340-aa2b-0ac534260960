<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          disabled
          type="year"
          style="width: 120px"
          placeholder="选择年"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:kpi:qual:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <u-table border v-loading="loading" :data="dataList" use-virtual :height="tableMaxHeight" >
      <u-table-column type="index" fixed align="center" label="序号" width="60"/>
      <u-table-column label="姓名" align="center" prop="name" width="80" fixed :show-overflow-tooltip="true"/>
      <u-table-column label="工号" align="center" prop="jobNumber" width="80" fixed/>
      <u-table-column label="主管VP" align="center" prop="vpName" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="所属公司" align="center" prop="orgName" width="80" :show-overflow-tooltip="true"/>
      <u-table-column label="一级部门" align="center" prop="firstDept" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="二级部门" align="center" prop="secondDept" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="三级部门" align="center" prop="thirdDept" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="四级部门" align="center" prop="fourthDept" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="职位" align="center" prop="post" width="120" :show-overflow-tooltip="true"/>
      <u-table-column label="职级" align="center" prop="jobGrade" width="100" :show-overflow-tooltip="true"/>
      <u-table-column label="入职日期" align="center" prop="entryDate" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="转正日期" align="center" prop="positiveDate" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="最高学历" align="center" prop="highestEducation" width="80"
                       :show-overflow-tooltip="true"
      />
      <u-table-column label="毕业时间" align="center" prop="graduationDate" width="110" :show-overflow-tooltip="true"/>
      <u-table-column label="工作地点" align="center" prop="workAddress" width="100"
                       :show-overflow-tooltip="true"
      />
      <u-table-column label="本年度休假月数" align="center" prop="vacationMonth" width="100"/>
      <u-table-column label="本年度在岗月数" align="center" prop="workMonth"/>
      <u-table-column label="本年度考评级别" align="center" prop="finalEvaluation"/>
      <u-table-column label="本年度素质测评" align="center">
        <u-table-column label="自评" align="center" prop="kqiSelfScore"/>
        <u-table-column label="综评" align="center" prop="kqiTotalScore"/>
      </u-table-column>
      <u-table-column label="往年考评级别" align="center">
        <template v-for="(item, index) in tableHeaders">
          <u-table-column align="center" :label="item.title" :key="item.key_str" width="70">
            <template slot-scope="scope">
              {{ scope.row.performanceHistoryRatingList[index].rating }}
<!--              {{ index }}-->
            </template>
          </u-table-column>
        </template>
      </u-table-column>

    </u-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getKpiQualificationList } from '@/api/hr/kpi'

export default {
  name: 'kpi-qualification',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: null,
        name: null,
        department: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      tableHeaders: []
    }
  },
  created() {
    this.initParam()
    this.initTableHeaders()
    this.getList()
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
      return screenHeight - 230
    }
  },
  methods: {
    initParam() {
      if (new Date().getMonth() + 1 >= 11) {
        this.queryParams.year = parseTime(new Date(), '{y}')
      } else {
        this.queryParams.year = parseTime(new Date(new Date().getFullYear() - 1, new Date().getMonth(), 1), '{y}')
      }
    },
    initTableHeaders() {
      this.tableHeaders = []
      for (let i = this.queryParams.year - 5; i < this.queryParams.year; i++) {
        this.tableHeaders.push({
          title: i + '年终考评',
          key_str: 'key' + i
        })
      }
      this.tableHeaders.reverse()
    },
    /** 查询请假明细列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      getKpiQualificationList(this.queryParams).then(response => {
        this.dataList = response
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/kpi/qual/export', {
        ...this.queryParams
      }, new Date().getFullYear() + `年终考核资格_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
