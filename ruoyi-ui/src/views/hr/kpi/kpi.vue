<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份">
        <el-date-picker
          v-model="queryParams.year"
          style="width: 120px"
          value-format="yyyy"
          type="year"
          placeholder="选择年份"/>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="processStatus">
        <el-select v-model="queryParams.processStatus" placeholder="请选择" style="width: 100px" clearable>
          <el-option
            v-for="item in processStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:kpi:list']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="kpiList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="index" label="序号" width="50" fixed align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" fixed="left"/>
      <el-table-column label="所属公司" align="center" prop="orgName" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="职级" align="center" prop="jobGrade" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="转正日期" align="center" prop="positiveDate" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="最高学历" align="center" prop="highestEducation" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="毕业时间" align="center" prop="graduationDate" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工作地点" align="center" prop="workAddress" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="所属年份" align="center" prop="year" width="80"/>
      <el-table-column label="休假月数" align="center" prop="vacationMonth" width="100"/>
      <el-table-column label="工作月数" align="center" prop="workMonth"/>
      <el-table-column label="自评" align="center" prop="selfEvaluation"/>
      <el-table-column label="部门评级" align="center" prop="leaderEvaluation"/>
      <el-table-column label="评级细分" align="center" prop="leaderSubEvaluation"/>
      <el-table-column label="主管VP评级" align="center" prop="vpEvaluation" width="130"/>
      <el-table-column label="公司评级" align="center" prop="finalEvaluation" width="130"/>
      <el-table-column label="二级部门" align="center" prop="secondDept" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="主管VP" align="center" prop="vpName" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="相关链接" align="center" prop="subject" width="230" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <a target="_blank" style="color:#007bff;"
             v-if="scope.row.processLink && scope.row.processLink.hyperlinkData && scope.row.processLink.hyperlinkData.address"
             v-bind:href="scope.row.processLink.hyperlinkData.address">
            {{ scope.row.processLink.stringValue }}
          </a>
          <span v-else> </span>
        </template>
      </el-table-column>
      <el-table-column label="流程状态" align="center" prop="processStatus" :formatter="statusFormatter" width="100"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import {getKpiList} from "@/api/hr/kpi";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "kpi",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效考核数据
      kpiList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 请假类型选项
      processStatusOptions: [
        {"label": '流程中', "value": '20'},
        {"label": '完结', "value": '30'},
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        department: null,
        year: null,
        processStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      if (new Date().getMonth() + 1 >= 11) {
        this.queryParams.year = parseTime(new Date(), '{y}')
      } else {
        this.queryParams.year = parseTime(new Date(new Date().getFullYear() - 1, new Date().getMonth(), 1), '{y}')
      }
    },
    statusFormatter(row) {
      let status;
      switch (row.processStatus) {
        case 30:
          status = '完结';
          break;
        case 20:
          status = '流程中';
          break;
        default:
          status = '';
      }
      return status;
    },
    /** 查询绩效考核列表 */
    getList() {
      this.loading = true;
      getKpiList(this.queryParams).then(response => {
        this.kpiList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jobNum: null,
        username: null,
        company: null,
        department: null,
        year: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/kpi/export', {
        ...this.queryParams
      }, `绩效考核_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
