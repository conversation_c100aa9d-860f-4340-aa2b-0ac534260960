<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份">
        <el-date-picker
          v-model="queryParams.year"
          style="width: 120px"
          value-format="yyyy"
          type="year"
          placeholder="选择年份"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:kqi:list']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="qualityList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="被考评人" align="center">
        <el-table-column label="姓名" fixed align="center" prop="name" width="70" fixed :show-overflow-tooltip='true'/>
        <el-table-column label="工号" align="center" prop="jobNumber" width="70"/>
        <el-table-column label="所属部/组" align="center" prop="department" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="职位" align="center" prop="post" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="入职日期" align="center" prop="entryDate" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="自评得分" align="center" prop="self" width="80"/>
      </el-table-column>
      <el-table-column label="考评得分" align="center">
        <el-table-column label="直属上级" align="center" prop="leader" width="80"/>
        <el-table-column label="合作部门1" align="center">
          <el-table-column label="部门" align="center" prop="cooperativeDepartment1.departmentName" width="160"
                           :show-overflow-tooltip='true'/>
          <el-table-column label="得分" align="center" prop="cooperativeDepartment1.score" width="60"/>
        </el-table-column>
        <el-table-column label="合作部门2" align="center">
          <el-table-column label="部门" align="center" prop="cooperativeDepartment2.departmentName" width="160"
                           :show-overflow-tooltip='true'/>
          <el-table-column label="得分" align="center" prop="cooperativeDepartment2.score" width="60"/>
        </el-table-column>
        <el-table-column label="合作部门3" align="center">
          <el-table-column label="部门" align="center" prop="cooperativeDepartment3.departmentName" width="160"
                           :show-overflow-tooltip='true'/>
          <el-table-column label="得分" align="center" prop="cooperativeDepartment3.score" width="60"/>
        </el-table-column>
        <el-table-column label="综合得分" align="center" prop="overall" width="80"/>
      </el-table-column>
      <el-table-column label="相关链接" align="center" prop="subject" width="230" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <a target="_blank" style="color:#007bff;" v-if="scope.row.processLink != null"
             v-bind:href="scope.row.processLink">
            {{ scope.row.subject }}
          </a>
          <span v-else> {{ scope.row.subject }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import {getKqiList} from "@/api/hr/kpi";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "quality",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效考核数据
      qualityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        department: null,
        year: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      if (new Date().getMonth() + 1 >= 11) {
        this.queryParams.year = parseTime(new Date(), '{y}')
      } else {
        this.queryParams.year = parseTime(new Date(new Date().getFullYear() - 1, new Date().getMonth(), 1), '{y}')
      }
    },
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询请假明细列表 */
    getList() {
      this.loading = true;
      getKqiList(this.queryParams).then(response => {
        this.qualityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jobNumber: null,
        username: null,
        company: null,
        department: null,
        year: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/kqi/export', {
        ...this.queryParams
      }, `素质测评_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
