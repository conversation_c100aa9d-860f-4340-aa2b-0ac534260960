<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          :editable="false"
          :clearable="false"
          style="width: 120px"
          placeholder="选择年"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:leave:balance:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="leaveBalanceList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" fixed/>
      <el-table-column label="姓名" fixed align="center" prop="name" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="公司" align="center" prop="company" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="departmentLevel1" width="140" :show-overflow-tooltip='true'/>
      <el-table-column label="细分部门" align="center" prop="department" width="140"/>
      <el-table-column label="当年年假" align="center">
        <el-table-column label="年度额度" align="center" prop="annualTotal"/>
        <el-table-column label="即时额度" align="center" prop="annualLimit"/>
        <el-table-column label="即时可休余额" align="center" prop="annualBalance"/>
      </el-table-column>
      <el-table-column label="当年全薪病事假" align="center">
        <el-table-column label="年度额度" align="center" prop="paidTotal"/>
        <el-table-column label="即时额度" align="center" prop="paidLimit"/>
        <el-table-column label="即时可休余额" align="center" prop="paidBalance"/>
      </el-table-column>
      <el-table-column label="当年调休余额" align="center" prop="compensatoryBalance"/>
      <el-table-column label="当年即时可休假时长" align="center" prop="currentTotalBalance"/>
      <el-table-column label="结余假" align="center">
        <el-table-column label="上年年假余额" align="center" prop="lastAnnualBalance"/>
        <el-table-column label="上年调休余额" align="center" prop="lastCompensatoryBalance"/>
        <el-table-column label="往年特批结余假余额" align="center" prop="lastSurplusBalance"/>
        <el-table-column label="合计" align="center" prop="surplusBalance"/>
      </el-table-column>
      <el-table-column label="即时可休假累计" align="center" prop="totalBalance"/>
      <el-table-column label="历史数据" align="center" prop="historyData"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listLeaveBalance} from "@/api/hr/attendance/leave";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "LeaveBalance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      leaveBalanceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        company: null,
        department: null,
        year: parseTime(new Date(), '{y}'),
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 查询请假明细列表 */
    getList() {
      this.loading = true;
      listLeaveBalance(this.queryParams).then(response => {
        this.leaveBalanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/leave/balance/export', {
        ...this.queryParams
      }, `假期余额_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
