<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="申请单编号" prop="nid">-->
<!--        <el-input-->
<!--          v-model="queryParams.nid"-->
<!--          placeholder="请输入申请单编号"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNum"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker
          v-model="leaveDateRange"
          style="width: 220px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="请假类型" prop="askForLeave">
        <el-select v-model="queryParams.askForLeave" placeholder="请选择"
                   style="width: 100px">
          <el-option
            v-for="item in leaveTypeOptions"
            :key="item.id"
            :label="item.value"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:leave:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="leaveList" :max-height="tableMaxHeight" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="username" width="80" fixed :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNum" width="80" fixed/>
      <el-table-column label="类型" align="center" prop="askForLeave" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="请假开始时间" align="center" prop="startAskForLeaveDate" width="140"/>
      <el-table-column label="请假结束时间" align="center" prop="endAskForLeaveDate" width="140"/>
      <!--      <el-table-column label="实际请假开始时间" align="center" prop="realStartAskForLeave" width="180"/>-->
      <!--      <el-table-column label="实际请假结束时间" align="center" prop="realEndAskForLeave" width="180"/>-->
      <el-table-column label="时长(h)" align="center" prop="askForLeaveDatenum" width="65"/>
      <el-table-column label="休假理由" align="center" prop="askForLeaveMsg" width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="公司" align="center" prop="company" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="部门" align="center" prop="department" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="岗位" align="center" prop="post" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" width="100" :show-overflow-tooltip='true'/>
<!--      <el-table-column label="日期" align="center" prop="todayDate" width="100"/>-->
      <el-table-column label="申请单编号" align="center" prop="nid" width="200" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listLeave} from "@/api/hr/attendance/leave";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "Leave",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      leaveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 请假类型选项
      leaveTypeOptions: [
        {"value": '年假', "id": 1},
        {"value": '带薪病事假', "id": 2},
        {"value": '调休', "id": 3},
        {"value": '事假', "id": 4},
        {"value": '病假', "id": 5},
        {"value": '结余假', "id": 6},
        {"value": '婚假', "id": 7},
        {"value": '产假', "id": 8},
        {"value": '陪产假', "id": 9},
        {"value": '护理假', "id": 10},
        {"value": '工伤假', "id": 11},
        {"value": '丧假', "id": 12},
        {"value": '其他假', "id": 13},
        {"value": '产检假', "id": 14}
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        startAskForLeaveDate: null,
        endAskForLeaveDate: null,
        askForLeave: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed:{
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight -230;
    }
  },
  methods: {
    initParam() {
      this.leaveDateRange.push(this.getDateStr(new Date(new Date().getFullYear(), new Date().getMonth(), 1)))
      this.leaveDateRange.push(this.getDateStr(new Date()))
    },
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询请假明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.leaveDateRange && '' !== this.leaveDateRange) {
        this.queryParams.params["rangeBegin"] = this.leaveDateRange[0];
        this.queryParams.params["rangeEnd"] = this.leaveDateRange[1];
      }
      listLeave(this.queryParams).then(response => {
        this.leaveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        uid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        startAskForLeaveDate: null,
        endAskForLeaveDate: null,
        realStartAskForLeave: null,
        realEndAskForLeave: null,
        askForLeave: null,
        askForLeaveDatenum: null,
        validity: null,
        levelState: null,
        updateClock: null,
        askForLeaveText: null,
        askForLeaveMsg: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/leave/export', {
        ...this.queryParams
      }, `请假明细_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
