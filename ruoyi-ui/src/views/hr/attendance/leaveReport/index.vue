<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="50px">
      <el-form-item label="类型" prop="askForLeave">
        <el-select v-model="queryParams.type" placeholder="请选择"
                   style="width: 100px">
          <el-option
            v-for="item in leaveTypeOptions"
            :key="item.id"
            :label="item.value"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年度">
        <el-date-picker
          style="width: 90px"
          value-format="yyyy"
          v-model="queryParams.year"
          type="year"
          placeholder="选择年">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          style="width: 120px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 120px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 120px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:leave:report:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="showType === '常规假期'"
              border
              v-loading="loading"
              :data="reportList"
              :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="basic.name" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="basic.jobNumber" width="80"/>
      <el-table-column label="公司" align="center" prop="basic.company" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="basic.firstDept" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="basic.secondDept" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="basic.thirdDept" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="basic.fourthDept" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="basic.entryDate" width="110"/>
      <el-table-column label="转正日期" align="center" prop="basic.positiveDate" width="110"/>
      <el-table-column label="离职日期" align="center" prop="basic.leaveDate" width="110"/>
      <el-table-column label="社会工龄/年" align="center" prop="basic.socialSeniority" width="60"/>
      <el-table-column label="司龄/年" align="center" prop="basic.currentSeniority" width="60"/>
      <el-table-column label="累计工龄/年" align="center" prop="basic.totalSeniority" width="60"/>
      <el-table-column label="五险一金缴纳地" align="center" prop="basic.socialSecurityLocation" width="80"/>
      <el-table-column label="年度" align="center" prop="basic.year" width="80"/>
      <el-table-column label="年度已休假天数统计" align="center">
        <el-table-column label="年假" align="center" prop="leaveTaken.annualLeave" width="60"/>
        <el-table-column label="全薪病事假" align="center" prop="leaveTaken.paidLeave" width="60"/>
        <el-table-column label="结余假" align="center" prop="leaveTaken.surplusLeave" width="60"/>
        <el-table-column label="病假" align="center" prop="leaveTaken.sickLeave" width="60"/>
        <el-table-column label="事假" align="center" prop="leaveTaken.personalLeave" width="60"/>
        <el-table-column label="婚假" align="center" prop="leaveTaken.marriageLeave" width="60"/>
        <el-table-column label="产检假" align="center" prop="leaveTaken.prenatalLeave" width="60"/>
        <el-table-column label="流产假" align="center" prop="leaveTaken.miscarriageLeave" width="60"/>
        <el-table-column label="固定产假" align="center" prop="leaveTaken.maternityLeave" width="60"/>
        <el-table-column label="产假奖励假" align="center" prop="leaveTaken.maternityBonusLeave" width="60"/>
        <el-table-column label="多胞胎产假" align="center" prop="leaveTaken.multipleBirthLeave" width="60"/>
        <el-table-column label="难产假" align="center" prop="leaveTaken.difficultDeliveryLeave" width="60"/>
        <el-table-column label="陪产假" align="center" prop="leaveTaken.paternityLeave" width="60"/>
        <el-table-column label="育儿假" align="center" prop="leaveTaken.childcareLeave" width="60"/>
        <el-table-column label="丧假" align="center" prop="leaveTaken.funeralLeave" width="60"/>
        <el-table-column label="护理假" align="center" prop="leaveTaken.nursingLeave" width="60"/>
        <el-table-column label="其他假" align="center" prop="leaveTaken.otherLeave" width="60"/>
        <el-table-column label="年度休假合计" align="center" prop="leaveTaken.totalLeave" width="60"/>
      </el-table-column>
      <el-table-column label="调休" align="center">
        <el-table-column label="本年已休假小时" align="center" prop="compensatoryLeave.takenHours" width="60"/>
        <el-table-column label="本年剩余可休假小时" align="center" prop="compensatoryLeave.balance" width="60"/>
      </el-table-column>
      <el-table-column label="年假" align="center">
        <el-table-column label="本年额度" align="center" prop="annualLeave.limit" width="60"/>
        <el-table-column label="本年已休假小时" align="center" prop="annualLeave.takenHours" width="60"/>
        <el-table-column label="即时可休假小时" align="center" prop="annualLeave.balance" width="60"/>
      </el-table-column>
      <el-table-column label="结余假" align="center">
        <el-table-column label="本年已休假小时" align="center" prop="balanceLeave.takenHours" width="60"/>
        <el-table-column label="本年剩余可休假小时" align="center" prop="balanceLeave.balance" width="60"/>
      </el-table-column>
      <el-table-column label="全薪病事假" align="center">
        <el-table-column label="本年额度" align="center" prop="paidLeave.limit" width="60"/>
        <el-table-column label="本年已休假小时" align="center" prop="paidLeave.takenHours" width="60"/>
        <el-table-column label="即时可休假小时" align="center" prop="paidLeave.balance" width="60"/>
      </el-table-column>
      <el-table-column label="历史数据" align="center" prop="basic.historyData" width="80"/>
    </el-table>
    <el-table v-if="showType === '特殊假期'"
              border
              v-loading="loading"
              :data="reportList"
              :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="basic.name" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="basic.jobNumber" width="80"/>
      <el-table-column label="公司" align="center" prop="basic.company" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="basic.firstDept" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="basic.secondDept" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="basic.thirdDept" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="basic.fourthDept" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="basic.entryDate" width="110"/>
      <el-table-column label="转正日期" align="center" prop="basic.positiveDate" width="110"/>
      <el-table-column label="离职日期" align="center" prop="basic.leaveDate" width="110"/>
      <el-table-column label="社会工龄/年" align="center" prop="basic.socialSeniority" width="60"/>
      <el-table-column label="司龄/年" align="center" prop="basic.currentSeniority" width="60"/>
      <el-table-column label="累计工龄/年" align="center" prop="basic.totalSeniority" width="60"/>
      <el-table-column label="五险一金缴纳地" align="center" prop="basic.socialSecurityLocation" width="80"/>
      <el-table-column label="年度" align="center" prop="basic.year" width="80"/>
      <el-table-column label="病假" align="center">
        <el-table-column label="法定额度" align="center" prop="sickLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="sickLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="sickLeave.includeOfficial" width="60"/>
        <el-table-column label="已休病假小时" align="center" prop="sickLeave.takenHours" width="60"/>
        <el-table-column label="剩余可休病假月数" align="center" prop="sickLeave.balance" width="60"/>
      </el-table-column>
      <el-table-column label="婚假" align="center">
        <el-table-column label="法定额度" align="center" prop="marriageLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="marriageLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="marriageLeave.includeOfficial" width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="marriageLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="marriageLeave.endDate" width="100"/>
        <el-table-column label="休假天数" align="center" prop="marriageLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="产检假" align="center">
        <el-table-column label="几孩" align="center" prop="prenatalLeave.childNumber" width="60"/>
        <el-table-column label="孕1-4周已休天数" align="center" prop="prenatalLeave.week1To4taken" width="60"/>
        <el-table-column label="孕5-8周已休天数" align="center" prop="prenatalLeave.week5To8taken" width="60"/>
        <el-table-column label="孕9-12周已休天数" align="center" prop="prenatalLeave.week9To12taken" width="60"/>
        <el-table-column label="孕13-16周已休天数" align="center" prop="prenatalLeave.week13To16taken" width="60"/>
        <el-table-column label="孕17-20周已休天数" align="center" prop="prenatalLeave.week17To20taken" width="60"/>
        <el-table-column label="孕21-24周已休天数" align="center" prop="prenatalLeave.week21To24taken" width="60"/>
        <el-table-column label="孕25-28周已休天数" align="center" prop="prenatalLeave.week25To28taken" width="60"/>
        <el-table-column label="孕29-32周已休天数" align="center" prop="prenatalLeave.week29To32taken" width="60"/>
        <el-table-column label="孕33-36周已休天数" align="center" prop="prenatalLeave.week33To36taken" width="60"/>
        <el-table-column label="孕37-40周已休天数" align="center" prop="prenatalLeave.week37To40taken" width="60"/>
      </el-table-column>
      <el-table-column label="流产假" align="center">
        <el-table-column label="孕周数" align="center" prop="miscarriageLeave.weeks" width="60"/>
        <el-table-column label="法定额度" align="center" prop="miscarriageLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="miscarriageLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="miscarriageLeave.includeOfficial" width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="miscarriageLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="miscarriageLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="miscarriageLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="产假" align="center">
        <el-table-column label="法定额度" align="center" prop="maternityLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="maternityLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="maternityLeave.includeOfficial" width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="maternityLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="maternityLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="maternityLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="奖励产假" align="center">
        <el-table-column label="法定额度" align="center" prop="maternityBonusLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="maternityBonusLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="maternityBonusLeave.includeOfficial"
                         width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="maternityBonusLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="maternityBonusLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="maternityBonusLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="多胞胎产假" align="center">
        <el-table-column label="法定额度" align="center" prop="multipleBirthLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="multipleBirthLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="multipleBirthLeave.includeOfficial"
                         width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="multipleBirthLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="multipleBirthLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="multipleBirthLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="难产假" align="center">
        <el-table-column label="法定额度" align="center" prop="difficultDeliveryLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="difficultDeliveryLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="difficultDeliveryLeave.includeOfficial"
                         width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="difficultDeliveryLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="difficultDeliveryLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="difficultDeliveryLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="陪产假" align="center">
        <el-table-column label="法定额度" align="center" prop="paternityLeave.legalQuota" width="60"/>
        <el-table-column label="是否包含公休日" align="center" prop="paternityLeave.includeHoliday" width="60"/>
        <el-table-column label="是否包含法定节假日" align="center" prop="paternityLeave.includeOfficial" width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="paternityLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="paternityLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="paternityLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="育儿假" align="center">
        <el-table-column label="截止几周岁（含）前可休" align="center" prop="childcareLeave.balanceTotal" width="60"/>
        <el-table-column label="额度（天/年）" align="center" prop="childcareLeave.balance" width="60"/>
        <el-table-column label="第1年已休" align="center" prop="childcareLeave.year1taken" width="60"/>
        <el-table-column label="第2年已休" align="center" prop="childcareLeave.year2taken" width="60"/>
        <el-table-column label="第3年已休" align="center" prop="childcareLeave.year3taken" width="60"/>
        <el-table-column label="第4年已休" align="center" prop="childcareLeave.year4taken" width="60"/>
        <el-table-column label="第5年已休" align="center" prop="childcareLeave.year5taken" width="60"/>
        <el-table-column label="第6年已休" align="center" prop="childcareLeave.year6taken" width="60"/>
      </el-table-column>
      <el-table-column label="丧假" align="center">
        <el-table-column label="亲属关系" align="center" prop="funeralLeave.kinship" width="60"/>
        <el-table-column label="额度" align="center" prop="funeralLeave.balance" width="60"/>
        <el-table-column label="请休假开始日期" align="center" prop="funeralLeave.startDate" width="94"/>
        <el-table-column label="请休假结束日期" align="center" prop="funeralLeave.endDate" width="94"/>
        <el-table-column label="休假天数" align="center" prop="funeralLeave.takenDays" width="60"/>
      </el-table-column>
      <el-table-column label="历史数据" align="center" prop="basic.historyData" width="80"/>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {leaveReportList, leaveReportSpecialList} from "@/api/hr/attendance/leave";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "Leave",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      reportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 请假类型选项
      leaveTypeOptions: [
        {"value": '常规假期', "id": 1},
        {"value": '特殊假期', "id": 2},
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        company: null,
        department: null,
        year: parseTime(new Date(), '{y}'),
        type: '常规假期',
      },
      showType: '常规假期',
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询请假明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.queryParams.type === '常规假期') {
        leaveReportList(this.queryParams).then(response => {
          this.reportList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.queryParams.type === '特殊假期') {
        leaveReportSpecialList(this.queryParams).then(response => {
          this.reportList = response.rows;
          this.total = response.total;
          this.loading = false
        });
      }
      this.showType = this.queryParams.type
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        uid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        startAskForLeaveDate: null,
        endAskForLeaveDate: null,
        realStartAskForLeave: null,
        realEndAskForLeave: null,
        askForLeave: null,
        askForLeaveDatenum: null,
        validity: null,
        levelState: null,
        updateClock: null,
        askForLeaveText: null,
        askForLeaveMsg: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.showType === '常规假期') {
        this.download('hr/attendance/leave/report/export', {
          ...this.queryParams
        }, this.queryParams.year + `年度假期汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
      } else if (this.showType === '特殊假期') {
        this.download('hr/attendance/leave/report/special/export', {
          ...this.queryParams
        }, this.queryParams.year + `年度特殊假期汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
      }
    }
  }
};
</script>
