<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNum"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:resume:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :max-height="tableMaxHeight" :data="resumeLeaveList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="username"/>
      <el-table-column label="工号" align="center" prop="jobNum"/>
      <el-table-column label="原休假数据" align="center">
        <el-table-column label="开始日期时间" align="center" prop="startAskForLeaveDate" width="180"/>
        <el-table-column label="结束日期时间" align="center" prop="endAskForLeaveDate" width="180"/>
      </el-table-column>
      <el-table-column label="实际休假数据" align="center">
        <el-table-column label="开始日期时间" align="center" prop="realStartAskForLeave" width="180"/>
        <el-table-column label="结束日期时间" align="center" prop="realEndAskForLeave" width="180"/>
      </el-table-column>
      <el-table-column label="实际休假时长" align="center" prop="reportLeaveDatenum"/>
      <el-table-column label="销假类型" align="center" prop="reportLeaveType"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {listResumeLeave} from "@/api/hr/attendance/leave";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "ResumeLeave",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销假表格数据
      resumeLeaveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nid: null,
        leaveNid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 查询销假列表 */
    getList() {
      this.loading = true;
      listResumeLeave(this.queryParams).then(response => {
        this.resumeLeaveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        leaveNid: null,
        uid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        startAskForLeaveDate: null,
        endAskForLeaveDate: null,
        realStartAskForLeave: null,
        realEndAskForLeave: null,
        reportLeaveType: null,
        reportLeaveDatenum: null,
        validity: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/resume/export', {
        ...this.queryParams
      }, `销假明细_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
