<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期" prop="attendanceDate">
        <el-date-picker
          :clearable="false"
          style="width: 250px"
          v-model="queryParams.attendanceDate"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"/>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="employeeName">
        <el-input
          v-model="queryParams.employeeName"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:extend:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :max-height="tableMaxHeight" :data="detailList"
              @selection-change="handleSelectionChange">
      <el-table-column label="工号" align="center" prop="jobNumber"/>
      <el-table-column label="姓名" fixed align="center" prop="employeeName"/>
      <el-table-column label="日期" align="center" prop="attendanceDate" width="120"/>
      <el-table-column label="星期" align="center" prop="week"/>
      <el-table-column label="班次" align="center" prop="shiftType" :show-overflow-tooltip='true'/>
      <el-table-column label="考勤状态" align="center" prop="attendanceStatus"/>
      <el-table-column label="上班时间" align="center" prop="clockIn" width="160"/>
      <el-table-column label="下班时间" align="center" prop="clockOut" width="160"/>
      <el-table-column label="打卡时长" align="center" prop="workHours"/>
      <el-table-column label="是否加班" align="center" prop="overtime"/>
      <el-table-column label="就餐费用" align="center">
        <el-table-column label="早餐" align="center" prop="breakfastCost"/>
        <el-table-column label="午餐" align="center" prop="lunchCost"/>
        <el-table-column label="晚餐" align="center" prop="dinnerCost"/>
      </el-table-column>
      <el-table-column label="实际承担餐费费用" align="center">
        <el-table-column label="早餐" align="center" prop="breakfastExpense"/>
        <el-table-column label="午餐" align="center" prop="lunchExpense"/>
        <el-table-column label="晚餐" align="center" prop="dinnerExpense"/>
      </el-table-column>
      <el-table-column label="加班餐补" align="center" prop="mealSubsidy"/>
      <el-table-column label="交通补贴" align="center" prop="transSubsidy"/>
      <el-table-column label="住宿费" align="center" prop="accommodationExpense"/>
      <el-table-column label="水电费" align="center" prop="waterAndElectricityExpense"/>
      <el-table-column label="公司" align="center" prop="company" :show-overflow-tooltip='true'/>
      <el-table-column label="部门" align="center" prop="department" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {parseTime} from "@/utils/ruoyi";
import {queryExtend} from "@/api/hr/attendance/attendance";

export default {
  name: "AttendanceDailyExtend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 加班明细表格数据
      detailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        employeeName: null,
        attendanceDate: null,
        company: null,
        department: null,
        mealType: null,
        mealLocation: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
              endDate.setDate(endDate.getDate()); // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate]);
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
              picker.$emit('pick', [startDate, now]);
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1); // 本年第一天
              picker.$emit('pick', [start, end]);
            }
          },]
      },
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      const now = new Date();
      // 本月第一天
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      this.queryParams.attendanceDate = [parseTime(startDate, '{y}-{m}-{d}'), parseTime(now, '{y}-{m}-{d}')]
    },
    /** 查询加班明细列表 */
    getList() {
      this.loading = true;
      queryExtend(this.queryParams).then(response => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeTodayDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/extend/export', {
        ...this.queryParams
      }, `考勤&食宿记录_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
