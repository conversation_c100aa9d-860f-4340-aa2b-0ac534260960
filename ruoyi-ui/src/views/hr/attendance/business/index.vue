<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          style="width: 100px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="type" label-width="70px">
        <el-select v-model="queryParams.type" clearable placeholder="请选择" style="width: 100px">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入住厂区" prop="type" label-width="70px">
        <el-select v-model="queryParams.factory" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in factoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="事由" prop="nid">
        <el-input
          v-model="queryParams.reason"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单号" prop="nid">
        <el-input
          v-model="queryParams.nid"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期" prop="attendanceDate">
        <el-date-picker
          :clearable="false"
          style="width: 250px"
          v-model="queryParams.businessDate"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:business:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="businessList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="username" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNum" width="80"/>
      <el-table-column label="日期" align="center" prop="todayDate" width="100"/>
      <el-table-column label="外出/出差开始时间" align="center" prop="startTime" width="160"/>
      <el-table-column label="外出/出差结束时间" align="center" prop="endTime" width="160"/>
      <el-table-column label="时长(h)" align="center" prop="duration" width="70"/>
      <el-table-column label="公司" align="center" prop="company" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="部门" align="center" prop="department" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="分类" align="center" prop="type" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="外出/出差地点" align="center" prop="destination" width="110"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="事由" align="center" prop="reason" :show-overflow-tooltip='true'/>
      <el-table-column label="入住厂区" align="center" prop="factory"  width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="申请单编号" align="center" prop="nid" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listBusiness} from "@/api/hr/attendance/business";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "Business",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外出/出差明细表格数据
      businessList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nid: null,
        jobNumber: null,
        username: null,
        company: null,
        department: null,
        reason: null,
        type: null,
        factory: null,
        businessDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      typeOptions: [
        {"label": '外出', "value": "外出"},
        {"label": '出差', "value": "出差"}
      ],
      factoryOptions: [
        {"label": '非厂区', "value": "NOT_FACTORY"},
        {"label": '中山翠亨', "value": "CUI_HENG"},
        {"label": '中山火炬', "value": "TORCH"},
        {"label": '广州知识城', "value": "KNOWLEDGE_CITY"}
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
              endDate.setDate(endDate.getDate()); // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate]);
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
              picker.$emit('pick', [startDate, now]);
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1); // 本年第一天
              picker.$emit('pick', [start, end]);
            }
          },]
      },
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      const now = new Date();
      // 本月第一天
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      this.queryParams.businessDate = [parseTime(startDate, '{y}-{m}-{d}'), parseTime(now, '{y}-{m}-{d}')]
    },
    /** 查询外出/出差明细列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.businessDate != null && this.queryParams.businessDate[0].indexOf(" ") === -1) {
        this.queryParams.businessDate[0] = this.queryParams.businessDate[0] + " 00:00:00"
        this.queryParams.businessDate[1] = this.queryParams.businessDate[1] + " 23:59:59"
      }
      listBusiness(this.queryParams).then(response => {
        this.businessList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        startTime: null,
        endTime: null,
        duration: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.queryParams.businessDate != null && this.queryParams.businessDate[0].indexOf(" ") === -1) {
        this.queryParams.businessDate[0] = this.queryParams.businessDate[0] + " 00:00:00"
        this.queryParams.businessDate[1] = this.queryParams.businessDate[1] + " 23:59:59"
      }
      this.download('hr/attendance/business/export', {
        ...this.queryParams
      }, `外出、出差明细_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
