<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
            v-model="queryParams.company"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
            v-model="queryParams.department"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
            v-model="queryParams.name"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNo">
        <el-input
            v-model="queryParams.jobNo"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
            style="width: 250px"
            v-model="queryParams.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
              type="primary"
              circle
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['hr:attn:breastfeeding:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :max-height="tableMaxHeight" :data="detailList"
              @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name"/>
      <el-table-column label="工号" align="center" prop="jobNo"/>
      <el-table-column label="公司" align="center" prop="company"/>
      <el-table-column label="部门" align="center" prop="department"/>
      <el-table-column label="生育孩子数量" align="center" prop="childrenBornCount"/>
      <el-table-column label="出生日期" align="center" prop="dateOfBirth"/>
      <el-table-column label="每日可休时长" align="center" prop="restHourPerDay"/>
      <el-table-column label="休假开始日期" align="center" prop="startDate"/>
      <el-table-column label="休假结束日期" align="center" prop="endDate"/>
      <el-table-column label="休息时段" align="center" prop="restPeriod"/>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { listBreastfeeding } from '@/api/hr/attendance/leave'

export default {
  name: 'Detail',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 加班明细表格数据
      detailList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNo: null,
        name: null,
        dateRange: null,
        company: null,
        department: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date()
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1) // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0) // 上月最后一天
              endDate.setDate(endDate.getDate()) // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date()
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1) // 本月第一天
              picker.$emit('pick', [startDate, now])
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date()
              const start = new Date(new Date().getFullYear(), 0, 1) // 本年第一天
              picker.$emit('pick', [start, end])
            }
          }]
      }
    }
  },
  created() {
    this.initParam()
    this.getList()
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
      return screenHeight - 230
    }
  },
  methods: {
    initParam() {
      const now = new Date()
      // 本月第一天
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      this.queryParams.dateRange = [parseTime(startDate, '{y}-{m}-{d}'), parseTime(now, '{y}-{m}-{d}')]
    },
    /** 查询加班明细列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      listBreastfeeding(this.queryParams).then(response => {
        this.detailList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        uid: null,
        jobNum: null,
        username: null,
        todayDate: null,
        company: null,
        department: null,
        startExtraWorkDate: null,
        endExtraWorkDate: null,
        realStartExtraWork: null,
        realEndExtraWork: null,
        extraWorkType: null,
        settlement: null,
        extraWorkNum: null,
        extraWorkEat: null,
        tollSubsidy: null,
        validity: null,
        mealSubsidy: null,
        weedExtraWorkNum: null,
        holidayExtraWorkNum: null,
        cardLog: null,
        realExtraWorkType: null,
        workExtraWorkNum: null,
        updateClock: null,
        extraText: null,
        eatHour: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/breastfeeding/export', {
        ...this.queryParams
      }, `哺乳假申请列表_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
