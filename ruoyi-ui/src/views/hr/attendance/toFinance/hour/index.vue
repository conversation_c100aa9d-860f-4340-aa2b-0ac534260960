<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型" prop="year" label-width="70px">
        <el-select v-model="queryParams.countType" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in countTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 150px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          style="width: 120px"
          placeholder="选择年">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-select v-model="queryParams.month" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in monthSelectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
<!--        <el-date-picker-->
<!--          v-model="queryParams.month"-->
<!--          type="month"-->
<!--          value-format="MM"-->
<!--          style="width: 120px"-->
<!--          clearable-->
<!--          placeholder="选择月份">-->
<!--        </el-date-picker>-->
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:finance:hour:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="hourList" :max-height="tableMaxHeight" :span-method="mergeMainBody">
      <el-table-column label="一级部门" align="center" prop="firstDept"/>
      <el-table-column v-if="this.currentCountType === 'COUNT_BY_SECOND_DEPARTMENT'" label="二级部门"
                       align="center" prop="secondDept"/>
      <el-table-column label="年月" align="center" prop="month"/>
      <el-table-column label="人数" align="center" prop="headcount"/>
      <el-table-column label="工时H（含加班）" align="center">
        <el-table-column label="正常出勤" align="center" prop="normalHour">
          <template slot="header">
            <span>正常出勤</span>
            <el-popover
              style="margin-left: 8px;"
              placement="left-start"
              title="备注"
              width="200"
              trigger="hover"
              content="正常出勤: 应出勤小时-请假时长-迟到-缺勤-早退(5分钟以内折算5分钟,5分钟以上则算半天缺勤)">
              <i class="el-icon-warning-outline table-column-icon" slot="reference"></i>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="加班" align="center" prop="overtime"/>
        <el-table-column label="合计" align="center" prop="total"/>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {parseTime} from "@/utils/ruoyi";
import {queryHourToFinance} from "@/api/hr/attendance/attendance";

export default {
  name: "to-finance-hour",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      hourList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 查询参数
      queryParams: {
        year: null,
        month: null,
        department: null,
        countType: "COUNT_BY_FIRST_DEPARTMENT"

      },
      currentCountType: null,
      // 合并行的行数组信息
      spanArr: [[], []],
      // 统计类型选项
      countTypeOptions: [
        {
          "label": "一级部门",
          "value": "COUNT_BY_FIRST_DEPARTMENT"
        },
        {
          "label": "二级部门",
          "value": "COUNT_BY_SECOND_DEPARTMENT"
        }],
      // 月份选项
      monthSelectOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  watch: {
    "queryParams.year": {
      immediate: true,
      deep: true,
      handler() {
        this.generateMonthOptions();
      },
    },
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      this.queryParams.year = parseTime(new Date(), '{y}')
      this.generateMonthOptions()
    },
    getCurrentDate() {
      const now = new Date();
      // JavaScript的month是从0开始计数的，所以需要+1
      return {year: now.getFullYear(), month: now.getMonth() + 1};
    },
    generateMonthOptions() {
      this.queryParams.month = null;
      const currentDate = this.getCurrentDate();
      const currentYear = currentDate.year;

      const futureYear = Number(this.queryParams.year) > currentYear;
      const pastYear = Number(this.queryParams.year) < currentYear;

      let monthOptions = [];

      // 根据年份判断生成不同的月份选项
      if (!futureYear) {
        for (let i = 1; i <= (pastYear ? 12 : currentDate.month); i++) {
          // 对月份值进行左补零操作
          const paddedMonth = i.toString().padStart(2, '0');
          monthOptions.push({
            label: `${i}月`,
            value: paddedMonth
          });
        }
      }
      this.monthSelectOptions = monthOptions;
    },
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询各部门工时列表 */
    getList() {
      this.currentCountType = this.queryParams.countType
      this.loading = true;
      this.queryParams.params = {};
      queryHourToFinance(this.queryParams).then(response => {
        this.hourList = response
        this.getSpanArr(this.hourList)
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getSpanArr(data) {
      this.spanArr = [[], []];
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr[0].push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].firstDept === data[i - 1].firstDept && data[i].firstDept) {
            this.spanArr[0][this.pos] += 1;
            this.spanArr[0].push(0);
          } else {
            this.spanArr[0].push(1);
            this.pos = i;
          }
        }
      }
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr[1].push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (this.queryParams.countType === 'COUNT_BY_SECOND_DEPARTMENT'
            && data[i].secondDept === data[i - 1].secondDept && data[i].secondDept) {
            this.spanArr[1][this.pos] += 1;
            this.spanArr[1].push(0);
          } else {
            this.spanArr[1].push(1);
            this.pos = i;
          }
        }
      }
    },
    // 合并相同的主体
    mergeMainBody({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.spanArr[columnIndex][rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row, //行
          colspan: _col //列
        };
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/attendance/to-finance/hour/export', {
        ...this.queryParams
      }, `To财务工时统计_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
