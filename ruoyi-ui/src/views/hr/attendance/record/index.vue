<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="employeeName">
        <el-input
          v-model="queryParams.employeeName"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 100px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期" prop="eventTime">
        <el-date-picker
          style="width: 220px"
          v-model="queryParams.eventTime"
          type="daterange"
          :clearable="false"
          :picker-options="pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"/>
      </el-form-item>
      <el-form-item label="地点" prop="eventLocation">
        <el-input
          v-model="queryParams.eventLocation"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="打卡方式" prop="eventType">
        <el-select v-model="queryParams.eventType" placeholder="请选择" clearable style="width: 100px">
          <el-option
            v-for="item in eventTypeOptions"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:attn:record:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :max-height="tableMaxHeight" :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column label="工号" align="center" width="180" prop="jobNumber"/>
      <el-table-column label="姓名" align="center" width="180" prop="employeeName"/>
      <el-table-column label="部门" align="center" prop="department" :show-overflow-tooltip='true'/>
      <el-table-column label="签到时间" align="center" prop="eventTime"/>
      <el-table-column label="签到地点" align="center" prop="eventLocation" :show-overflow-tooltip='true'/>
      <el-table-column label="签到方式" align="center" prop="eventType"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listRecords} from "@/api/hr/attendance/record";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "Record",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 未打卡说明明细表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventLocation: null,
        jobNumber: null,
        employeeName: null,
        company: null,
        department: null,
        eventType: null,
        eventTime: null
      },
      eventTypeOptions: [
        {"label": "手机", "value": "MOBILE"},
        {"label": "钉钉", "value": "DING_TALK"},
        {"label": "考勤机", "value": "MACHINE"}
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
              endDate.setDate(endDate.getDate()); // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate]);
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
              picker.$emit('pick', [startDate, now]);
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1); // 本年第一天
              picker.$emit('pick', [start, end]);
            }
          },]
      },
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      this.queryParams.eventTime = [parseTime(new Date(), '{y}-{m}-{d}'), parseTime(new Date(), '{y}-{m}-{d}')]
    },
    /** 查询未打卡说明明细列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.eventTime != null && this.queryParams.eventTime[0].indexOf(" ") === -1) {
        this.queryParams.eventTime[0] = this.queryParams.eventTime[0] + " 00:00:00"
        this.queryParams.eventTime[1] = this.queryParams.eventTime[1] + " 23:59:59"
      }
      listRecords(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        uid: null,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        todayDate: null,
        notCardDate: null,
        notCardType: null,
        validity: null,
        notCardInfo: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.queryParams.eventTime != null && this.queryParams.eventTime[0].indexOf(" ") === -1) {
        this.queryParams.eventTime[0] = this.queryParams.eventTime[0] + " 00:00:00"
        this.queryParams.eventTime[1] = this.queryParams.eventTime[1] + " 23:59:59"
      }
      this.download('hr/attendance/record/export', {
        ...this.queryParams
      }, `考勤签到_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.ellipsis {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis:hover {
  overflow: visible;
}
</style>
