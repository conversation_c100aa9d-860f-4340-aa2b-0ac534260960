<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="主体" prop="mainBody">
        <el-select v-model="queryParams.mainBody" style="width: 120px">
          <el-option
            v-for="item in mainBodyOption"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-select placeholder="请选择年份" v-model="queryYear" style="width: 120px">
          <el-option
            v-for="item in years"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:pm:report:annual:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="annualReportList" ref="table"
              :span-method="mergeMainBody" show-summary :summary-method="getSummaries"
              @selection-change="handleSelectionChange">
      <el-table-column label="一级部门" align="center" prop="department" v-if="realQueryParams.mainBody === 1 && columns[1].visible"
                       width="120" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="公司" align="center" prop="company" v-if="realQueryParams.mainBody === 1 && columns[0].visible"
                       width="120" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="公司" align="center" prop="company" v-if="realQueryParams.mainBody === 0 && columns[0].visible"
                       width="120" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="一级部门" align="center" prop="department" v-if="realQueryParams.mainBody === 0 && columns[1].visible"
                       width="120" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="上年末总人数" align="center" prop="lastYear" v-if="columns[2].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="1月" align="center" v-if="columns[3].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth1" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase1" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease1" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime1" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="2月" align="center" v-if="columns[4].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth2"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase2"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease2"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime2" key="currentlyEmployedCount2"
                         width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="3月" align="center" v-if="columns[5].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth3" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase3" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease3" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime3" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="4月" align="center" v-if="columns[6].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth4" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase4" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease4" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime4" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="5月" align="center" v-if="columns[7].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth5" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase5" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease5" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime5" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="6月" align="center" v-if="columns[8].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth6" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase6" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease6" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime6" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="7月" align="center" v-if="columns[9].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth7" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase7" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease7" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime7" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="8月" align="center" v-if="columns[10].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth8" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase8" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease8" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime8" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="9月" align="center" v-if="columns[11].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth9" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase9" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease9" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime9" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="10月" align="center" v-if="columns[12].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth10" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase10" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease10" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime10" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="11月" align="center" v-if="columns[13].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth11" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase11" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease11" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime11" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="12月" align="center" v-if="columns[14].visible">
        <el-table-column label="上月末人数" align="center" prop="lastMonth12" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="新增" align="center" prop="increase12" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="减少" align="center" prop="decrease12" width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="目前在职" align="center" prop="realtime12" width="100" :show-overflow-tooltip='true'/>
      </el-table-column>
      <el-table-column label="本年内累计增加" align="center" prop="increase" v-if="columns[15].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="本年内累计减少" align="center" prop="decrease" v-if="columns[16].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="本年度即时在职" align="center" prop="realtime" v-if="columns[17].visible" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="本年度编制" align="center" prop="headcount" v-if="columns[18].visible" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="本年度剩余编制" align="center" prop="remainHeadcount" v-if="columns[19].visible" width="120" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {queryAnnualReport} from "@/api/hr/personnel/report";
import {parseTime} from "@/utils/ruoyi"

export default {
  name: "AnnualReport",
  data() {
    return {
      queryYear: new Date().getFullYear(),
      years: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 实际显示的报表数据
      annualReportList: [],
      // 真实返回的报表数据
      responseReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      tableHeaders: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        department: null,
        company: null,
        mainBody: 0,
        year: new Date().getFullYear()
      },
      // 页面结果当前查询参数(处理结果用)
      realQueryParams: {
        department: null,
        company: null,
        mainBody: 0,
        year: new Date().getFullYear()
      },
      mainBodyOption: [
        {"label": '公司', "value": 0},
        {"label": '部门', "value": 1},
      ],
      // 合并行的行数组信息
      spanArr: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 20, label: `公司`, visible: true},
        {key: 21, label: `一级部门`, visible: true},
        {key: 22, label: `上年末总人数`, visible: true},
        {key: 1, label: `1月`, visible: true},
        {key: 2, label: `2月`, visible: true},
        {key: 3, label: `3月`, visible: true},
        {key: 4, label: `4月`, visible: true},
        {key: 5, label: `5月`, visible: true},
        {key: 6, label: `6月`, visible: true},
        {key: 7, label: `7月`, visible: true},
        {key: 8, label: `8月`, visible: true},
        {key: 9, label: `9月`, visible: true},
        {key: 10, label: `10月`, visible: true},
        {key: 11, label: `11月`, visible: true},
        {key: 12, label: `12月`, visible: true},
        {key: 23, label: `本年内累计增加`, visible: true},
        {key: 24, label: `本年内累计减少`, visible: true},
        {key: 25, label: `本年度即时在职`, visible: true},
        {key: 26, label: `本年度编制`, visible: true},
        {key: 27, label: `本年度剩余编制`, visible: true},
      ],
      temp: 1,
    };
  },
  created() {
    this.getYear();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 170;
    }
  },
  watch: {
    "columns": {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        this.handlerSubBody();
      },
    },
    "responseReportList": {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        this.handlerSubBody();
      },
    }
  },
  methods: {
    /** 查询HR年报 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.year = this.queryYear;
      queryAnnualReport(this.queryParams).then(response => {
        this.responseReportList = response;
        // 过滤校验结果
        this.responseReportList = this.responseReportList.filter(item => item.company !== "总数" && item.department !== "总数")
        this.getSpanArr(this.responseReportList)
        // this.total = response.total;
        this.loading = false;
      });
    },
    // 处理隐藏公司或者部门显示时只显示总数
    handlerSubBody() {
      let companyVisible = this.columns[0].visible
      let departmentVisible = this.columns[1].visible
      if (this.realQueryParams.mainBody === 0 ) {
        if (departmentVisible || (!companyVisible && !departmentVisible)) {
          this.annualReportList = this.responseReportList
          this.getSpanArr(this.annualReportList)
        } else {
          this.annualReportList = this.responseReportList.filter(item => item.department === '汇总')
          this.getSpanArr(this.annualReportList)
        }
      } else {
        if (companyVisible || (!companyVisible && !departmentVisible)) {
          this.annualReportList = this.responseReportList
          this.getSpanArr(this.annualReportList)
        } else {
          this.annualReportList = this.responseReportList.filter(item => item.company === '汇总')
          this.getSpanArr(this.annualReportList)
        }
      }
    },
    // 合并相同的主体
    mergeMainBody({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row, //行
          colspan: _col //列
        };
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (this.realQueryParams.mainBody === 0) {
            if (data[i].company === data[i - 1].company && data[i].company) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          } else {
            if (data[i].department === data[i - 1].department && data[i].department) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          }
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.queryParams.company === null && this.queryParams.department !== null) {
        this.realQueryParams.mainBody = 1
        this.realQueryParams.company = null
        this.realQueryParams.department = this.queryParams.department
      } else if (this.queryParams.company !== null && this.queryParams.department === null) {
        this.realQueryParams.mainBody = 0
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = null
      } else {
        this.realQueryParams.mainBody = this.queryParams.mainBody
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = this.queryParams.department
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      let date = new Date()
      let year = date.getFullYear().toString()
      let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      let day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      let h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      let m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      let s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      let dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/personnel/report/annual/export', {
        ...this.queryParams
      }, `HR年报_${dateTime}.xlsx`)
    },
    // 合计
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总数';
          return;
        }
        let values;
        if (this.realQueryParams.mainBody === 0) {
          values = data.filter(item => item.department === '汇总').map(item => Number(item[column.property]));
        } else {
          values = data.filter(item => item.company === '汇总').map(item => Number(item[column.property]));
        }
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    getYear() {
      let startYear = 2012;
      let year = new Date().getFullYear();
      for (let i = year; i >= startYear; i--) {
        this.years.push(i);
      }

    }

  }
};
</script>
