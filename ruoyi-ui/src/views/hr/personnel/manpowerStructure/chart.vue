<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="主体" prop="mainBody">
        <el-select v-model="queryParams.mainBody" style="width: 120px">
          <el-option
            v-for="item in mainBodyOption"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row>
      <div id="echart" ref="echart">echart</div>
    </el-row>
  </div>
</template>

<script>
import {queryManpowerStructure} from "@/api/hr/personnel/manpowerStructure";

const {body} = document
const WIDTH = 992
export default {
  name: "ManpowerStructure",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 实际显示的结构数据
      responseStructureList: [],
      // 实际显示的结构数据
      structureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        department: null,
        company: null,
        mainBody: 0
      },
      // 页面结果当前查询参数(处理结果用)
      realQueryParams: {
        department: null,
        company: null,
        mainBody: 0
      },
      mainBodyOption: [
        {"label": '公司', "value": 0},
        {"label": '部门', "value": 1},
      ],
      // 表单参数
      form: {},
      option: {},
      maxEffectiveSecondaryLength: 0,
    };
  },
  created() {
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      queryManpowerStructure(this.queryParams).then(response => {
        this.responseStructureList = response;
        this.responseStructureList = this.responseStructureList.filter(item => item.company !== "总数" && item.department !== "总数")
        this.getChart()
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    isBlank(value) {
      return value === null || value === undefined || value === '';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.isBlank(this.queryParams.company) && this.queryParams.department) {
        this.realQueryParams.mainBody = 1
        this.realQueryParams.company = null
        this.realQueryParams.department = this.queryParams.department
      } else if (this.queryParams.company && this.isBlank(this.queryParams.department)) {
        this.realQueryParams.mainBody = 0
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = null
      } else {
        this.realQueryParams.mainBody = this.queryParams.mainBody
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = this.queryParams.department
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    $_isMobile() {
      const rect = body.getBoundingClientRect()
      return rect.width - 1 < WIDTH
    },
    // 获取图表
    getChart() {
      this.updateChartOption()
      //基于准备好的dom，初始化echarts实例
      let dataChart = this.$echarts.init(this.$refs.echart)
      dataChart.clear()
      dataChart.setOption(this.option)

      // 根据部门/公司数量动态设置图表的高度
      let tempHeight;
      if (this.realQueryParams.mainBody === 0) {
        const compensate = this.isBlank(this.queryParams.company) ? 0 : 230;
        tempHeight = this.option.yAxis.length * this.maxEffectiveSecondaryLength * 20 + 100 + compensate;
      } else {
        const compensate = this.isBlank(this.queryParams.department) ? 0 : 100;
        tempHeight = this.option.yAxis.length * this.maxEffectiveSecondaryLength * 8 + 100 + compensate;
      }
      dataChart.getDom().style.height = tempHeight + "px";
      dataChart.resize();
    },
    // 初始化图表参数
    updateChartOption() {
      const isMobile = this.$_isMobile();
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(235, 235, 235, 0.05)',
              width: '1'
            }
          }
        },
        grid: {
          y: isMobile ? this.realQueryParams.mainBody === 0 ? 270 : 150 : 70,
        },
        formatter: function (params) {
          if (typeof params === 'object') {
            let result = '<div>';
            result += '<b>' + params[0].axisValue + '</b><br/>';
            const dataColumn = params.filter(item => item.seriesName !== '汇总');
            let total = 0;
            dataColumn.forEach(function (item) {
              if (item.value) {
                total += item.value;
              }
            });
            params.forEach(function (param) {
              if (param.value > 0) {
                const percent = (param.value / total * 100).toFixed(2);
                if (param.seriesName !== '汇总') {
                  result += param.marker + param.seriesName + ': ' + param.value + '&nbsp;&nbsp;(' + percent + '%)<br/>';
                } else {
                  result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                }
              }
            });
            result += '<br/>汇总: ' + total;
            return result;
          } else {
            return params
          }
        },
        legend: {
          selected: {
            '汇总': false,
          }
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: [],
        series: [],

      };
      const resultList = this.responseStructureList.filter(item => item.company !== '总数' && item.department !== '总数');
      // 公司为主体
      if (this.realQueryParams.mainBody === 0) {
        // Y 轴，公司
        const companyList = this.responseStructureList.filter(item => item.company !== '总数' && item.department === '汇总' && item.realtime !== 0)
          .sort((a, b) => a.realtime - b.realtime)
          .map(item => item.company);

        // 初始化一个二维数组，大小为 companyList.length x companyList.length
        const yAxisData = companyList.map(() => ({
          type: 'category',
          position: 'left',
          // 初始化每一行的数据
          data: companyList.map((_) => _)
        }));

        // 更新对角线元素
        for (let i = 0; i < companyList.length; i++) {
          yAxisData[i].data[i] = companyList[i];
        }
        this.option.yAxis = yAxisData

        // 所有部门，根据总人数排序
        const departmentList = resultList.reduce((acc, curr) => {
          const existingItem = acc.find(item => item.department === curr.department);
          if (existingItem) {
            existingItem.realtime += curr.realtime;
          } else {
            acc.push({...curr});
          }
          const department = curr.department;
          if (!acc.some(item => item.department === department)) {
            acc.push(department);
          }
          return acc;
        }, [])
          .sort((a, b) => b.realtime - a.realtime)
          .map(item => item.department);
        for (let i = 0; i < companyList.length; i++) {
          const company = companyList[i];
          let effectiveSecondaryLength = 0;
          for (let j = 0; j < departmentList.length; j++) {
            const dataList = [];
            companyList.forEach(() => dataList.push(null));
            const department = departmentList[j];
            let foundItem = resultList.find(item => item.company === company && item.department === department);
            if (foundItem) {
              effectiveSecondaryLength += 1;
              dataList[i] = foundItem.realtime;
              this.option.series.push({
                name: department,
                type: 'bar',
                label: {
                  show: true,
                  position: 'right',
                  formatter: function (params) {
                    if (params.value > 0) {
                      return params.value;
                    } else {
                      return '';
                    }
                  },
                },
                yAxisIndex: i,
                data: dataList
              })
            }
          }
          if (effectiveSecondaryLength > this.maxEffectiveSecondaryLength) {
            this.maxEffectiveSecondaryLength = effectiveSecondaryLength;
          }
        }
      } else {
        // Y 轴，部门
        const departmentList = this.responseStructureList.filter(item => item.department !== '总数' && item.company === '汇总' && item.realtime !== 0)
          .sort((a, b) => a.realtime - b.realtime)
          .map(item => item.department);

        // 初始化一个二维数组
        const yAxisData = departmentList.map(() => ({
          type: 'category',
          position: 'left',
          // 初始化每一行的数据
          data: departmentList.map((_) => _)
        }));

        // 更新对角线元素
        for (let i = 0; i < departmentList.length; i++) {
          yAxisData[i].data[i] = departmentList[i];
        }
        this.option.yAxis = yAxisData

        // 所有公司，根据总人数排序
        const companyList = resultList.reduce((acc, curr) => {
          const existingItem = acc.find(item => item.company === curr.company);
          if (existingItem) {
            existingItem.realtime += curr.realtime;
          } else {
            acc.push({...curr});
          }
          const company = curr.company;
          if (!acc.some(item => item.company === company)) {
            acc.push(company);
          }
          return acc;
        }, [])
          .sort((a, b) => b.realtime - a.realtime)
          .map(item => item.company);
        for (let i = 0; i < departmentList.length; i++) {
          const department = departmentList[i];
          let effectiveSecondaryLength = 0;
          for (let j = 0; j < companyList.length; j++) {
            const dataList = [];
            departmentList.forEach(() => dataList.push(null));
            const company = companyList[j];
            let foundItem = resultList.find(item => item.company === company && item.department === department);
            if (foundItem && foundItem.realtime > 0) {
              effectiveSecondaryLength += 1;
              dataList[i] = foundItem.realtime;
              this.option.series.push({
                name: company,
                type: 'bar',
                label: {
                  show: true,
                  position: 'right'
                },
                yAxisIndex: i,
                data: dataList
              })
            }
          }
          if (effectiveSecondaryLength > this.maxEffectiveSecondaryLength) {
            this.maxEffectiveSecondaryLength = effectiveSecondaryLength;
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
#echart {
  height: 700px;
  margin: 50px;
}
</style>
