<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份">
        <el-date-picker
          v-model="queryParams.year"
          style="width: 120px"
          value-format="yyyy"
          type="year"
          placeholder="选择年份"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="changeType">
        <el-select v-model="queryParams.changeType" placeholder="请选择" style="width: 100px" clearable>
          <el-option
            v-for="item in changeTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['hr:pm:position-change:export']"
        >导出明细
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExportSummary"
                   v-hasPermi="['hr:pm:position-change:export']"
        >导出汇总
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="dataList" :max-height="tableMaxHeight"
              @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" label="序号" width="50" fixed align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" width="80"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" fixed="left"/>
      <el-table-column label="部门" align="center" prop="deptName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="状态" align="center" prop="employeeStatus" width="60" :show-overflow-tooltip="true"/>
      <el-table-column label="学历" align="center" prop="highestEducation" width="60" :show-overflow-tooltip="true"/>
      <el-table-column label="入职日期" align="center" prop="entryDate" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="毕业日期" align="center" prop="graduationDate" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="变更日期" align="center" prop="changeDate" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="变更类型" align="center" prop="changeType" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="职位" align="center">
        <el-table-column label="变更前" align="center" prop="postBeforeChange" width="120"
                         :show-overflow-tooltip="true"
        />
        <el-table-column label="变更后" align="center" prop="postAfterChange" width="120"
                         :show-overflow-tooltip="true"
        />
      </el-table-column>
      <el-table-column label="职级" align="center">
        <el-table-column label="变更前" align="center" prop="positionGradeBeforeChange" width="120"
                         :show-overflow-tooltip="true"
        />
        <el-table-column label="变更后" align="center" prop="positionGradeAfterChange" width="120"
                         :show-overflow-tooltip="true"
        />
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import { parseTime } from '@/utils/ruoyi'
import { getList } from '@/api/hr/personnel/position/change'

export default {
  name: 'positionChange',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 类型选项
      changeTypeOptions: [
        { 'label': '晋职', 'value': 'PROMOTE' },
        { 'label': '降职', 'value': 'DEMOTION' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        department: null,
        year: null,
        changeType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    }
  },
  created() {
    this.initParam()
    this.getList()
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
      return screenHeight - 230
    }
  },
  methods: {
    initParam() {
      this.queryParams.year = parseTime(new Date(), '{y}')
    },
    /** 查询绩效考核列表 */
    getList() {
      this.loading = true
      getList(this.queryParams).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        jobNum: null,
        username: null,
        company: null,
        department: null,
        year: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/hr/personnel/position/change/export', {
        ...this.queryParams
      }, `晋降职记录_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    },

    handleExportSummary() {
      this.download('/hr/personnel/position/change/summary/export', {}, `晋降职记录汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
