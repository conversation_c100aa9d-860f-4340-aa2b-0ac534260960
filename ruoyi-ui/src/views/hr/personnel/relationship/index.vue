<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:pm:relationship:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <u-table border v-loading="loading" :data="dataList" use-virtual :height="tableMaxHeight">
      <u-table-column type="index" fixed align="center" width="100" label="序号"/>
      <u-table-column label="姓名" align="center" prop="name" fixed :show-overflow-tooltip="true"/>
      <u-table-column label="工号" align="center" prop="jobNumber"/>
      <u-table-column label="一级部门" align="center" prop="firstDept" :show-overflow-tooltip="true"/>
      <u-table-column label="紧密关系人信息" align="center">
        <u-table-column label="姓名" align="center" prop="relationshipPersonName" fixed :show-overflow-tooltip="true"/>
        <u-table-column label="工号" align="center" prop="relationshipPersonJobNumber"/>
        <u-table-column label="一级部门" align="center" prop="relationshipPersonDept" :show-overflow-tooltip="true"/>
        <u-table-column label="关系" align="center" prop="relationship" :show-overflow-tooltip="true"/>
        <u-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"/>
      </u-table-column>
    </u-table>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getRelationshipList } from '@/api/hr/personnel/relationship'

export default {
  name: 'relationship',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: null,
        jobNumber: null,
        company: null,
        department: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    }
  },
  created() {
    this.getList()
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
      return screenHeight - 230
    }
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      getRelationshipList(this.queryParams).then(response => {
        this.dataList = response
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/relationship/export', {
        ...this.queryParams
      }, `紧密关系信息_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
}
</script>
