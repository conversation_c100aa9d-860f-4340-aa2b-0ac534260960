<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="姓名或工号" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名或工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职位" prop="post">
        <el-input
          v-model="queryParams.post"
          placeholder="请输入职位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单号" prop="singleNumber">
        <el-input
          v-model="queryParams.singleNumber"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入职日期">
        <el-date-picker
          ref="entryDatePicker"
          v-model="dateRangeEntryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(2)"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="申请日期" label-width="90px">
        <el-date-picker
          ref="contractDatePicker"
          v-model="dateApplicationDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(4)"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:pm:train:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="trainList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" key="name"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="loginName" key="loginName"/>
      <el-table-column label="公司" align="center" prop="company" key="company"
                       width="150" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" key="firstDept"
                       width="150" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" key="entryDate"
                       width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训开始时间" align="center" prop="trainBeginDate" key="trainBeginDate"
                       width="130">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.trainBeginDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训结束时间" align="center" prop="trainEndDate" key="trainEndDate"
                       width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.trainEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训形式" align="center" prop="trainFormat" key="trainFormat"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="培训种类" align="center" prop="trainType" key="trainType"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="是否有证书" align="center" prop="certificate" key="certificate"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="培训总费用" align="center" prop="trainAssembleMoney" key="trainAssembleMoney" width="100"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="费用承担" align="center">
      <el-table-column label="公司比例" align="center" prop="companyProportion" key="companyProportion" width="100"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ scope.row.companyProportion}}%</span>
        </template>
      </el-table-column>
      <el-table-column label="公司费用" align="center" prop="companyMoney" key="companyMoney" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="个人比例" align="center" prop="personProportion" key="personProportion" width="100"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ scope.row.personProportion}}%</span>
        </template>
      </el-table-column>
      <el-table-column label="个人费用" align="center" prop="personMoney" key="personMoney" width="100"
                       :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="培训协议状态" align="center" prop="trainAgreement" key="trainAgreement" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="培训协议" align="center">
      <el-table-column label="生效日期" align="center" prop="effectiveDate" key="effectiveDate" width="100"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="豁免/失效日期" align="center" prop="expiringDate" key="expiringDate" width="100"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expiringDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="电子附件" align="center" prop="annex" key="annex" width="100"-->
<!--                       v-if="columns[19].visible" :show-overflow-tooltip='true'/>-->
      <el-table-column label="违约金额" align="center" prop="defaultMoney" key="defaultMoney" width="100"
                       :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="职级" align="center" prop="jobGrade" key="jobGrade" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="单号" align="center" prop="singleNumber" key="singleNumber" width="200"
                       :show-overflow-tooltip='true'>
      <template slot-scope="scope">
        <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer">{{ scope.row.singleNumber }}</span>
      </template>
      </el-table-column>
      <el-table-column label="单据状态" align="center" prop="documentStatus" key="documentStatus" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" key="applicationDate" width="100"
                       :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {queryTrain} from "@/api/hr/personnel/train";
import {parseTime} from "@/utils/ruoyi"

export default {
  name: "Train",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 培训台账数据
      trainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 入职日期时间范围
      dateRangeEntryDate: [],
      // 转正日期时间范围
      dateRangePositiveDate: [],
      // 申请时间范围
      dateApplicationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        name: null,
        post: null,
        position: null,
        beginApplicationDate: null,
        endApplicationDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
      ],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 查询培训台账 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.beginEntryDate = this.dateRangeEntryDate[0];
      this.queryParams.endEntryDate = this.dateRangeEntryDate[1];
      this.queryParams.beginApplicationDate = this.dateApplicationDate[0];
      this.queryParams.endApplicationDate = this.dateApplicationDate[1];
      queryTrain(this.queryParams).then(response => {
        this.trainList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        idCard: null,
        birthday: null,
        email: null,
        entryDate: null,
        status: null,
        mobile: null,
        orgId: null,
        deptId: null,
        loginName: null,
        post: null,
        orgName: null,
        deptName: null,
        deptNameAll: null,
        jobGrade: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateApplicationDate = [];
      this.dateRangeEntryDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    selectDetail(row) {
      if (window.getSelection().toString() === '') {
        const loc = "/hr/pm/archive/" + row.id
        this.$router.push(loc)
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/personnel/train/export', {
        ...this.queryParams
      }, `培训台账_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    },
    getPickerOptions(controlId) {
      const today = new Date(); // 当前日期
      const aWeekAgo = new Date; // 一周前
      const aMonthAgo = new Date; // 一个月前
      const threeMonthsAgo = new Date(); // 三个月前的日期
      const threeMonthsLater = new Date(); // 三个月后的日期
      aWeekAgo.setDate(today.getDate() - 7);
      aMonthAgo.setMonth(today.getMonth() - 1);
      threeMonthsAgo.setMonth(today.getMonth() - 3);
      threeMonthsLater.setMonth(today.getMonth() + 3)
      const todayStr = parseTime(today, '{y}-{m}-{d}')
      const aWeekAgoStr = parseTime(aWeekAgo, '{y}-{m}-{d}')
      const aMonthAgoStr = parseTime(aMonthAgo, '{y}-{m}-{d}')
      const threeMonthsAgoStr = parseTime(threeMonthsAgo, '{y}-{m}-{d}')
      const threeMonthsLaterStr = parseTime(threeMonthsLater, '{y}-{m}-{d}')

      function updateDatePicker(from, to) {
        return () => {
          if (controlId === 2) {
            this.dateRangeEntryDate = [from, to];
            this.$refs.entryDatePicker.pickerVisible = false;
          } else if (controlId === 4) {
            this.dateRangeContractDate = [from, to];
            this.$refs.contractDatePicker.pickerVisible = false;
          }
        };
      }

      return {
        shortcuts: [
          {
            text: '近一周',
            onClick: updateDatePicker.call(this, aWeekAgoStr, todayStr),
          },
          {
            text: '近一个月',
            onClick: updateDatePicker.call(this, aMonthAgoStr, todayStr),
          },
          {
            text: '近三个月',
            onClick: updateDatePicker.call(this, threeMonthsAgoStr, todayStr),
          },
          {
            text: '未来三个月',
            onClick: updateDatePicker.call(this, todayStr, threeMonthsLaterStr),
          },
        ],
      };
    },
    jump(row) {
      window.open(row.prefix + row.id)
    },
  }
};
</script>
