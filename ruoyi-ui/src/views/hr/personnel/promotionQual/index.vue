<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          disabled
          type="year"
          style="width: 120px"
          placeholder="选择年">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          style="width: 90px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 90px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:promotion:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="leaveList" :max-height="tableMaxHeight" :cell-style="handleCellStyle">
      <el-table-column type="index" fixed align="center" label="序号" width="60"/>
      <el-table-column label="姓名" align="center" prop="name" width="80" fixed :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" fixed/>
      <el-table-column label="所属公司" align="center" prop="orgName" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="所属VP+" align="center" prop="vpName" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="secondDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="调整前职位" align="center" prop="post" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="调整前职级" align="center" prop="jobGrade" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="调整前职等" align="center" prop="jobLevel" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="转正日期" align="center" prop="positiveDate" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="本年度产假结束时间" align="center" prop="maternityLeaveEndDate" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="学历" align="center" prop="highestEducation" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="毕业学校" align="center" prop="graduationSchool" width="120"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="毕业日期" align="center" prop="graduationDate" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="毕业时长/年" align="center" prop="yearsSinceGraduation"   width="100"/>
      <el-table-column label="累计工龄/年" align="center" prop="totalSeniorityYear"  width="100"/>
      <el-table-column label="工作地" align="center" prop="workAddress" width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="上年工作月数" align="center" prop="lastYearWorkMonths" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="上年最终考评" align="center" prop="lastYearKpiResult" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="离职单状态" align="center" prop="resignationStatus" width="80"
                       :show-overflow-tooltip='true'  />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {getPromotionQualificationList} from "@/api/hr/personnel/promotion";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "promotion-qualification",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请假明细表格数据
      leaveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 添加说明时间范围
      leaveDateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: null,
        name: null,
        department: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      this.queryParams.year = parseTime(new Date(), '{y}')
    },
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询请假明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      getPromotionQualificationList(this.queryParams).then(response => {
        this.leaveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    handleCellStyle({ row, column, rowIndex, columnIndex }) {
      const style = {};
      if (row.resignationStatus === '待审' || row.resignationStatus === '完结' || row.resignationStatus === '驳回') {
        style.color = 'red'; // 直接设置 color 属性为红色
      }

      return style;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/promotion-qual/export', {
        ...this.queryParams
      }, new Date().getFullYear() + `晋职晋薪资格_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
