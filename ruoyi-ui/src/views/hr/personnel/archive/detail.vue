<template>
  <div>
    <!--    <p>id：{{ id }}</p>-->
    <el-card class="box-card" v-loading="loading">
      <el-button type="" @click="handleBack" circle icon="el-icon-arrow-left"/>
      <el-card class="box-card detail-card" shadow="always">
        <el-descriptions title="在司信息" :column="2" size="medium" border :labelStyle='labelStyle'
                         :contentStyle="contentStyle">
          <el-descriptions-item label="工号"> {{ detail.jobNumber }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ detail.name }}</el-descriptions-item>
          <el-descriptions-item label="员工状态">{{ detail.employeeStatus }}</el-descriptions-item>
          <el-descriptions-item label="离职日期">{{ detail.fdLeaveTime }}</el-descriptions-item>
          <el-descriptions-item label="考勤号">{{ detail.fdKqbh }}</el-descriptions-item>
          <el-descriptions-item label="工时类型">{{ detail.workType }}</el-descriptions-item>
          <el-descriptions-item label="所属公司">{{ detail.orgName }}</el-descriptions-item>
          <el-descriptions-item label="一级部门">{{ detail.firstDept }}</el-descriptions-item>
          <el-descriptions-item label="二级部门">{{ detail.secondDept }}</el-descriptions-item>
          <el-descriptions-item label="三级部门">{{ detail.thirdDept }}</el-descriptions-item>
          <el-descriptions-item label="四级部门">{{ detail.fourthDept }}</el-descriptions-item>
          <el-descriptions-item label="最终归属部门">{{ detail.deptName }}</el-descriptions-item>
          <el-descriptions-item label="成本中心名称">{{ detail.costCenterName }}</el-descriptions-item>
          <el-descriptions-item label="成本中心代码">{{ detail.costCenterCode }}</el-descriptions-item>
          <el-descriptions-item label="职位/岗位">{{ detail.post }}</el-descriptions-item>
          <el-descriptions-item label="职级/职等">{{ detail.jobGrade }}</el-descriptions-item>
          <el-descriptions-item label="入职日期">{{ detail.entryDate }}</el-descriptions-item>
          <el-descriptions-item label="试用期/月">{{ detail.fdTrialOperationPeriod }}</el-descriptions-item>
          <el-descriptions-item label="转正日期">{{ detail.fdPositiveTime }}</el-descriptions-item>
          <el-descriptions-item label="工资所在公司">{{ detail.gzffssgs }}</el-descriptions-item>
          <el-descriptions-item label="五险一金所在公司1">{{ detail.socialSecurityOrg1 }}</el-descriptions-item>
          <el-descriptions-item label="五险一金所在公司2">{{ detail.socialSecurityOrg2 }}</el-descriptions-item>
          <el-descriptions-item label="季度优秀员工">{{ detail.quarterlyExcellent }}</el-descriptions-item>
          <el-descriptions-item label="年度绩效考核结果">{{ detail.annualKpi }}</el-descriptions-item>
          <el-descriptions-item label="员工互助基金金额">{{ detail.mutualFund }}</el-descriptions-item>
          <el-descriptions-item label="康方工龄">{{ detail.currentSeniority }}</el-descriptions-item>
          <el-descriptions-item label="入康方前工龄/凭证">{{ detail.realshgl }}</el-descriptions-item>
          <el-descriptions-item label="累计连续工龄">{{ detail.totalSeniority }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="box-card detail-card" shadow="always">
        <el-descriptions title="个人信息" :column="2" size="medium" border :labelStyle='labelStyle'
                         :contentStyle="contentStyle">
          <el-descriptions-item label="劳动合同主体">{{ detail.contractName }}</el-descriptions-item>
          <el-descriptions-item label="劳动合同到期日期">{{ detail.contractEndDate }}</el-descriptions-item>
          <el-descriptions-item label="证件号码">{{ detail.idCard }}</el-descriptions-item>
          <el-descriptions-item label="证件有效日期">{{ detail.youxiaoqi }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ detail.birthday }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ detail.age }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ detail.sex }}</el-descriptions-item>
          <el-descriptions-item label="身高">{{ detail.fdStature }}</el-descriptions-item>
          <el-descriptions-item label="体重">{{ detail.fdWeight }}</el-descriptions-item>
          <el-descriptions-item label="民族">{{ detail.fdNation }}</el-descriptions-item>
          <el-descriptions-item label="婚姻状况">{{ detail.fdMaritalStatus }}</el-descriptions-item>
          <el-descriptions-item label="入团时间">{{ detail.fdDateOfGroup }}</el-descriptions-item>
          <el-descriptions-item label="入党时间">{{ detail.fdDateOfParty }}</el-descriptions-item>
          <el-descriptions-item label="政治面貌">{{ detail.fdPoliticalLandscape }}</el-descriptions-item>
          <el-descriptions-item label="学历">{{ detail.highestEducation }}</el-descriptions-item>
          <el-descriptions-item label="专业">{{ detail.fdMajor }}</el-descriptions-item>
          <el-descriptions-item label="毕业院校">{{ detail.fdSchoolName }}</el-descriptions-item>
          <el-descriptions-item label="毕业时间">{{ detail.graduationDate }}</el-descriptions-item>
          <el-descriptions-item label="退休日期">{{ detail.txrq }}</el-descriptions-item>
          <el-descriptions-item label="本人联系电话">{{ detail.fdMobileNo }}</el-descriptions-item>
          <el-descriptions-item label="紧急联系人">{{ detail.fdEmergencyContact }}</el-descriptions-item>
          <el-descriptions-item label="紧急联系人电话">{{ detail.fdEmergencyContactPhone }}</el-descriptions-item>
          <el-descriptions-item label="工作地点（城市名）">{{ detail.fdWorkAddress }}</el-descriptions-item>
          <el-descriptions-item label="工作地住址">{{ detail.gzdi }}</el-descriptions-item>
          <el-descriptions-item label="五险一金缴交地（城市）">{{ detail.socialSecurityLocale }}</el-descriptions-item>
          <el-descriptions-item label="籍贯（省/市）">{{ detail.fdNativePlace }}</el-descriptions-item>
          <el-descriptions-item label="户口所在地">{{ detail.fdRegisteredResidence }}</el-descriptions-item>
          <el-descriptions-item label="户口所在地省">{{ detail.registeredProvince }}</el-descriptions-item>
          <el-descriptions-item label="户口所在地市">{{ detail.registeredCity }}</el-descriptions-item>
          <el-descriptions-item label="户口所在地区">{{ detail.registeredDistrict }}</el-descriptions-item>
          <el-descriptions-item label="户口类型">{{ detail.fdAccountProperties }}</el-descriptions-item>
          <el-descriptions-item label="职称证">{{ detail.titleCertificate }}</el-descriptions-item>
          <el-descriptions-item label="职业资格证">{{ detail.certificate }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card class="box-card detail-card" shadow="always">
        <el-descriptions title="薪资核发账号" :column="2" size="medium" border :labelStyle='labelStyle'
                         :contentStyle="contentStyle">
          <el-descriptions-item label="银行名称">{{ detail.bankmain }}</el-descriptions-item>
          <el-descriptions-item label="账户名称">{{ detail.accountname }}</el-descriptions-item>
          <el-descriptions-item label="工资卡账号">{{ detail.bank }}</el-descriptions-item>
          <el-descriptions-item label="开户行信息">{{ detail.bankname }}</el-descriptions-item>
          <el-descriptions-item label="联行号">{{ detail.bankcode }}</el-descriptions-item>
          <el-descriptions-item label="Swift code">{{ detail.swiftCode }}</el-descriptions-item>
          <el-descriptions-item label="BSB">{{ detail.bsb }}</el-descriptions-item>
          <el-descriptions-item label="Beneficiary address">{{ detail.beneficiaryAddress }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import {getArchiveDetail} from "@/api/hr/personnel/archive";

export default {
  name: "detail",
  data() {
    return {
      loading: false,
      id: null,
      labelStyle: {'width': '300px',},
      contentStyle: {'width': '500px',},
      detail: {
        loginName: null,
        name: null,
        status: null,
        fdLeaveTime: null,
        fdKqbh: null,
        workType: null,
        orgName: null,
        firstDept: null,
        secondDept: null,
        thirdDept: null,
        fourthDept: null,
        deptName: null,
        costCenterName: null,
        costCenterCode: null,
        post: null,
        jobGrade: null,
        entryDate: null,
        fdTrialOperationPeriod: null,
        fdPositiveTime: null,
        gzffssgs: null,
        socialSecurityOrg1: null,
        socialSecurityOrg2: null,
        quarterlyExcellent: null,
        annualKpi: null,
        mutualFund: null,
        trafficAllowance: null,
        currentSeniority: null,
        realshgl: null,
        totalSeniority: null,
        contractName: null,
        contractEndDate: null,
        idCard: null,
        youxiaoqi: null,
        birthday: null,
        age: null,
        sex: null,
        fdStature: null,
        fdWeight: null,
        fdNation: null,
        fdMaritalStatus: null,
        fdDateOfGroup: null,
        fdDateOfParty: null,
        fdPoliticalLandscape: null,
        highestEducation: null,
        fdMajor: null,
        fdSchoolName: null,
        graduationDate: null,
        txrq: null,
        fdMobileNo: null,
        fdEmergencyContact: null,
        fdEmergencyContactPhone: null,
        fdWorkAddress: null,
        gzdi: null,
        socialSecurityLocale: null,
        fdNativePlace: null,
        fdRegisteredResidence: null,
        registeredProvince: null,
        registeredCity: null,
        registeredDistrict: null,
        fdAccountProperties: null,
        titleCertificate: null,
        certificate: null,
        bankname: null,
        accountname: null,
        bank: null,
        bankmain: null,
        bankcode: null,
        swiftCode: null,
        bsb: null,
        beneficiaryAddress: null,
      }
    }
  },
  methods: {
    getList(id) {
      this.loading = true;
      getArchiveDetail(id).then(response => {
        this.detail = response.data
        this.loading = false;
      });
    },
    handleBack() {
      this.$router.push("/hr/pm/archive")
    }
  },
  created() {
    const staffId = this.$route.params.id
    this.id = staffId
    this.getList(staffId)
  }
}
</script>

<style scoped>
.detail-card {
  margin: 20px;
}
</style>
