<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label-width="auto" label="状态" prop="selectedValue">
        <el-select placeholder="请选择状态" v-model="selectedValue">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="auto" label="签订状态" prop="selectedValue2">
        <el-select placeholder="请选择合同签订状态" v-model="selectedValue2">
          <el-option
            v-for="item in options2"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="到期日期" label-width="90px">
        <!--        <el-date-picker-->
        <!--          ref="contractDatePicker"-->
        <!--          v-model="queryParams.queryDate"-->
        <!--          style="width: 240px"-->
        <!--          value-format="yyyy-MM-dd"-->
        <!--          type="daterange"-->
        <!--          unlink-panels-->
        <!--          range-separator="-"-->
        <!--          start-placeholder="开始日期"-->
        <!--          end-placeholder="结束日期"-->
        <!--        ></el-date-picker>-->
        <el-date-picker
          v-model="queryParams.endsDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:pm:contract:export']"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border max-height="550" :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" key="id"
                       width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="姓名" align="center" prop="name" key="name" v-if="columns[0].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="工号" align="center" prop="loginName" key="loginName" v-if="columns[1].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="部门" align="center" prop="department" key="department"
                       v-if="columns[2].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="直接上级" align="center" prop="leaderName" key="leaderName"
                       v-if="columns[3].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post"
                       v-if="columns[4].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryTime" key="entryTime"
                       v-if="columns[5].visible"
                       width="100" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="员工状态" align="center" prop="status" key="status"
                       v-if="columns[6].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="合同签订情况" align="center" prop="contractStatus" key="contractStatus"
                       v-if="columns[7].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="历史签订情况" align="center" prop="historyContractStatus" key="historyContractStatus"
                       v-if="columns[8].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="合约首签到期日期" align="center" prop="firstEndDate" key="firstEndDate"
                       v-if="columns[9].visible"
                       width="200" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>
<!--            {{parseTime(scope.row.firstBeginDate, '{y}-{m}-{d}') }}-->
            <!--            {{ !scope.row.firstBeginDate && !scope.row.firstEndDate ? '' : '~' }}-->
            {{ parseTime(scope.row.firstEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合约续签到期日期" align="center" prop="endDate" key="endDate"
                       v-if="columns[10].visible"
                       width="200" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>
<!--            {{parseTime(scope.row.beginDate, '{y}-{m}-{d}') }}-->
            <!--            {{ !scope.row.beginDate && !scope.row.endDate ? '' : '~' }}-->
            {{
              parseTime(scope.row.endDate, '{y}-{m}-{d}') == '1956-09-12' ? '~' : parseTime(scope.row.endDate, '{y}-{m}-{d}')
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合约类型" align="center" prop="contractType" key="contractType"
                       v-if="columns[11].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="合约附件" align="center" prop="contractAnnex" key="contractAnnex"
                       v-if="columns[12].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="合约签收日期" align="center" prop="contractSignDate" key="contractSignDate"
                       v-if="columns[13].visible"
                       width="200" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.contractSignDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经办人" align="center" prop="handlePerson" key="handlePerson"
                       v-if="columns[14].visible"
                       width="200" :show-overflow-tooltip='true'/>
      <el-table-column label="离职日期" align="center" prop="leaveTime" key="leaveTime"
                       v-if="columns[15].visible"
                       width="200" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.leaveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {getContractList} from "@/api/hr/personnel/contract";

export default {
  name: "ProcessControlContract",
  data() {
    return {
      // 遮罩层
      options: [{
        value: '',
        label: '所有'
      },
        {
          value: '在职',
          label: '在职'
        },
        {
          value: 'leave',
          label: '离职'
        }],
      options2: [{
        value: '',
        label: '所有'
      },
        {
          value: '未签订',
          label: '未签订'
        }],
      selectedValue: '',
      selectedValue2: '',
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: '',
        contractStatus: '',
        queryDate: '',
        startDate: null,
        endsDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 1, label: `姓名`, visible: true},
        {key: 2, label: `工号`, visible: true},
        {key: 3, label: `部门`, visible: true},
        {key: 4, label: `直接上级`, visible: true},
        {key: 5, label: `职位`, visible: true},
        {key: 6, label: `入职日期`, visible: true},
        {key: 7, label: `员工状态`, visible: true},
        {key: 8, label: `合同签订情况`, visible: true},
        {key: 9, label: `历史签订情况`, visible: true},
        {key: 10, label: `合约首签到期日期`, visible: true},
        {key: 11, label: `合约续签到期日期`, visible: true},
        {key: 12, label: `合约类型`, visible: true},
        {key: 13, label: `合约附件`, visible: true},
        {key: 14, label: `合约签收日期`, visible: true},
        {key: 15, label: `经办人`, visible: true},
        {key: 16, label: `离职日期`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询HR年报 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.status = this.selectedValue;
      this.queryParams.contractStatus = this.selectedValue2;
      // this.queryParams.startDate = this.queryParams.queryDate[0];
      // this.queryParams.endsDate = this.queryParams.queryDate[1];
      getContractList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = '';
      this.queryParams.endsDate = '';
      this.selectedValue = '';
      this.selectedValue2 = '';
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/personnel/contract/export', {
        ...this.queryParams
      }, `流程管控_合同${dateTime}.xlsx`)
    },
    jump(row) {
      window.open("https://oa.akesobio.com/hr/ratify/hr_ratify_transfer/hrRatifyTransfer.do?method=view&fdId=" + row.id)
    },
    watch: {
      // 监听日期清理后数据为null进行处理否则会报错
      'queryParams.queryDate'(newVal) {
        if (newVal == null) {
          this.queryParams.queryDate = ''
        }
      },
      'queryParams.endsDate'(newVal) {
        if (newVal == null) {
          this.queryParams.endsDate = ''
        }
      }
    }

  }


};
</script>
