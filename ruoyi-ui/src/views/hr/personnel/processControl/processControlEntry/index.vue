<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="当前处理人" prop="handlePerson">
        <el-input
          v-model="queryParams.handlePerson"
          placeholder="请输入处理人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:process:entry:export']"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border max-height="550" :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" key="id"
                       width="100" :show-overflow-tooltip='true' v-if="false"/>
      <el-table-column label="姓名" align="center" prop="name" key="name" v-if="columns[0].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="工号" align="center" prop="loginName" key="loginName" v-if="columns[1].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="部门" align="center" prop="department" key="department"
                       v-if="columns[2].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="直接上级" align="center" prop="leaderName" key="leaderName"
                       v-if="columns[3].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post"
                       v-if="columns[4].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="工作地点" align="center" prop="workAddr" key="workAddr"
                       v-if="columns[5].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="入职时间" align="center" prop="entryDate" key="entryDate"
                       v-if="columns[6].visible"
                       width="100" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="入职时间" align="center" prop="entryDate" key="entryDate"-->
      <!--                       v-if="columns[6].visible"-->
      <!--                       width="100" :show-overflow-tooltip='true'>-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="手机号码" align="center" prop="mobile" key="mobile"
                       v-if="columns[8].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="报到地点" align="center" prop="entryAddr" key="entryAddr"
                       v-if="columns[9].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="住宿要求" align="center" prop="liveAsk" key="liveAsk"
                       v-if="columns[10].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="办公设备" align="center" prop="device" key="device"
                       v-if="columns[11].visible"
                       width="100" :show-overflow-tooltip='true'/>

      <el-table-column label="入职进度" align="center">
        <el-table-column label="经办HR" align="center" prop="hr" key="hr"
                         v-if="columns[12].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="创建时间" align="center" prop="createTime" key="createTime"
                         v-if="columns[13].visible"
                         width="100" :show-overflow-tooltip='true'>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="主题" align="center" prop="subject" key="subject"
                         v-if="columns[14].visible"
                         width="200" :show-overflow-tooltip='true'>
          <template slot-scope="scope">
            <span @click="jump(scope.row)" style="color: #3967FF;cursor:pointer">{{ scope.row.subject }}</span>
          </template>
        </el-table-column>
        <el-table-column label="当前环节" align="center" prop="currentSession" key="currentSession"
                         v-if="columns[15].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="当前处理人" align="center" prop="handlePerson" key="handlePerson"
                         v-if="columns[16].visible"
                         width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {entryProcessList} from "@/api/hr/process";

export default {
  name: "ProcessControlEntry",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        handlePerson: null,
        queryDate: '',
        startDate: null,
        endDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 1, label: `姓名`, visible: true},
        {key: 2, label: `工号`, visible: true},
        {key: 3, label: `部门`, visible: true},
        {key: 4, label: `直接上级`, visible: true},
        {key: 5, label: `职位`, visible: true},
        {key: 6, label: `工作地点`, visible: true},
        {key: 7, label: `预计入职日期`, visible: true},
        {key: 8, label: `实际入职日期`, visible: true},
        {key: 9, label: `手机号码`, visible: true},
        {key: 10, label: `报到地点`, visible: true},
        {key: 11, label: `住宿要求`, visible: true},
        {key: 12, label: `办公设备`, visible: true},
        {key: 13, label: `经办HR`, visible: true},
        {key: 14, label: `创建时间`, visible: true},
        {key: 15, label: `主题`, visible: true},
        {key: 16, label: `当前环节`, visible: true},
        {key: 17, label: `当前处理人`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询HR年报 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.startDate = this.queryParams.queryDate[0];
      this.queryParams.endDate = this.queryParams.queryDate[1];
      entryProcessList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = '';
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/process/entry/export', {
        ...this.queryParams
      }, `流程管控_入职${dateTime}.xlsx`)
    },
    jump(row) {
      window.open("https://oa.akesobio.com/hr/ratify/hr_ratify_entry/hrRatifyEntry.do?method=view&fdId=" + row.id)
    },

  },
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
      }
    }
  }


};
</script>
