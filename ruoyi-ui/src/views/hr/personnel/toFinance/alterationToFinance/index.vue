<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="入职时间" label-width="90px">-->
      <!--        <el-date-picker-->
      <!--          ref="contractDatePicker"-->
      <!--          v-model="queryParams.queryDate"-->
      <!--          style="width: 240px"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          type="daterange"-->
      <!--          unlink-panels-->
      <!--          range-separator="-"-->
      <!--          start-placeholder="开始日期"-->
      <!--          end-placeholder="结束日期"-->
      <!--        ></el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:pm:toFinance:alterationToFinance:export']"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border max-height="550" :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" align="center" prop="name" key="name" v-if="columns[0].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>

      <el-table-column label="基础信息" align="center">
      <el-table-column label="工号" align="center" prop="loginName" key="loginName" v-if="columns[1].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="部门" align="center" prop="position" key="position"
                       v-if="columns[2].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="入职时间" align="center" prop="entryDate" key="entryDate"
                       v-if="columns[3].visible"
                       width="100" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="证件类别" align="center" prop="idType" key="idType"
                       v-if="columns[4].visible"
                       width="100" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ scope.row.idNumber != null ? "身份证" : scope.row.idType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证号码" align="center" prop="idNumber" key="idNumber"
                       v-if="columns[5].visible"
                       width="190" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="跨公司调动" align="center">
        <el-table-column label="原所属公司" align="center" prop="companyStart" key="companyStart"
                         v-if="columns[6].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="调入后所属公司" align="center" prop="companyEnd" key="companyEnd"
                         v-if="columns[7].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="调动生效日期" align="center" prop="effectiveDate" key="effectiveDate"
                         v-if="columns[8].visible"
                         width="100" :show-overflow-tooltip='true'>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="变动后薪资卡信息" align="center">
        <el-table-column label="账户名称" align="center" prop="accountName" key="accountName"
                         v-if="columns[9].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="银行卡号" align="center" prop="bank" key="bank"
                         v-if="columns[10].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="银行名称" align="center" prop="bankName" key="bankName"
                         v-if="columns[11].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="开户行所在地" align="center" prop="bankNameKAddr" key="bankNameKAddr"
                         v-if="columns[12].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="开户行" align="center" prop="bankNameK" key="bankNameK"
                         v-if="columns[13].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="银行联系电话" align="center" prop="bankPhone" key="bankPhone"
                         v-if="columns[14].visible"
                         width="120" :show-overflow-tooltip='true'/>
        <el-table-column label="联行号" align="center" prop="bankCode" key="bankCode"
                         v-if="columns[15].visible"
                         width="120" :show-overflow-tooltip='true'/>

        <el-table-column label="Swift code" align="center" prop="swiftCode" key="swiftCode"
                         v-if="columns[16].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="BSB" align="center" prop="bsb" key="bsb"
                         v-if="columns[17].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="Beneficiary address" align="center" prop="beneficiaryAddress" key="beneficiaryAddress"
                         v-if="columns[18].visible"
                         width="170" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="备注" align="center" prop="notes" key="notes"
                       v-if="columns[19].visible"
                       width="100" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {queryAlterationToFinance} from "@/api/hr/personnel/toFinance/alterationToFinance";

export default {
  name: "AlterationToFinance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 变动 to 财务
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        queryDate: '',
        startDate: null,
        endDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 0, label: `姓名`, visible: true},
        {key: 1, label: `工号`, visible: true},
        {key: 2, label: `部门`, visible: true},
        {key: 3, label: `入职时间`, visible: true},
        {key: 4, label: `证件类别`, visible: true},
        {key: 5, label: `身份证号码`, visible: true},
        {key: 6, label: `原所属公司`, visible: true},
        {key: 7, label: `调入后所属公司`, visible: true},
        {key: 8, label: `调动生效日期`, visible: true},
        {key: 9, label: `账户名称`, visible: true},
        {key: 10, label: `银行卡号`, visible: true},
        {key: 11, label: `银行名称`, visible: true},
        {key: 12, label: `开户行所在地`, visible: true},
        {key: 13, label: `开户行`, visible: true},
        {key: 14, label: `银行联系电话`, visible: true},
        {key: 15, label: `联行号`, visible: true},
        {key: 16, label: `Swift code`, visible: true},
        {key: 17, label: `BSB`, visible: true},
        {key: 18, label: `Beneficiary address`, visible: true},
        {key: 19, label: `备注`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询HR年报 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.startDate = this.queryParams.queryDate[0];
      this.queryParams.endDate = this.queryParams.queryDate[1];
      queryAlterationToFinance(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = '';
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/personnel/toFinance/alterationToFinance/export', {
        ...this.queryParams
      }, `跨公司or银行卡_本月变动_To财务${dateTime}.xlsx`)
    },

  },
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
      }
    }
  }


};
</script>
