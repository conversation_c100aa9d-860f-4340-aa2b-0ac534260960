<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="姓名" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名或工号"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="position">
        <el-input
            v-model="queryParams.position"
            placeholder="请输入部门"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入职时间" label-width="90px">
        <el-date-picker
            ref="contractDatePicker"
            v-model="queryParams.queryDate"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            unlink-panels
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:pm:toFinance:activeEmployeesToFinance:export']"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="dataList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" align="center" prop="name" key="name" v-if="columns[0].visible"
                       width="100" :show-overflow-tooltip='true' fixed/>
      <el-table-column label="工号" align="center" prop="loginName" key="loginName" v-if="columns[1].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="所属公司" align="center" prop="company" key="company"
                       v-if="columns[2].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" key="firstDept"
                       v-if="columns[3].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="secondDept" key="secondDept"
                       v-if="columns[4].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" key="thirdDept"
                       v-if="columns[5].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" key="fourthDept"
                       v-if="columns[6].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post"
                       v-if="columns[7].visible"
                       width="135" :show-overflow-tooltip='true'/>
      <el-table-column label="入职时间" align="center" prop="entryDate" key="entryDate"
                       v-if="columns[9].visible"
                       width="100" :show-overflow-tooltip='true'>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号码" align="center" prop="phoneNumber" key="phoneNumber"
                       v-if="columns[10].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="性别" align="center" prop="sex" key="sex"
                       v-if="columns[11].visible"
                       width="100" :show-overflow-tooltip='true'/>

      <el-table-column label="成本中心信息" align="center">
        <el-table-column label="成本中心名称" align="center" prop="costCenterName" key="costCenterName"
                         v-if="columns[12].visible"
                         width="100" :show-overflow-tooltip='true'/>
        <el-table-column label="成本中心代码" align="center" prop="costCenterCode" key="costCenterCode"
                         v-if="columns[13].visible"
                         width="100" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="当月新入职需填写" align="center">
        <el-table-column label="证件类别" align="center" prop="idType" key="idType"
                         v-if="columns[14].visible"
                         width="100" :show-overflow-tooltip='true'>
          <template slot-scope="scope">
            <span>{{ scope.row.idNumber != null ? "身份证" : scope.row.idType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="身份证号" align="center" prop="idNumber" key="idNumber"
                         v-if="columns[15].visible"
                         width="190" :show-overflow-tooltip='true'/>
      </el-table-column>

      <el-table-column label="ESG职能分类" align="center" prop="esg" key="esg"
                       v-if="columns[16].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="职级" align="center" prop="jobGrade" key="jobGrade"
                       v-if="columns[8].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="员工状态" align="center" prop="status" key="status"
                       v-if="columns[17].visible"
                       width="100" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />


  </div>
</template>

<script>
import {queryActiveEmployeesToFinance} from "@/api/hr/personnel/toFinance/activeEmployeesToFinance";

export default {
  name: "ActiveEmployeesToFinance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 培训台账数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        position: null,
        queryDate: '',
        startDate: null,
        endDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 0, label: `姓名`, visible: true},
        {key: 1, label: `工号`, visible: true},
        {key: 2, label: `公司`, visible: true},
        {key: 3, label: `一级部门`, visible: true},
        {key: 4, label: `二级部门`, visible: true},
        {key: 5, label: `三级部门`, visible: true},
        {key: 6, label: `四级部门`, visible: true},
        {key: 7, label: `职位`, visible: true},
        {key: 8, label: `职级`, visible: true},
        {key: 9, label: `入职时间`, visible: true},
        {key: 10, label: `手机号码`, visible: true},
        {key: 11, label: `性别`, visible: true},
        {key: 12, label: `成本中心名称`, visible: true},
        {key: 13, label: `成本中心代码`, visible: true},
        {key: 14, label: `证件类别`, visible: true},
        {key: 15, label: `身份证号`, visible: true},
        {key: 16, label: `ESG职能分类`, visible: true},
        {key: 17, label: `员工状态`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 查询HR年报 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.startDate = this.queryParams.queryDate[0];
      this.queryParams.endDate = this.queryParams.queryDate[1];
      queryActiveEmployeesToFinance(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryDate = '';
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/personnel/toFinance/activeEmployeesToFinance/export', {
        ...this.queryParams
      }, `在职员工花名册_To财务${dateTime}.xlsx`)
    },

  },
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'queryParams.queryDate'(newVal) {
      if (newVal == null) {
        this.queryParams.queryDate = ''
      }
    }
  }


};
</script>
