<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="工号" prop="loginName">
        <el-input
          v-model="queryParams.jobNumber"
          placeholder="工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="岗位" prop="post">
        <el-input
          v-model="queryParams.post"
          placeholder="请输入岗位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptNameAll">
        <el-input
          v-model="queryParams.deptNameAll"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入职日期">
        <el-date-picker
          ref="entryDatePicker"
          v-model="dateRangeEntryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(2)"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="职级" prop="jobGrade">
        <el-input
          v-model="queryParams.jobGrade"
          placeholder="请输入职级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学历" prop="highestEducation">
        <el-input
          v-model="queryParams.highestEducation"
          placeholder="请输入学历"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同到期日" label-width="90px">
        <el-date-picker
          ref="contractDatePicker"
          v-model="dateRangeContractDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="getPickerOptions(4)"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="离职类型" prop="status">
        <el-select v-model="queryParams.employeeType" placeholder="请选择"
                   style="width: 150px">
          <el-option
            v-for="item in statusOptions"
            :key="item.id"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="离职日期">
        <el-date-picker
          ref="contractDatePicker"
          v-model="leaveDateEntryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:pm:archive:depart:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border max-height="510" :data="archiveList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name" key="name" v-if="columns[1].visible"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNumber" key="loginName" v-if="columns[0].visible" fixed/>
      <el-table-column label="一级部门" align="center" prop="firstDept" key="firstDept" v-if="columns[2].visible"
                       width="150" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" key="post" v-if="columns[3].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="职级" align="center" prop="jobGrade" key="jobGrade" v-if="columns[4].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" key="entryDate" v-if="columns[5].visible"
                       width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="劳动合同主体" align="center" prop="contractName" key="contractName"
                       v-if="columns[6].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="劳动合同到期日" align="center" prop="contractEndDate" key="contractEndDate"
                       v-if="columns[7].visible" width="130">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.contractEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证件号码" align="center" prop="idCard" key="idCard" v-if="columns[8].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="毕业院校" align="center" prop="fdSchoolName" key="fdSchoolName" v-if="columns[9].visible"
                       width="100" :show-overflow-tooltip='true'/>
      <el-table-column label="专业" align="center" prop="fdMajor" key="fdMajor" v-if="columns[10].visible" width="100"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="学历" align="center" prop="highestEducation" key="highestEducation"
                       v-if="columns[11].visible" width="80"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="毕业时间" align="center" prop="graduationDate" key="graduationDate"
                       v-if="columns[12].visible" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.graduationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="本人联系方式" align="center" prop="fdMobileNo" key="fdMobileNo" v-if="columns[13].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="紧急联系人" align="center" prop="fdEmergencyContact" key="fdEmergencyContact"
                       v-if="columns[14].visible"
                       width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="紧急联系人电话" align="center" prop="fdEmergencyContactPhone"
                       key="fdEmergencyContactPhone" v-if="columns[15].visible"
                       width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="<境内员工>薪资账号信息" align="center" width="100"
                       v-if="columns[16].visible || columns[17].visible || columns[18].visible || columns[19].visible">
        <el-table-column label="银行名称" align="center" prop="bankmain" key="bankmain" width="100"
                         v-if="columns[16].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="账号名称" align="center" prop="accountname" key="accountname" width="100"
                         v-if="columns[17].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="银行账号" align="center" prop="bank" key="bank" width="100"
                         v-if="columns[18].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="开户行" align="center" prop="bankname" key="bankname" width="100"
                         v-if="columns[19].visible" :show-overflow-tooltip='true'/>
      </el-table-column>
      <el-table-column label="<境外员工>薪资账号信息" align="center" width="100"
                       v-if="columns[20].visible || columns[21].visible || columns[22].visible">
        <el-table-column label="Swift code" align="center" prop="swiftCode" key="swiftCode" width="100"
                         v-if="columns[20].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="BSB" align="center" prop="bsb" key="bsb" width="100"
                         v-if="columns[21].visible" :show-overflow-tooltip='true'/>
        <el-table-column label="Beneficiary address" align="center" prop="beneficiaryAddress" key="beneficiaryAddress"
                         width="100"
                         v-if="columns[22].visible" :show-overflow-tooltip='true'/>
      </el-table-column>
      <el-table-column label="离职类型" align="center" prop="employeeType" key="employeeType" width="100"
                       v-if="columns[23].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="离职原因" align="center" prop="fdLeaveReason" key="fdLeaveReason" width="100"
                       v-if="columns[24].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="离职概述" align="center" prop="resignationSummary" key="resignationSummary" width="100"
                       v-if="columns[32].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="离职日期" align="center" prop="leaveDate" key="leaveDate" width="120"
                       v-if="columns[31].visible">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.leaveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="拟定离职日期" align="center" prop="fdLeaveApplyDate" key="fdLeaveApplyDate" width="120"
                       v-if="columns[25].visible">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdLeaveApplyDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际离职日期" align="center" prop="fdActualLeaveTime" key="fdActualLeaveTime" width="120"
                       v-if="columns[26].visible">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fdActualLeaveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="fdLeaveRemark" key="fdLeaveRemark" width="100"
                       v-if="columns[27].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="secondDept" key="secondDept" width="100"
                       v-if="columns[28].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" key="thirdDept" width="100"
                       v-if="columns[29].visible" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" key="fourthDept" width="100"
                       v-if="columns[30].visible" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {queryDepart} from "@/api/hr/personnel/depart";
import {parseTime} from "@/utils/ruoyi"

export default {
  name: "Depart",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工信息表格数据
      archiveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 入职日期时间范围
      dateRangeEntryDate: [],
      //离职日期时间范围
      leaveDateEntryDate: [],
      // 转正日期时间范围
      dateRangePositiveDate: [],
      // 合同到期日期时间范围
      dateRangeContractDate: [],
      statusOptions: [
        {value: '', "id": 0, label: "所有"},
        {value: '实习', "id": 1, label: "实习"},
        {value: '试用', "id": 2, label: "试用"},
        {value: '正式', "id": 3, label: "正式"}
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        name: null,
        post: null,
        deptNameAll: null,
        jobGrade: null,
        highestEducation: null,
        employeeStatus: '离职',
        employeeType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 0, label: `工号`, visible: true},
        {key: 1, label: `姓名`, visible: true},
        {key: 2, label: `一级部门`, visible: true},
        {key: 3, label: `职位`, visible: true},
        {key: 4, label: `职级`, visible: true},
        {key: 5, label: `入职日期`, visible: true},
        {key: 6, label: `劳动合同主体`, visible: true},
        {key: 7, label: `劳动合同到期日`, visible: true},
        {key: 8, label: `身份证号码`, visible: true},
        {key: 9, label: `毕业院校`, visible: true},
        {key: 10, label: `专业`, visible: true},
        {key: 11, label: `学历`, visible: true},
        {key: 12, label: `毕业时间`, visible: true},
        {key: 13, label: `本人联系电话`, visible: true},
        {key: 14, label: `紧急联系人`, visible: true},
        {key: 15, label: `紧急联系人电话`, visible: true},
        {key: 16, label: `工资卡发放银行`, visible: true},
        {key: 17, label: `银行账户名称`, visible: true},
        {key: 18, label: `银行账号`, visible: true},
        {key: 19, label: `开户行`, visible: true},
        {key: 20, label: `Swift code`, visible: true},
        {key: 21, label: `BSB`, visible: true},
        {key: 22, label: `Beneficiary address`, visible: true},
        {key: 23, label: `离职类型`, visible: true},
        {key: 24, label: `离职原因`, visible: true},
        {key: 25, label: `拟定离职日期`, visible: true},
        {key: 26, label: `实际离职日期`, visible: true},
        {key: 27, label: `备注`, visible: true},
        {key: 28, label: `二级部门`, visible: true},
        {key: 29, label: `三级部门`, visible: true},
        {key: 30, label: `四级部门`, visible: true},
        {key: 31, label: `离职日期`, visible: true},
        {key: 32, label: `离职概述`, visible: true},
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    /** 查询员工信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRangeEntryDate && '' != this.dateRangeEntryDate) {
        this.queryParams.params["beginEntryDate"] = this.dateRangeEntryDate[0];
        this.queryParams.params["endEntryDate"] = this.dateRangeEntryDate[1];
      }
      if (null != this.dateRangeContractDate && '' != this.dateRangeContractDate) {
        this.queryParams.params["beginContractExpireDate"] = this.dateRangeContractDate[0];
        this.queryParams.params["endContractExpireDate"] = this.dateRangeContractDate[1];
      }
      if (null != this.leaveDateEntryDate && '' != this.leaveDateEntryDate) {
        this.queryParams.params["startDate"] = this.leaveDateEntryDate[0];
        this.queryParams.params["endDate"] = this.leaveDateEntryDate[1];
      }
      queryDepart(this.queryParams).then(response => {
        this.archiveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        idCard: null,
        birthday: null,
        email: null,
        entryDate: null,
        employeeStatus: null,
        mobile: null,
        orgId: null,
        deptId: null,
        loginName: null,
        post: null,
        orgName: null,
        deptName: null,
        deptNameAll: null,
        jobGrade: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.employeeType = '';
      this.dateRangeEntryDate = '';
      this.leaveDateEntryDate = '';
      this.dateRangeContractDate = '';
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    selectDetail(row) {
      if (window.getSelection().toString() === '') {
        const loc = "/hr/pm/archive/" + row.id
        this.$router.push(loc)
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/personnel/archive/depart/export', {
        ...this.queryParams
      }, `离职员工档案_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    },
    getPickerOptions(controlId) {
      const today = new Date(); // 当前日期
      const aWeekAgo = new Date; // 一周前
      const aMonthAgo = new Date; // 一个月前
      const threeMonthsAgo = new Date(); // 三个月前的日期
      const threeMonthsLater = new Date(); // 三个月后的日期
      aWeekAgo.setDate(today.getDate() - 7);
      aMonthAgo.setMonth(today.getMonth() - 1);
      threeMonthsAgo.setMonth(today.getMonth() - 3);
      threeMonthsLater.setMonth(today.getMonth() + 3)
      const todayStr = parseTime(today, '{y}-{m}-{d}')
      const aWeekAgoStr = parseTime(aWeekAgo, '{y}-{m}-{d}')
      const aMonthAgoStr = parseTime(aMonthAgo, '{y}-{m}-{d}')
      const threeMonthsAgoStr = parseTime(threeMonthsAgo, '{y}-{m}-{d}')
      const threeMonthsLaterStr = parseTime(threeMonthsLater, '{y}-{m}-{d}')

      function updateDatePicker(from, to) {
        return () => {
          if (controlId === 2) {
            this.dateRangeEntryDate = [from, to];
            this.$refs.entryDatePicker.pickerVisible = false;
          } else if (controlId === 4) {
            this.dateRangeContractDate = [from, to];
            this.$refs.contractDatePicker.pickerVisible = false;
          }
        };
      }

      return {
        shortcuts: [
          {
            text: '近一周',
            onClick: updateDatePicker.call(this, aWeekAgoStr, todayStr),
          },
          {
            text: '近一个月',
            onClick: updateDatePicker.call(this, aMonthAgoStr, todayStr),
          },
          {
            text: '近三个月',
            onClick: updateDatePicker.call(this, threeMonthsAgoStr, todayStr),
          },
          {
            text: '未来三个月',
            onClick: updateDatePicker.call(this, todayStr, threeMonthsLaterStr),
          },
        ],
      };
    },
  },
  watch: {
    // 监听日期清理后数据为null进行处理否则会报错
    'leaveDateEntryDate'(newVal) {
      if (newVal == null) {
        this.leaveDateEntryDate = ''
      }
    },
    'dateRangeEntryDate'(newVal) {
      if (newVal == null) {
        this.dateRangeEntryDate = ''
      }
    },
    'dateRangeContractDate'(newVal) {
      if (newVal == null) {
        this.dateRangeContractDate = ''
      }
    },
  }
};
</script>
