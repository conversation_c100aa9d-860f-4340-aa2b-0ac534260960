<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="公司名称" prop="year">
        <el-select v-model="queryParams.company" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in companyOptions"
            :key="item.id"
            :label="item.company"
            :value="item.company">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门类型" prop="year">
        <el-select v-model="queryParams.departmentType" placeholder="请选择" @change="getDepartmentSelector()"
                   style="width: 100px">
          <el-option
            v-for="item in departmentTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门名称" prop="year">
        <el-select v-model="queryParams.departments"
                   :show-overflow-tooltip='true'
                   clearable
                   filterable
                   multiple
                   collapse-tags
                   placeholder="请选择"
                   style="width: 200px">
          <el-row>
            <el-button type="primary" class="dept-selector-btn" @click="selectAll">全选</el-button>
            <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>
            <el-button type="info" class="dept-selector-btn" @click="clearSelect">清空</el-button>
          </el-row>
          <el-option
            v-for="item in departmentOptions"
            :key="item.id"
            :label="item.department"
            :value="item.department">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年份" prop="year" label-width="70px">
        <el-select v-model="queryParams.year" clearable placeholder="请选择" style="width: 80px">
          <el-option
            v-for="item in yearOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button style="margin-left: 50px" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
          搜索
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:ot:report:dept:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :max-height="tableMaxHeight" v-loading="loading" :data="departmentList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="部门名称" fixed align="center" prop="department" width="130px"
                       :show-overflow-tooltip='true'/>

      <template v-for="(item, index) in tableHeaders">
        <el-table-column align="center" :label="item.mon" :key="item.key_str">
          <el-table-column label="加班计休时" align="center" width="90">
            <template slot-scope="scope">
              {{ scope.row.reportMonthlyList[index].otToLeave }}
            </template>
          </el-table-column>
          <el-table-column label="加班计薪时长H" align="center">
            <el-table-column label="工作日" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryWorkday }}
              </template>
            </el-table-column>
            <el-table-column label="周末" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryWeekend }}
              </template>
            </el-table-column>
            <el-table-column label="法定" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryOfficial }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="总和" align="center">
            <template slot-scope="scope">
              {{ scope.row.reportMonthlyList[index].total }}
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>


    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listCompanySelector, listDepartmentSelector, listDepartmentReport} from "@/api/hr/overtime/overtime";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "DepartmentReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 一级部门汇总表格数据
      departmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        departmentType: 1,
        company: 'AKESO',
        departments: [],
        year: new Date().getFullYear()
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 年份选项
      yearOptions: [],
      // 公司选项
      companyOptions: [],
      // 部门类型选项
      departmentTypeOptions: [
        {"label": '一级部门', "value": 1},
        {"label": '二级部门', "value": 2}
      ],
      // 部门选项
      departmentOptions: [],
      // 表头
      tableHeaders: []
    };
  },
  created() {
    this.init();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    init() {
      /** 初始化选项 */
      this.getSelector();
    },
    /** 查询符合类型的选项 */
    getSelector() {
      this.getYearSelector()
      this.getCompanySelector()
      this.getDepartmentSelector()
    },
    /** 初始化年份选项 */
    getYearSelector() {
      let year = new Date().getFullYear();
      for (let i = 2021; i <= year; i++) {
        this.yearOptions.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询公司选项集合 */
    getCompanySelector() {
      listCompanySelector().then(response => {
        this.companyOptions = response.rows;
      })
    },
    /** 查询部门选项集合 */
    getDepartmentSelector() {
      this.clearSelect()
      listDepartmentSelector(this.queryParams).then(response => {
        this.departmentOptions = response.rows;
      });
    },
    /** 查询部门汇总列表 */
    getList() {
      this.loading = true;
      listDepartmentReport(this.queryParams).then(response => {
        this.departmentList = response.rows;
        if (this.departmentList.length > 0) {
          this.initTableHeaders(this.departmentList[0].reportMonthlyList.length);
        }
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 初始化表头 */
    initTableHeaders(size) {
      this.tableHeaders = [{mon: "年度合计", key_str: "key0"}]
      for (let i = 1; i < size; i++) {
        this.tableHeaders.push({
          mon: i + "月",
          key_str: "key" + i
        })
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        departments: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.department)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 全选部门选项
    selectAll() {
      this.queryParams.departments = this.departmentOptions.map(option => option.department);
    },
    // 反选部门选项
    invertSelect() {
      const selectedSet = new Set(this.queryParams.departments);
      this.queryParams.departments = this.departmentOptions.filter(option => !selectedSet.has(option.department)).map(option => option.department);
    },
    // 清空部门选项
    clearSelect() {
      this.queryParams.departments = []
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/overtime/report/dept/export', {
        ...this.queryParams
      }, `${this.queryParams.company + this.queryParams.departments}加班汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
.dept-selector-btn {
  width: 59px;
  margin: 5px 5px 5px 10px;
}
</style>

