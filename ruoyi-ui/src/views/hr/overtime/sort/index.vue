<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNum">
        <el-input
          v-model="queryParams.jobNum"
          clearable
          style="width: 100px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="queryParams.type" placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="queryParams.type === 'year'" label="年份">
        <el-date-picker
          required
          style="width: 150px"
          key="picker_year"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy-MM"
          placeholder="选择年">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-else-if="queryParams.type === 'month'" label="月份">
        <el-date-picker
          key="picker_month"
          style="width: 150px"
          required
          v-model="queryParams.month"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-else label="日期">
        <el-date-picker
          key="picker_date"
          style="width: 250px"
          required
          v-model="queryParams.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          placeholder="选择月">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :max-height="tableMaxHeight"
      :cell-class-name="customCellClassName"
      v-loading="loading"
      :data="sortList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="name"/>
      <el-table-column label="工号" align="center" prop="jobNumber"/>
      <el-table-column label="一级部门" :show-overflow-tooltip='true' align="center" prop="firstDept"/>
      <el-table-column v-if="formType === 'year'" label="年份" align="center" prop="monthDate"/>
      <el-table-column v-else-if="formType === 'month'" label="月份" align="center" prop="monthDate"
                       :formatter="monthFormat"/>
      <el-table-column v-else label="日期" align="center" prop="monthDate"/>
      <el-table-column label="时长" align="center" prop="duration"/>
      <el-table-column v-if="formType !== 'year'" label="状态" align="center" prop="exceed">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row[scope.column.property]===1">正常</el-tag>
          <el-tag type="warning" v-else-if="scope.row[scope.column.property]===2">警告</el-tag>
          <el-tag type="danger" v-else-if="scope.row[scope.column.property]===3">超限</el-tag>
          <!--          <el-button style="pointer-events: none" v-if="scope.row[scope.column.property]===1" type="success"-->
          <!--                     icon="el-icon-check" circle/>-->
          <!--          <el-button style="pointer-events: none" v-else-if="scope.row[scope.column.property]===2" type="warning"-->
          <!--                     icon="el-icon-minus" circle/>-->
          <!--          <el-button style="pointer-events: none" v-else-if="scope.row[scope.column.property]===3" type="danger"-->
          <!--                     icon="el-icon-close" circle/>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listSort} from "@/api/hr/overtime/overtime";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "Sort",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 排序警示表格数据
      sortList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNum: null,
        username: null,
        company: null,
        department: null,
        type: 'date',
        year: null,
        month: null,
        date: [],
      },
      // 类型选项
      typeOptions: [{label: "年", value: "year"}, {label: "月", value: "month"}, {label: "日", value: "date"}],
      // form表头类型
      formType: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    /** 默认当前年月日 */
    initParam() {
      this.queryParams.year = this.getDateStr(new Date())
      this.queryParams.month = this.getDateStr(new Date())
      this.queryParams.date.push(this.getDateStr(new Date(new Date().getFullYear(), new Date().getMonth(), 1)))
      this.queryParams.date.push(this.getDateStr(new Date()))
    },
    getDateStr(date) {
      let separator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return year + separator + month + separator + strDate;
    },
    /** 查询排序警示列表 */
    getList() {
      this.loading = true;
      listSort(this.queryParams).then(response => {
        this.sortList = response.rows;
        this.total = response.total;
        this.formType = this.queryParams.type
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jobNum: null,
        name: null,
        department: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/ot/sort/export', {
        ...this.queryParams
      }, `sort_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    },
    /** 月份格式化 */
    monthFormat(row) {
      let monthProperty = row.monthDate;
      let year = monthProperty.split("-")[0];
      let month = monthProperty.split("-")[1];
      if (month >= 10) {
        return monthProperty
      } else {
        return year + "-0" + month
      }
    },
    customCellClassName({row, column, rowIndex, columnIndex}) {
      // if (column.property === 'exceed') {
      //   let columnProperty = this.sortList[rowIndex][column.property];
      //   if (columnProperty === 1) {
      //     return 'greenClass'; // class名称
      //   } else if (columnProperty === 2) {
      //     return 'orangeClass'; // class名称
      //   } else {
      //     return 'redClass';　　// class名称
      //   }
      //   // console.log(this.sortList[rowIndex][column.property])
      // }
    }
  }
};
</script>

<style>

.orangeClass, .greenClass, .redClass {
  position: relative;
  font-size: 0;

  &::before {
    content: '!';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    line-height: 16px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    color: orange;
    border: 2px solid orange;
    border-radius: 50%;
  }
}

.greenClass {
  &::before {
    content: '√';
    color: green;
    border: 2px solid green;
  }
}

.redClass {
  &::before {
    content: '×';
    line-height: 14px;
    color: red;
    border: 2px solid red;
  }
}
</style>
