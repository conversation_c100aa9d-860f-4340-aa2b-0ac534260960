<template>
  <div class="app-container">
    <el-row>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
        <el-form-item label="公司名称" prop="year">
          <el-select v-model="queryParams.company" clearable placeholder="请选择" style="width: 120px">
            <el-option
              v-for="item in companyOptions"
              :key="item.id"
              :label="item.company"
              :value="item.company">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门类型" prop="year">
          <el-select v-model="queryParams.departmentType" placeholder="请选择"
                     @change="getDepartmentSelector()"
                     style="width: 100px">
            <el-option
              v-for="item in departmentTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门名称" prop="year">
          <el-select v-model="queryParams.departments"
                     :show-overflow-tooltip='true'
                     clearable
                     filterable
                     multiple
                     collapse-tags
                     placeholder="请选择"
                     style="width: 200px">
            <el-row>
              <el-button type="primary" class="dept-selector-btn" @click="selectAll">全选</el-button>
              <el-button type="primary" class="dept-selector-btn" @click="invertSelect">反选</el-button>
              <el-button type="info" class="dept-selector-btn" @click="clearSelect">清空</el-button>
            </el-row>
            <el-option
              v-for="item in departmentOptions"
              :key="item.id"
              :label="item.department"
              :value="item.department">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年份" prop="year" label-width="70px">
          <el-select v-model="queryParams.year" clearable placeholder="请选择" style="width: 80px">
            <el-option
              v-for="item in yearOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button style="margin-left: 50px" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row>
      <div id="echart" ref="echart">echart</div>
    </el-row>


  </div>
</template>

<script>
import {
  listCompanySelector,
  listDepartmentSelector,
  chartDepartment
} from "@/api/hr/overtime/overtime";

export default {
  name: "DepartmentReportChart",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 部门汇总表格数据
      departmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        departmentType: 1,
        company: 'AKESO',
        departments: [],
        year: new Date().getFullYear()
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 年份选项
      yearOptions: [],
      // 公司选项
      companyOptions: [],
      // 部门类型选项
      departmentTypeOptions: [
        {"label": '一级部门', "value": 1},
        {"label": '二级部门', "value": 2}
      ],
      // 月份总数
      monthlyLength: 0,
      // 部门选项
      departmentOptions: [],
      totalMap: {},
      otToLeaveMap: {},
      otToSalaryWorkdayMap: {},
      otToSalaryWeekendMap: {},
      otToSalaryOfficialMap: {},
      avgDurationMap: {},
      option: {},
      dataMap: {},
    };
  },
  created() {
    this.getList();
    this.initParam();
  },
  mounted() {
    this.getCharts();
  },
  methods: {
    initParam() {
      // 初始化数据字典
      this.initDataMap(this.monthlyLength);
      // 初始化图表参数
      this.updateChartOption();
      // 初始化选项
      this.initSelector();
    },
    // 查询符合类型的选项
    initSelector() {
      this.getYearSelector()
      this.getCompanySelector()
      this.getDepartmentSelector()
    },
    // 初始化年选项
    getYearSelector() {
      let year = new Date().getFullYear();
      for (let i = 2021; i <= year; i++) {
        this.yearOptions.push({
          "label": i,
          "value": i
        })
      }
    },
    // 查询公司选项集合\
    getCompanySelector() {
      listCompanySelector().then(response => {
        this.companyOptions = response.rows;
      })
    },
    // 查询部门选项集合
    getDepartmentSelector() {
      this.clearSelect()
      listDepartmentSelector(this.queryParams).then(response => {
        this.departmentOptions = response.rows;
      });
    },
    // 查询部门汇总列表
    getList() {
      chartDepartment(this.queryParams).then(response => {
        this.dataList = response.rows;

        this.monthlyLength = this.dataList[0].reportMonthlyList.length

        this.initDataMap(this.monthlyLength)

        for (let i = 0; i < this.dataList.length; i++) {

          this.departmentList.push(this.dataList[i].department)

          for (let j = 0; j < this.monthlyLength; j++) {
            this.totalMap[j + ''].push(this.dataList[i].reportMonthlyList[j].total)
            this.otToLeaveMap[j + ''].push(this.dataList[i].reportMonthlyList[j].otToLeave)
            this.otToSalaryWorkdayMap[j + ''].push(this.dataList[i].reportMonthlyList[j].otToSalaryWorkday)
            this.otToSalaryWeekendMap[j + ''].push(this.dataList[i].reportMonthlyList[j].otToSalaryWeekend)
            this.otToSalaryOfficialMap[j + ''].push(this.dataList[i].reportMonthlyList[j].otToSalaryOfficial)
            this.avgDurationMap[j + ''].push(this.dataList[i].reportMonthlyList[j].avgDuration)
          }
        }
        this.getCharts()
      });
    },
    // 初始化数据Map
    initDataMap(monthlyLength) {
      // 每次重进刷新部门的数据
      this.option = {}
      this.departmentList = []
      for (let i = 0; i < monthlyLength; i++) {
        this.totalMap[i + ''] = []
        this.otToLeaveMap[i + ''] = []
        this.otToSalaryWorkdayMap[i + ''] = []
        this.otToSalaryWeekendMap[i + ''] = []
        this.otToSalaryOfficialMap[i + ''] = []
        this.avgDurationMap[i + ''] = []
      }
      this.dataMap.total = this.dataFormatter(this.totalMap)
      this.dataMap.otToLeave = this.dataFormatter(this.otToLeaveMap)
      this.dataMap.otToSalaryWorkday = this.dataFormatter(this.otToSalaryWorkdayMap)
      this.dataMap.otToSalaryWeekend = this.dataFormatter(this.otToSalaryWeekendMap)
      this.dataMap.otToSalaryOfficial = this.dataFormatter(this.otToSalaryOfficialMap)
      this.dataMap.avgDuration = this.dataFormatter(this.avgDurationMap)
    },
    // 初始化图表参数
    updateChartOption() {
      this.option = {
        baseOption: {
          timeline: {
            axisType: 'category',
            // realtime: false,
            loop: false,
            autoPlay: false,
            // currentIndex: 2,
            playInterval: 5000,
            controlStyle: {
              position: 'right',
              show: false
            },
            data: [
              this.queryParams.year + '年度合计',
            ],
            label: {
              formatter: function (s) {
                let tempYear = s.substr(0, 4)
                s = s.replace("-", "");
                s = s.replace(tempYear, "")
                if (s !== '年度合计') {
                  s = Number(s) + '月'
                }
                return s;
              }
            }
          },
          title: {
            subtext: '数据来源于考勤系统'
          },
          tooltip: {},
          legend: {
            left: 'right',
            data: ['加班计休', '加班计薪(工作日)', '加班计薪(周末)', '加班计薪(法定)', '总和', '人均'],
            selected: {
              '总和': false,
              '加班计休': false,
              '加班计薪(法定)': false,
              '加班计薪(周末)': false,
              '加班计薪(工作日)': false
            }
          },
          calculable: true,
          grid: {
            top: 80,
            bottom: 100,
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
                label: {
                  show: true,
                  formatter: function (params) {
                    return params.value.replace('\n', '');
                  }
                }
              }
            }
          },
          yAxis: [
            {
              type: 'category',
              axisLabel: {interval: 0, rotate: 30,},
              data: this.departmentList,
              splitLine: {show: false}
            }
          ],
          xAxis: [
            {
              type: 'value',
              name: '加班时长(小时)',
              nameLocation: 'end',
            }
          ],
          series: [
            {name: '加班计休', type: 'bar'},
            {name: '加班计薪(工作日)', type: 'bar'},
            {name: '加班计薪(周末)', type: 'bar'},
            {name: '加班计薪(法定)', type: 'bar'},
            {name: '总和', type: 'bar'},
            {name: '人均', type: 'bar'},
          ]
        },
        options: [
          {
            title: {text: this.queryParams.year + '年部门加班年度汇总数据'},
            series: [
              {data: this.dataMap.otToLeave['0']},
              {data: this.dataMap.otToSalaryWorkday['0']},
              {data: this.dataMap.otToSalaryWeekend['0']},
              {data: this.dataMap.otToSalaryOfficial['0']},
              {data: this.dataMap.total['0']},
              {data: this.dataMap.avgDuration['0']},
            ]
          },
        ]
      };
      for (let i = 1; i < this.monthlyLength; i++) {
        if (i < 10) {
          this.option.baseOption.timeline.data.push(this.queryParams.year + "-0" + i)
        } else {
          this.option.baseOption.timeline.data.push(this.queryParams.year + "-" + i)
        }
        let tempOption = {
          title: {text: this.queryParams.year + '年部门加班' + i + '月汇总数据'},
          series: [
            {data: this.dataMap.otToLeave[i + '']},
            {data: this.dataMap.otToSalaryWorkday[i + '']},
            {data: this.dataMap.otToSalaryWeekend[i + '']},
            {data: this.dataMap.otToSalaryOfficial[i + '']},
            {data: this.dataMap.total[i + '']},
            {data: this.dataMap.avgDuration[i + '']},
          ]
        }
        this.option.options.push(tempOption)
      }
    },
    // 表单重置
    reset() {
      this.form = {
        department: []
      };
      this.resetForm("form");
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList();
    },
    // 格式化数据
    dataFormatter(obj) {
      let pList = this.departmentList;
      let temp;
      for (let month = 0; month < this.monthlyLength; month++) {
        let max = 0;
        let sum = 0;
        temp = obj[month];
        for (let i = 0, l = temp.length; i < l; i++) {
          max = Math.max(max, temp[i]);
          sum += temp[i];
          obj[month][i] = {
            name: pList[i],
            value: temp[i]
          };
        }
        obj[month + 'max'] = Math.floor(max / 100) * 100;
        obj[month + 'sum'] = sum;
      }
      return obj;
    },
    // 更新图表
    getCharts() {
      this.updateChartOption()
      //基于准备好的dom，初始化echarts实例
      let departOvertimeChart = this.$echarts.init(this.$refs.echart)

      this.dataMap.total = this.dataFormatter(this.totalMap)

      this.dataMap.otToLeave = this.dataFormatter(this.otToLeaveMap)
      this.dataMap.otToSalaryWorkday = this.dataFormatter(this.otToSalaryWorkdayMap)
      this.dataMap.otToSalaryWeekend = this.dataFormatter(this.otToSalaryWeekendMap)
      this.dataMap.otToSalaryOfficial = this.dataFormatter(this.otToSalaryOfficialMap)
      this.dataMap.avgDuration = this.dataFormatter(this.avgDurationMap)

      // 根据部门数量动态设置图表的高度
      departOvertimeChart.setOption(this.option)
      let tempHeight = this.departmentList.length * 50 + 200
      departOvertimeChart.getDom().style.height = tempHeight + "px";
      departOvertimeChart.resize();

    },
    // 全选部门选项
    selectAll() {
      this.queryParams.departments = this.departmentOptions.map(option => option.department);
    },
    // 反选部门选项
    invertSelect() {
      const selectedSet = new Set(this.queryParams.departments);
      this.queryParams.departments = this.departmentOptions.filter(option => !selectedSet.has(option.department)).map(option => option.department);
    },
    // 清空部门选项
    clearSelect() {
      this.queryParams.departments = []
    },
  }
};
</script>

<style lang="scss" scoped>
#echart {
  height: 700px;
  margin: 50px;
}

.dept-selector-btn {
  width: 59px;
  margin: 5px 5px 5px 10px;
}
</style>

