<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="employeeName">
        <el-input
          v-model="queryParams.employeeName"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="工号" prop="jobNumber">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="单号" prop="nid">
        <el-input
          v-model="queryParams.nid"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 130px"
        />
      </el-form-item>
      <el-form-item label="加班日期" prop="overtimeDate">
        <el-date-picker
          :clearable="false"
          style="width: 250px"
          v-model="queryParams.overtimeDate"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"/>
      </el-form-item>
      <el-form-item label="结算方式" prop="settlement" label-width="70px">
        <el-select v-model="queryParams.settlement" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in settlementOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="加班类型" prop="type" label-width="70px">
        <el-select v-model="queryParams.type" clearable placeholder="请选择" style="width: 120px">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:ot:detail:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :max-height="tableMaxHeight" :data="detailList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="employeeName"/>
      <el-table-column label="工号" align="center" prop="jobNumber"/>
      <el-table-column label="日期" align="center" prop="todayDate" width="100"/>
      <el-table-column label="员工类型" align="center" prop="employeeType" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="职位" align="center" prop="post" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="职级" align="center" prop="jobGrade" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="申请开始时间" align="center" prop="startExtraWorkDate" width="70"/>
      <el-table-column label="申请结束时间" align="center" prop="endExtraWorkDate" width="70"/>
      <el-table-column label="实际开始时间" align="center" prop="realStartExtraWork" width="70"/>
      <el-table-column label="实际结束时间" align="center" prop="realEndExtraWork" width="70"/>
      <el-table-column label="加班事由" align="center" prop="extraText" width="120" :show-overflow-tooltip='true'/>
      <el-table-column label="打卡地点" align="center" prop="clockInLocation" width="120"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="加班类型" align="center" prop="realExtraWorkType" width="60"
                       :show-overflow-tooltip='true'/>
      <el-table-column label="结算方式" align="center" prop="settlement" width="50"/>
      <el-table-column label="加班(h)" align="center" prop="extraWorkNum" width="50"/>
      <el-table-column label="工作日加班(h)" align="center" prop="workExtraWorkNum" width="70"/>
      <el-table-column label="周末加班(h)" align="center" prop="weedExtraWorkNum" width="70"/>
      <el-table-column label="节假日加班(h)" align="center" prop="holidayExtraWorkNum" width="70"/>
      <el-table-column label="休息时长" align="center" prop="eatHour" width="50"/>
      <el-table-column label="交通补贴" align="center" prop="tollSubsidy" width="50"/>
      <el-table-column label="就餐补贴" align="center" prop="mealSubsidy" width="50"/>
      <el-table-column label="强制休息次数" align="center" prop="mandatoryRestCount" width="70"/>
      <el-table-column label="就餐次数" align="center" prop="mealCount" width="50"/>
      <el-table-column label="申请表编号" align="center" prop="nid" width="180" :show-overflow-tooltip='true'/>
      <el-table-column label="公司" align="center" prop="company" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" width="130" :show-overflow-tooltip='true'/>
      <el-table-column label="二级部门" align="center" prop="secondDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" width="110" :show-overflow-tooltip='true'/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listDetail} from "@/api/hr/overtime/overtime";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "Detail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 加班明细表格数据
      detailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 就餐时长时间范围
      daterangeTodayDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobNumber: null,
        employeeName: null,
        overtimeDate: null,
        company: null,
        department: null,
        type: null,
        settlement: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '上月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 上月第一天
              const endDate = new Date(now.getFullYear(), now.getMonth(), 0); // 上月最后一天
              endDate.setDate(endDate.getDate()); // 将时间设置为上月最后一天 23:59:59（如果不加这句，时间会变为下月的第一天 00:00:00）
              picker.$emit('pick', [startDate, endDate]);
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const now = new Date();
              const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
              picker.$emit('pick', [startDate, now]);
            }
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0, 1); // 本年第一天
              picker.$emit('pick', [start, end]);
            }
          },]
      },
      typeOptions: [
        {"label": '工作日加班', "value": "WEEKDAY"},
        {"label": '周六日加班', "value": "WEEKEND"},
        {"label": '法定假日加班', "value": "HOLIDAY"}
      ],
      settlementOptions: [
        {"label": '计薪', "value": "SALARY"},
        {"label": '计休', "value": "COMPENSATORY_LEAVE"}
      ],
    };
  },
  created() {
    this.initParam();
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 230;
    }
  },
  methods: {
    initParam() {
      const now = new Date();
      // 本月第一天
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      this.queryParams.overtimeDate = [parseTime(startDate, '{y}-{m}-{d}'), parseTime(now, '{y}-{m}-{d}')]
    },
    /** 查询加班明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeTodayDate && '' != this.daterangeTodayDate) {
        this.queryParams.params["beginTodayDate"] = this.daterangeTodayDate[0];
        this.queryParams.params["endTodayDate"] = this.daterangeTodayDate[1];
      }
      listDetail(this.queryParams).then(response => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nid: null,
        uid: null,
        jobNum: null,
        username: null,
        todayDate: null,
        company: null,
        department: null,
        startExtraWorkDate: null,
        endExtraWorkDate: null,
        realStartExtraWork: null,
        realEndExtraWork: null,
        extraWorkType: null,
        settlement: null,
        extraWorkNum: null,
        extraWorkEat: null,
        tollSubsidy: null,
        validity: null,
        mealSubsidy: null,
        weedExtraWorkNum: null,
        holidayExtraWorkNum: null,
        cardLog: null,
        realExtraWorkType: null,
        workExtraWorkNum: null,
        updateClock: null,
        extraText: null,
        eatHour: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeTodayDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/overtime/detail/export', {
        ...this.queryParams
      }, `加班明细_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
