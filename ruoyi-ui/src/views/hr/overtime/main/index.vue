<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="loginName">
        <el-input
          v-model="queryParams.jobNumber"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" clearable style="width: 120px" placeholder="请选择">
          <el-option
            v-for="item in yearOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:ot:report:main:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :max-height="tableMaxHeight" v-loading="loading" :data="mainList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="姓名" fixed align="center" prop="username" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="工号" align="center" prop="jobNumber" width="80" :show-overflow-tooltip='true'/>
      <el-table-column label="所属公司" align="center" prop="company" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="一级部门" align="center" prop="firstDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="入职日期" align="center" prop="entryDate" width="100"/>
      <template v-for="(item, index) in tableHeaders">
        <el-table-column align="center" :label="item.mon" :key="item.key_str">
          <el-table-column label="加班计休时" align="center" width="90">
            <template slot-scope="scope">
              {{ scope.row.reportMonthlyList[index].otToLeave }}
            </template>
          </el-table-column>
          <el-table-column label="加班计薪时长H" align="center">
            <el-table-column label="工作日" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryWorkday }}
              </template>
            </el-table-column>
            <el-table-column label="周末" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryWeekend }}
              </template>
            </el-table-column>
            <el-table-column label="法定" align="center">
              <template slot-scope="scope">
                {{ scope.row.reportMonthlyList[index].otToSalaryOfficial }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="总和" align="center">
            <template slot-scope="scope">
              {{ scope.row.reportMonthlyList[index].total }}
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <el-table-column label="二级部门" align="center" prop="secondDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="三级部门" align="center" prop="thirdDept" width="110" :show-overflow-tooltip='true'/>
      <el-table-column label="四级部门" align="center" prop="fourthDept" width="110" :show-overflow-tooltip='true'/>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listMainReport} from "@/api/hr/overtime/overtime";
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "Main",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedAttendanceReport: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工信息表格数据
      mainList: [],
      // 考勤档案表格数据
      attendanceReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        jobNumber: null,
        company: null,
        department: null,
        year: new Date().getFullYear()
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 年份选项
      yearOptions: [],
      // 表头
      tableHeaders: [],
    };
  },
  created() {
    this.init();
    this.getList();
  },
  computed:{
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight -230;
    }
  },
  methods: {
    init() {
      /** 初始化年选项 */
      let year = new Date().getFullYear();
      for (let i = 2021; i <= year; i++) {
        this.yearOptions.push({
          "label": i,
          "value": i
        })
      }
    },
    /** 查询员工信息列表 */
    getList() {
      this.loading = true;
      listMainReport(this.queryParams).then(response => {
        this.mainList = response.rows;
        if (this.mainList.length > 0) {
          this.initTableHeaders(this.mainList[0].reportMonthlyList.length)
        }
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 初始化表头 */
    initTableHeaders(size) {
      this.tableHeaders = [{mon: "年度合计", key_str: "key0"}]
      for (let i = 1; i < size; i++) {
        this.tableHeaders.push({
          mon: i + "月",
          key_str: "key" + i
        })
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        idCard: null,
        birthday: null,
        email: null,
        entryDate: null,
        status: null,
        mobile: null,
        orgId: null,
        deptId: null,
        loginName: null,
        postId: null,
        post: null,
        orgName: null,
        deptName: null,
        deptNameAll: null,
        jobGrade: null
      };
      this.attendanceReportList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 考勤档案序号 */
    rowAttendanceReportIndex({row, rowIndex}) {
      row.index = rowIndex + 1;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('hr/overtime/report/main/export', {
        ...this.queryParams
      }, `员工加班汇总_${parseTime(new Date(), '{y}{m}{d}{h}{i}{s}')}.xlsx`)
    }
  }
};
</script>
