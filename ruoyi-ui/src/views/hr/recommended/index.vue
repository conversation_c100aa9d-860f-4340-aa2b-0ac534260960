<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
             label-width="68px">
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="queryParams.signDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label-width="auto" label="推荐人" prop="reference">
        <el-input
          v-model="queryParams.reference"
          placeholder="请输入推荐人名称或工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="被推荐人" prop="recommendedPerson">
        <el-input
          v-model="queryParams.recommendedPerson"
          placeholder="请输入被推荐人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="招聘HR" prop="HR">
        <el-input
          v-model="queryParams.HR"
          placeholder="请输入招聘HR姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="OA单号" prop="singleNumber">
        <el-input
          v-model="queryParams.singleNumber"
          placeholder="请输入OA单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label-width="auto" label="被推荐人部门" prop="departments">
        <el-input
          v-model="queryParams.departments"
          placeholder="请输入被推荐人部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['hr:recommended:export']"
          >导出
          </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recommendedList" :max-height="tableMaxHeight" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="推荐人" align="center" prop="reference" fixed :show-overflow-tooltip="true"/>
      <el-table-column label="推荐人工号" align="center" prop="jobNumber" :show-overflow-tooltip="true"/>
      <el-table-column label="推荐人部门" align="center" prop="department" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="被推荐人" align="center" prop="recommendedPerson" :show-overflow-tooltip="true"/>
      <el-table-column label="被推荐人工号" align="center" prop="jobNumbers" :show-overflow-tooltip="true"/>
      <el-table-column label="被推荐人职位" align="center" prop="post" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="被推荐人入职日期" align="center" prop="entryDate" width="100"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="被推荐人转正日期" align="center" prop="positiveDate" width="100"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.positiveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="被推荐人离职日期" align="center" prop="leaveDate" width="100"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.leaveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="被推荐人部门" align="center" prop="departments" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="推荐费" align="center" prop="bonus" :show-overflow-tooltip="true"/>
      <el-table-column label="发放方式" align="center" prop="distributionWay" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{scope.row.distributionWay==1?"财务报销":"工资发放"}}</span>
        </template>
      </el-table-column>
      <el-table-column label="招聘HR" align="center" prop="hr" :show-overflow-tooltip="true"/>
      <el-table-column label="申请日期" align="center" prop="applicationDate" width="100" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发放日期" align="center" prop="distributionDate" width="100"
                       :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.distributionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="OA单号" align="center" prop="singleNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="文档状态" align="center" prop="documentStatus" :show-overflow-tooltip="true"/>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {queryRecommended} from "@/api/hr/recommended/recommended";
import {parseTime} from "../../../utils/ruoyi";

export default {
    name: "Recommended",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 推荐工作奖金申请
        recommendedList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          signDate: '',
          pageNum: 1,
          pageSize: 10,
          applyForStartDate: null,
          applyForEndDate: null,
          reference: null,
          jobNumber: null,
          HR: null,
          singleNumber: null,
          departments: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {},
        pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }]
        },
      };
    },
    created() {
      this.defaultDate();
      this.getList();
    },
    computed: {
      tableMaxHeight() {
        const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        return screenHeight - 230;
      }
    },
    methods: {
      parseTime,
      /** 查询第三方供应商数据 */
      getList() {
        this.loading = true;
        this.queryParams.params = {};
        this.queryParams.applyForStartDate = this.queryParams.signDate[0];
        this.queryParams.applyForEndDate = this.queryParams.signDate[1];
        queryRecommended(this.queryParams).then(response => {
          this.recommendedList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          applyForStartDate: null,
          applyForEndDate: null,
          reference: null,
          jobNumber: null,
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams.signDate = ''
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.tableName)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 导出按钮操作 */
      handleExport() {
        var date = new Date()
        var year = date.getFullYear().toString()
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
        var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
        var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
        var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
        var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
        console.log(dateTime)
        this.download('hr/recommended/export', {
          ...this.queryParams
        }, `推荐工作奖金申请数据_${dateTime}.xlsx`)
      },
      defaultDate() {
        var date = new Date()
        var year = date.getFullYear().toString()
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
        var da = new Date(date.getFullYear(), month, 0).getDate()
        da < 10 ? '0' + da.toString() : da.toString()
        var beg = year + '-01' + '-01'
        var end = year + '-12' + '-' + da
        this.queryParams.signDate = [beg, end] //将值设置给插件绑定的数据
      }
    },
    watch: {
      // 监听日期清理后数据为null进行处理否则会报错
      'queryParams.signDate'(newVal) {
        if (newVal == null) {
          this.queryParams.signDate = ''
        }
      }
    }


  }
</script>
