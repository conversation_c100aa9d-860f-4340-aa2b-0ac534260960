<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="商业编码" prop="businessCoding">
        <el-input
          v-model="queryParams.businessCoding"
          placeholder="请输入商业编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业级别" prop="commercialGrade">
        <el-input
          v-model="queryParams.commercialGrade"
          placeholder="请输入商业级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业省份" prop="commercialProvince">
        <el-input
          v-model="queryParams.commercialProvince"
          placeholder="请输入商业省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业名称" prop="businessName">
        <el-input
          v-model="queryParams.businessName"
          placeholder="请输入商业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="供应商编码" prop="vendorCode">
        <el-input
          v-model="queryParams.vendorCode"
          placeholder="请输入供应商编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商名称" prop="vendorName">
        <el-input
          v-model="queryParams.vendorName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="购进日期">
        <el-date-picker
          v-model="daterangePurchaseDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ddi:ddiDayProcurement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ddiDayProcurementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键id" align="center" prop="id" />-->
      <el-table-column label="执行月" align="center" prop="executionMonth"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务月" align="center" prop="businessMonth"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业级别" align="center" prop="commercialGrade" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业省份" align="center" prop="commercialProvince" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业编码" align="center" prop="businessCoding"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业名称" align="center" prop="businessName"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="购进日期" align="center" prop="purchaseDate"  width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.purchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商编码" align="center" prop="vendorCode"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商名称" align="center" prop="vendorName"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品编码" align="center" prop="standardProductCodes"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品名称" align="center" prop="standardProductName"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品规格" align="center" prop="standardProductSpecification"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准单位" align="center" prop="standardUnits"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准数量" align="center" prop="standardQuantity"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单价" align="center" prop="unitPrice"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amount"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准批号" align="center" prop="standardLotNumber"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="上游供货商编码" align="center" prop="upstreamVendorCode"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="上游供货商名称" align="center" prop="upstreamVendorName"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品编码" align="center" prop="productCode"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品名称" align="center" prop="productName"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品规格" align="center" prop="productSpecifications"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="生产厂家" align="center" prop="manufacturer"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量单位" align="center" prop="quantityUnits"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="quantity"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品单价(含税)" align="center" prop="productUnitPrice"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品金额(含税)" align="center" prop="productAmount"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品批号" align="center" prop="productLotNumber"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="效期" align="center" prop="expirationDate"  width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" align="center" prop="dataType"  width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="运维状态" align="center" prop="maintenanceStatus"  width="200" :show-overflow-tooltip="true"/>
<!--      <el-table-column label="创建时间" align="center" prop="creationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updated" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updated, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="删除时间" align="center" prop="deletionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deletionTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="删除状态" align="center" prop="deleteStatus" />-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDdiDayProcurement} from "@/api/ddi/ddiDayProcurement";

export default {
  name: "DdiDayProcurement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日采购表格数据
      ddiDayProcurementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangePurchaseDate: [],
      // 删除状态时间范围
      daterangeExpirationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        purchaseDate: null,
        vendorCode: null,
        vendorName: null,
        standardProductCodes: null,
        standardProductName: null,
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        unitPrice: null,
        amount: null,
        standardLotNumber: null,
        upstreamVendorCode: null,
        upstreamVendorName: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productUnitPrice: null,
        productAmount: null,
        productLotNumber: null,
        expirationDate: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询日采购列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePurchaseDate && '' != this.daterangePurchaseDate) {
        this.queryParams.params["beginPurchaseDate"] = this.daterangePurchaseDate[0];
        this.queryParams.params["endPurchaseDate"] = this.daterangePurchaseDate[1];
      }
      if (null != this.daterangeExpirationDate && '' != this.daterangeExpirationDate) {
        this.queryParams.params["beginExpirationDate"] = this.daterangeExpirationDate[0];
        this.queryParams.params["endExpirationDate"] = this.daterangeExpirationDate[1];
      }
      listDdiDayProcurement(this.queryParams).then(response => {
        this.ddiDayProcurementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        purchaseDate: null,
        vendorCode: null,
        vendorName: null,
        standardProductCodes: null,
        standardProductName: null,
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        unitPrice: null,
        amount: null,
        standardLotNumber: null,
        upstreamVendorCode: null,
        upstreamVendorName: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productUnitPrice: null,
        productAmount: null,
        productLotNumber: null,
        expirationDate: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePurchaseDate = [];
      this.daterangeExpirationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ddi/ddiDayProcurement/export', {
        ...this.queryParams
      }, `ddiDayProcurement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
