<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <!--      <el-form-item label="二级部门" prop="secondaryDepartment">
              <el-input
                v-model="queryParams.secondaryDepartment"
                placeholder="请输入二级部门"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>-->
      <el-form-item label="三级部门" prop="thirdLevelDepartments">
        <el-input
          v-model="queryParams.thirdLevelDepartments"
          placeholder="请输入三级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="四级部门" prop="fourthLevelDepartment">
        <el-input
          v-model="queryParams.fourthLevelDepartment"
          placeholder="请输入四级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商业省份" prop="commercialProvince">
        <el-input
          v-model="queryParams.commercialProvince"
          placeholder="请输入商业省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商业级别" prop="commercialGrade">
        <el-input
          v-model="queryParams.commercialGrade"
          placeholder="请输入商业级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商务代表" prop="businessRepresentative">
        <el-input
          v-model="queryParams.businessRepresentative"
          placeholder="请输入商务代表"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户编码" prop="customerCode">
        <el-input
          v-model="queryParams.customerCode"
          placeholder="请输入客户编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品编码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="进销存状况" prop="reason">
        <el-input
          v-model="queryParams.inventoryStatus"
          placeholder="请输入进销存状况"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="年月" prop="yearMonth">
        <el-input
          v-model="queryParams.yearMonth"
          placeholder="请输入年月"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['ddi:inventoryReport:add']"
              >新增
              </el-button>
            </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ddi:inventoryReport:edit']"
        >修改
        </el-button>
      </el-col>
      <!--      <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['ddi:inventoryReport:remove']"
              >删除
              </el-button>
            </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ddi:inventoryReport:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inventoryReportList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="ID" align="center" prop="id"/>-->
      <el-table-column label="二级部门" align="center" prop="secondaryDepartment" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="三级部门" align="center" prop="thirdLevelDepartments" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="四级部门" align="center" prop="fourthLevelDepartment" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="商业省份" align="center" prop="commercialProvince" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业级别" align="center" prop="commercialGrade" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商务代表" align="center" prop="businessRepresentative" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="客户编码" align="center" prop="customerCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="客户名称" align="center" prop="customerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品编码" align="center" prop="productCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期初结存" align="center" prop="openingBalance" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="购进数" align="center" prop="purchaseQuantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="购进其他" align="center" prop="purchaseOther" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="购进汇总" align="center" prop="purchaseSummary" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="销售数" align="center" prop="salesQuantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="销售其他" align="center" prop="salesOthers" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="销售汇总" align="center" prop="salesSummary" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="其他数据的相关信息" align="center" prop="otherData" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="理论库存" align="center" prop="theoreticalStock" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="打印时点流向库存" align="center" prop="flowToInventory" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="超期销售" align="center" prop="overdueSales" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="超期购进" align="center" prop="overduePurchase" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期末结存" align="center" prop="closingBalance" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="进销存状况" align="center" prop="inventoryStatus" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="进销存不成立原因" align="center" prop="reason" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="购进校验" align="center" prop="purchaseVerification" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="校验" align="center" prop="verification" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="备注" align="center" prop="remarks" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="年月" align="center" prop="yearMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="当月库存可销天数" align="center" prop="marketableDays" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="较上月天数变化" align="center" prop="daysChange" width="200" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['ddi:inventoryReport:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['ddi:inventoryReport:remove']"
                >删除
                </el-button>
              </template>
            </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改进销存报表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="二级部门" prop="secondaryDepartment">
          <el-input v-model="form.secondaryDepartment" placeholder="请输入二级部门"/>
        </el-form-item>
        <el-form-item label="三级部门" prop="thirdLevelDepartments">
          <el-input v-model="form.thirdLevelDepartments" placeholder="请输入三级部门"/>
        </el-form-item>
        <el-form-item label="四级部门" prop="fourthLevelDepartment">
          <el-input v-model="form.fourthLevelDepartment" placeholder="请输入四级部门"/>
        </el-form-item>
        <el-form-item label="商业省份" prop="commercialProvince">
          <el-input v-model="form.commercialProvince" placeholder="请输入商业省份"/>
        </el-form-item>
        <el-form-item label="商业级别" prop="commercialGrade">
          <el-input v-model="form.commercialGrade" placeholder="请输入商业级别"/>
        </el-form-item>
        <el-form-item label="商务代表" prop="businessRepresentative">
          <el-input v-model="form.businessRepresentative" placeholder="请输入商务代表"/>
        </el-form-item>
        <el-form-item label="客户编码" prop="customerCode">
          <el-input v-model="form.customerCode" placeholder="请输入客户编码"/>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="form.customerName" placeholder="请输入客户名称"/>
        </el-form-item>
        <el-form-item label="产品编码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入产品编码"/>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称"/>
        </el-form-item>
        <el-form-item label="期初结存" prop="openingBalance">
          <el-input v-model="form.openingBalance" placeholder="请输入期初结存"/>
        </el-form-item>
        <el-form-item label="购进数" prop="purchaseQuantity">
          <el-input v-model="form.purchaseQuantity" placeholder="请输入购进数"/>
        </el-form-item>
        <el-form-item label="购进其他" prop="purchaseOther">
          <el-input v-model="form.purchaseOther" placeholder="请输入购进其他"/>
        </el-form-item>
        <el-form-item label="购进汇总" prop="purchaseSummary">
          <el-input v-model="form.purchaseSummary" placeholder="请输入购进汇总"/>
        </el-form-item>
        <el-form-item label="销售数" prop="salesQuantity">
          <el-input v-model="form.salesQuantity" placeholder="请输入销售数"/>
        </el-form-item>
        <el-form-item label="销售其他" prop="salesOthers">
          <el-input v-model="form.salesOthers" placeholder="请输入销售其他"/>
        </el-form-item>
        <el-form-item label="销售汇总" prop="salesSummary">
          <el-input v-model="form.salesSummary" placeholder="请输入销售汇总"/>
        </el-form-item>
        <el-form-item label="其他数据的相关信息" prop="otherData">
          <el-input v-model="form.otherData" placeholder="请输入其他数据的相关信息"/>
        </el-form-item>
        <el-form-item label="理论库存" prop="theoreticalStock">
          <el-input v-model="form.theoreticalStock" placeholder="请输入理论库存"/>
        </el-form-item>
        <el-form-item label="打印时点流向库存" prop="flowToInventory">
          <el-input v-model="form.flowToInventory" placeholder="请输入打印时点流向库存"/>
        </el-form-item>
        <el-form-item label="超期销售" prop="overdueSales">
          <el-input v-model="form.overdueSales" placeholder="请输入超期销售"/>
        </el-form-item>
        <el-form-item label="超期购进" prop="overduePurchase">
          <el-input v-model="form.overduePurchase" placeholder="请输入超期购进"/>
        </el-form-item>
        <el-form-item label="期末结存" prop="closingBalance">
          <el-input v-model="form.closingBalance" placeholder="请输入期末结存"/>
        </el-form-item>
        <el-form-item label="进销存不成立原因" prop="reason">
          <el-input v-model="form.reason" placeholder="请输入进销存不成立原因"/>
        </el-form-item>
        <el-form-item label="购进校验" prop="purchaseVerification">
          <el-input v-model="form.purchaseVerification" placeholder="请输入购进校验"/>
        </el-form-item>
        <el-form-item label="校验" prop="verification">
          <el-input v-model="form.verification" placeholder="请输入校验"/>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注"/>
        </el-form-item>
        <el-form-item label="年月" prop="yearMonth">
          <el-input v-model="form.yearMonth" placeholder="请输入年月"/>
        </el-form-item>
        <el-form-item label="当月库存可销天数" prop="marketableDays">
          <el-input v-model="form.marketableDays" placeholder="请输入当月库存可销天数"/>
        </el-form-item>
        <el-form-item label="较上月天数变化" prop="daysChange">
          <el-input v-model="form.daysChange" placeholder="请输入较上月天数变化"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInventoryReport,
  getInventoryReport,
  delInventoryReport,
  addInventoryReport,
  updateInventoryReport
} from "@/api/ddi/inventoryReport";

export default {
  name: "InventoryReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 进销存报表表格数据
      inventoryReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        secondaryDepartment: null,
        thirdLevelDepartments: null,
        fourthLevelDepartment: null,
        commercialProvince: null,
        commercialGrade: null,
        businessRepresentative: null,
        customerCode: null,
        customerName: null,
        productCode: null,
        productName: null,
        openingBalance: null,
        purchaseQuantity: null,
        purchaseOther: null,
        purchaseSummary: null,
        salesQuantity: null,
        salesOthers: null,
        salesSummary: null,
        otherData: null,
        theoreticalStock: null,
        flowToInventory: null,
        overdueSales: null,
        overduePurchase: null,
        closingBalance: null,
        inventoryStatus: null,
        reason: null,
        verification: null,
        remarks: null,
        yearMonth: null,
        marketableDays: null,
        daysChange: null,
        purchaseVerification: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询进销存报表列表 */
    getList() {
      this.loading = true;
      listInventoryReport(this.queryParams).then(response => {
        this.inventoryReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        secondaryDepartment: null,
        thirdLevelDepartments: null,
        fourthLevelDepartment: null,
        commercialProvince: null,
        commercialGrade: null,
        businessRepresentative: null,
        customerCode: null,
        customerName: null,
        productCode: null,
        productName: null,
        openingBalance: null,
        purchaseQuantity: null,
        purchaseOther: null,
        purchaseSummary: null,
        salesQuantity: null,
        salesOthers: null,
        salesSummary: null,
        otherData: null,
        theoreticalStock: null,
        flowToInventory: null,
        overdueSales: null,
        overduePurchase: null,
        closingBalance: null,
        inventoryStatus: null,
        reason: null,
        verification: null,
        remarks: null,
        yearMonth: null,
        marketableDays: null,
        daysChange: null,
        purchaseVerification: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加进销存报表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getInventoryReport(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改进销存报表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInventoryReport(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryReport(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除进销存报表编号为"' + ids + '"的数据项？').then(function () {
        return delInventoryReport(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ddi/inventoryReport/export', {
        ...this.queryParams
      }, `inventoryReport_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
