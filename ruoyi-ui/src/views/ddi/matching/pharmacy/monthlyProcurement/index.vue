<template>
  <div class="app-container">
<!--    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">-->
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="月份" align="center" prop="id" />-->
<!--      <el-table-column label="销售流向附件" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          <p v-if="scope.row.saleFileName != null">{{scope.row.saleFileName}} </p>-->
<!--          <el-upload v-if="scope.row.saleFileName === null" class="upload-demo"-->
<!--                     ref="upload"-->
<!--                     :action="upload.url"-->
<!--                     :headers="upload.headers"-->
<!--                     :on-preview="handlePreview"-->
<!--                     :on-remove="handleRemove"-->
<!--                     :on-success="handleAvatarSuccess"-->
<!--                     :on-progress="handleFileUploadProgress"-->
<!--                     :TFile-list="upload.fileData"-->
<!--                     :disabled="upload.isUploading"-->
<!--                     :data="{foreignKey:scope.row.id,fileName:'saleFileId'}"-->
<!--          >-->
<!--            <el-button slot="trigger" size="small" type="primary">上传文件</el-button>-->
<!--          </el-upload>-->
<!--          <el-button v-if="scope.row.saleFileName !== null "-->
<!--                     size="mini"-->
<!--                     type="text"-->
<!--                     icon="el-icon-view"-->
<!--                     @click="downloadAttachment(scope.row.saleFileId,scope.row.saleFileName)"-->
<!--          >下载</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="采购流向附件" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          <p v-if="scope.row.procurementName != null">{{scope.row.procurementName}} </p>-->
<!--          <el-upload v-if="scope.row.procurementName === null" class="upload-demo"-->
<!--                     ref="upload"-->
<!--                     :action="upload.url"-->
<!--                     :headers="upload.headers"-->
<!--                     :on-preview="handlePreview"-->
<!--                     :on-remove="handleRemove"-->
<!--                     :on-success="handleAvatarSuccess"-->
<!--                     :on-progress="handleFileUploadProgress"-->
<!--                     :TFile-list="upload.fileData"-->
<!--                     :disabled="upload.isUploading"-->
<!--                     :data="{foreignKey:scope.row.id,fileName:'procurementId'}"-->
<!--          >-->
<!--            <el-button slot="trigger" size="small" type="primary">上传文件</el-button>-->
<!--          </el-upload>-->
<!--          <el-button v-if="scope.row.procurementName !== null "-->
<!--                     size="mini"-->
<!--                     type="text"-->
<!--                     icon="el-icon-view"-->
<!--                     @click="downloadAttachment(scope.row.procurementId,scope.row.procurementName)"-->
<!--          >下载</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="监管码附件" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          <p v-if="scope.row.encodeName != null">{{scope.row.encodeName}} </p>-->
<!--          <el-upload v-if="scope.row.encodeName === null" class="upload-demo"-->
<!--                     ref="upload"-->
<!--                     :action="upload.url"-->
<!--                     :headers="upload.headers"-->
<!--                     :on-preview="handlePreview"-->
<!--                     :on-remove="handleRemove"-->
<!--                     :on-success="handleAvatarSuccess"-->
<!--                     :on-progress="handleFileUploadProgress"-->
<!--                     :TFile-list="upload.fileData"-->
<!--                     :disabled="upload.isUploading"-->
<!--                     :data="{foreignKey:scope.row.id,fileName:'encodeId'}"-->
<!--          >-->
<!--            <el-button slot="trigger" size="small" type="primary">上传文件</el-button>-->
<!--          </el-upload>-->
<!--          <el-button v-if="scope.row.encodeName !== null "-->
<!--                     size="mini"-->
<!--                     type="text"-->
<!--                     icon="el-icon-view"-->
<!--                     @click="downloadAttachment(scope.row.encodeId,scope.row.encodeName)"-->
<!--          >下载</el-button>-->

<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            :disabled="disabled"-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--          >导出匹配结果</el-button>-->
<!--          <el-button v-if="scope.row.condition === 1 "-->
<!--                     size="mini"-->
<!--                     type="text"-->
<!--                     icon="el-icon-view"-->
<!--                     @click="detail(scope.row)"-->
<!--          >下载</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->
<!--    <pagination-->
<!--      v-show="total>0"-->
<!--      :total="total"-->
<!--      :page.sync="queryParams.pageNum"-->
<!--      :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->
  </div>
</template>

<script>
export default {
  name: "PharmacyMonthlyProcurement"
}
</script>

<style scoped>

</style>
