<template>
  <div class="app-container">
  <el-table v-loading="loading" :data="dataList" >
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="月份" align="center" prop="businessMonth" />
    <el-table-column label="采购流向附件" align="center">
      <template slot-scope="scope">
        <p>{{scope.row.procurementDirection}} </p>
        <el-button
                   size="mini"
                   type="text"
                   icon="el-icon-download"
                   @click="handleExport(scope.row)"
        >下载</el-button>
      </template>
    </el-table-column>
    <el-table-column label="码上放心附件" align="center">
      <template slot-scope="scope">
        <p v-if="scope.row.traceabilityAssurance != null">{{scope.row.traceabilityAssurance}} </p>
        <el-button
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="handleEncodeExport(scope.row)"
        >下载</el-button>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
        <el-button size="mini"
                   type="text"
                   icon="el-icon-download"
                   @click="handleMatchingExport(scope.row)">生成并下载匹配结果</el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total>0"
    :total="total"
    :page.sync="queryParams.pageNum"
    :limit.sync="queryParams.pageSize"
    @pagination="getList"
  />
  </div>
</template>

<script>
import {getToken} from "@/utils/auth";
import {procurementBusinessMonth} from "@/api/ddi/matching";

export default {
  name: "CommercialMonthlyProcurement",
  data() {
    return {
      disabled: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设施运维部设备表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        purchaseDate: null,
        vendorCode: null,
        vendorName: null,
        standardProductCodes: null,
        standardProductName: null,
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        unitPrice: null,
        amount: null,
        standardLotNumber: null,
        upstreamVendorCode: null,
        upstreamVendorName: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productUnitPrice: null,
        productAmount: null,
        productLotNumber: null,
        expirationDate: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null,

      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      detailed: false,
      fileTableData: [],
      dataForm: {
        id: 0,
        noticeId: '',
        attachName: '',
        attachUrl: '',
      },
      dataList: [],
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/FileController/uploadByOne",
        fileData: [],
        foreignKey: null
      }

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设施运维部设备列表 */
    getList() {
      this.loading = true;
      procurementBusinessMonth().then(res => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      })
    },
    // downloadMatchResults (row){
    //   this.download('system/matchingtask/export', {
    //     id:row.id
    //   }, `匹配结果_${new Date().getTime()}.xlsx`)
    // },
    /** 导出按钮操作 */
    handleMatchingExport(row) {
      this.queryParams.businessMonth = row.businessMonth;
      this.download('ddi/ddiMonthProcurement/matchingExport', {
        ...this.queryParams
      }, `采购流向匹配_${new Date().getTime()}.xlsx`)
    },
    /** 导出按钮操作 */
    handleExport(row) {
      this.queryParams.businessMonth = row.businessMonth;
      this.download('ddi/ddiMonthProcurement/export', {
        ...this.queryParams
      },  row.businessMonth+`采购数据_${new Date().getTime()}.xlsx`)
    },
    handleEncodeExport(row) {
      this.queryParams.businessMonth = row.businessMonth;
      this.download('ddi/MatchingController/encodesExport', {
        ...this.queryParams
      },  row.businessMonth+`码上放心附件_${new Date().getTime()}.xlsx`)
    },
  }
}
</script>

<style scoped>

</style>
