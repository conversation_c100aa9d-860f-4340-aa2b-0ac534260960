<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="商业级别" prop="commercialGrade">
        <el-input
          v-model="queryParams.commercialGrade"
          placeholder="请输入商业级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业省份" prop="commercialProvince">
        <el-input
          v-model="queryParams.commercialProvince"
          placeholder="请输入商业省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业编码" prop="businessCoding">
        <el-input
          v-model="queryParams.businessCoding"
          placeholder="请输入商业编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商业名称" prop="businessName">
        <el-input
          v-model="queryParams.businessName"
          placeholder="请输入商业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准产品编码" prop="standardProductCodes">
        <el-input
          v-model="queryParams.standardProductCodes"
          placeholder="请输入标准产品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准产品名称" prop="standardProductName">
        <el-input
          v-model="queryParams.standardProductName"
          placeholder="请输入标准产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="库存日期">
        <el-date-picker
          v-model="daterangeInventoryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ddi:ddiDayInventory:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ddiDayInventoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="主键id" align="center" prop="id" />-->
      <el-table-column label="执行月" align="center" prop="executionMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务月" align="center" prop="businessMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业级别" align="center" prop="commercialGrade" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业省份" align="center" prop="commercialProvince" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业编码" align="center" prop="businessCoding" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业名称" align="center" prop="businessName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="库存日期" align="center" prop="inventoryDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.inventoryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标准产品编码" align="center" prop="standardProductCodes" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品名称" align="center" prop="standardProductName" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品规格" align="center" prop="standardProductSpecification" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="标准单位" align="center" prop="standardUnits" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准数量" align="center" prop="standardQuantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准批号" align="center" prop="standardLotNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品编码" align="center" prop="productCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品规格" align="center" prop="productSpecifications" width="200"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="生产厂家" align="center" prop="manufacturer" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量单位" align="center" prop="quantityUnits" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="quantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品批号" align="center" prop="productLotNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="效期" align="center" prop="expirationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" align="center" prop="dataType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="运维状态" align="center" prop="maintenanceStatus" width="200" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="创建时间" align="center" prop="creationTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" align="center" prop="updated" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.updated, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="删除时间" align="center" prop="deletionTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.deletionTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="删除状态" align="center" prop="deleteStatus" />-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {listDdiDayInventory} from "@/api/ddi/ddiDayInventory";

export default {
  name: "DdiDayInventory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日库存表格数据
      ddiDayInventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangeInventoryDate: [],
      // 删除状态时间范围
      daterangeExpirationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        inventoryDate: null,
        standardProductCodes: null,
        standardProductName: null,
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        standardLotNumber: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productLotNumber: null,
        expirationDate: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询日库存列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeInventoryDate && '' != this.daterangeInventoryDate) {
        this.queryParams.params["beginInventoryDate"] = this.daterangeInventoryDate[0];
        this.queryParams.params["endInventoryDate"] = this.daterangeInventoryDate[1];
      }
      if (null != this.daterangeExpirationDate && '' != this.daterangeExpirationDate) {
        this.queryParams.params["beginExpirationDate"] = this.daterangeExpirationDate[0];
        this.queryParams.params["endExpirationDate"] = this.daterangeExpirationDate[1];
      }
      listDdiDayInventory(this.queryParams).then(response => {
        this.ddiDayInventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        inventoryDate: null,
        standardProductCodes: null,
        standardProductName: null,
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        standardLotNumber: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productLotNumber: null,
        expirationDate: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeInventoryDate = [];
      this.daterangeExpirationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ddi/ddiDayInventory/export', {
        ...this.queryParams
      }, `ddiDayInventory_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
