<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px" :rules="rules">

          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择">
              <el-option
                v-for="item in yearData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="产品名称">
            <el-select v-model="queryParams.productName" placeholder="请选择">
              <el-option label="开坦尼" value="开坦尼"></el-option>
              <el-option label="依达方" value="依达方"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否目标终端">
            <el-select v-model="queryParams.isTargetTerminal" placeholder="请选择">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="日期类型">
            <el-select v-model="queryParams.dateType">
              <el-option label="日" value="日"></el-option>
              <el-option label="月" value="月"></el-option>
              <el-option label="季" value="季"></el-option>
              <el-option label="年" value="年"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="provinceDistributionChart" style="width: 100%;height:1000px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="provinceSalesVolumeChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="provinceSalesGrowthRateChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="provinceMonthlySalesTrendChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import china from '@/api/chinaMap/china_map.json';
import {
  getProvinceSalesVolumeChart,
  getProvinceSalesGrowthRateChart,
  getProvinceDistributionChart,
  getProvinceMonthlySalesTrendChart
} from "@/api/ddi/ddiChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //省份销售分布 集合
      provinceDistributionList: [],
      //省份销量 集合
      provinceSalesVolumeList: [],
      //省份销售增长率 集合
      provinceSalesGrowthRateList: [],
      //省份月度销量走势 集合
      provinceMonthlySalesTrendList: [],
      // 查询参数
      queryParams: {
        year: new Date().getFullYear(),
        dateType: '日',
        productName: '开坦尼',
        yesterday: this.$moment().subtract(2, "days").format("YYYY-MM-DD"),
        isTargetTerminal: '是'
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getYear();
    this.getProvinceSalesVolumeChart();
    this.getProvinceSalesGrowthRateChart();
    this.getProvinceMonthlySalesTrendChart();
  },
  mounted() {
    setTimeout(this.getProvinceDistributionChart, 2000);
  },
  methods: {
    getYear() {
      let year = new Date().getFullYear();
      for (let i = 2024; i <= year; i++) {
        this.yearData.push({
          'label': i,
          'value': i
        })
      }
    },
    /** 查询省份销量  图表数据 */
    async getProvinceSalesVolumeChart() {
      this.loading = true;
      this.queryParams.params = {};
      getProvinceSalesVolumeChart(this.queryParams).then(response => {
        this.provinceSalesVolumeList = response.rows;
        var a = this.provinceSalesVolumeList[0].provinceList;
        var b = this.provinceSalesVolumeList[0].mapListData;
        this.getProvinceSalesVolumeChartVoList(a, b);
      });
    },
    getProvinceSalesVolumeChartVoList(a, b) {
      let chartDom = document.getElementById('provinceSalesVolumeChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;
      option = {
        title: {
          text: '省份' + this.queryParams.dateType + '销量' + '-截止' + this.queryParams.yesterday,
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          /* orient: 'horizontal',
           x: 'center',
           y: 'bottom',
           bottom: 0,*/
          data: a,
          left: '15%',
          top: 'middle',
          orient: 'vertical'

        },
        series: [
          {
            name: ' ',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: b,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 16
                  },
                  formatter: function (params) {
                    if (params.value != 0) {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (' + params.percent + '%)';
                    } else {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (0 %)';
                    }
                  }
                  /*formatter: '{c}万元-{d}%',*/
                },
                labelLine: {
                  show: true,
                  smooth: 0.5,
                  length: 20,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    /** 查询省份销售增长率  图表数据 */
    async getProvinceSalesGrowthRateChart() {
      this.loading = true;
      this.queryParams.params = {};
      getProvinceSalesGrowthRateChart(this.queryParams).then(response => {
        this.provinceSalesGrowthRateList = response.rows;
        var a = this.provinceSalesGrowthRateList[0].legendList;
        var b = this.provinceSalesGrowthRateList[0].provinceList;
        var c = this.provinceSalesGrowthRateList[0].mapListData;
        this.getProvinceSalesGrowthRateChartVoList(a, b, c);
      });
    },
    getProvinceSalesGrowthRateChartVoList(a, b, c) {
      let chartDom = document.getElementById('provinceSalesGrowthRateChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;
      option = {
        title: {
          text: '省份销售' + this.queryParams.dateType + '增长率' + '-截止' + this.queryParams.yesterday,
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '%' + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          top: '10%',
        },
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          data: b,
          axisLabel: {
            interval: 0,
            rotate: 45
          },
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'bar';
          x.barWidth = '50%';
          x.barMaxWidth = '20%';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value) + '%';
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询省份销售分布  图表数据 */
    async getProvinceDistributionChart() {
      this.loading = true;
      this.queryParams.params = {};
      getProvinceDistributionChart(this.queryParams).then(response => {
        this.provinceDistributionList = response.rows;
        var a = this.provinceDistributionList[0].mapListData;
        this.getProvinceDistributionChartVoList(a);
      });
    },
    getProvinceDistributionChartVoList(a) {
      echarts.registerMap('china', china); //这个特别重要
      // let chartDom = document.getElementById('chinaMaps');
      let chartDom = document.getElementById('provinceDistributionChart');
      let myChart = echarts.init(chartDom, null, {renderer: 'svg'});
      myChart.off('click');
      //echart 配制option
      var options = {
        title: {
          text: '省份' + this.queryParams.dateType + '销量热力图-截止' + this.queryParams.yesterday,
          left: 'center',
          textStyle: {
            fontWeight: 'lighter'
          },
          top: '10%'
        },
        tooltip: {
          triggerOn: "mousemove",   //mousemove、click
          padding: 8,
          borderWidth: 1,
          borderColor: '#40ffd9',
          backgroundColor: 'rgba(255,255,255,0.66)',
          textStyle: {
            color: '#ff0000',
            fontSize: 16
          },
          show: true,
          /* formatter: function (e, t, n) {
             let data = e.data;
             // 定义一个空字符串用于拼接显示内容
             let result = '';
             // 遍历数据，如果数据不为0，则加入到显示内容中
             data.forEach(function (item) {
               if (item.value != 0) {
                 result += item.seriesName + ': ' + item.value + '%' + '<br/>';
               }
             });
             // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
             return result || '';
             /!*let context = ' <div>\n' +
               '<p><b style="font-size:15px;">${data.name}</b></p>\n' +
               '<p class="tooltip_style"><span class="tooltip_left">成果总数</span><span class="tooltip_right">${data.value}</span></p>\n' +
               '</div>';
             return context;*!/
           }*/
        },
        visualMap: {
          show: true,
          /*left: 26,
          bottom: 40,*/
          showLabel: true,
          left: '20%',
          top: '40%',
          pieces: [
            {
              gte: 50000,
              label: ">= 50000",
              color: "#f55474",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 20000,
              lt: 49999,
              label: "20000 - 49999",
              color: "rgba(230,83,215,0.87)",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 10000,
              lt: 19999,
              label: "10000 - 19999",
              color: "rgba(144,83,230,0.81)",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 5000,
              lt: 9999,
              label: "5000 - 9999",
              color: "#5384e6",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 2000,
              lt: 4999,
              label: "2000 - 4999",
              color: "#e69853",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 1000,
              lt: 1999,
              label: "1000 - 1999",
              color: "#eae09f",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 500,
              lt: 999,
              label: "500 - 999",
              color: "#85daef",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 50,
              lt: 499,
              label: "50 - 499",
              color: "#74e2bc",
              textStyle: {
                fontSize: 20
              }
            },
            {
              lt: 50,
              label: '<50',
              color: "#a4ea9f",
              textStyle: {
                fontSize: 20
              }
            }
          ]
        },
        geo: {
          map: "china",
          scaleLimit: {
            min: 1,
            max: 2
          },
          zoom: 1.1,
          top: 120,
          layoutSize: "100%", //保持地图宽高比
          label: {
            normal: {
              show: true,
              fontSize: "16",
              color: "rgba(0,0,0,0.7)"
            },
            emphasis: {
              show: false,
              textStyle: {
                color: "#F3F3F3"
              }
            }
          },
          itemStyle: {
            normal: {
              //shadowBlur: 50,
              //shadowColor: 'rgba(0, 0, 0, 0.2)',
              borderColor: "rgb(253,0,30)",
              areaColor: 'rgb(214,183,183)',
            },
            emphasis: {
              areaColor: "#f2d5ad",
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              borderWidth: 0
            }
          }
        },
        series: [
          {
            name: this.queryParams.dateType + "销售数量",
            type: "map",
            map: 'china',
            roam: true,
            geoIndex: 0,
            data: a,
            /*  label: {
                normal: {
                  show: true,  // 显示省份标签
                  formatter: function (params) {
                    console.log(params)
                    return params.name + params.value;
                  },  // 格式化标签内容，显示省份名
                  position: 'right',  // 标签位置，默认右对齐
                  color: '#666',  // 标签颜色
                  fontSize: 12,  // 字体大小
                },
              }*/
          }
        ]
      }
      myChart.clear()
      myChart.setOption(options);
      myChart.resize();
      //地图的点击事件 ，钻取到市我就不举例了，类似的方法，点击事件可以获取到当前点击的省份的数据；
      myChart.on('click', function (params) {
      })
    },
    /** 查询省份月度销量走势  图表数据 */
    async getProvinceMonthlySalesTrendChart() {
      this.loading = true;
      this.queryParams.params = {};
      getProvinceMonthlySalesTrendChart(this.queryParams).then(response => {
        this.provinceMonthlySalesTrendList = response.rows;
        var a = this.provinceMonthlySalesTrendList[0].legendList;
        var b = this.provinceMonthlySalesTrendList[0].monthlyList;
        var c = this.provinceMonthlySalesTrendList[0].mapListData;
        this.getProvinceMonthlySalesTrendChartVoList(a, b, c);
      });
    },
    getProvinceMonthlySalesTrendChartVoList(a, b, c) {
      let chartDom = document.getElementById('provinceMonthlySalesTrendChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;
      option = {
        title: {
          text: this.queryParams.year + '年省份月度销量走势',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          left: '5%',
          right: '5%',
          top: '10%',
        },
        grid: {
          top: "30%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          data: b,
          /* axisLabel: {
             interval: 0,
             rotate: 45
           },*/
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'line';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'right',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    // 搜索按钮操作
    handleQuery() {
      this.getProvinceSalesVolumeChart();
      this.getProvinceSalesGrowthRateChart();
      this.getProvinceDistributionChart();
      this.getProvinceMonthlySalesTrendChart();
    }
  }
}
</script>
