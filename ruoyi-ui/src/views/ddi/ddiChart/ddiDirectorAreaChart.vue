<template>
  <div>
    <div class="app-container" style="height: 15px">
      <el-row>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px" :rules="rules">

          <el-form-item label="年份">
            <el-select v-model="queryParams.year" placeholder="请选择">
              <el-option
                v-for="item in yearData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="产品名称">
            <el-select v-model="queryParams.productName" placeholder="请选择">
              <el-option label="开坦尼" value="开坦尼"></el-option>
              <el-option label="依达方" value="依达方"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否目标终端">
            <el-select v-model="queryParams.isTargetTerminal" placeholder="请选择">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="日期类型">
            <el-select v-model="queryParams.dateType">
              <el-option label="日" value="日"></el-option>
              <el-option label="月" value="月"></el-option>
              <el-option label="季" value="季"></el-option>
              <el-option label="年" value="年"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="directorAreaDistributionChart" style="width: 100%;height:1000px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="directorAreaSalesVolumeChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="directorAreaSalesGrowthRateChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
    <div style="display: flex">
      <div style="flex: 1;;margin-top: 30px">
        <div id="directorAreaMonthlySalesTrendChart" style="width: 100%;height:400px"></div>
      </div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import chinaJson from '@/api/chinaMap/chinaMap.json';
import {
  getDirectorAreaDistributionChart, getDirectorAreaMonthlySalesTrendChart,
  getDirectorAreaSalesGrowthRateChart,
  getDirectorAreaSalesVolumeChart
} from "@/api/ddi/ddiChart";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //总监区销售大区分布 集合
      directorAreaDistributionList: [],
      //总监区大区销量 集合
      directorAreaSalesVolumeList: [],
      //总监区大区销售增长率 集合
      directorAreaSalesGrowthRateList: [],
      //总监区月度销量走势 集合
      directorAreaMonthlySalesTrendList: [],
      // 查询参数
      queryParams: {
        year: new Date().getFullYear(),
        dateType: '日',
        productName: '开坦尼',
        yesterday: this.$moment().subtract(2, "days").format("YYYY-MM-DD"),
        isTargetTerminal: '是'
      },
      //年份数据
      yearData: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      province: [
        ['上海市', '江苏省', '浙江省'],
        ['湖南省', '福建省', '江西省'],
        ['广东省', '广西壮族自治区', '海南省', '云南省', '贵州省',],
        ['甘肃省', '青海省', '宁夏回族自治区', '陕西省', '四川省', '重庆市', '新疆维吾尔自治区', '西藏自治区'],
        ['北京市', '内蒙古自治区', '天津市', '山西省', '河北省', '黑龙江省', '吉林省', '辽宁省'],
        ['山东省', '河南省', '湖北省', '安徽省', '', '']
        /*['上海市', '江苏省', '安徽省', '浙江省', '江西省', '福建省', '台湾省'],
        ['广东省', '海南省', '贵州省', '云南省', '湖北省', '湖南省', '广西壮族自治区', '香港特别行政区', '澳门特别行政区'],
        ['陕西省', '宁夏回族自治区', '河南省', '新疆维吾尔自治区', '西藏自治区', '青海省', '重庆市', '四川省', '甘肃省'],
        ['北京市', '内蒙古自治区', '天津市', '山西省', '河北省'],
        ['山东省', '黑龙江省', '吉林省', '辽宁省'],*/
      ],
      // region: ['东中国区', '南中国区', '西中国区', '北中国一区', '北中国二区']
      region: ['东中国区', '南中国一区', '南中国二区', '西中国区', '北中国区', '中中国区']
    }
  },
  created() {
    // this.getChinaJson();
    this.getYear();
    this.getDirectorAreaSalesVolumeChart();
    this.getDirectorAreaSalesGrowthRateChart();
    this.getDirectorAreaMonthlySalesTrendChart();
  },
  mounted() {
    setTimeout(this.getDirectorAreaDistributionChart, 2000);
  },
  methods: {
    getYear() {
      let year = new Date().getFullYear();
      for (let i = 2024; i <= year; i++) {
        this.yearData.push({
          'label': i,
          'value': i
        })
      }
    },
    mergeProvinces(features, province, region) {
      let newFeatures = [];
      if (features[0].properties.name.indexOf('区') !== -1) {
        return
      }
      for (let i = 0; i < province.length; i++) {
        for (let j = 0; j < province[i].length; j++) {
          for (let k = 0; k < features.length; k++) {
            if (features[k].properties.name == province[i][j]) {
              if (newFeatures[i]) {
                if (features[k].properties.name == '内蒙古自治区') {
                  features[k].geometry.coordinates = [features[k].geometry.coordinates]
                }
                newFeatures[i].geometry.coordinates.push(...features[k].geometry.coordinates)
              } else {
                newFeatures.push(features[k])
                newFeatures[i].properties.name = region[i]
              }
              break
            }
          }
        }
      }
      chinaJson.features = newFeatures;
    },
    /** 查询总监区销售大区分布  图表数据 */
    async getDirectorAreaDistributionChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDirectorAreaDistributionChart(this.queryParams).then(response => {
        this.directorAreaDistributionList = response.rows;
        var a = this.directorAreaDistributionList[0].mapListData;
        this.getDirectorAreaDistributionChartVoList(a);
      });
    },
    async getDirectorAreaDistributionChartVoList(a) {
      console.log(chinaJson.features)
      this.mergeProvinces(chinaJson.features, this.province, this.region)
      echarts.registerMap('china', chinaJson); //这个特别重要
      console.log(chinaJson)
      var chartDom = document.getElementById('directorAreaDistributionChart');
      var myChart = echarts.init(chartDom, null, {renderer: 'svg'});
      myChart.off('click');
      //echart 配制option
      var options = {
        title: {
          text: '区域' + this.queryParams.dateType + '销量热力图-截止' + this.queryParams.yesterday,
          left: 'center',
          textStyle: {
            fontWeight: 'lighter'
          },
          top: '10%'
        },
        tooltip: {
          triggerOn: "mousemove",   //mousemove、click
          padding: 8,
          borderWidth: 1,
          borderColor: '#40ffd9',
          backgroundColor: 'rgba(255,255,255,0.66)',
          textStyle: {
            color: '#ff0000',
            fontSize: 16
          },
          show: true,
        },
        visualMap: {
          show: true,
          /*left: 26,
          bottom: 40,*/
          showLabel: true,
          left: '20%',
          top: '40%',
          pieces: [
            {
              gte: 50000,
              label: ">= 50000",
              color: "#f55474",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 20000,
              lt: 49999,
              label: "20000 - 49999",
              color: "rgba(230,83,215,0.87)",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 10000,
              lt: 19999,
              label: "10000 - 19999",
              color: "rgba(144,83,230,0.81)",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 5000,
              lt: 9999,
              label: "5000 - 9999",
              color: "#5384e6",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 2000,
              lt: 4999,
              label: "2000 - 4999",
              color: "#e69853",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 1000,
              lt: 1999,
              label: "1000 - 1999",
              color: "#eae09f",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 500,
              lt: 999,
              label: "500 - 999",
              color: "#85daef",
              textStyle: {
                fontSize: 20
              }
            },
            {
              gte: 50,
              lt: 499,
              label: "50 - 499",
              color: "#74e2bc",
              textStyle: {
                fontSize: 20
              }
            },
            {
              lt: 50,
              label: '<50',
              color: "#a4ea9f",
              textStyle: {
                fontSize: 20
              }
            }
          ]
        },
        geo: {
          map: "china",
          scaleLimit: {
            min: 1,
            max: 2
          },// 缩放级别
          /* roam: true,//不开启缩放和平移*/
          zoom: 1,//视角缩放比例
          top: 120,
          layoutSize: "100%", //保持地图宽高比
          regions: [
            {
              name: "南海诸岛",
              itemStyle: {
                normal: {
                  opacity: 0, // 不绘制
                }
              },
            }
          ],
          label: {
            normal: {
              show: true,
              fontSize: "16",
              color: "rgba(0,0,0,0.7)"
            },
            emphasis: {
              show: false,
              textStyle: {
                color: "#F3F3F3"
              }
            }
          },
          itemStyle: {
            normal: {
              //shadowBlur: 50,
              //shadowColor: 'rgba(0, 0, 0, 0.2)',
              borderColor: "rgb(253,0,30)",
              areaColor: 'rgb(214,183,183)',
            },
            emphasis: {
              areaColor: "#f2d5ad",
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              borderWidth: 0
            }
          }
        },
        series: [
          {
            name: this.queryParams.dateType + "销售数量",
            type: "map",
            map: 'china',
            roam: true,
            geoIndex: 0,
            data: a
          }
        ]
      }
      myChart.clear()
      myChart.setOption(options);
      myChart.resize();
      //地图的点击事件 ，钻取到市我就不举例了，类似的方法，点击事件可以获取到当前点击的省份的数据；
      myChart.on('click', function (params) {
      })
    },
    /** 查询总监区大区销量  图表数据 */
    async getDirectorAreaSalesVolumeChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDirectorAreaSalesVolumeChart(this.queryParams).then(response => {
        this.directorAreaSalesVolumeList = response.rows;
        var a = this.directorAreaSalesVolumeList[0].areaList;
        var b = this.directorAreaSalesVolumeList[0].mapListData;
        this.getDirectorAreaSalesVolumeChartVoList(a, b);
      });
    },
    getDirectorAreaSalesVolumeChartVoList(a, b) {
      let chartDom = document.getElementById('directorAreaSalesVolumeChart');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      var option;
      option = {
        title: {
          text: '总监区大区' + this.queryParams.dateType + '销量-' + '截止' + this.queryParams.yesterday,
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          x: 'center',
          y: 'bottom',
          /* bottom: 0,*/
          data: a,
          /* formatter: function(name) {
             // 获取legend显示内容
             let data = option.series[0].data;
             let total = 0;
             let tarValue = 0;
             for (let i = 0, l = data.length; i < l; i++) {
               total += data[i].value;
               if (data[i].name == name) {
                 tarValue = data[i].value;
               }
             }
             let p = (tarValue / total * 100).toFixed(2);
             return name + ' ' + ' ' + p + '%';
           },*/
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            /*  selectedMode: 'single',*/
            data: b,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontWeight: 200,
                    fontsize: 20
                  },
                  formatter: function (params) {
                    if (params.value != 0) {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (' + params.percent + '%)';
                    } else {
                      return params.name + ' ' + echarts.format.addCommas(params.value) + ' (0 %)';
                    }
                  }
                },
                labelLine: {
                  show: true,
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                }
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      myChart.resize();
      myChart.on('legendselectchanged', function (params) {
        // 这里仅作为示例，实际使用时请根据实际情况调整逻辑
        // 获取当前选中的图例项索引或名称
        const selectedItems = params.selected;

        // 基于选中状态重新计算并设置有效的数据源
        // 假设 b 是原始数据，可以根据 selectedItems 过滤出有效数据
        const filteredData = b.filter(item => selectedItems.includes(item.name));
        myChart.setOption({
          series: [{
            data: filteredData
          }]
        });
      });
    },
    /** 查询总监区大区销售增长率  图表数据 */
    async getDirectorAreaSalesGrowthRateChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDirectorAreaSalesGrowthRateChart(this.queryParams).then(response => {
        this.directorAreaSalesGrowthRateList = response.rows;
        var a = this.directorAreaSalesGrowthRateList[0].legendList;
        var b = this.directorAreaSalesGrowthRateList[0].areaList;
        var c = this.directorAreaSalesGrowthRateList[0].mapListData;
        this.getDirectorAreaSalesGrowthRateChartVoList(a, b, c);
      });
    },
    getDirectorAreaSalesGrowthRateChartVoList(a, b, c) {
      let chartDom = document.getElementById('directorAreaSalesGrowthRateChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: '总监区大区销售' + this.queryParams.dateType + "增长率-" + '截止' + this.queryParams.yesterday,
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '%' + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          top: '10%',
        },
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'bar';
          x.barWidth = '50%';
          x.barMaxWidth = '20%';
          //x.stack = 'Total';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value) + '%';
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
    /** 查询总监区月度销量走势  图表数据 */
    async getDirectorAreaMonthlySalesTrendChart() {
      this.loading = true;
      this.queryParams.params = {};
      getDirectorAreaMonthlySalesTrendChart(this.queryParams).then(response => {
        this.directorAreaMonthlySalesTrendList = response.rows;
        var a = this.directorAreaMonthlySalesTrendList[0].legendList;
        var b = this.directorAreaMonthlySalesTrendList[0].monthlyList;
        var c = this.directorAreaMonthlySalesTrendList[0].mapListData;
        this.getDirectorAreaMonthlySalesTrendChartVoList(a, b, c);
      });
    },
    getDirectorAreaMonthlySalesTrendChartVoList(a, b, c) {
      let chartDom = document.getElementById('directorAreaMonthlySalesTrendChart');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      let option;

      option = {
        title: {
          text: this.queryParams.year + '年总监区月度销量走势',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            // 定义一个空字符串用于拼接显示内容
            let result = '';
            // 遍历数据，如果数据不为0，则加入到显示内容中
            params.forEach(function (item) {
              if (item.value != 0) {
                result += item.seriesName + ': ' + item.value + '<br/>';
              }
            });
            // 如果result为空字符串，则返回空字符串，否则返回拼接好的字符串
            return result || '';
          }
        },
        legend: {
          data: a,
          top: '10%',
        },
        grid: {
          top: "20%",
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          /*axisLabel: {
            interval: 0,
            rotate: 40
          },*/
          data: b
        },
        yAxis: {
          type: 'value'
          // min: 0,
          // max: 5000,
          // interval:1000,
          // axisLabel: {
          //   formatter: '{value}'
          // }
        },
        series: []
      };
      for (var i = 0; i < c.length; i++) {
        var map = c[i];
        for (var key in map) {
          let x = {};
          x.name = key;
          x.type = 'line';
          x.data = map[key];
          x.label = {
            normal: {
              show: true,
              position: 'top',
              formatter: function (num) {
                if (num.value != 0) {
                  return echarts.format.addCommas(num.value);
                } else {
                  return '';
                }
              }
            }
          }
          option.series.push(x);
        }
      }
      option && myChart.setOption(option);
    },
// 搜索按钮操作
    handleQuery() {
      this.getDirectorAreaSalesVolumeChart();
      this.getDirectorAreaSalesGrowthRateChart();
      this.getDirectorAreaDistributionChart();
      this.getDirectorAreaMonthlySalesTrendChart();
    }
  }
}
;
</script>

