<template>
  <div class="app-container">
    <el-form :model="queryParams"  ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="请选择Product Family" prop="caseNum" label-width="200px">
        <el-select v-model="queryParams.PRO_FAMILY_ID" filterable multiple placeholder="请选择">
          <el-option
            v-for="item in caseNums"
            :key="item.familyId"
            :label="item.namej"
            :value="item.familyId">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="请选择Project" prop="protocols" label-width="200px">
        <el-select v-model="queryParams.STU_PROTOCOL_ID" filterable multiple placeholder="请选择">
          <el-option
            v-for="item in protocols"
            :key="item.protocolId"
            :label="item.protocolDesc"
            :value="item.protocolId">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="请选择Study" prop="studyIds" label-width="200px">
        <el-select v-model="queryParams.STU_STUDY_KEY" filterable multiple placeholder="请选择">
          <el-option
            v-for="item in studyIds"
            :key="item.studyKey"
            :label="item.studyNum"
            :value="item.studyKey">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="请选择Arm" prop="Arms" label-width="200px">
        <el-select v-model="queryParams.STU_COHORT_ID" filterable multiple placeholder="请选择">
          <el-option
            v-for="item in armIds"
            :key="item.cohortId"
            :label="item.blindNameJ"
            :value="item.cohortId">
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="不良事件发生日期" prop="EVT_ONSET" label-width="200px">
        <el-date-picker
          v-model="queryParams.EVT_ONSET"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="不良事件结束日期" prop="EVT_STOP_DATE" label-width="200px">
        <el-date-picker
          v-model="queryParams.EVT_STOP_DATE"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>


      <el-form-item label="报告收到开始日期" prop="EVT_ONSET" label-width="200px">
        <el-date-picker
          v-model="queryParams.PORT_RECEIPT_START_DATE"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="报告收到结束日期" prop="EVT_STOP_DATE" label-width="200px">
        <el-date-picker
          v-model="queryParams.PORT_RECEIPT_STOP_DATE"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="事件发生日期是否为空" prop="event" label-width="200px">
        <el-select v-model="queryParams.events" placeholder="请选择">
          <el-option
            v-for="item in events"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="去除自定义分类" prop="event" label-width="200px">
        <el-select v-model="queryParams.scraps" multiple placeholder="请选择">
          <el-option
            v-for="item in scrap"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['report:PV:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="PVList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="caseId"/>
      <el-table-column label="报告编号" align="center" prop="caseNum"/>
      <el-table-column label="研究方案编号" align="center" prop="stuStudyNum" :show-overflow-tooltip="true"/>
      <el-table-column label="公司首次收到日期" align="center" prop="masInitReptDate" :show-overflow-tooltip="true"/>
      <el-table-column label="收到报告日期" align="center" prop="fupReceiptDate" :show-overflow-tooltip="true"/>
      <el-table-column label="病例流程及锁定状态" align="center" prop="masStateName" :show-overflow-tooltip="true"/>
      <el-table-column label="患者编号" align="center" prop="patPatSubjNum" :show-overflow-tooltip="true"/>
      <el-table-column label="性别" align="center" prop="patGenderId" :show-overflow-tooltip="true"/>
      <el-table-column label="出生日期" align="center" prop="patPatDob" :show-overflow-tooltip="true"/>
      <el-table-column label="通用名称" align="center" prop="proGenericName" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件名称(报告术语)" align="center" prop="evtDescReptdJ" :show-overflow-tooltip="true"/>
      <el-table-column label="PT CODE" align="center" prop="evtPtCode" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件名称(PT Name CN)" align="center" prop="evtPrefTermJ" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件名称(PT Name EN)" align="center" prop="evtPrefTerm" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件发生时间" align="center" prop="evtOnset" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件结束时间" align="center" prop="evtStopDate" :show-overflow-tooltip="true"/>
      <el-table-column label="不良事件结果" align="center" prop="evtEvtOutcomeJ" :show-overflow-tooltip="true"/>
      <el-table-column label="是否严重" align="center" prop="evtSeriousnessJ" :show-overflow-tooltip="true"/>
      <el-table-column label="严重性标准" align="center" prop="finalDesc" :show-overflow-tooltip="true"/>
      <el-table-column label="严重程度" align="center" prop="evtEvtIntensityJ" :show-overflow-tooltip="true"/>
      <el-table-column label="CTCAE分级" align="center" prop="evtCtcaeJ" :show-overflow-tooltip="true"/>
      <el-table-column label="特别关注事件说明" align="center" prop="evtDetails" :show-overflow-tooltip="true"/>
      <el-table-column label="特别关注事件类别" align="center" prop="evnEvtNatureJ" :show-overflow-tooltip="true"/>
      <el-table-column label="是否已知" align="center" prop="evlDetListednessId" :show-overflow-tooltip="true"/>
      <el-table-column label="针对产品采取的措施" align="center" prop="evdActionTakenJ" :show-overflow-tooltip="true"/>
      <el-table-column label="公司因果评价" align="center" prop="evcDetCausalityJ" :show-overflow-tooltip="true"/>
      <el-table-column label="报告者因果评价" align="center" prop="evcRptCausalityJ" :show-overflow-tooltip="true"/>
      <el-table-column label="报告描述" align="center" prop="anaNarrativeJ" :show-overflow-tooltip="true"/>
      <el-table-column label="公司评述" align="center" prop="anaCaseCompanyCmtsJ" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-edit"-->
      <!--            @click="handleUpdate(scope.row)"-->
      <!--            v-hasPermi="['report:PV:edit']"-->
      <!--          >修改</el-button>-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-delete"-->
      <!--            @click="handleDelete(scope.row)"-->
      <!--            v-hasPermi="['report:PV:remove']"-->
      <!--          >删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改PV对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listPV, getPV, delPV, addPV, updatePV, getProtocols, getStudyIds, getArmId} from "@/api/pv/PV";

export default {
  name: "PV",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // PV表格数据
      PVList: [],
      caseNums: [],
      protocols: [],
      studyIds: [],
      armIds: [],
      events: [{
        value: 'true',
        label: '包含'
      }, {
        value: 'false',
        label: '不包含'
      }],
      scrap: [
        {
        value: '用量调查',
        label: '用量调查'
      }, {
        value: '无效报告',
        label: '无效报告'
      },
        {
        value: '作废报告',
        label: '作废报告'
      }],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scraps: null,
        STU_PROTOCOL_NUM: null,
        STU_STUDY_NUM: null,
        STU_BLIND_NAME_J: null,
        PRO_GENERIC_NAME_J: null,
        EVT_ONSET: '1999-01-01',
        EVT_STOP_DATE: '2099-01-01',
        PORT_RECEIPT_START_DATE: '1999-01-01',
        PORT_RECEIPT_STOP_DATE: '2099-01-01'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        EVT_STOP_DATE: [
          { type: 'date', required: true, message: '请选择时间', trigger: 'change' }
        ],
        EVT_ONSET: [
          { type: 'date', required: true, message: '请选择时间', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getfamily();
  },
  methods: {
    getfamily() {
      getPV().then(response => {
        this.caseNums = response.data;
      })
      getProtocols().then(response => {

        this.protocols = response.data;
      })
      getStudyIds().then(response => {

        this.studyIds = response.data;
      })
      getArmId().then(response => {
        this.armIds = response.data;
      })
    },
    /** 查询PV列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      listPV(this.queryParams).then(response => {
        this.PVList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        caseId: null,
        caseNum: null,
        evtDescReptdJ: null,
        evtPtCode: null,
        evtPrefTermJ: null,
        evtPrefTerm: null,
        evtOnset: null,
        evtStopDate: null,
        evtEvtOutcomeJ: null,
        evtSeriousnessJ: null,
        evtEvtIntensityJ: null,
        evtCtcaeJ: null,
        evtDetails: null,
        stuStudyNum: null,
        masInitReptDate: null,
        fupReceiptDate: null,
        masStateName: null,
        patPatSubjNum: null,
        patGenderId: null,
        patPatDob: null,
        proGenericName: null,
        finalDesc: null,
        evnEvtNatureJ: null,
        evlDetListednessId: null,
        evdActionTakenJ: null,
        evcDetCausalityJ: null,
        evcRptCausalityJ: null,
        anaNarrativeJ: null,
        anaCaseCompanyCmtsJ: null,
        PRO_FAMILY_ID: [],
        STU_PROTOCOL_ID: [],
        STU_STUDY_KEY: [],
        STU_COHORT_ID: [],
        events: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.caseId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加PV";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const caseId = row.caseId || this.ids
      getPV(caseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改PV";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.caseId != null) {
            updatePV(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPV(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const caseIds = row.caseId || this.ids;
      this.$modal.confirm('是否确认删除PV编号为"' + caseIds + '"的数据项？').then(function () {
        return delPV(caseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('report/PV/export', {
        ...this.queryParams
      }, `PV_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
