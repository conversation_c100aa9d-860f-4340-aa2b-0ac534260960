<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="单号" prop="oddNumbers">
        <el-input
          v-model="queryParams.oddNumbers"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人姓名" prop="applicantName">
        <el-input
          v-model="queryParams.applicantName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人部门" prop="applicantDepartment">
        <el-input
          v-model="queryParams.applicantDepartment"
          placeholder="请输入申请人部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="期望时间">
        <el-date-picker
          v-model="daterangeExpectedTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['fe:feTaskWorkOrder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="feTaskWorkOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="单号" align="center" prop="oddNumbers" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人姓名" align="center" prop="applicantName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人部门" align="center" prop="applicantDepartment" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="服务类型" align="center" prop="serviceType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期望时间" align="center" prop="expectedTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="MFE小组负责人" align="center" prop="teamLeader" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="MFE执行人" align="center" prop="executor" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="内容说明" align="center" prop="contentDescription" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="处理过程" align="center" prop="processingProcess" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="结论报告" align="center" prop="conclusionReport" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="处理时间" align="center" prop="processingTime" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listFeTaskWorkOrder } from "@/api/fe/feTaskWorkOrder";

export default {
  name: "FeTaskWorkOrder",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // FE任务工单表格数据
      feTaskWorkOrderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 处理时间时间范围
      daterangeExpectedTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oddNumbers: null,
        applicantName: null,
        applicantDepartment: null,
        serviceType: null,
        expectedTime: null,
        teamLeader: null,
        executor: null,
        contentDescription: null,
        processingProcess: null,
        conclusionReport: null,
        processingTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询FE任务工单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeExpectedTime && '' != this.daterangeExpectedTime) {
        this.queryParams.params["beginExpectedTime"] = this.daterangeExpectedTime[0];
        this.queryParams.params["endExpectedTime"] = this.daterangeExpectedTime[1];
      }
      listFeTaskWorkOrder(this.queryParams).then(response => {
        this.feTaskWorkOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        oddNumbers: null,
        applicantName: null,
        applicantDepartment: null,
        serviceType: null,
        expectedTime: null,
        teamLeader: null,
        executor: null,
        contentDescription: null,
        processingProcess: null,
        conclusionReport: null,
        processingTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeExpectedTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddNumbers)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fe/feTaskWorkOrder/export', {
        ...this.queryParams
      }, `feTaskWorkOrder_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
