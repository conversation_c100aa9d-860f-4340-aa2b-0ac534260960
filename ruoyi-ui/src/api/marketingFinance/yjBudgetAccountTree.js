import request from '@/utils/request'

// 查询成本中心树状预算列表（用于树状图和方块图展示）
export function getBudgetTreeData(query) {
  return request({
    url: '/marketingFinance/yjBudgetAccountTree/list',
    method: 'get',
    params: query
  })
}

// 分页查询成本中心树状预算列表
export function getBudgetTreePage(query) {
  return request({
    url: '/marketingFinance/yjBudgetAccountTree/page',
    method: 'get',
    params: query
  })
}

// 导出成本中心树状预算报表
export function exportBudgetTree(query) {
  return request({
    url: '/marketingFinance/yjBudgetAccountTree/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

