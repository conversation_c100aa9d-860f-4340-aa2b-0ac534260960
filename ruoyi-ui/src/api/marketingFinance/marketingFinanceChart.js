import request from '@/utils/request'


// 销售发货总览
export function listSalesShipmentOverviewChart(query) {
  return request({
    url: '/marketingFinance/marketingFinanceChart/salesShipmentOverviewChart',
    method: 'get',
    params: query
  })
}

// 销售发货片区走势
export function listMarketingFinanceChart(query) {
  return request({
    url: '/marketingFinance/marketingFinanceChart/dealerChart',
    method: 'get',
    params: query
  })
}
//目标达成--销售净收入
export function listGoalAchievementChart(query) {
  return request({
    url: '/marketingFinance/marketingFinanceChart/goalAchievementChart',
    method: 'get',
    params: query
  })
}
//资金回笼情况
export function listFundWithdrawalChart(query) {
  return request({
    url: '/marketingFinance/marketingFinanceChart/fundWithdrawalChart',
    method: 'get',
    params: query
  })
}
//应收账龄（资金占用）
export function listAccountsReceivableAgingChart(query) {
  return request({
    url: '/marketingFinance/marketingFinanceChart/accountsReceivableAgingChart',
    method: 'get',
    params: query
  })
}


//终端销售总览
export function listTerminalSalesOverviewChart(query) {
  return request({
    url: '/marketingFinance/salesFlowChart/terminalSalesOverviewChart',
    method: 'get',
    params: query
  })
}

//销售流向
export function listSalesFlowChart(query) {
  return request({
    url: '/marketingFinance/salesFlowChart/salesFlowChart',
    method: 'get',
    params: query
  })
}

//终端销售分布
export function listTerminalSalesDistributionChart(query) {
  return request({
    url: '/marketingFinance/salesFlowChart/terminalSalesDistributionChart',
    method: 'get',
    params: query
  })
}


//终端销售走势
export function listTerminalSalesTrendChart(query) {
  return request({
    url: '/marketingFinance/salesFlowChart/terminalSalesTrendChart',
    method: 'get',
    params: query
  })
}



//费用结构
export function listCostStructureChart(query) {
  return request({
    url: '/marketingFinance/revenueExpenditureChart/costStructureChart',
    method: 'get',
    params: query
  })
}

//收支总览
export function listOverviewIncomeExpenditureChart(query) {
  return request({
    url: '/marketingFinance/revenueExpenditureChart/overviewIncomeExpenditureChart',
    method: 'get',
    params: query
  })
}

//收支总览-实际支出+预提
export function listOverviewIncomeExpenditureChart1(query) {
  return request({
    url: '/marketingFinance/revenueExpenditureChart/overviewIncomeExpenditureChart1',
    method: 'get',
    params: query
  })
}


//费用对比（同比、环比）
export function listCostComparisonChart(query) {
  return request({
    url: '/marketingFinance/revenueExpenditureChart/costComparisonChart',
    method: 'get',
    params: query
  })
}

//主要费用率对比
export function listCostRateComparisonChart(query) {
  return request({
    url: '/marketingFinance/revenueExpenditureChart/costRateComparisonChart',
    method: 'get',
    params: query
  })
}




