import request from '@/utils/request'

// 查询
export function queryDoctorDataList(query) {
  return request({
    url: '/Data/Doctor/list',
    method: 'get',
    params: query
  })
}

// 查询单个
export function queryDoctorDataById(query) {
  return request({
    url: '/Data/Doctor/queryById/'+query,
    method: 'get'
  })
}

// 新增
export function insertDoctorData(data) {
  return request({
    url: '/Data/Doctor/insert',
    method: 'post',
    data: data
  })
}

// 修改
export function updateDoctorData(data) {
  return request({
    url: '/Data/Doctor/update',
    method: 'put',
    data: data
  })
}
