import request from '@/utils/request'

// 查询
export function queryHospitalDataList(query) {
  return request({
    url: '/Data/Hospital/list',
    method: 'get',
    params: query
  })
}
// 查询单个
export function queryHospitalDataById(query) {
  return request({
    url: '/Data/Hospital/queryById/'+query,
    method: 'get'
  })
}

// 新增
export function insertHospitalData(data) {
  return request({
    url: '/Data/Hospital/insert',
    method: 'post',
    data: data
  })
}

// 修改
export function updateHospitalData(data) {
  return request({
    url: '/Data/Hospital/update',
    method: 'put',
    data: data
  })
}
