import request from '@/utils/request'

// 查询财务审批节点追踪报表列表
export function listYJApprovalNodeTracking(query) {
  return request({
    url: '/marketingFinance/yjApprovalNodeTracking/list',
    method: 'get',
    params: query
  })
}

// 导出财务审批节点追踪报表
export function exportYJApprovalNodeTracking(query) {
  return request({
    url: '/marketingFinance/yjApprovalNodeTracking/export',
    method: 'post',
    data: query
  })
}
