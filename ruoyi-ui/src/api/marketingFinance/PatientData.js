import request from '@/utils/request'

// 查询
export function queryDataList(query) {
  return request({
    url: '/Data/Patient/list',
    method: 'get',
    params: query
  })
}

// 查询单个
export function queryDataById(query) {
  return request({
    url: '/Data/Patient/queryById/'+query,
    method: 'get'
  })
}

// 新增
export function insertData(data) {
  return request({
    url: '/Data/Patient/insert',
    method: 'post',
    data: data
  })
}

// 修改
export function updateData(data) {
  return request({
    url: '/Data/Patient/update',
    method: 'put',
    data: data
  })
}
