import request from '@/utils/request'

// 查询审批异常检查报表列表
export function listApprovalAnomalyCheck(query) {
  return request({
    url: '/marketingFinance/approvalAnomalyCheck/list',
    method: 'get',
    params: query
  })
}

// 导出审批异常检查报表
export function exportApprovalAnomalyCheck(query) {
  return request({
    url: '/marketingFinance/approvalAnomalyCheck/export',
    method: 'post',
    data: query
  })
}