
// 获取部门预算树状数据
export function getBudgetTreeData(query) {
  return request({
    url: '/marketingFinance/budgetAccount/tree',
    method: 'get',
    params: query
  })
}

// 获取部门预算详情
export function getBudgetDetail(departmentCode) {
  return request({
    url: `/marketingFinance/budgetAccount/detail/${departmentCode}`,
    method: 'get'
  })
}

// 导出部门预算数据
export function exportBudgetData(query) {
  return request({
    url: '/marketingFinance/budgetAccount/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取预算使用率统计
export function getBudgetUsageStats(query) {
  return request({
    url: '/marketingFinance/budgetAccount/stats',
    method: 'get',
    params: query
  })
}
