import request from '@/utils/request'

// 查询合理化建议列表
export function listEkpRationalizationProposal(query) {
  return request({
    url: '/marketingFinance/ekpRationalizationProposal/list',
    method: 'get',
    params: query
  })
}

// 查询合理化建议详细
export function getEkpRationalizationProposal(fdId) {
  return request({
    url: '/marketingFinance/ekpRationalizationProposal/' + fdId,
    method: 'get'
  })
}

// 新增合理化建议
export function addEkpRationalizationProposal(data) {
  return request({
    url: '/marketingFinance/ekpRationalizationProposal',
    method: 'post',
    data: data
  })
}

// 修改合理化建议
export function updateEkpRationalizationProposal(data) {
  return request({
    url: '/marketingFinance/ekpRationalizationProposal',
    method: 'put',
    data: data
  })
}

// 删除合理化建议
export function delEkpRationalizationProposal(fdId) {
  return request({
    url: '/marketingFinance/ekpRationalizationProposal/' + fdId,
    method: 'delete'
  })
}
