import request from '@/utils/request'

// 查询增值税票(新)列表
export function listAddedTaxInvoices(query) {
  return request({
    url: '/marketingFinance/AddedTaxInvoices/list',
    method: 'get',
    params: query
  })
}

// 查询增值税票(新)详细
export function getAddedTaxInvoices(id) {
  return request({
    url: '/marketingFinance/AddedTaxInvoices/' + id,
    method: 'get'
  })
}

// 新增增值税票(新)
export function addAddedTaxInvoices(data) {
  return request({
    url: '/marketingFinance/AddedTaxInvoices',
    method: 'post',
    data: data
  })
}

// 修改增值税票(新)
export function updateAddedTaxInvoices(data) {
  return request({
    url: '/marketingFinance/AddedTaxInvoices',
    method: 'put',
    data: data
  })
}

// 删除增值税票(新)
export function delAddedTaxInvoices(id) {
  return request({
    url: '/marketingFinance/AddedTaxInvoices/' + id,
    method: 'delete'
  })
}
