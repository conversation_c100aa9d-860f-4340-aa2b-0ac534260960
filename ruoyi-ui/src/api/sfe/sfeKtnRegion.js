import request from '@/utils/request'

// 查询开坦尼报-大区列表
export function listSfeKtnRegion(query) {
  return request({
    url: '/sfe/sfeKtnRegion/list',
    method: 'get',
    params: query
  })
}

// 查询开坦尼报-大区详细
export function getSfeKtnRegion(id) {
  return request({
    url: '/sfe/sfeKtnRegion/' + id,
    method: 'get'
  })
}

// 新增开坦尼报-大区
export function addSfeKtnRegion(data) {
  return request({
    url: '/sfe/sfeKtnRegion',
    method: 'post',
    data: data
  })
}

// 修改开坦尼报-大区
export function updateSfeKtnRegion(data) {
  return request({
    url: '/sfe/sfeKtnRegion',
    method: 'put',
    data: data
  })
}

// 删除开坦尼报-大区
export function delSfeKtnRegion(id) {
  return request({
    url: '/sfe/sfeKtnRegion/' + id,
    method: 'delete'
  })
}
