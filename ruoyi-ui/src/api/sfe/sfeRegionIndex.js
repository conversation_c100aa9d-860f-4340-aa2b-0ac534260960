import request from '@/utils/request'

// 查询大区指标列表
export function listSfeRegionIndex(query) {
  return request({
    url: '/sfe/sfeRegionIndex/list',
    method: 'get',
    params: query
  })
}

// 查询大区指标详细
export function getSfeRegionIndex(id) {
  return request({
    url: '/sfe/sfeRegionIndex/' + id,
    method: 'get'
  })
}

// 新增大区指标
export function addSfeRegionIndex(data) {
  return request({
    url: '/sfe/sfeRegionIndex',
    method: 'post',
    data: data
  })
}

// 修改大区指标
export function updateSfeRegionIndex(data) {
  return request({
    url: '/sfe/sfeRegionIndex',
    method: 'put',
    data: data
  })
}

// 删除大区指标
export function delSfeRegionIndex(id) {
  return request({
    url: '/sfe/sfeRegionIndex/' + id,
    method: 'delete'
  })
}
