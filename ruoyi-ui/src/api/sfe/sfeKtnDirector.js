import request from '@/utils/request'

// 查询开坦尼报-总监列表
export function listSfeKtnDirector(query) {
  return request({
    url: '/sfe/sfeKtnDirector/list',
    method: 'get',
    params: query
  })
}

// 查询开坦尼报-总监详细
export function getSfeKtnDirector(id) {
  return request({
    url: '/sfe/sfeKtnDirector/' + id,
    method: 'get'
  })
}

// 新增开坦尼报-总监
export function addSfeKtnDirector(data) {
  return request({
    url: '/sfe/sfeKtnDirector',
    method: 'post',
    data: data
  })
}

// 修改开坦尼报-总监
export function updateSfeKtnDirector(data) {
  return request({
    url: '/sfe/sfeKtnDirector',
    method: 'put',
    data: data
  })
}

// 删除开坦尼报-总监
export function delSfeKtnDirector(id) {
  return request({
    url: '/sfe/sfeKtnDirector/' + id,
    method: 'delete'
  })
}
