import request from '@/utils/request'

// 查询依达方报-总监列表
export function listSfeYdfDirector(query) {
  return request({
    url: '/sfe/sfeYdfDirector/list',
    method: 'get',
    params: query
  })
}

// 查询依达方报-总监详细
export function getSfeYdfDirector(id) {
  return request({
    url: '/sfe/sfeYdfDirector/' + id,
    method: 'get'
  })
}

// 新增依达方报-总监
export function addSfeYdfDirector(data) {
  return request({
    url: '/sfe/sfeYdfDirector',
    method: 'post',
    data: data
  })
}

// 修改依达方报-总监
export function updateSfeYdfDirector(data) {
  return request({
    url: '/sfe/sfeYdfDirector',
    method: 'put',
    data: data
  })
}

// 删除依达方报-总监
export function delSfeYdfDirector(id) {
  return request({
    url: '/sfe/sfeYdfDirector/' + id,
    method: 'delete'
  })
}
