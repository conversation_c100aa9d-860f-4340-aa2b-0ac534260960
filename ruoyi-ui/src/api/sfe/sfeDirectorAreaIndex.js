import request from '@/utils/request'

// 查询总监区指标列表
export function listSfeDirectorAreaIndex(query) {
  return request({
    url: '/sfe/sfeDirectorAreaIndex/list',
    method: 'get',
    params: query
  })
}

// 查询总监区指标详细
export function getSfeDirectorAreaIndex(id) {
  return request({
    url: '/sfe/sfeDirectorAreaIndex/' + id,
    method: 'get'
  })
}

// 新增总监区指标
export function addSfeDirectorAreaIndex(data) {
  return request({
    url: '/sfe/sfeDirectorAreaIndex',
    method: 'post',
    data: data
  })
}

// 修改总监区指标
export function updateSfeDirectorAreaIndex(data) {
  return request({
    url: '/sfe/sfeDirectorAreaIndex',
    method: 'put',
    data: data
  })
}

// 删除总监区指标
export function delSfeDirectorAreaIndex(id) {
  return request({
    url: '/sfe/sfeDirectorAreaIndex/' + id,
    method: 'delete'
  })
}
