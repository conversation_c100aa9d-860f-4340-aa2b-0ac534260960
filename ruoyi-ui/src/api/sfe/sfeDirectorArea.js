import request from '@/utils/request'

// 查询总监区报列表
export function listSfeDirectorArea(query) {
  return request({
    url: '/sfe/sfeDirectorArea/list',
    method: 'get',
    params: query
  })
}

// 查询总监区报详细
export function getSfeDirectorArea(id) {
  return request({
    url: '/sfe/sfeDirectorArea/' + id,
    method: 'get'
  })
}

// 新增总监区报
export function addSfeDirectorArea(data) {
  return request({
    url: '/sfe/sfeDirectorArea',
    method: 'post',
    data: data
  })
}

// 修改总监区报
export function updateSfeDirectorArea(data) {
  return request({
    url: '/sfe/sfeDirectorArea',
    method: 'put',
    data: data
  })
}

// 删除总监区报
export function delSfeDirectorArea(id) {
  return request({
    url: '/sfe/sfeDirectorArea/' + id,
    method: 'delete'
  })
}
