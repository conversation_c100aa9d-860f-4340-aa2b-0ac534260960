import request from '@/utils/request'

// 查询依达方报-大区列表
export function listSfeYdfRegion(query) {
  return request({
    url: '/sfe/sfeYdfRegion/list',
    method: 'get',
    params: query
  })
}

// 查询依达方报-大区详细
export function getSfeYdfRegion(id) {
  return request({
    url: '/sfe/sfeYdfRegion/' + id,
    method: 'get'
  })
}

// 新增依达方报-大区
export function addSfeYdfRegion(data) {
  return request({
    url: '/sfe/sfeYdfRegion',
    method: 'post',
    data: data
  })
}

// 修改依达方报-大区
export function updateSfeYdfRegion(data) {
  return request({
    url: '/sfe/sfeYdfRegion',
    method: 'put',
    data: data
  })
}

// 删除依达方报-大区
export function delSfeYdfRegion(id) {
  return request({
    url: '/sfe/sfeYdfRegion/' + id,
    method: 'delete'
  })
}
