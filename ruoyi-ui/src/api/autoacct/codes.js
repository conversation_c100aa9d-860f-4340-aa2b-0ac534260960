import request from '@/utils/request'

// 查询MPM编码列表
export function listCodes(query) {
  return request({
    url: '/autoacct/codes/list',
    method: 'get',
    params: query
  })
}

// 查询MPM编码详细
export function getCodes(mpmCode) {
  return request({
    url: '/autoacct/codes/' + mpmCode,
    method: 'get'
  })
}

// 新增MPM编码
export function addCodes(data) {
  return request({
    url: '/autoacct/codes',
    method: 'post',
    data: data
  })
}

// 修改MPM编码
export function updateCodes(data) {
  return request({
    url: '/autoacct/codes',
    method: 'put',
    data: data
  })
}

// 删除MPM编码
export function delCodes(mpmCode) {
  return request({
    url: '/autoacct/codes/' + mpmCode,
    method: 'delete'
  })
}
