import request from '@/utils/request'

// 查询工厂信息列表
export function listFactory(query) {
  return request({
    url: '/autoacct/factory/list',
    method: 'post',
    data: query
  })
}

// 查询工厂信息详细
export function getFactory(id) {
  return request({
    url: '/autoacct/factory/get',
    method: 'post',
    data: { id }
  })
}

// 新增工厂信息
export function addFactory(data) {
  return request({
    url: '/autoacct/factory/add',
    method: 'post',
    data: data
  })
}

// 修改工厂信息
export function updateFactory(data) {
  return request({
    url: '/autoacct/factory/edit',
    method: 'post',
    data: data
  })
}

// 删除工厂信息
export function delFactory(ids) {
  return request({
    url: '/autoacct/factory/remove',
    method: 'post',
    data: { ids }
  })
}

// 获取启用的工厂信息
export function getEnabledFactories() {
  return request({
    url: '/autoacct/factory/enabled',
    method: 'post'
  })
}

// 导出工厂信息
export function exportFactory(query) {
  return request({
    url: '/autoacct/factory/export',
    method: 'post',
    data: query
  })
}