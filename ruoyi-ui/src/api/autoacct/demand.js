import request from '@/utils/request'

// 查询BOM-物料需求列表
export function listDemand(query) {
  return request({
    url: '/autoacct/demand/list',
    method: 'get',
    params: query
  })
}

// 查询BOM-物料需求详细
export function getDemand(mpmCode) {
  return request({
    url: '/autoacct/demand/' + mpmCode,
    method: 'get'
  })
}

// 新增BOM-物料需求
export function addDemand(data) {
  return request({
    url: '/autoacct/demand',
    method: 'post',
    data: data
  })
}

// 修改BOM-物料需求
export function updateDemand(data) {
  return request({
    url: '/autoacct/demand',
    method: 'put',
    data: data
  })
}

// 删除BOM-物料需求
export function delDemand(mpmCode) {
  return request({
    url: '/autoacct/demand/' + mpmCode,
    method: 'delete'
  })
}
