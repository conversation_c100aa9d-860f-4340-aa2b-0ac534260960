import request from '@/utils/request'

// 查询物料计划列表
export function listPlan(query) {
  return request({
    url: '/autoacct/plan/list',
    method: 'get',
    params: query
  })
}

// 查询物料计划详细
export function getPlan(id) {
  return request({
    url: '/autoacct/plan/' + id,
    method: 'get'
  })
}

// 新增物料计划
export function addPlan(data) {
  return request({
    url: '/autoacct/plan',
    method: 'post',
    data: data
  })
}

// 修改物料计划
export function updatePlan(data) {
  return request({
    url: '/autoacct/plan',
    method: 'put',
    data: data
  })
}

// 删除物料计划
export function delPlan(id) {
  return request({
    url: '/autoacct/plan/' + id,
    method: 'delete'
  })
}
