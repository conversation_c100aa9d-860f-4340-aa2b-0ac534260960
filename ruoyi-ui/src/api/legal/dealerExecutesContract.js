import request from '@/utils/request'

// 查询经销商执行合同列表
export function listDealerExecutesContract(query) {
  return request({
    url: '/legal/dealerExecutesContract/list',
    method: 'get',
    params: query
  })
}

// 查询经销商执行合同详细
export function getDealerExecutesContract(dateCreated) {
  return request({
    url: '/legal/dealerExecutesContract/' + dateCreated,
    method: 'get'
  })
}

// 新增经销商执行合同
export function addDealerExecutesContract(data) {
  return request({
    url: '/legal/dealerExecutesContract',
    method: 'post',
    data: data
  })
}

// 修改经销商执行合同
export function updateDealerExecutesContract(data) {
  return request({
    url: '/legal/dealerExecutesContract',
    method: 'put',
    data: data
  })
}

// 删除经销商执行合同
export function delDealerExecutesContract(dateCreated) {
  return request({
    url: '/legal/dealerExecutesContract/' + dateCreated,
    method: 'delete'
  })
}
