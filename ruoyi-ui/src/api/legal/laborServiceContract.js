import request from '@/utils/request'

// 查询劳务对公合同列表
export function listLaborServiceContract(query) {
  return request({
    url: '/legal/laborServiceContract/list',
    method: 'get',
    params: query
  })
}

// 查询劳务对公合同详细
export function getLaborServiceContract(dateCreated) {
  return request({
    url: '/legal/laborServiceContract/' + dateCreated,
    method: 'get'
  })
}

// 新增劳务对公合同
export function addLaborServiceContract(data) {
  return request({
    url: '/legal/laborServiceContract',
    method: 'post',
    data: data
  })
}

// 修改劳务对公合同
export function updateLaborServiceContract(data) {
  return request({
    url: '/legal/laborServiceContract',
    method: 'put',
    data: data
  })
}

// 删除劳务对公合同
export function delLaborServiceContract(dateCreated) {
  return request({
    url: '/legal/laborServiceContract/' + dateCreated,
    method: 'delete'
  })
}
