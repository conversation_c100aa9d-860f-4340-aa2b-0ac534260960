import request from '@/utils/request'

// 查询营销合同审批列表
export function listYxContractApproval(query) {
  return request({
    url: '/legal/yxContractApproval/list',
    method: 'get',
    params: query
  })
}

// 查询营销合同审批详细
export function getYxContractApproval(dateCreated) {
  return request({
    url: '/legal/yxContractApproval/' + dateCreated,
    method: 'get'
  })
}

// 新增营销合同审批
export function addYxContractApproval(data) {
  return request({
    url: '/legal/yxContractApproval',
    method: 'post',
    data: data
  })
}

// 修改营销合同审批
export function updateYxContractApproval(data) {
  return request({
    url: '/legal/yxContractApproval',
    method: 'put',
    data: data
  })
}

// 删除营销合同审批
export function delYxContractApproval(dateCreated) {
  return request({
    url: '/legal/yxContractApproval/' + dateCreated,
    method: 'delete'
  })
}
