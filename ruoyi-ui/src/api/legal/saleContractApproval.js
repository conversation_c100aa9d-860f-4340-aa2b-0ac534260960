import request from '@/utils/request'

// 查询销售类合同审批列表
export function listSaleContractApproval(query) {
  return request({
    url: '/legal/saleContractApproval/list',
    method: 'get',
    params: query
  })
}

// 查询销售类合同审批详细
export function getSaleContractApproval(dateCreated) {
  return request({
    url: '/legal/saleContractApproval/' + dateCreated,
    method: 'get'
  })
}

// 新增销售类合同审批
export function addSaleContractApproval(data) {
  return request({
    url: '/legal/saleContractApproval',
    method: 'post',
    data: data
  })
}

// 修改销售类合同审批
export function updateSaleContractApproval(data) {
  return request({
    url: '/legal/saleContractApproval',
    method: 'put',
    data: data
  })
}

// 删除销售类合同审批
export function delSaleContractApproval(dateCreated) {
  return request({
    url: '/legal/saleContractApproval/' + dateCreated,
    method: 'delete'
  })
}
