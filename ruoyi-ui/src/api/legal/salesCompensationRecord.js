import request from '@/utils/request'

// 查询销售补差备案列表
export function listSalesCompensationRecord(query) {
  return request({
    url: '/legal/salesCompensationRecord/list',
    method: 'get',
    params: query
  })
}

// 查询销售补差备案详细
export function getSalesCompensationRecord(dateCreated) {
  return request({
    url: '/legal/salesCompensationRecord/' + dateCreated,
    method: 'get'
  })
}

// 新增销售补差备案
export function addSalesCompensationRecord(data) {
  return request({
    url: '/legal/salesCompensationRecord',
    method: 'post',
    data: data
  })
}

// 修改销售补差备案
export function updateSalesCompensationRecord(data) {
  return request({
    url: '/legal/salesCompensationRecord',
    method: 'put',
    data: data
  })
}

// 删除销售补差备案
export function delSalesCompensationRecord(dateCreated) {
  return request({
    url: '/legal/salesCompensationRecord/' + dateCreated,
    method: 'delete'
  })
}
