import request from '@/utils/request'

// 查询IIT研究合同审批列表
export function listResearchContract(query) {
  return request({
    url: '/legal/researchContract/list',
    method: 'get',
    params: query
  })
}

// 查询IIT研究合同审批详细
export function getResearchContract(dateCreated) {
  return request({
    url: '/legal/researchContract/' + dateCreated,
    method: 'get'
  })
}

// 新增IIT研究合同审批
export function addResearchContract(data) {
  return request({
    url: '/legal/researchContract',
    method: 'post',
    data: data
  })
}

// 修改IIT研究合同审批
export function updateResearchContract(data) {
  return request({
    url: '/legal/researchContract',
    method: 'put',
    data: data
  })
}

// 删除IIT研究合同审批
export function delResearchContract(dateCreated) {
  return request({
    url: '/legal/researchContract/' + dateCreated,
    method: 'delete'
  })
}
