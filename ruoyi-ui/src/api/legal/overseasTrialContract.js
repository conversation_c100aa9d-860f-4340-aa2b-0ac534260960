import request from '@/utils/request'

// 查询海外试用合同列表
export function listOverseasTrialContract(query) {
  return request({
    url: '/legal/overseasTrialContract/list',
    method: 'get',
    params: query
  })
}

// 查询海外试用合同详细
export function getOverseasTrialContract(dateCreated) {
  return request({
    url: '/legal/overseasTrialContract/' + dateCreated,
    method: 'get'
  })
}

// 新增海外试用合同
export function addOverseasTrialContract(data) {
  return request({
    url: '/legal/overseasTrialContract',
    method: 'post',
    data: data
  })
}

// 修改海外试用合同
export function updateOverseasTrialContract(data) {
  return request({
    url: '/legal/overseasTrialContract',
    method: 'put',
    data: data
  })
}

// 删除海外试用合同
export function delOverseasTrialContract(dateCreated) {
  return request({
    url: '/legal/overseasTrialContract/' + dateCreated,
    method: 'delete'
  })
}
