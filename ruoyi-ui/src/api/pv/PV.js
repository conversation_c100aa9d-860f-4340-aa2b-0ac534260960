import request from '@/utils/request'

// 查询PV列表
export function listPV(query) {
  return request({
    url: '/report/PV/list',
    method: 'get',
    params: query
  })
}

// 查询PV详细
export function getPV() {
  return request({
    url: '/report/PV/getFamily',
    method: 'get'
  })
}
export function getProtocols() {
  return request({
    url: '/report/PV/getProtocol',
    method: 'get'
  })
}
export function getStudyIds() {
  return request({
    url: '/report/PV/getStudyId',
    method: 'get'
  })
}
export function getArmId() {
  return request({
    url: '/report/PV/armId',
    method: 'get'
  })
}


// 新增PV
export function addPV(data) {
  return request({
    url: '/report/PV',
    method: 'post',
    data: data
  })
}

// 修改PV
export function updatePV(data) {
  return request({
    url: '/report/PV',
    method: 'put',
    data: data
  })
}

// 删除PV
export function delPV(caseId) {
  return request({
    url: '/report/PV/' + caseId,
    method: 'delete'
  })
}
