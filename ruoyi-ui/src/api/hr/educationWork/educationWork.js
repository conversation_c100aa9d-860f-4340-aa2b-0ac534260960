import request from '@/utils/request'

/**
 * 查询
 * @param query
 */
export function queryEducationWork(query) {
  return request({
    url: '/hr/educationWork/list',
    method: 'get',
    params: query
  })
}

export function queryEducations(query) {
  return request({
    url: '/hr/personnel/exp/education/list',
    method: 'get',
    params: query
  })
}

export function queryWorks(query) {
  return request({
    url: '/hr/educationWork/works/list',
    method: 'get',
    params: query
  })
}
