import request from '@/utils/request'

// 查询物资出门申请列表
export function listWzcm(query) {
  return request({
    url: '/system/wzcm/list',
    method: 'get',
    params: query
  })
}

// 查询物资出门申请详细
export function getWzcm(fdId) {
  return request({
    url: '/system/wzcm/' + fdId,
    method: 'get'
  })
}

// 新增物资出门申请

export function addWzcm(data) {
  return request({
    url: '/system/wzcm',
    method: 'post',
    data: data
  })
}

// 修改物资出门申请

export function updateWzcm(data) {
  return request({
    url: '/system/wzcm',
    method: 'put',
    data: data
  })
}

// 删除物资出门申请

export function delWzcm(fdId) {
  return request({
    url: '/system/wzcm/' + fdId,
    method: 'delete'
  })
}
