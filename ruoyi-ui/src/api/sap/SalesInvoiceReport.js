import request from '@/utils/request'

// 查询SAP销售发票查询报表列表
export function listSalesInvoiceReport(query) {
  return request({
    url: '/sap/SalesInvoiceReport/list',
    method: 'get',
    params: query
  })
}

// 查询SAP销售发票查询报表详细
export function getSalesInvoiceReport(sid) {
  return request({
    url: '/sap/SalesInvoiceReport/' + sid,
    method: 'get'
  })
}

// 新增SAP销售发票查询报表
export function addSalesInvoiceReport(data) {
  return request({
    url: '/sap/SalesInvoiceReport',
    method: 'post',
    data: data
  })
}

// 修改SAP销售发票查询报表
export function updateSalesInvoiceReport(data) {
  return request({
    url: '/sap/SalesInvoiceReport',
    method: 'put',
    data: data
  })
}

// 删除SAP销售发票查询报表
export function delSalesInvoiceReport(sid) {
  return request({
    url: '/sap/SalesInvoiceReport/' + sid,
    method: 'delete'
  })
}
export function queryListZRPSD004(sales_organization,invoice_date,sale_office,seller) {
  return request({
    url: '/sap/SalesInvoiceReport/queryList',
    method: 'post',
    params: {
      sales_organization,
      invoice_date,
      sale_office,
      seller
    },
  })
}
