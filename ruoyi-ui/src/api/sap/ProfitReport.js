import request from '@/utils/request'

// 查询SAP利润报表列表
export function listProfitReport(query) {
  return request({
    url: '/sap/ProfitReport/list',
    method: 'get',
    params: query
  })
}

// 查询SAP利润报表详细
export function getProfitReport(sid) {
  return request({
    url: '/sap/ProfitReport/' + sid,
    method: 'get'
  })
}

// 新增SAP利润报表
export function addProfitReport(data) {
  return request({
    url: '/sap/ProfitReport',
    method: 'post',
    data: data
  })
}

// 修改SAP利润报表
export function updateProfitReport(data) {
  return request({
    url: '/sap/ProfitReport',
    method: 'put',
    data: data
  })
}

// 删除SAP利润报表
export function delProfitReport(sid) {
  return request({
    url: '/sap/ProfitReport/' + sid,
    method: 'delete'
  })
}

export function queryListZRPFI004(company_num,fiscal_year,fiscal_month) {
  return request({
    url: '/sap/ProfitReport/queryList',
    method: 'post',
    params: {
      company_num,
      fiscal_year,
      fiscal_month,
    },
  })
}

// 查询SAP现金流量报表列表
export function listBy(query) {
  return request({
    url: '/sap/ProfitReport/listBy',
    method: 'get',
    params: query
  })
}

