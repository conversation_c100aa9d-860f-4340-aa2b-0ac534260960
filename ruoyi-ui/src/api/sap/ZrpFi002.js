import request from '@/utils/request'

// 查询SAP执行获利能力报告报表列表
export function listZrpFi002(query) {
  return request({
    url: '/sap/ZrpFi002/list',
    method: 'get',
    params: query
  })
}

// 查询SAP执行获利能力报告报表详细
export function getZrpFi002(sid) {
  return request({
    url: '/sap/ZrpFi002/' + sid,
    method: 'get'
  })
}

// 根据公司代码、期间/年度查询SAP执行获利能力报告报表列表
export function queryListZrpFi002(code,startYear,endYear,query) {
  return request({
    url: '/sap/ZrpFi002/queryList',
    method: 'post',
    params: {
      code,
      startYear,
      endYear
    },
    data: {
      query: query
    }
  })
}
