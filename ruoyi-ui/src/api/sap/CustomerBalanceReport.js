import request from '@/utils/request'

// 查询以本币计的客户余额报表列表
export function listCustomerBalanceReport(query) {
  return request({
    url: '/sap/CustomerBalanceReport/list',
    method: 'get',
    params: query
  })
}

// 查询以本币计的客户余额报表详细
export function getCustomerBalanceReport(sid) {
  return request({
    url: '/sap/CustomerBalanceReport/' + sid,
    method: 'get'
  })
}

// 新增以本币计的客户余额报表
export function addCustomerBalanceReport(data) {
  return request({
    url: '/sap/CustomerBalanceReport',
    method: 'post',
    data: data
  })
}

// 修改以本币计的客户余额报表
export function updateCustomerBalanceReport(data) {
  return request({
    url: '/sap/CustomerBalanceReport',
    method: 'put',
    data: data
  })
}

// 删除以本币计的客户余额报表
export function delCustomerBalanceReport(sid) {
  return request({
    url: '/sap/CustomerBalanceReport/' + sid,
    method: 'delete'
  })
}

export function queryListZRPSD006(companyNum,clientNum,fiscalYear) {
  return request({
    url: '/sap/CustomerBalanceReport/queryList',
    method: 'post',
    params: {
      companyNum,
      clientNum,
      fiscalYear
    },
  })
}
