import request from '@/utils/request'

// 查询固定资产清单报表列表
export function listFixedAssetsReport(query) {
  return request({
    url: '/sap/fixedAssetsReport/list',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表1列表
export function listDetailsTable1(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList1',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表2列表
export function listDetailsTable2(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList2',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表3列表
export function listDetailsTable3(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList3',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表4列表
export function listDetailsTable4(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList4',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表5列表
export function listDetailsTable5(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList5',
    method: 'get',
    params: query
  })
}

// 查询固定资产明细表4列表
export function listDetailsTable6(query) {
  return request({
    url: '/sap/fixedAssetsReport/detailsTableList6',
    method: 'get',
    params: query
  })
}

// 查询固定资产清单报表详细
export function getFixedAssetsReport(sid) {
  return request({
    url: '/sap/fixedAssetsReport/' + sid,
    method: 'get'
  })
}

// 新增固定资产清单报表
export function addFixedAssetsReport(data) {
  return request({
    url: '/sap/fixedAssetsReport',
    method: 'post',
    data: data
  })
}

// 修改固定资产清单报表
export function updateFixedAssetsReport(data) {
  return request({
    url: '/sap/fixedAssetsReport',
    method: 'put',
    data: data
  })
}

// 删除固定资产清单报表
export function delFixedAssetsReport(sid) {
  return request({
    url: '/sap/fixedAssetsReport/' + sid,
    method: 'delete'
  })
}

//到SAP查询
export function queryListZRPFI007(company_num,fiscal_year,fiscal_month) {
  return request({
    url: '/sap/fixedAssetsReport/queryList',
    method: 'post',
    params: {
      company_num,
      fiscal_year,
      fiscal_month,
    },
  })
}
