import request from '@/utils/request'

// 查询营销财务收款流水报表列表
export function listCollectionFlowReport(query) {
  return request({
    url: '/sap/CollectionFlowReport/list',
    method: 'get',
    params: query
  })
}

// 查询营销财务收款流水报表详细
export function getCollectionFlowReport(sid) {
  return request({
    url: '/sap/CollectionFlowReport/' + sid,
    method: 'get'
  })
}

// 新增营销财务收款流水报表
export function addCollectionFlowReport(data) {
  return request({
    url: '/sap/CollectionFlowReport',
    method: 'post',
    data: data
  })
}

// 修改营销财务收款流水报表
export function updateCollectionFlowReport(data) {
  return request({
    url: '/sap/CollectionFlowReport',
    method: 'put',
    data: data
  })
}

// 删除营销财务收款流水报表
export function delCollectionFlowReport(sid) {
  return request({
    url: '/sap/CollectionFlowReport/' + sid,
    method: 'delete'
  })
}

// 根据公司代码、交易日期区间查询
export function queryListZRPSD003(code,beginTime,endTime) {
  return request({
    url: '/sap/CollectionFlowReport/queryList',
    method: 'post',
    params: {
      code,
      beginTime,
      endTime
    },

  })
}
