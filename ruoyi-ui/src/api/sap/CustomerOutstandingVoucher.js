import request from '@/utils/request'

// 查询客户未清凭证报表列表
export function listCustomerOutstandingVoucher(query) {
  return request({
    url: '/sap/CustomerOutstandingVoucher/list',
    method: 'get',
    params: query
  })
}

// 查询客户未清凭证报表详细
export function getCustomerOutstandingVoucher(sid) {
  return request({
    url: '/sap/CustomerOutstandingVoucher/' + sid,
    method: 'get'
  })
}

// 新增客户未清凭证报表
export function addCustomerOutstandingVoucher(data) {
  return request({
    url: '/sap/CustomerOutstandingVoucher',
    method: 'post',
    data: data
  })
}

// 修改客户未清凭证报表
export function updateCustomerOutstandingVoucher(data) {
  return request({
    url: '/sap/CustomerOutstandingVoucher',
    method: 'put',
    data: data
  })
}

// 删除客户未清凭证报表
export function delCustomerOutstandingVoucher(sid) {
  return request({
    url: '/sap/CustomerOutstandingVoucher/' + sid,
    method: 'delete'
  })
}

export function queryListZRPSD007(companyNum,clientNum,fiscalYear) {
  return request({
    url: '/sap/CustomerOutstandingVoucher/queryList',
    method: 'post',
    params: {
      companyNum,
      clientNum,
      fiscalYear
    },
  })
}
