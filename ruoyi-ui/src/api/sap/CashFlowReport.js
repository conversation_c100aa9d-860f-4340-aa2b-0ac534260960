import request from '@/utils/request'

// 查询SAP现金流量报表列表
export function listCashFlowReport(query) {
  return request({
    url: '/sap/CashFlowReport/list',
    method: 'get',
    params: query
  })
}

// 查询SAP现金流量报表详细
export function getCashFlowReport(sid) {
  return request({
    url: '/sap/CashFlowReport/' + sid,
    method: 'get'
  })
}

// 新增SAP现金流量报表
export function addCashFlowReport(data) {
  return request({
    url: '/sap/CashFlowReport',
    method: 'post',
    data: data
  })
}

// 修改SAP现金流量报表
export function updateCashFlowReport(data) {
  return request({
    url: '/sap/CashFlowReport',
    method: 'put',
    data: data
  })
}

// 删除SAP现金流量报表
export function delCashFlowReport(sid) {
  return request({
    url: '/sap/CashFlowReport/' + sid,
    method: 'delete'
  })
}

export function queryListZRPFI005(company_num,fiscal_year,fiscal_month) {
  return request({
    url: '/sap/CashFlowReport/queryList',
    method: 'post',
    params: {
      company_num,
      fiscal_year,
      fiscal_month,
    },
  })
}

// 查询SAP现金流量报表列表
export function listBy(query) {
  return request({
    url: '/sap/CashFlowReport/listBy',
    method: 'get',
    params: query
  })
}
