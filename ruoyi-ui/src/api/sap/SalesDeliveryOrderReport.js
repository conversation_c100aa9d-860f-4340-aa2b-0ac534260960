import request from '@/utils/request'

// 查询SAP销售交货单查询列表
export function listSalesDeliveryOrderReport(query) {
  return request({
    url: '/sap/SalesDeliveryOrderReport/list',
    method: 'get',
    params: query
  })
}

// 查询SAP销售交货单查询详细
export function getSalesDeliveryOrderReport(sid) {
  return request({
    url: '/sap/SalesDeliveryOrderReport/' + sid,
    method: 'get'
  })
}

// 新增SAP销售交货单查询
export function addSalesDeliveryOrderReport(data) {
  return request({
    url: '/sap/SalesDeliveryOrderReport',
    method: 'post',
    data: data
  })
}

// 修改SAP销售交货单查询
export function updateSalesDeliveryOrderReport(data) {
  return request({
    url: '/sap/SalesDeliveryOrderReport',
    method: 'put',
    data: data
  })
}

// 删除SAP销售交货单查询
export function delSalesDeliveryOrderReport(sid) {
  return request({
    url: '/sap/SalesDeliveryOrderReport/' + sid,
    method: 'delete'
  })
}

export function queryListZRPSD005(shippingPoint,actualShippingDate,seller) {
  return request({
    url: '/sap/SalesDeliveryOrderReport/queryList',
    method: 'post',
    params: {
      shippingPoint,
      actualShippingDate,
      seller
    },
  })
}
