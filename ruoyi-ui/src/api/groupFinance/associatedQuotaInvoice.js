import request from '@/utils/request'

// 查询已关联通用定额发票列表
export function listAssociatedQuotaInvoice(query) {
  return request({
    url: '/groupFinance/associatedQuotaInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联通用定额发票详细
export function getAssociatedQuotaInvoice(userName) {
  return request({
    url: '/groupFinance/associatedQuotaInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联通用定额发票
export function addAssociatedQuotaInvoice(data) {
  return request({
    url: '/groupFinance/associatedQuotaInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联通用定额发票
export function updateAssociatedQuotaInvoice(data) {
  return request({
    url: '/groupFinance/associatedQuotaInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联通用定额发票
export function delAssociatedQuotaInvoice(userName) {
  return request({
    url: '/groupFinance/associatedQuotaInvoice/' + userName,
    method: 'delete'
  })
}
