import request from '@/utils/request'

// 查询已关联火车发票列表
export function listAssociatedTrainInvoice(query) {
  return request({
    url: '/groupFinance/associatedTrainInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联火车发票详细
export function getAssociatedTrainInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTrainInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联火车发票
export function addAssociatedTrainInvoice(data) {
  return request({
    url: '/groupFinance/associatedTrainInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联火车发票
export function updateAssociatedTrainInvoice(data) {
  return request({
    url: '/groupFinance/associatedTrainInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联火车发票
export function delAssociatedTrainInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTrainInvoice/' + userName,
    method: 'delete'
  })
}
