import request from '@/utils/request'

// 查询已关联出租车发票列表
export function listAssociatedTaxiInvoice(query) {
  return request({
    url: '/groupFinance/associatedTaxiInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联出租车发票详细
export function getAssociatedTaxiInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTaxiInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联出租车发票
export function addAssociatedTaxiInvoice(data) {
  return request({
    url: '/groupFinance/associatedTaxiInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联出租车发票
export function updateAssociatedTaxiInvoice(data) {
  return request({
    url: '/groupFinance/associatedTaxiInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联出租车发票
export function delAssociatedTaxiInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTaxiInvoice/' + userName,
    method: 'delete'
  })
}
