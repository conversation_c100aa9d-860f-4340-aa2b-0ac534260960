import request from '@/utils/request'

// 查询物流费用采购申请明细列表
export function listLogisticsCostDetails(query) {
  return request({
    url: '/groupFinance/logisticsCostDetails/list',
    method: 'get',
    params: query
  })
}

// 查询物流费用采购申请明细详细
export function getLogisticsCostDetails(id) {
  return request({
    url: '/groupFinance/logisticsCostDetails/' + id,
    method: 'get'
  })
}

// 新增物流费用采购申请明细
export function addLogisticsCostDetails(data) {
  return request({
    url: '/groupFinance/logisticsCostDetails',
    method: 'post',
    data: data
  })
}

// 修改物流费用采购申请明细
export function updateLogisticsCostDetails(data) {
  return request({
    url: '/groupFinance/logisticsCostDetails',
    method: 'put',
    data: data
  })
}

// 删除物流费用采购申请明细
export function delLogisticsCostDetails(id) {
  return request({
    url: '/groupFinance/logisticsCostDetails/' + id,
    method: 'delete'
  })
}
