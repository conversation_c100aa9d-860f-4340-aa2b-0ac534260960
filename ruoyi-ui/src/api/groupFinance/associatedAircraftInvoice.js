import request from '@/utils/request'

// 查询已关联飞机发票列表
export function listAssociatedAircraftInvoice(query) {
  return request({
    url: '/groupFinance/associatedAircraftInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联飞机发票详细
export function getAssociatedAircraftInvoice(userName) {
  return request({
    url: '/groupFinance/associatedAircraftInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联飞机发票
export function addAssociatedAircraftInvoice(data) {
  return request({
    url: '/groupFinance/associatedAircraftInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联飞机发票
export function updateAssociatedAircraftInvoice(data) {
  return request({
    url: '/groupFinance/associatedAircraftInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联飞机发票
export function delAssociatedAircraftInvoice(userName) {
  return request({
    url: '/groupFinance/associatedAircraftInvoice/' + userName,
    method: 'delete'
  })
}
