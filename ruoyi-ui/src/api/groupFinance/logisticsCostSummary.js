import request from '@/utils/request'

// 查询物流费用采购申请汇总列表
export function listLogisticsCostSummary(query) {
  return request({
    url: '/groupFinance/logisticsCostSummary/list',
    method: 'get',
    params: query
  })
}

// 查询物流费用采购申请汇总详细
export function getLogisticsCostSummary(id) {
  return request({
    url: '/groupFinance/logisticsCostSummary/' + id,
    method: 'get'
  })
}

// 新增物流费用采购申请汇总
export function addLogisticsCostSummary(data) {
  return request({
    url: '/groupFinance/logisticsCostSummary',
    method: 'post',
    data: data
  })
}

// 修改物流费用采购申请汇总
export function updateLogisticsCostSummary(data) {
  return request({
    url: '/groupFinance/logisticsCostSummary',
    method: 'put',
    data: data
  })
}

// 删除物流费用采购申请汇总
export function delLogisticsCostSummary(id) {
  return request({
    url: '/groupFinance/logisticsCostSummary/' + id,
    method: 'delete'
  })
}
