import request from '@/utils/request'

// 查询已关联通行费发票列表
export function listAssociatedTollInvoice(query) {
  return request({
    url: '/groupFinance/associatedTollInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联通行费发票详细
export function getAssociatedTollInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTollInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联通行费发票
export function addAssociatedTollInvoice(data) {
  return request({
    url: '/groupFinance/associatedTollInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联通行费发票
export function updateAssociatedTollInvoice(data) {
  return request({
    url: '/groupFinance/associatedTollInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联通行费发票
export function delAssociatedTollInvoice(userName) {
  return request({
    url: '/groupFinance/associatedTollInvoice/' + userName,
    method: 'delete'
  })
}
