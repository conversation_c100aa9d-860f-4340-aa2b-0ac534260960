import request from '@/utils/request'

// 查询已关联通用机打发票列表
export function listMachinePrintedInvoice(query) {
  return request({
    url: '/groupFinance/machinePrintedInvoice/list',
    method: 'get',
    params: query
  })
}

// 查询已关联通用机打发票详细
export function getMachinePrintedInvoice(userName) {
  return request({
    url: '/groupFinance/machinePrintedInvoice/' + userName,
    method: 'get'
  })
}

// 新增已关联通用机打发票
export function addMachinePrintedInvoice(data) {
  return request({
    url: '/groupFinance/machinePrintedInvoice',
    method: 'post',
    data: data
  })
}

// 修改已关联通用机打发票
export function updateMachinePrintedInvoice(data) {
  return request({
    url: '/groupFinance/machinePrintedInvoice',
    method: 'put',
    data: data
  })
}

// 删除已关联通用机打发票
export function delMachinePrintedInvoice(userName) {
  return request({
    url: '/groupFinance/machinePrintedInvoice/' + userName,
    method: 'delete'
  })
}
