

//研发组图表
import request from "@/utils/request";

/**
 * SAP  物料库存  图表
 */
export  function getMaterialInventoryChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getMaterialInventoryChart',
    method:'get',
    params:query
  })
}

/**
 * SAP  物料检索信息
 */
export  function getMaterialInfo(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getMaterialInfo',
    method:'get',
    params:query
  })
}

/**
 * 合同结算单价
 * 项目研究内容 图表
 */
export  function getProjectResearchContentChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getProjectResearchContentChart',
    method:'get',
    params:query
  })
}

/**
 * 项目号 检索信息
 */
export  function getProjectInfo(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getProjectInfo',
    method:'get',
    params:query
  })
}

/**
 * 合同结算单价
 * 管线对比 图表
 */
export  function getPipelineComparisonChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getPipelineComparisonChart',
    method:'get',
    params:query
  })
}

/**
 * 合同结算单价
 * 项目对比 图表
 */
export  function getProjectComparisonChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getProjectComparisonChart',
    method:'get',
    params:query
  })
}

/**
 * 合同结算单价
 * 不同项目同一类型对比 图表
 */
export  function getProjectTypeComparisonChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getProjectTypeComparisonChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 各年度研发费用分类汇总 图表
 */
export  function getResearchDevelopmentExpenseYearClassificationSummaryChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getResearchDevelopmentExpenseYearClassificationSummaryChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 成本分类汇总占比 图表
 */
export  function getCostClassificationSummaryProportionChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getCostClassificationSummaryProportionChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 各公司研发费用汇总 图表
 */
export  function getCompanyResearchDevelopmentExpenseSummaryChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getCompanyResearchDevelopmentExpenseSummaryChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 成本分类汇总同比 图表
 */
export  function getCostClassificationSummaryYOYChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getCostClassificationSummaryYOYChart',
    method:'get',
    params:query
  })
}


/**
 * 研发费用
 * 单管线、项目研发费用汇总同比 图表
 */
export  function getSinglePipelineProjectSummaryYOYChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getSinglePipelineProjectSummaryYOYChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 多管线、研发费用汇总同比 图表
 */
export  function getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getMultiplePipelineResearchDevelopmentExpenseSummaryYOYChart',
    method:'get',
    params:query
  })
}

/**
 * 研发费用
 * 单项目各年度成本类别 图表
 */
export  function getSingleProjectYearCostChart(query){
  return request({
    url:'/groupFinance/researchDevelopmentGroupChart/getSingleProjectYearCostChart',
    method:'get',
    params:query
  })
}

