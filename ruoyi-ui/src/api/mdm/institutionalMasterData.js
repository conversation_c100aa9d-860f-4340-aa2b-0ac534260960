import request from '@/utils/request'

// 查询机构主数据列表
export function listInstitutionalMasterData(query) {
  return request({
    url: '/mdm/institutionalMasterData/list',
    method: 'get',
    params: query
  })
}

// 查询机构主数据详细
export function getInstitutionalMasterData(id) {
  return request({
    url: '/mdm/institutionalMasterData/' + id,
    method: 'get'
  })
}

// 新增机构主数据
export function addInstitutionalMasterData(data) {
  return request({
    url: '/mdm/institutionalMasterData',
    method: 'post',
    data: data
  })
}

// 修改机构主数据
export function updateInstitutionalMasterData(data) {
  return request({
    url: '/mdm/institutionalMasterData',
    method: 'put',
    data: data
  })
}

// 删除机构主数据
export function delInstitutionalMasterData(id) {
  return request({
    url: '/mdm/institutionalMasterData/' + id,
    method: 'delete'
  })
}
