import request from '@/utils/request'

// 查询OA机构主数据清洗列表
export function listOaInstitutionalMasterData(query) {
  return request({
    url: '/mdm/oaInstitutionalMasterData/list',
    method: 'get',
    params: query
  })
}

// 查询OA机构主数据清洗详细
export function getOaInstitutionalMasterData(id) {
  return request({
    url: '/mdm/oaInstitutionalMasterData/' + id,
    method: 'get'
  })
}

// 新增OA机构主数据清洗
export function addOaInstitutionalMasterData(data) {
  return request({
    url: '/mdm/oaInstitutionalMasterData',
    method: 'post',
    data: data
  })
}

// 修改OA机构主数据清洗
export function updateOaInstitutionalMasterData(data) {
  return request({
    url: '/mdm/oaInstitutionalMasterData',
    method: 'put',
    data: data
  })
}

// 删除OA机构主数据清洗
export function delOaInstitutionalMasterData(id) {
  return request({
    url: '/mdm/oaInstitutionalMasterData/' + id,
    method: 'delete'
  })
}
