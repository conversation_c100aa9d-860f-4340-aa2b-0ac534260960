import request from '@/utils/request'

// 查询企业信息列表
export function listInformation(query) {
  return request({
    url: '/platform/information/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息详细
export function getInformation(id) {
  return request({
    url: '/platform/information/' + id,
    method: 'get'
  })
}

// 新增企业信息
export function addInformation(data) {
  return request({
    url: '/platform/information',
    method: 'post',
    data: data
  })
}

// 修改企业信息
export function updateInformation(data) {
  return request({
    url: '/platform/information',
    method: 'put',
    data: data
  })
}

// 删除企业信息
export function delInformation(id) {
  return request({
    url: '/platform/information/' + id,
    method: 'delete'
  })
}
