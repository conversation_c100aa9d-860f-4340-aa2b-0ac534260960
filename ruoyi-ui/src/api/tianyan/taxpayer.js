import request from '@/utils/request'

// 查询纳税人列表
export function listTaxpayer(query) {
  return request({
    url: '/tianyan/taxpayer/list',
    method: 'get',
    params: query
  })
}

// 查询纳税人列表
export function getTaxpayerList(name) {
  return request({
    url: '/tianyan/taxpayer/getTaxpayerList/' + name,
    method: 'get'
  })
}

// 查询纳税人详细
export function getTaxpayer(id) {
  return request({
    url: '/tianyan/taxpayer/' + id,
    method: 'get'
  })
}

// 新增纳税人
export function addTaxpayer(data) {
  return request({
    url: '/tianyan/taxpayer',
    method: 'post',
    data: data
  })
}

// 修改纳税人
export function updateTaxpayer(data) {
  return request({
    url: '/tianyan/taxpayer',
    method: 'put',
    data: data
  })
}

// 删除纳税人
export function delTaxpayer(id) {
  return request({
    url: '/tianyan/taxpayer/' + id,
    method: 'delete'
  })
}
