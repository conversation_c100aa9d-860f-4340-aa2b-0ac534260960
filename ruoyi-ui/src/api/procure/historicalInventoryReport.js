import request from '@/utils/request'

// 查询SAP历史库存报表列表
export function listHistoricalInventoryReport(query) {
  return request({
    url: '/procure/historicalInventoryReport/list',
    method: 'get',
    params: query
  })
}

// 查询SAP历史库存报表详细
export function getHistoricalInventoryReport(sid) {
  return request({
    url: '/procure/historicalInventoryReport/' + sid,
    method: 'get'
  })
}

// 新增SAP历史库存报表
export function addHistoricalInventoryReport(data) {
  return request({
    url: '/procure/historicalInventoryReport',
    method: 'post',
    data: data
  })
}

// 修改SAP历史库存报表
export function updateHistoricalInventoryReport(data) {
  return request({
    url: '/procure/historicalInventoryReport',
    method: 'put',
    data: data
  })
}

// 删除SAP历史库存报表
export function delHistoricalInventoryReport(sid) {
  return request({
    url: '/procure/historicalInventoryReport/' + sid,
    method: 'delete'
  })
}
