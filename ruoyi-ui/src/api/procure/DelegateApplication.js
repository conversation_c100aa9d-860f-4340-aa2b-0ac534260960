import request from '@/utils/request'

// 查询SAP历史库存报表列表
export function DelegateApplicationList(query) {
  return request({
    url: '/procure/DelegateApplication/list',
    method: 'get',
    params: query
  })
}

export function getRole(userName,tableType) {
  return request({
    url: '/procure/DelegateApplication/getRole/' + userName +"/" + tableType,
    method: 'get'
  })
}

export function getCompany(userName,tableType) {
  return request({
    url: '/procure/DelegateApplication/getCompany/' + userName + "/" + tableType,
    method: 'get'
  })
}

export function DelegateApplicationListTow(query) {
  return request({
    url: '/procure/DelegateApplication/listTow',
    method: 'get',
    params: query
  })
}
