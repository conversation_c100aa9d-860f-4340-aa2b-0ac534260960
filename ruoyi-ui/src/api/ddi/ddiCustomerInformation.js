import request from '@/utils/request'

// 查询DDI客户信息列表
export function listDdiCustomerInformation(query) {
  return request({
    url: '/ddi/ddiCustomerInformation/list',
    method: 'get',
    params: query
  })
}

// 查询DDI客户信息详细
export function getDdiCustomerInformation(id) {
  return request({
    url: '/ddi/ddiCustomerInformation/' + id,
    method: 'get'
  })
}

// 新增DDI客户信息
export function addDdiCustomerInformation(data) {
  return request({
    url: '/ddi/ddiCustomerInformation',
    method: 'post',
    data: data
  })
}

// 修改DDI客户信息
export function updateDdiCustomerInformation(data) {
  return request({
    url: '/ddi/ddiCustomerInformation',
    method: 'put',
    data: data
  })
}

// 删除DDI客户信息
export function delDdiCustomerInformation(id) {
  return request({
    url: '/ddi/ddiCustomerInformation/' + id,
    method: 'delete'
  })
}
