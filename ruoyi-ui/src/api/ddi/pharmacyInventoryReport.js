import request from '@/utils/request'

// 查询药房进销存报表列表
export function listPharmacyInventoryReport(query) {
  return request({
    url: '/ddi/pharmacyInventoryReport/list',
    method: 'get',
    params: query
  })
}

// 查询药房进销存报表详细
export function getPharmacyInventoryReport(id) {
  return request({
    url: '/ddi/pharmacyInventoryReport/' + id,
    method: 'get'
  })
}

// 新增药房进销存报表
export function addPharmacyInventoryReport(data) {
  return request({
    url: '/ddi/pharmacyInventoryReport',
    method: 'post',
    data: data
  })
}

// 修改药房进销存报表
export function updatePharmacyInventoryReport(data) {
  return request({
    url: '/ddi/pharmacyInventoryReport',
    method: 'put',
    data: data
  })
}

// 删除药房进销存报表
export function delPharmacyInventoryReport(id) {
  return request({
    url: '/ddi/pharmacyInventoryReport/' + id,
    method: 'delete'
  })
}
