import request from "@/utils/request";


/**
 * 总监区
 * 总监区销售大区分布
 */
export function getDirectorAreaDistributionChart(query){
  return request({
    url: '/ddi/ddiChart/getDirectorAreaDistributionChart',
    method: 'get',
    params: query
  })
}


/**
 * 总监区
 * 总监区大区销量
 */
export function getDirectorAreaSalesVolumeChart(query){
  return request({
    url: '/ddi/ddiChart/getDirectorAreaSalesVolumeChart',
    method: 'get',
    params: query
  })
}


/**
 * 总监区
 * 总监区大区销售增长率
 */
export function getDirectorAreaSalesGrowthRateChart(query){
  return request({
    url: '/ddi/ddiChart/getDirectorAreaSalesGrowthRateChart',
    method: 'get',
    params: query
  })
}

/**
 * 总监区
 * 总监区月度销量走势
 */
export function getDirectorAreaMonthlySalesTrendChart(query){
  return request({
    url: '/ddi/ddiChart/getDirectorAreaMonthlySalesTrendChart',
    method: 'get',
    params: query
  })
}




/**
 * 省份
 * 省份销售分布
 */
export function getProvinceDistributionChart(query){
  return request({
    url: '/ddi/ddiChart/getProvinceDistributionChart',
    method: 'get',
    params: query
  })
}


/**
 * 省份
 * 省份销量
 */
export function getProvinceSalesVolumeChart(query){
  return request({
    url: '/ddi/ddiChart/getProvinceSalesVolumeChart',
    method: 'get',
    params: query
  })
}


/**
 * 省份
 * 省份销售增长率
 */
export function getProvinceSalesGrowthRateChart(query){
  return request({
    url: '/ddi/ddiChart/getProvinceSalesGrowthRateChart',
    method: 'get',
    params: query
  })
}

/**
 * 省份
 * 省份月度销量走势
 */
export function getProvinceMonthlySalesTrendChart(query){
  return request({
    url: '/ddi/ddiChart/getProvinceMonthlySalesTrendChart',
    method: 'get',
    params: query
  })
}


