import request from '@/utils/request'

// 查询地区业绩列表
export function listRegionPerformance(query) {
  return request({
    url: '/ddi/regionPerformance/list',
    method: 'get',
    params: query
  })
}

// 查询地区业绩详细
export function getRegionPerformance(id) {
  return request({
    url: '/ddi/regionPerformance/' + id,
    method: 'get'
  })
}

// 新增地区业绩
export function addRegionPerformance(data) {
  return request({
    url: '/ddi/regionPerformance',
    method: 'post',
    data: data
  })
}

// 修改地区业绩
export function updateRegionPerformance(data) {
  return request({
    url: '/ddi/regionPerformance',
    method: 'put',
    data: data
  })
}

// 删除地区业绩
export function delRegionPerformance(id) {
  return request({
    url: '/ddi/regionPerformance/' + id,
    method: 'delete'
  })
}
