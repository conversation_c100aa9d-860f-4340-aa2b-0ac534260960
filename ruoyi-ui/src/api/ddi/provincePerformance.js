import request from '@/utils/request'

// 查询省份业绩列表
export function listProvincePerformance(query) {
  return request({
    url: '/ddi/provincePerformance/list',
    method: 'get',
    params: query
  })
}

// 查询省份业绩详细
export function getProvincePerformance(id) {
  return request({
    url: '/ddi/provincePerformance/' + id,
    method: 'get'
  })
}

// 新增省份业绩
export function addProvincePerformance(data) {
  return request({
    url: '/ddi/provincePerformance',
    method: 'post',
    data: data
  })
}

// 修改省份业绩
export function updateProvincePerformance(data) {
  return request({
    url: '/ddi/provincePerformance',
    method: 'put',
    data: data
  })
}

// 删除省份业绩
export function delProvincePerformance(id) {
  return request({
    url: '/ddi/provincePerformance/' + id,
    method: 'delete'
  })
}
