import request from '@/utils/request'

// 查询2025药房进销存报表列表
export function listPharmacyInventoryReport2025(query) {
  return request({
    url: '/ddi/pharmacyInventoryReport2025/list',
    method: 'get',
    params: query
  })
}

// 查询2025药房进销存报表详细
export function getPharmacyInventoryReport2025(id) {
  return request({
    url: '/ddi/pharmacyInventoryReport2025/' + id,
    method: 'get'
  })
}

// 新增2025药房进销存报表
export function addPharmacyInventoryReport2025(data) {
  return request({
    url: '/ddi/pharmacyInventoryReport2025',
    method: 'post',
    data: data
  })
}

// 修改2025药房进销存报表
export function updatePharmacyInventoryReport2025(data) {
  return request({
    url: '/ddi/pharmacyInventoryReport2025',
    method: 'put',
    data: data
  })
}

// 删除2025药房进销存报表
export function delPharmacyInventoryReport2025(id) {
  return request({
    url: '/ddi/pharmacyInventoryReport2025/' + id,
    method: 'delete'
  })
}
