import request from '@/utils/request'

// 查询DDI-库存列表
export function listInventory(query) {
  return request({
    url: '/ddi/inventory/list',
    method: 'get',
    params: query
  })
}

// 查询DDI-库存详细
export function getInventory(id) {
  return request({
    url: '/ddi/inventory/' + id,
    method: 'get'
  })
}

// 新增DDI-库存
export function addInventory(data) {
  return request({
    url: '/ddi/inventory',
    method: 'post',
    data: data
  })
}

// 修改DDI-库存
export function updateInventory(data) {
  return request({
    url: '/ddi/inventory',
    method: 'put',
    data: data
  })
}

// 删除DDI-库存
export function delInventory(id) {
  return request({
    url: '/ddi/inventory/' + id,
    method: 'delete'
  })
}
