import request from '@/utils/request'

// 查询2025商业进销存报表列表
export function listBusinessInventoryReport2025(query) {
  return request({
    url: '/ddi/businessInventoryReport2025/list',
    method: 'get',
    params: query
  })
}

// 查询2025商业进销存报表详细
export function getBusinessInventoryReport2025(id) {
  return request({
    url: '/ddi/businessInventoryReport2025/' + id,
    method: 'get'
  })
}

// 新增2025商业进销存报表
export function addBusinessInventoryReport2025(data) {
  return request({
    url: '/ddi/businessInventoryReport2025',
    method: 'post',
    data: data
  })
}

// 修改2025商业进销存报表
export function updateBusinessInventoryReport2025(data) {
  return request({
    url: '/ddi/businessInventoryReport2025',
    method: 'put',
    data: data
  })
}

// 删除2025商业进销存报表
export function delBusinessInventoryReport2025(id) {
  return request({
    url: '/ddi/businessInventoryReport2025/' + id,
    method: 'delete'
  })
}
