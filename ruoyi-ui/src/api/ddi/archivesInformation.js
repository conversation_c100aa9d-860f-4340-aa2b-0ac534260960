import request from '@/utils/request'

// 查询档案信息列表
export function listArchivesInformation(query) {
  return request({
    url: '/ddi/archivesInformation/list',
    method: 'get',
    params: query
  })
}

// 查询档案信息详细
export function getArchivesInformation(id) {
  return request({
    url: '/ddi/archivesInformation/' + id,
    method: 'get'
  })
}

// 新增档案信息
export function addArchivesInformation(data) {
  return request({
    url: '/ddi/archivesInformation',
    method: 'post',
    data: data
  })
}

// 修改档案信息
export function updateArchivesInformation(data) {
  return request({
    url: '/ddi/archivesInformation',
    method: 'put',
    data: data
  })
}

// 删除档案信息
export function delArchivesInformation(id) {
  return request({
    url: '/ddi/archivesInformation/' + id,
    method: 'delete'
  })
}
