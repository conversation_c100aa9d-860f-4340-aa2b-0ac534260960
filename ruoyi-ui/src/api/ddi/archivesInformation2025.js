import request from '@/utils/request'

// 查询2025终端档案信息列表
export function listArchivesInformation2025(query) {
  return request({
    url: '/ddi/archivesInformation2025/list',
    method: 'get',
    params: query
  })
}

// 查询2025终端档案信息详细
export function getArchivesInformation2025(id) {
  return request({
    url: '/ddi/archivesInformation2025/' + id,
    method: 'get'
  })
}

// 新增2025终端档案信息
export function addArchivesInformation2025(data) {
  return request({
    url: '/ddi/archivesInformation2025',
    method: 'post',
    data: data
  })
}

// 修改2025终端档案信息
export function updateArchivesInformation2025(data) {
  return request({
    url: '/ddi/archivesInformation2025',
    method: 'put',
    data: data
  })
}

// 删除2025终端档案信息
export function delArchivesInformation2025(id) {
  return request({
    url: '/ddi/archivesInformation2025/' + id,
    method: 'delete'
  })
}
