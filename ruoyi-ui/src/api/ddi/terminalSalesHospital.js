import request from '@/utils/request'

// 查询终端销售统计表-医院购进列表
export function listTerminalSalesHospital(query) {
  return request({
    url: '/ddi/terminalSalesHospital/list',
    method: 'get',
    params: query
  })
}

// 查询终端销售统计表-医院购进详细
export function getTerminalSalesHospital(id) {
  return request({
    url: '/ddi/terminalSalesHospital/' + id,
    method: 'get'
  })
}

// 新增终端销售统计表-医院购进
export function addTerminalSalesHospital(data) {
  return request({
    url: '/ddi/terminalSalesHospital',
    method: 'post',
    data: data
  })
}

// 修改终端销售统计表-医院购进
export function updateTerminalSalesHospital(data) {
  return request({
    url: '/ddi/terminalSalesHospital',
    method: 'put',
    data: data
  })
}

// 删除终端销售统计表-医院购进
export function delTerminalSalesHospital(id) {
  return request({
    url: '/ddi/terminalSalesHospital/' + id,
    method: 'delete'
  })
}
