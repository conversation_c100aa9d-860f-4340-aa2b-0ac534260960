import request from '@/utils/request'

// 查询终端销售统计表-省份列表
export function listTerminalSalesProvince(query) {
  return request({
    url: '/ddi/terminalSalesProvince/list',
    method: 'get',
    params: query
  })
}

// 查询终端销售统计表-省份详细
export function getTerminalSalesProvince(id) {
  return request({
    url: '/ddi/terminalSalesProvince/' + id,
    method: 'get'
  })
}

// 新增终端销售统计表-省份
export function addTerminalSalesProvince(data) {
  return request({
    url: '/ddi/terminalSalesProvince',
    method: 'post',
    data: data
  })
}

// 修改终端销售统计表-省份
export function updateTerminalSalesProvince(data) {
  return request({
    url: '/ddi/terminalSalesProvince',
    method: 'put',
    data: data
  })
}

// 删除终端销售统计表-省份
export function delTerminalSalesProvince(id) {
  return request({
    url: '/ddi/terminalSalesProvince/' + id,
    method: 'delete'
  })
}
