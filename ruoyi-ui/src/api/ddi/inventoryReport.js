import request from '@/utils/request'

// 查询进销存报表列表
export function listInventoryReport(query) {
  return request({
    url: '/ddi/inventoryReport/list',
    method: 'get',
    params: query
  })
}

// 查询进销存报表详细
export function getInventoryReport(id) {
  return request({
    url: '/ddi/inventoryReport/' + id,
    method: 'get'
  })
}

// 新增进销存报表
export function addInventoryReport(data) {
  return request({
    url: '/ddi/inventoryReport',
    method: 'post',
    data: data
  })
}

// 修改进销存报表
export function updateInventoryReport(data) {
  return request({
    url: '/ddi/inventoryReport',
    method: 'put',
    data: data
  })
}

// 删除进销存报表
export function delInventoryReport(id) {
  return request({
    url: '/ddi/inventoryReport/' + id,
    method: 'delete'
  })
}
