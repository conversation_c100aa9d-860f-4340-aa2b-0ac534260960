import request from '@/utils/request'

// 查询终端销售统计表-总监区列表
export function listTerminalSalesDirector(query) {
  return request({
    url: '/ddi/terminalSalesDirector/list',
    method: 'get',
    params: query
  })
}

// 查询终端销售统计表-总监区详细
export function getTerminalSalesDirector(id) {
  return request({
    url: '/ddi/terminalSalesDirector/' + id,
    method: 'get'
  })
}

// 新增终端销售统计表-总监区
export function addTerminalSalesDirector(data) {
  return request({
    url: '/ddi/terminalSalesDirector',
    method: 'post',
    data: data
  })
}

// 修改终端销售统计表-总监区
export function updateTerminalSalesDirector(data) {
  return request({
    url: '/ddi/terminalSalesDirector',
    method: 'put',
    data: data
  })
}

// 删除终端销售统计表-总监区
export function delTerminalSalesDirector(id) {
  return request({
    url: '/ddi/terminalSalesDirector/' + id,
    method: 'delete'
  })
}
