import request from '@/utils/request'

// 查询客户执行计划列表
export function listCustomerExecutionPlan(query) {
  return request({
    url: '/ddi/customerExecutionPlan/list',
    method: 'get',
    params: query
  })
}

// 查询客户执行计划详细
export function getCustomerExecutionPlan(id) {
  return request({
    url: '/ddi/customerExecutionPlan/' + id,
    method: 'get'
  })
}

// 新增客户执行计划
export function addCustomerExecutionPlan(data) {
  return request({
    url: '/ddi/customerExecutionPlan',
    method: 'post',
    data: data
  })
}

// 修改客户执行计划
export function updateCustomerExecutionPlan(data) {
  return request({
    url: '/ddi/customerExecutionPlan',
    method: 'put',
    data: data
  })
}

// 删除客户执行计划
export function delCustomerExecutionPlan(id) {
  return request({
    url: '/ddi/customerExecutionPlan/' + id,
    method: 'delete'
  })
}
