import request from '@/utils/request'

// 查询输注反应收集列表
export function listInfusionReactionCollect(query) {
  return request({
    url: '/infusionReactionCollect/infusionReactionCollect/list',
    method: 'get',
    params: query
  })
}

// 查询输注反应收集详细
export function getInfusionReactionCollect(fdId) {
  return request({
    url: '/infusionReactionCollect/infusionReactionCollect/' + fdId,
    method: 'get'
  })
}

// 新增输注反应收集
export function addInfusionReactionCollect(data) {
  return request({
    url: '/infusionReactionCollect/infusionReactionCollect',
    method: 'post',
    data: data
  })
}

// 修改输注反应收集
export function updateInfusionReactionCollect(data) {
  return request({
    url: '/infusionReactionCollect/infusionReactionCollect',
    method: 'put',
    data: data
  })
}

// 删除输注反应收集
export function delInfusionReactionCollect(fdId) {
  return request({
    url: '/infusionReactionCollect/infusionReactionCollect/' + fdId,
    method: 'delete'
  })
}
