package com.akesobio.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

// 主请求体对象
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OaContractRequest {

    @JsonProperty("fdId")
    private String fdId;
    // fdModelId String 是 文档所属表单id，不允许为空
    @JsonProperty("fdModelId")
    private String fdModelId;

    @JsonProperty("flowParam")
    private String flowParam;

    // fdFlowId String 是 流程Id，不允许为空
    @JsonProperty("fdFlowId")
    private String fdFlowId;

    
    // docSubject String 否 文档标题，允许为空，为空的情况下根据预定义的主题生成规则生成
    @JsonProperty("docSubject")
    private String docSubject;

    // docStatus String 是 文档状态，只可为草稿（"10"）或者待审（"20"）两种状态，默认为待审
    @JsonProperty("docStatus")
    private String docStatus;

    // docCreator Object 是 流程发起人，为单值，不允许为空
    @JsonProperty("docCreator")
    private String docCreator;

    // attachmentFormFiles List<AttachmentForm> 否 附件列表，允许为空
    @JsonProperty("attachmentFormFiles")
    private String attachmentFormFiles;

    // formValues String 否 表单数据，允许为空. This will hold a JSON string.
    // Use @JsonRawValue to embed it as a raw JSON object, not as a string field.
    @JsonProperty("formValues")
    private String formValues;

    public OaContractRequest() {
        this.attachmentFormFiles = "";
        // formValuesJson will be set with a pre-formatted JSON string
    }

    // Getters and Setters
    public String getFdModelId() { return fdModelId; }
    public void setFdModelId(String fdModelId) { this.fdModelId = fdModelId; }
    public String getFdFlowId() { return fdFlowId; }
    public void setFdFlowId(String fdFlowId) { this.fdFlowId = fdFlowId; }
    public String getDocSubject() { return docSubject; }
    public void setDocSubject(String docSubject) { this.docSubject = docSubject; }
    public String getDocStatus() { return docStatus; }
    public void setDocStatus(String docStatus) { this.docStatus = docStatus; }
    public String getDocCreator() { return docCreator; }
    public void setDocCreator(String docCreator) { this.docCreator = docCreator; }
    public String getFormValues() { return formValues; }
    public void setFormValues(String formValuesJson) { this.formValues = formValuesJson; }
}
