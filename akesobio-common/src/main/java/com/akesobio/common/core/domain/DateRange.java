package com.akesobio.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 时间范围，SQL取值区间用, 申请开始时间 < 结束时间  && 申请结束时间 > 开始时间
 *
 * @author: shenglin.qin
 * @date: 2024/01/09
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DateRange {
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
}