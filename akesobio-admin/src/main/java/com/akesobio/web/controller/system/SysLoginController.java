package com.akesobio.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.akesobio.report.hr.mapper.SelfHrEmployeeInfoMapper;
import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.constant.Constants;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.domain.entity.SysMenu;
import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.core.domain.model.LoginBody;
import com.akesobio.common.utils.SecurityUtils;
import com.akesobio.framework.web.service.SysLoginService;
import com.akesobio.framework.web.service.SysPermissionService;
import com.akesobio.system.service.ISysMenuService;

import javax.annotation.Resource;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Resource
    private SelfHrEmployeeInfoMapper selfHrEmployeeInfoMapper;


    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();

        Map<String,Object> hrEmployeeInfo =  selfHrEmployeeInfoMapper.getHrEmployeeInfoByJobNumber(user.getUserName());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        if(hrEmployeeInfo!=null)
            ajax.put("fullDeptName",(String) hrEmployeeInfo.get("dept_name_all"));
        System.out.println(JSON.toJSONString(ajax));
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
