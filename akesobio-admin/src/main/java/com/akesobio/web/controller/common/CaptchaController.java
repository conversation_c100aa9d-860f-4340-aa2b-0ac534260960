package com.akesobio.web.controller.common;

import com.akesobio.common.config.RuoYiConfig;
import com.akesobio.common.constant.CacheConstants;
import com.akesobio.common.constant.Constants;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.core.domain.entity.SysUser;
import com.akesobio.common.core.redis.RedisCache;
import com.akesobio.common.utils.sign.Base64;
import com.akesobio.common.utils.uuid.IdUtils;
import com.akesobio.system.service.ISysConfigService;
import com.google.code.kaptcha.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysConfigService configService;
    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = RuoYiConfig.getCaptchaType();
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    /**
     * 测试 GET 请求接口 (需要通过网关鉴权)
     * 访问方式: GET http://<gateway_host>:<gateway_port>/data-analysis/test/get  (假设路由前缀是 /data-analysis)
     * @return AjaxResult
     */
    @GetMapping("/test/get")
    public AjaxResult testGet() {
        // 这里可以加入您想测试的业务逻辑，或者简单返回成功信息
        return AjaxResult.success("GET request successful! Authentication passed.");
    }

    /**
     * 测试 POST 请求接口 (需要通过网关鉴权)
     * 访问方式: POST http://<gateway_host>:<gateway_port>/data-analysis/test/post (假设路由前缀是 /data-analysis)
     * 请求体示例: {"message": "hello", "value": 123}
     * @param requestBody 接收到的 JSON 请求体，这里用 Map 接收比较灵活
     * @return AjaxResult
     */
    @PostMapping("/test/post")
    public AjaxResult testPost(@RequestBody Map<String, Object> requestBody) {
        // 打印接收到的 Body (可选)
        System.out.println("Received POST body: " + requestBody);

        // 这里可以处理接收到的数据，或者直接将其包装返回
        AjaxResult result = AjaxResult.success("POST request successful! Authentication passed.");
        result.put("received_data", requestBody); // 将接收到的数据放入返回结果
        return result;
    }

    /**
     * (可选) 测试带查询参数的 GET 请求
     * 访问方式: GET http://<gateway_host>:<gateway_port>/data-analysis/test/query?name=test&id=1
     * @param name 查询参数 name
     * @param id 查询参数 id
     * @return AjaxResult
     */
    @GetMapping("/test/query")
    public AjaxResult testQuery(String name, Integer id) {
        System.out.println("Received query parameters: name=" + name + ", id=" + id);
        AjaxResult result = AjaxResult.success("GET request with query successful! Authentication passed.");
        result.put("query_name", name);
        result.put("query_id", id);
        return result;
    }

    // ==================================================
    // === 新增: 接收 SysUser 对象的测试接口 ===
    // ==================================================

    /**
     * 测试接收 SysUser 对象的 POST 接口 (需要通过网关鉴权)
     * 访问方式: POST http://<gateway_host>:<gateway_port>/data-analysis/test/addUser
     * 请求体: SysUser 对象的 JSON 表示
     * @param user 通过 @RequestBody 接收并自动进行 JSR-303 校验 (@Validated)
     * @return AjaxResult
     */
    @PostMapping("/test/addUser")
    public AjaxResult addUser(@Validated @RequestBody SysUser user) {
        // @Validated 会触发 SysUser 类中定义的 JSR-303 验证注解 (如 @NotBlank, @Size)
        // 如果验证失败，Spring Boot 默认会抛出 MethodArgumentNotValidException，
        // 通常会被全局异常处理器捕获并返回 400 Bad Request。

        log.info("Received SysUser object: {}", user.toString()); // 使用 SysUser 的 toString 方法打印

        // 在实际应用中，这里会调用 Service 层来保存用户等操作
        // service.addUser(user);

        // 为了测试，我们简单地返回成功信息，并可以回显部分接收到的数据
        AjaxResult result = AjaxResult.success("User added successfully (simulated)! Authentication passed.");
        result.put("receivedUserName", user.getUserName());
        result.put("receivedNickName", user.getNickName());
        // 注意：不要在响应中返回敏感信息，如密码

        return result;
    }
}
