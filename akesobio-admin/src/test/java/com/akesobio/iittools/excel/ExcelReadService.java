package com.akesobio.iittools.excel;

import com.akesobio.iittools.model.InputDataModel;

import java.io.InputStream;
import java.util.List;

/**
 * Excel读取服务接口
 * 用于从Excel文件中读取数据
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface ExcelReadService {

    /**
     * 从Excel文件输入流中读取数据
     *
     * @param inputStream Excel文件输入流
     * @return 输入数据模型列表
     */
    List<InputDataModel> readFromExcel(InputStream inputStream);

    /**
     * 从Excel文件路径中读取数据
     *
     * @param filePath Excel文件路径
     * @return 输入数据模型列表
     */
    List<InputDataModel> readFromExcel(String filePath);
} 