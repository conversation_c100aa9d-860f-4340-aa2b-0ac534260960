package com.akesobio.iittools.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HeaderLoggingListener<T> extends AnalysisEventListener<T> {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeaderLoggingListener.class);
    private List<T> list = new ArrayList<>();

    public HeaderLoggingListener() { // 添加构造函数日志
        LOGGER.info("HeaderLoggingListener instance created.");
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        LOGGER.info("invokeHeadMap CALLED. AnalysisContext sheet name: {}", context.readSheetHolder().getSheetName());
        if (headMap == null || headMap.isEmpty()) {
            LOGGER.warn("invokeHeadMap CALLED but headMap is null or empty.");
        } else {
            LOGGER.info("Excel Headers Detected by EasyExcel: {}", headMap);
        }
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        // LOGGER.info("invoke CALLED for data: {}", data); // 可以取消注释此行以查看每条数据是否被处理
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) { // 确保此方法存在
        LOGGER.info("Finished reading all data. Total rows processed: {}", list.size());
    }

    public List<T> getList() { // 确保此方法存在
        return list;
    }
}
