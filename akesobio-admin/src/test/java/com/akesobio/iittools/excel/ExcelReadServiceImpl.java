package com.akesobio.iittools.excel;

import com.alibaba.excel.EasyExcel;
import com.akesobio.iittools.excel.HeaderLoggingListener; // Added import for custom listener
// import com.alibaba.excel.read.listener.PageReadListener; // Original listener
import com.akesobio.iittools.model.InputDataModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel读取服务实现类
 * 使用EasyExcel库读取Excel数据
 * 利用EasyExcel的注解功能简化Excel读取过程
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public class ExcelReadServiceImpl implements ExcelReadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelReadServiceImpl.class);

    /**
     * 批处理大小，每次读取多少条数据
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 表头行号，默认为0（第一行）
     */
    private static final int HEAD_ROW_NUMBER = 0;

    /**
     * 是否输出调试信息
     */
    private static final boolean DEBUG_ENABLED = true;

    @Override
    public List<InputDataModel> readFromExcel(InputStream inputStream) {
        List<InputDataModel> resultList = new ArrayList<>();

        try {
            HeaderLoggingListener<InputDataModel> listener = new HeaderLoggingListener<>();
            EasyExcel.read(inputStream, InputDataModel.class, listener)
                    .headRowNumber(HEAD_ROW_NUMBER) // 直接使用 HEAD_ROW_NUMBER
                    .ignoreEmptyRow(true)  // 忽略空行
                    .autoTrim(true)        // 自动去除数据两端的空格
                    .sheet()               // 读取第一个sheet
                    .doRead();             // 执行读取操作

            // Retrieve the data from the listener
            resultList.addAll(listener.getList());

            // Original processing logic can be moved to the listener's invoke method if needed,
            // or processed here after getting the full list.
            // For now, the primary goal is to log headers.
            // The debug logging for individual data items can be re-enabled in HeaderLoggingListener's invoke method.
//            if (DEBUG_ENABLED) {
//                for (InputDataModel data : resultList) {
//                    LOGGER.debug("读取到数据: {}", data);
//                    LOGGER.debug("  - 姓名: {}", data.getApplicant());
//                    LOGGER.debug("  - 预计费用预算: {}", data.getTotalAmount());
//                    LOGGER.debug("  - 片区: {}", data.getDepartmentArea());
//                    LOGGER.debug("  - 项目号: {}", data.getIitProjectNumber());
//                    LOGGER.debug("  - 申请日期: {}", data.getProjectStartDate());
//
//                    // Data pre-processing and validation (can also be in listener or here)
//                    if (data.getApplicant() != null) {
//                        data.setApplicant(data.getApplicant().trim());
//                    }
//                    if (data.getTotalAmount() != null && !data.getTotalAmount().isEmpty()) {
//                        data.setTotalAmount(data.getTotalAmount().replaceAll("[,\\s]", ""));
//                    }
//                }
//            }

            // 记录读取完成的信息
            LOGGER.info("成功从Excel中读取{}条数据", resultList.size());
            return resultList;
        } catch (Exception e) {
            LOGGER.error("读取Excel文件失败", e);
            throw new RuntimeException("读取Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<InputDataModel> readFromExcel(String filePath) {
        LOGGER.info("开始读取Excel文件: {}", filePath);
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return readFromExcel(inputStream);
        } catch (IOException e) {
            LOGGER.error("打开Excel文件失败: {}", filePath, e);
            throw new RuntimeException("打开Excel文件失败: " + filePath, e);
        }
    }
}