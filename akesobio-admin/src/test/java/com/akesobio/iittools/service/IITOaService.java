package com.akesobio.iittools.service;

import com.akesobio.common.dto.OaContractRequest;
import com.akesobio.iittools.converter.DataConverterService;
import com.akesobio.iittools.converter.DataConverterServiceImpl;
import com.akesobio.iittools.excel.ExcelReadService;
import com.akesobio.iittools.excel.ExcelReadServiceImpl;
import com.akesobio.iittools.model.InputDataModel;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import springfox.documentation.spring.web.json.Json;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * IIT OA服务类
 * 协调Excel读取和数据转换
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Slf4j
public class IITOaService {

    private  String fdModelId = "191e0125c5c885f3a88c2654e3381045";
    private  String fdFlowId = "191e047c2a0ae88004dc3584cc5a3b00";
    private  String docCreator;
    private final com.fasterxml.jackson.databind.ObjectMapper objectMapper; // Added for JSON serialization

    /**
     * Excel读取服务
     */
    private final ExcelReadService excelReadService;

    /**
     * 数据转换服务
     */
    private final DataConverterService dataConverterService;

    /**
     * 构造函数
     *
     * @param fdModelId  OA表单模型ID
     * @param fdFlowId   OA流程ID
     * @param docCreator OA文档创建者ID
     */
    public IITOaService(String fdModelId, String fdFlowId, String docCreator) {
        this.fdModelId = fdModelId;
        this.fdFlowId = fdFlowId;
        this.docCreator = docCreator;
        this.excelReadService = new ExcelReadServiceImpl();
        this.dataConverterService = new DataConverterServiceImpl(fdModelId, fdFlowId, docCreator);
        this.objectMapper = new com.fasterxml.jackson.databind.ObjectMapper(); // Initialize ObjectMapper
    }

    /**
     * 从Excel文件读取数据并转换为OA请求对象
     *
     * @param excelFilePath Excel文件路径
     * @return OA请求对象列表
     */
    public List<OaContractRequest> processExcelToOaRequests(String excelFilePath, Map<String,String> projectStartDateMap) {
        // 1. 从Excel文件读取数据
//        List<InputDataModel> inputDataList = excelReadService.readFromExcel(excelFilePath);
        List<InputDataModel> inputDataList = new ArrayList<>();
        EasyExcel.read(excelFilePath, InputDataModel.class, new PageReadListener<InputDataModel>(dataList -> {
            for (InputDataModel demoData : dataList) {
//                log.info("读取到一条数据{}", JSON.toJSONString(demoData));
                inputDataList.add(demoData);
            }
        })).sheet().doRead();
        
        // 2. 将输入数据转换为OA请求对象
        List<OaContractRequest> requestList = new ArrayList<>();
        for (InputDataModel inputData : inputDataList) {
            if(projectStartDateMap.containsKey(inputData.getIitProjectNumber())
                    && StringUtils.hasText(projectStartDateMap.get(inputData.getIitProjectNumber()))){
                OaContractRequest request = dataConverterService.convertToOaRequest(inputData);
                JSONObject jsonObject = JSONObject.parse(request.getDocCreator());
                jsonObject.put("PersonNo",projectStartDateMap.get(inputData.getIitProjectNumber()));
                request.setDocCreator(jsonObject.toJSONString());
                requestList.add(request);
            }
        }
        return requestList;
    }

    /**
     * 处理单个输入数据模型并转换为OA请求对象
     *
     * @param inputData 输入数据模型
     * @return OA请求对象
     */
    /**
     * 从Excel文件读取项目详细信息数据
     *
     * @param excelFilePath Excel文件路径
     * @return 项目详细信息输入模型列表
     */
    public List<com.akesobio.iittools.model.ProjectDetailInputModel> processProjectDetailExcel(String excelFilePath) {
        List<com.akesobio.iittools.model.ProjectDetailInputModel> projectDetailList = new ArrayList<>();
        try {
            EasyExcel.read(excelFilePath, com.akesobio.iittools.model.ProjectDetailInputModel.class, new PageReadListener<com.akesobio.iittools.model.ProjectDetailInputModel>(dataList -> {
                for (com.akesobio.iittools.model.ProjectDetailInputModel projectData : dataList) {
//                    log.info("读取到项目详细数据: {}", JSON.toJSONString(projectData));
                    projectDetailList.add(projectData);
                }
            })).sheet().doRead();
        } catch (Exception e) {
            log.error("从Excel读取项目详细数据失败: {}", excelFilePath, e);
            // 根据需要可以抛出自定义异常或返回空列表
        }
        return projectDetailList;
    }

    /**
     * 从Excel文件读取项目详细信息数据，转换为IITApplicationFormValues，然后构建OaContractRequest对象列表
     *
     * @param excelFilePath Excel文件路径
     * @return OaContractRequest对象列表
     */
    public List<OaContractRequest> processProjectDetailToOaRequests(String excelFilePath,Map<String,String> projectStartDateMap) {
        // 1. 从Excel文件读取项目详细数据
        List<com.akesobio.iittools.model.ProjectDetailInputModel> projectDetailList = processProjectDetailExcel(excelFilePath);
        List<OaContractRequest> oaRequestList = new ArrayList<>();

        if (projectDetailList == null || projectDetailList.isEmpty()) {
            log.warn("从Excel未能读取到项目详细数据，或列表为空: {}", excelFilePath);
            return oaRequestList; // 返回空列表
        }
        // 2. 将每个ProjectDetailInputModel转换为IITApplicationFormValues，然后构建OaContractRequest
        for (com.akesobio.iittools.model.ProjectDetailInputModel projectDetail : projectDetailList) {
            if(projectStartDateMap.containsKey(projectDetail.getProjectNumber())
                    && StringUtils.hasText(projectStartDateMap.get(projectDetail.getProjectNumber()))){
                com.akesobio.iittools.model.IITApplicationFormValues formValues = com.akesobio.iittools.model.ModelConverter.convertProjectDetailToFormValues(projectDetail);
                OaContractRequest oaRequest = new OaContractRequest();
                oaRequest.setFdModelId(this.fdModelId);
                oaRequest.setFdFlowId(this.fdFlowId);
                oaRequest.setDocCreator(this.docCreator);
//                oaRequest.setDocSubject("测试");
                oaRequest.setDocStatus("20");
                JSONObject jsonObject = JSONObject.parse(oaRequest.getDocCreator());
                jsonObject.put("PersonNo",projectStartDateMap.get(projectDetail.getProjectNumber()));
                oaRequest.setDocCreator(jsonObject.toJSONString());
                try {
                    String formValuesJson = this.objectMapper.writeValueAsString(formValues);
                    oaRequest.setFormValues(formValuesJson);
                } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                    log.error("Error serializing IITApplicationFormValues to JSON for ProjectDetailInputModel: {}", projectDetail, e);
                    oaRequest.setFormValues(null); // Or some error placeholder JSON string
                }
                oaRequestList.add(oaRequest);
            }
        }
        
        return oaRequestList;
    }

    public OaContractRequest processInputDataToOaRequest(InputDataModel inputData) {
        return dataConverterService.convertToOaRequest(inputData);
    }
} 