package com.akesobio.iittools.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

/**
 * 输入数据模型类
 * 用于表示从Excel读取的数据
 * 使用EasyExcel的@ExcelProperty注解进行映射
 *
 * <AUTHOR>
 * @since 2025-05-30
 */

public class InputDataModel {

//    /**
//     * 单据类型
//     */
//    private String documentType;
//
//    /**
//     * 费控系统单据编号
//     */
//    private String expenseControlNumber;
//
//    /**
//     * OA系统单据编号
//     */
//    private String oaDocumentNumber;
//
//    /**
//     * 所属公司
//     */
//    private String companyName;
//
//    /**
//     * 所属公司Code
//     */
//    private String companyCode;
//
//    /**
//     * 提交人
//     */
//    private String submitter;
//
//    /**
//     * 提交人Code
//     */
//    private String submitterCode;
//
    /**
     * 提交部门
     */
    @ExcelProperty("地区")
    private String submitterDepartment;
//
//    /**
//     * 提交部门Code
//     */
//    private String submitterDepartmentCode;

    /**
     * 申请人
     * 对应Excel中的"姓名"字段
     */
    @ExcelProperty("姓名")
    private String applicant;

//    @ExcelProperty("地区")
//    private String area;

//    /**
//     * 申请人Code
//     */
//    private String applicantCode;

    /**
     * IIT申请总金额
     * 对应Excel中的"预计费用预算"字段
     */
    @ExcelProperty("预计费用预算")
    private String totalAmount;

//    /**
//     * 预算期间
//     */
//    private String budgetPeriod;
//
//    /**
//     * 业务类型
//     */
//    private String businessType;
//
//    /**
//     * 业务类型Code
//     */
//    private String businessTypeCode;

    /**
     * 部门所属片区
     * 对应Excel中的"片区"字段
     */
    @ExcelProperty("片区")
    private String departmentArea;

//    /**
//     * 部门所属片区Code
//     */
//    private String departmentAreaCode;
//
    /**
     * 产品管线
     */
    private String productLine;

    /**
     * 产品管线Code
     */
    private String productLineCode;

    /**
     * IIT项目编号
     * 对应Excel中的"项目号（整理）"字段
     */
    @ExcelProperty("项目号（整理）")
    private String iitProjectNumber;

//    /**
//     * IIT分类
//     */
//    private String iitCategory;
//
//    /**
//     * IIT分类Code
//     */
//    private String iitCategoryCode;
//
//    /**
//     * 项目名称
//     */
//    private String projectName;

    /**
     * 项目开始时间/申请日期
     * 对应Excel中的"申请日期"字段
     * 支持多种常见的日期格式
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("申请日期")
    private String projectStartDate;

    /**
     * 项目结束时间
     * 支持多种常见的日期格式
     */
    @DateTimeFormat("yyyy-MM-dd")
    private String projectEndDate;

//    /**
//     * 预估入组病例数
//     */
//    private String estimatedCases;
//
//    /**
//     * 主要研究者
//     */
//    private String principalInvestigator;
//
//    /**
//     * 备注
//     */
//    private String remarks;
//
//    /**
//     * 同步合同草稿至OA状态及说明
//     */
//    private String syncStatus;



    public void setProjectStartDate(String projectStartDate) {
        this.projectStartDate = projectStartDate;
    }
    public String getProjectStartDate() {
        return projectStartDate;
    }

//    // Getters and Setters for documentType
//    public String getDocumentType() {
//        return documentType;
//    }
//    public void setDocumentType(String documentType) {
//        this.documentType = documentType;
//    }
//
//    // Getters and Setters for expenseControlNumber
//    public String getExpenseControlNumber() {
//        return expenseControlNumber;
//    }
//    public void setExpenseControlNumber(String expenseControlNumber) {
//        this.expenseControlNumber = expenseControlNumber;
//    }

    // Getters and Setters for oaDocumentNumber
//    public String getOaDocumentNumber() {
//        return oaDocumentNumber;
//    }
//    public void setOaDocumentNumber(String oaDocumentNumber) {
//        this.oaDocumentNumber = oaDocumentNumber;
//    }
//
//    // Getters and Setters for companyName
//    public String getCompanyName() {
//        return companyName;
//    }
//    public void setCompanyName(String companyName) {
//        this.companyName = companyName;
//    }
//
//    // Getters and Setters for companyCode
//    public String getCompanyCode() {
//        return companyCode;
//    }
//    public void setCompanyCode(String companyCode) {
//        this.companyCode = companyCode;
//    }
//
//    // Getters and Setters for submitter
//    public String getSubmitter() {
//        return submitter;
//    }
//    public void setSubmitter(String submitter) {
//        this.submitter = submitter;
//    }
//
//    // Getters and Setters for submitterCode
//    public String getSubmitterCode() {
//        return submitterCode;
//    }
//    public void setSubmitterCode(String submitterCode) {
//        this.submitterCode = submitterCode;
//    }
//
    // Getters and Setters for submitterDepartment
    public String getSubmitterDepartment() {
        return submitterDepartment;
    }
    public void setSubmitterDepartment(String submitterDepartment) {
        this.submitterDepartment = submitterDepartment;
    }

//    // Getters and Setters for submitterDepartmentCode
//    public String getSubmitterDepartmentCode() {
//        return submitterDepartmentCode;
//    }
//    public void setSubmitterDepartmentCode(String submitterDepartmentCode) {
//        this.submitterDepartmentCode = submitterDepartmentCode;
//    }

    // Getters and Setters for applicant
    public String getApplicant() {
        return applicant;
    }
    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

//    // Getters and Setters for applicantCode
//    public String getApplicantCode() {
//        return applicantCode;
//    }
//    public void setApplicantCode(String applicantCode) {
//        this.applicantCode = applicantCode;
//    }

    // Getters and Setters for totalAmount
    public String getTotalAmount() {
        return totalAmount;
    }
    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

//    // Getters and Setters for budgetPeriod
//    public String getBudgetPeriod() {
//        return budgetPeriod;
//    }
//    public void setBudgetPeriod(String budgetPeriod) {
//        this.budgetPeriod = budgetPeriod;
//    }
//
//    // Getters and Setters for businessType
//    public String getBusinessType() {
//        return businessType;
//    }
//    public void setBusinessType(String businessType) {
//        this.businessType = businessType;
//    }
//
//    // Getters and Setters for businessTypeCode
//    public String getBusinessTypeCode() {
//        return businessTypeCode;
//    }
//    public void setBusinessTypeCode(String businessTypeCode) {
//        this.businessTypeCode = businessTypeCode;
//    }

    // Getters and Setters for departmentArea
    public String getDepartmentArea() {
        return departmentArea;
    }
    public void setDepartmentArea(String departmentArea) {
        this.departmentArea = departmentArea;
    }

//    // Getters and Setters for departmentAreaCode
//    public String getDepartmentAreaCode() {
//        return departmentAreaCode;
//    }
//    public void setDepartmentAreaCode(String departmentAreaCode) {
//        this.departmentAreaCode = departmentAreaCode;
//    }
//
//    // Getters and Setters for productLine
//    public String getProductLine() {
//        return productLine;
//    }
//    public void setProductLine(String productLine) {
//        this.productLine = productLine;
//    }
//
//    // Getters and Setters for productLineCode
//    public String getProductLineCode() {
//        return productLineCode;
//    }
//    public void setProductLineCode(String productLineCode) {
//        this.productLineCode = productLineCode;
//    }

    // Getters and Setters for iitProjectNumber
    public String getIitProjectNumber() {
        return iitProjectNumber;
    }
    public void setIitProjectNumber(String iitProjectNumber) {
        this.iitProjectNumber = iitProjectNumber;
    }

//    // Getters and Setters for iitCategory
//    public String getIitCategory() {
//        return iitCategory;
//    }
//    public void setIitCategory(String iitCategory) {
//        this.iitCategory = iitCategory;
//    }
//
//    // Getters and Setters for iitCategoryCode
//    public String getIitCategoryCode() {
//        return iitCategoryCode;
//    }
//    public void setIitCategoryCode(String iitCategoryCode) {
//        this.iitCategoryCode = iitCategoryCode;
//    }
//
//    // Getters and Setters for projectName
//    public String getProjectName() {
//        return projectName;
//    }
//    public void setProjectName(String projectName) {
//        this.projectName = projectName;
//    }

    // Getters and Setters for projectEndDate
    public String getProjectEndDate() {
        return projectEndDate;
    }
    public void setProjectEndDate(String projectEndDate) {
        this.projectEndDate = projectEndDate;
    }
//
//    // Getters and Setters for estimatedCases
//    public String getEstimatedCases() {
//        return estimatedCases;
//    }
//    public void setEstimatedCases(String estimatedCases) {
//        this.estimatedCases = estimatedCases;
//    }

    // Getters and Setters for principalInvestigator
//    public String getPrincipalInvestigator() {
//        return principalInvestigator;
//    }
//    public void setPrincipalInvestigator(String principalInvestigator) {
//        this.principalInvestigator = principalInvestigator;
//    }
//
//    // Getters and Setters for remarks
//    public String getRemarks() {
//        return remarks;
//    }
//    public void setRemarks(String remarks) {
//        this.remarks = remarks;
//    }
//
//    // Getters and Setters for syncStatus
//    public String getSyncStatus() {
//        return syncStatus;
//    }
//    public void setSyncStatus(String syncStatus) {
//        this.syncStatus = syncStatus;
//    }
}