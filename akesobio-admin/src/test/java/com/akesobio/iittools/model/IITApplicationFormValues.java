package com.akesobio.iittools.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * IIT立项申请表单数据接收类
 * 用于接收OA系统表单数据
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class IITApplicationFormValues {

    /**
     * 单据类型
     */
    @JsonProperty("fd_3d8f9550810cd6")
    private String documentType;

    /**
     * 费控系统单据编号
     */
    @JsonProperty("fd_3d8f9815e3aa1a")
    private String expenseControlNumber;

    /**
     * OA系统单据编号
     */
    @JsonProperty("fd_3d8f986d504674")
    private String oaDocumentNumber;

    /**
     * 所属公司
     */
    @JsonProperty("fd_3d8f9552174718")
    private String companyName;

    /**
     * 所属公司Code
     */
    @JsonProperty("fd_3d8f973d569874")
    private String companyCode;

    /**
     * 提交人
     */
    @JsonProperty("fd_3d8f95564e581a")
    private String submitter;

    /**
     * 提交人Code
     */
    @JsonProperty("fd_3d8f95f948f7cc")
    private String submitterCode;

    /**
     * 提交部门
     */
    @JsonProperty("fd_3d8f95a8992dbc")
    private String submitterDepartment;

    /**
     * 提交部门Code
     */
    @JsonProperty("fd_3d8f973eb373a2")
    private String submitterDepartmentCode;

    /**
     * 申请人
     */
    @JsonProperty("fd_3d8f95574170f8")
    private String applicant;

    /**
     * 申请人Code
     */
    @JsonProperty("fd_3d8f95fab6cd0e")
    private String applicantCode;

    /**
     * IIT申请总金额
     */
    @JsonProperty("fd_3d8f95a9e7c1dc")
    private String totalAmount;

    /**
     * 预算期间
     */
    @JsonProperty("fd_3d8f957dd741fe")
    private String budgetPeriod;

    /**
     * 业务类型
     */
    @JsonProperty("fd_3d8f9580845ede")
    private String businessType;

    /**
     * 业务类型Code
     */
    @JsonProperty("fd_3d8f973a4d21ce")
    private String businessTypeCode;

    /**
     * 部门所属片区
     */
    @JsonProperty("fd_3d8f959a64d236")
    private String departmentArea;

    /**
     * 部门所属片区Code
     */
    @JsonProperty("fd_3d8f96601c1714")
    private String departmentAreaCode;

    /**
     * 产品管线
     */
    @JsonProperty("fd_3d8f939119e4f6")
    private String productLine;

    /**
     * 产品管线Code
     */
    @JsonProperty("fd_3d8f968367955e")
    private String productLineCode;

    /**
     * IIT项目编号
     */
    @JsonProperty("fd_3d8f959baf9d86")
    private String iitProjectNumber;

    /**
     * IIT分类
     */
    @JsonProperty("fd_3d8f959ced1418")
    private String iitCategory;

    /**
     * IIT分类Code
     */
    @JsonProperty("fd_3d8f9675e7c136")
    private String iitCategoryCode;

    /**
     * 项目名称
     */
    @JsonProperty("fd_3d8f9712c0a7be")
    private String projectName;

    /**
     * 项目开始时间
     */
    @JsonProperty("fd_3d8f95ad14ee98")
    private String projectStartDate;

    /**
     * 项目结束时间
     */
    @JsonProperty("fd_3d8f95af0f4680")
    private String projectEndDate;

    /**
     * 预估入组病例数
     */
    @JsonProperty("fd_3d8f95b0860f3a")
    private String estimatedCases;

    /**
     * 主要研究者
     */
    @JsonProperty("fd_3d8f95b1c70f2a")
    private String principalInvestigator;

    /**
     * 备注
     */
    @JsonProperty("fd_3d8f95b3418a10")
    private String remarks;

    /**
     * 同步合同草稿至OA状态及说明
     */
    @JsonProperty("fd_3d8f95b47060c2")
    private String syncStatus;

} 