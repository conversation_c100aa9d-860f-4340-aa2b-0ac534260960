package com.akesobio.iittools.model;

import com.akesobio.iittools.model.IITApplicationFormValues;
import com.akesobio.iittools.model.ProjectDetailInputModel;

public class ModelConverter {

    public static IITApplicationFormValues convertProjectDetailToFormValues(ProjectDetailInputModel projectDetail) {
        if (projectDetail == null) {
            return null;
        }

        IITApplicationFormValues formValues = new IITApplicationFormValues();

        // Direct mapping based on user's request
        formValues.setSubmitterDepartment(projectDetail.getArea()); // 地区 -> 提交部门
        formValues.setDepartmentArea(projectDetail.getDirectorateArea()); // 总监区 -> 部门所属片区
        formValues.setIitProjectNumber(projectDetail.getProjectNumber()); // 项目编号 -> IIT项目编号
        formValues.setIitCategory(projectDetail.getResearchCategory()); // 研究类别 -> IIT分类
        formValues.setProjectName(projectDetail.getIitResearchName()); // IIT研究名称 -> 项目名称
        formValues.setProductLine(projectDetail.getProjectNumber().substring(0,5));
        formValues.setProductLineCode(projectDetail.getProjectNumber().substring(0,5));
        if(projectDetail.getProjectNumber().contains("AK112")){
            formValues.setCompanyCode("康方赛诺医药有限公司");
            formValues.setCompanyName("1030");
        }else if(projectDetail.getProjectNumber().contains("AK104")) {
            formValues.setCompanyCode("康方药业有限公司");
            formValues.setCompanyName("1050");
        }

        formValues.setApplicant(projectDetail.getPrincipalInvestigator());
        formValues.setSubmitter(projectDetail.getPrincipalInvestigator());
        formValues.setIitCategory("C类");
//        formValues.setPrincipalInvestigator(projectDetail.getPrincipalInvestigator()); // 项目负责人 -> 主要研究者

        // --- Fields requiring Code or further business logic ---
        // These are set to null by default if not explicitly set.
        // TODO: Populate these Code fields based on business logic (e.g., lookup tables or if Excel provides them)
        // formValues.setSubmitterDepartmentCode(/* derived from projectDetail.getArea() or other source */);
        // formValues.setDepartmentAreaCode(/* derived from projectDetail.getDirectorateArea() or other source */);
        // formValues.setIitCategoryCode(/* derived from projectDetail.getResearchCategory() or other source */);

        // --- Other IITApplicationFormValues fields not present in ProjectDetailInputModel ---
        // These will be null or their default values if initialized in the IITApplicationFormValues class.
        // The formHeader field is initialized in IITApplicationFormValues, so no explicit set needed here unless overriding.
        // TODO: Populate other fields (e.g., documentType, companyName, submitter, applicant, totalAmount, etc.)
        // as needed based on context (e.g., configuration, user input, fixed defaults, or other business logic).
        // Example:
        // formValues.setCompanyName("默认公司名");
        // formValues.setApplicant("默认申请人"); 

        return formValues;
    }
}
