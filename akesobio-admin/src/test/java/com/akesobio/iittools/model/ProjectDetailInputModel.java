package com.akesobio.iittools.model;

import com.alibaba.excel.annotation.ExcelProperty;

public class ProjectDetailInputModel {

    @ExcelProperty("地区")
    private String area;

    @ExcelProperty("总监区")
    private String directorateArea;

    @ExcelProperty("项目编号")
    private String projectNumber;

    @ExcelProperty("研究类别")
    private String researchCategory;

    @ExcelProperty("IIT研究名称")
    private String iitResearchName;

    @ExcelProperty("地区经理")
    private String principalInvestigator;

    // Getters and Setters
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getDirectorateArea() {
        return directorateArea;
    }

    public void setDirectorateArea(String directorateArea) {
        this.directorateArea = directorateArea;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getResearchCategory() {
        return researchCategory;
    }

    public void setResearchCategory(String researchCategory) {
        this.researchCategory = researchCategory;
    }

    public String getIitResearchName() {
        return iitResearchName;
    }

    public void setIitResearchName(String iitResearchName) {
        this.iitResearchName = iitResearchName;
    }

    public String getPrincipalInvestigator() {
        return principalInvestigator;
    }

    public void setPrincipalInvestigator(String principalInvestigator) {
        this.principalInvestigator = principalInvestigator;
    }
}
