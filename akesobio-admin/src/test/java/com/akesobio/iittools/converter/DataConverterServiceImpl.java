package com.akesobio.iittools.converter;

import com.akesobio.common.dto.OaContractRequest;
import com.akesobio.iittools.model.IITApplicationFormValues;
import com.akesobio.iittools.model.InputDataModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 数据转换服务实现类
 * 用于将Excel数据转换为OA系统表单数据
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public class DataConverterServiceImpl implements DataConverterService {

    /**
     * OA表单模型ID
     */
    private final String fdModelId;

    /**
     * OA流程ID
     */
    private final String fdFlowId;

    /**
     * OA文档创建者ID
     */
    private final String docCreator;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 构造函数
     *
     * @param fdModelId  OA表单模型ID
     * @param fdFlowId   OA流程ID
     * @param docCreator OA文档创建者ID
     */
    public DataConverterServiceImpl(String fdModelId, String fdFlowId, String docCreator) {
        this.fdModelId = fdModelId;
        this.fdFlowId = fdFlowId;
        this.docCreator = docCreator;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public OaContractRequest convertToOaRequest(InputDataModel inputData) {
        // 1. 创建表单数据对象
        IITApplicationFormValues formValues = new IITApplicationFormValues();

        if(inputData.getIitProjectNumber().contains("AK112")){
            formValues.setCompanyCode("康方赛诺医药有限公司");
            formValues.setCompanyName("1030");
        }else if(inputData.getIitProjectNumber().contains("AK104")) {
            formValues.setCompanyCode("康方药业有限公司");
            formValues.setCompanyName("1050");
        }
//        formValues.setDocumentType(inputData.getDocumentType());
//        formValues.setExpenseControlNumber(inputData.getExpenseControlNumber());
//        formValues.setOaDocumentNumber(inputData.getOaDocumentNumber());
//        formValues.setCompanyName(inputData.getCompanyName());
//        formValues.setCompanyCode(inputData.getCompanyCode());
//        formValues.setSubmitter(inputData.getSubmitter());
//        formValues.setSubmitterCode(inputData.getSubmitterCode());
        formValues.setSubmitterDepartment(inputData.getSubmitterDepartment());
//        formValues.setSubmitterDepartmentCode(inputData.getSubmitterDepartmentCode());
        formValues.setApplicant(inputData.getApplicant());
//        formValues.setApplicantCode(inputData.getApplicantCode());
        formValues.setTotalAmount(inputData.getTotalAmount().replace(",", "").replace("-",""));
        formValues.setIitCategory("C类");// 假设IIT类别为C类
//        formValues.setBudgetPeriod(inputData.getBudgetPeriod());
//        formValues.setBusinessType(inputData.getBusinessType());
//        formValues.setBusinessTypeCode(inputData.getBusinessTypeCode());
        formValues.setDepartmentArea(inputData.getDepartmentArea());
//        formValues.setSubmitterDepartment(inputData.getArea());
//        formValues.setDepartmentAreaCode(inputData.getDepartmentAreaCode());
//        formValues.setProductLine(inputData.getProductLine());
//        formValues.setProductLineCode(inputData.getProductLineCode());
        formValues.setIitProjectNumber(inputData.getIitProjectNumber());
        formValues.setProductLine(formValues.getIitProjectNumber().substring(0, 5));
        formValues.setApplicantCode(formValues.getIitProjectNumber().substring(0, 5));
        formValues.setSubmitter(inputData.getApplicant());
//        formValues.setIitCategory(inputData.getIitCategory());
//        formValues.setIitCategoryCode(inputData.getIitCategoryCode());
//        formValues.setProjectName(inputData.getProjectName());
        formValues.setProjectStartDate(inputData.getProjectStartDate());
        formValues.setProjectEndDate(inputData.getProjectEndDate());
//        formValues.setEstimatedCases(inputData.getEstimatedCases());
//        formValues.setPrincipalInvestigator(inputData.getPrincipalInvestigator());
//        formValues.setRemarks(inputData.getRemarks());
//        formValues.setSyncStatus(inputData.getSyncStatus());




        // 2. 创建OA请求对象
        OaContractRequest request = new OaContractRequest();
        request.setFdModelId(fdModelId);
        request.setFdFlowId(fdFlowId);
        request.setDocStatus("20"); // 20表示待审
        request.setDocCreator(docCreator);
        request.setDocSubject("");

//        // 如果有项目名称，设置为文档标题
//        if (inputData.getProjectName() != null && !inputData.getProjectName().isEmpty()) {
//            request.setDocSubject("IIT立项申请-" + inputData.getProjectName());
//        }

        // 3. 将表单数据对象转换为JSON字符串
        try {
            String formValuesJson = objectMapper.writeValueAsString(formValues);
            request.setFormValues(formValuesJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("表单数据序列化失败", e);
        }

        return request;
    }
} 