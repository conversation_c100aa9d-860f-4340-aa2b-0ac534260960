package com.akesobio.iittools.converter;

import com.akesobio.common.dto.OaContractRequest;
import com.akesobio.iittools.model.InputDataModel;

/**
 * 数据转换服务接口
 * 用于将Excel数据转换为OA系统表单数据
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface DataConverterService {

    /**
     * 将输入数据模型转换为OA请求对象
     *
     * @param inputData 输入数据模型
     * @return OA请求对象
     */
    OaContractRequest convertToOaRequest(InputDataModel inputData);
} 