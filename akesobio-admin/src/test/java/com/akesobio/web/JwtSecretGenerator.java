package com.akesobio.web;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

public class JwtSecretGenerator {
    public static void main(String[] args) {
        SecureRandom secureRandom = new SecureRandom();
        byte[] keyBytes = new byte[32];
        secureRandom.nextBytes(keyBytes);
        String jwtSecret = Base64.getEncoder().encodeToString(keyBytes);
        System.out.println(jwtSecret);

        // 生成一个随机的 UUID
        UUID userId = UUID.randomUUID();
        System.out.println(userId);
    }
}
