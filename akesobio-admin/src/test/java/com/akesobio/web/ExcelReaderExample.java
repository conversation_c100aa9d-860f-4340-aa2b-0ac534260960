package com.akesobio.web;

import cn.hutool.http.HttpUtil;
import com.akesobio.ReportApplication;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.ekp.mapper.EkpCommonMapper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.Jar;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
@Slf4j
public class ExcelReaderExample {


    @Resource
    EkpCommonMapper ekpCommonMapper;
    public static void main(String[] args) {
        // Excel文件路径
        String filePath = "C:\\Users\\<USER>\\Downloads\\document-viewing2025-01-07+11_35_27-1.xlsx";

        List<Map<String, String>> data = new ArrayList<>();

        // 读取 Excel 文件
        EasyExcel.read(filePath, new AnalysisEventListener<Map<String, String>>() {
            @Override
            public void invoke(Map<String, String> rowMap, AnalysisContext analysisContext) {
                // 每读取一行数据，都会调用这个方法
//                System.out.println("读取到一行数据: " + rowMap);
                data.add(rowMap);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                // 所有数据解析完成后会调用这个方法
//                System.out.println("读取完成！");
            }
        }).sheet().doRead();

        System.out.println("读取到的数据: " + data);



    }

    @org.junit.Test
    public void test() throws Exception {

        // Excel文件路径
        String filePath = "C:\\Users\\<USER>\\Downloads\\document-viewing2025-01-07+11_35_27-1.xlsx";

        List<Map<String, String>> data = new ArrayList<>();

        // 读取 Excel 文件
        EasyExcel.read(filePath, new AnalysisEventListener<Map<String, String>>() {
            @Override
            public void invoke(Map<String, String> rowMap, AnalysisContext analysisContext) {
                // 每读取一行数据，都会调用这个方法
//                System.out.println("读取到一行数据: " + rowMap);
                data.add(rowMap);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                // 所有数据解析完成后会调用这个方法
//                System.out.println("读取完成！");
            }
        }).sheet().doRead();

         Set<String> codes = data.stream().map(item -> item.get(0)).collect(Collectors.toSet());

        List<Map<String, Object>> todoList = ekpCommonMapper.sysNotifyTodo();

        int i = 0;
        for(Map<String, Object> map : todoList){
            String fd_model_id = (String) map.get("fd_model_id");

            //获取 fd_model_id 前 15 位作为 code
            String code = fd_model_id.substring(0, 13);

            if(codes.contains(code)){
                i++;
//                System.out.println("i:" + i);
//                System.out.println("存在该 fd_model_id: " + fd_model_id);

                JSONObject jsonObject = JSONObject.parseObject("{\n" +
                        "    \"modelId\": \"FSAA000000521-71879\",\n" +
                        "    \"appName\": \"云简业财\",\n" +
                        "    \"optType\": 1,\n" +
                        "    \"modelName\": \"云简业财\"\n" +
                        "}");
                jsonObject.put("modelId", fd_model_id);
                HttpUtils.sendPostJson("https://oa.akesobio.com/api/sys-notify/sysNotifyTodoRestService/setTodoDone", jsonObject.toJSONString());

            }


        }
    }

}


