package com.akesobio.web;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.akesobio.report.autoacct.domain.vo.InventoryOccupationDetailVo;
import com.alibaba.excel.write.metadata.WriteSheet;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelGenerator {

    public static void main(String[] args) {
        System.out.println("Hello World!");
        generateExcel(new ArrayList<>(), new HashMap<>(), new HashMap<>());
    }

    public static void generateExcel(List<InventoryOccupationDetailVo> list, Map<String, BigDecimal> monthlyRequirementMap, Map<String, BigDecimal> monthlyGapMap) {
        // 文件输出路径
        String fileName = "C:\\Users\\<USER>\\Desktop\\inventory_occupation.xlsx";

        // 将数据转换为 EasyExcel 需要的格式
        List<ExcelData> excelDataList = convertToExcelData(list, monthlyRequirementMap, monthlyGapMap);

        // 定义自定义表头
        List<List<String>> head = new ArrayList<>();
        List<String> firstRow = new ArrayList<>();
        firstRow.add("物料代码");
        firstRow.add("物料描述");
        firstRow.add("厂家");
        firstRow.add("货号");
        firstRow.add("包装规格");
        firstRow.add("基本计量单位");
        for (int i = 1; i <= 12; i++) {
            firstRow.add(i + "月需求量");
            firstRow.add(i + "月缺口");
        }
        head.add(firstRow);

        List<CustomHead> customHeads = new ArrayList<>();
        customHeads.add(new CustomHead("每月需求数量", 6, 23));

        // 使用 EasyExcel 写入文件
        WriteSheet writeSheet = EasyExcel.writerSheet("库存占用明细").head(head).build();
        EasyExcel.write(fileName, ExcelData.class)
                .registerWriteHandler(new CustomHeadMergeStrategy(customHeads))
                .sheet(writeSheet.getSheetNo(), writeSheet.getSheetName())
                .doWrite(excelDataList);
    }

    /**
     * 将原始数据转换为 EasyExcel 数据模型
     */
    private static List<ExcelData> convertToExcelData(List<InventoryOccupationDetailVo> list, Map<String, BigDecimal> monthlyRequirementMap, Map<String, BigDecimal> monthlyGapMap) {
        List<ExcelData> excelDataList = new ArrayList<>();
        for (InventoryOccupationDetailVo detail : list) {
            ExcelData excelData = new ExcelData();
            excelData.setMaterial(detail.getMaterial());
            excelData.setMaterialDesc(detail.getMaterialDesc());
            excelData.setManufacturers(detail.getManufacturers());
            excelData.setArticleNumber(detail.getArticleNumber());
            excelData.setPackingSpecifications(detail.getPackingSpecifications());
            excelData.setBasicUnit(detail.getBasicUnit());

            // 填充每月需求量和缺口数据
            for (int month = 1; month <= 12; month++) {
                String key = String.format("%d/%d", detail.getProductMonth().getYear(), month);
                BigDecimal requirement = monthlyRequirementMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal gap = monthlyGapMap.getOrDefault(key, BigDecimal.ZERO);

                switch (month) {
                    case 1:
                        excelData.setJanRequirement(requirement.doubleValue());
                        excelData.setJanGap(gap.doubleValue());
                        break;
                    case 2:
                        excelData.setFebRequirement(requirement.doubleValue());
                        excelData.setFebGap(gap.doubleValue());
                        break;
                    case 3:
                        excelData.setMarRequirement(requirement.doubleValue());
                        excelData.setMarGap(gap.doubleValue());
                        break;
                    case 4:
                        excelData.setAprRequirement(requirement.doubleValue());
                        excelData.setAprGap(gap.doubleValue());
                        break;
                    case 5:
                        excelData.setMayRequirement(requirement.doubleValue());
                        excelData.setMayGap(gap.doubleValue());
                        break;
                    case 6:
                        excelData.setJunRequirement(requirement.doubleValue());
                        excelData.setJunGap(gap.doubleValue());
                        break;
                    case 7:
                        excelData.setJulRequirement(requirement.doubleValue());
                        excelData.setJulGap(gap.doubleValue());
                        break;
                    case 8:
                        excelData.setAugRequirement(requirement.doubleValue());
                        excelData.setAugGap(gap.doubleValue());
                        break;
                    case 9:
                        excelData.setSepRequirement(requirement.doubleValue());
                        excelData.setSepGap(gap.doubleValue());
                        break;
                    case 10:
                        excelData.setOctRequirement(requirement.doubleValue());
                        excelData.setOctGap(gap.doubleValue());
                        break;
                    case 11:
                        excelData.setNovRequirement(requirement.doubleValue());
                        excelData.setNovGap(gap.doubleValue());
                        break;
                    case 12:
                        excelData.setDecRequirement(requirement.doubleValue());
                        excelData.setDecGap(gap.doubleValue());
                        break;
                }
            }

            excelDataList.add(excelData);
        }
        return excelDataList;
    }

    /**
     * 定义 EasyExcel 数据模型
     */

    public static class CustomHead {
        private String title;
        private int startColumn;
        private int endColumn;

        public CustomHead(String title, int startColumn, int endColumn) {
            this.title = title;
            this.startColumn = startColumn;
            this.endColumn = endColumn;
        }

        public String getTitle() {
            return title;
        }

        public int getStartColumn() {
            return startColumn;
        }

        public int getEndColumn() {
            return endColumn;
        }
    }
}
