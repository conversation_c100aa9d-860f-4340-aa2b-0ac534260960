package com.akesobio.web;

import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;

public class IITTest {
    
    /**
     * 需要过滤的目标项目号列表
     */
    private static final List<String> TARGET_PROJECT_NUMBERS = Arrays.asList(
        "AK112-IIT-C-N2-0013",
        "AK112-IIT-C-N2-0020",
        "AK112-IIT-C-N2-0021",
        "AK112-IIT-C-N2-0022",
        "AK112-IIT-C-N2-0023",
        "AK104-IIT-C-N2-0145",
        "AK104-IIT-C-N2-0153",
        "AK104-IIT-C-N2-0161",
        "AK104-IIT-C-E-0121",
        "AK104-IIT-C-E-0122",
        "AK104-IIT-C-E-0120",
        "AK104-IIT-C-E-0129"
    );
    
    public static void main(String[] args) {
  
        
        // 调用测试方法
        testReadExcelAndFilterProjects();
//        testCreateMockExcelData();
    }
    
    /**
     * 测试使用EasyExcel读取Excel文件并过滤特定项目号
     * Excel表头：姓名、申请日期、片区、大区、地区、项目号（整理）、预计费用预算
     */
    public static void testReadExcelAndFilterProjects() {
        System.out.println("=== EasyExcel读取和过滤测试开始 ===");
        
        String fileName = "C:\\Users\\<USER>\\Downloads\\存量IIT项目对公支付整理4.30 (1).xlsx"; // 假设的Excel文件路径
        List<IITFormValues> allProjects = new ArrayList<>();

            // 使用EasyExcel读取数据
            EasyExcel.read(fileName, IITFormValues.class, new ReadListener<IITFormValues>() {
                @Override
                public void invoke(IITFormValues data, AnalysisContext context) {
                    // 每读取一行数据，都会调用这个方法
                    allProjects.add(data);
                    System.out.println("读取到数据: " + data.toString());
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    System.out.println("所有数据解析完成!");
                }
            }).sheet().doRead();
            
            // 过滤目标项目号
            List<IITFormValues> filteredProjects = filterProjectsByTargetNumbers(allProjects);
            
            // 输出结果
            printFilterResults(allProjects, filteredProjects);


        
        System.out.println("=== EasyExcel读取和过滤测试结束 ===\n");
    }
    

    /**
     * 根据目标项目号列表过滤项目
     * 
     * @param allProjects 所有项目列表
     * @return 过滤后的项目列表
     */
    private static List<IITFormValues> filterProjectsByTargetNumbers(List<IITFormValues> allProjects) {
        return allProjects.stream()
                .filter(project -> TARGET_PROJECT_NUMBERS.contains(project.getIitProjectNumber()))
                .collect(Collectors.toList());
    }
    
    /**
     * 打印过滤结果
     */
    private static void printFilterResults(List<IITFormValues> allProjects, List<IITFormValues> filteredProjects) {
        System.out.println("总项目数量: " + allProjects.size());
        System.out.println("目标项目号数量: " + TARGET_PROJECT_NUMBERS.size());
        System.out.println("过滤后项目数量: " + filteredProjects.size());
        System.out.println();
        
        System.out.println("=== 过滤结果详情 ===");
        filteredProjects.forEach(project -> {
            System.out.println(String.format("项目号: %s | 姓名: %s | 片区: %s  | 预算: %s",
                project.getIitProjectNumber(),
                project.getSubmitter(),
                project.getDepartmentRegion(),
                project.getIitTotalAmount()));
        });
        
        // 统计分析
        analyzeFilteredProjects(filteredProjects);
    }
    
    /**
     * 分析过滤后的项目数据
     */
    private static void analyzeFilteredProjects(List<IITFormValues> filteredProjects) {
        System.out.println();
        System.out.println("=== 数据分析 ===");
        
        // 按片区统计
        System.out.println("按片区统计:");
        filteredProjects.stream()
                .collect(Collectors.groupingBy(IITFormValues::getDepartmentRegion, Collectors.counting()))
                .forEach((region, count) -> System.out.println("  " + region + ": " + count + "个项目"));
        
        // 按项目类型统计（根据项目号前缀）
        System.out.println("按项目类型统计:");
        filteredProjects.stream()
                .filter(project -> project.getIitProjectNumber() != null && project.getIitProjectNumber().length() >= 5)
                .collect(Collectors.groupingBy(
                    project -> project.getIitProjectNumber().substring(0, 5), // 取前5位作为项目类型
                    Collectors.counting()))
                .forEach((type, count) -> System.out.println("  " + type + ": " + count + "个项目"));
        
        // 按试验类型统计（N2, E等）
        System.out.println("按试验类型统计:");
        filteredProjects.stream()
                .filter(project -> project.getIitProjectNumber() != null)
                .collect(Collectors.groupingBy(
                    project -> {
                        String projectNumber = project.getIitProjectNumber();
                        if (projectNumber.contains("-N2-")) return "N2试验";
                        else if (projectNumber.contains("-E-")) return "E试验";
                        else return "其他";
                    },
                    Collectors.counting()))
                .forEach((type, count) -> System.out.println("  " + type + ": " + count + "个项目"));
        
        // 预算统计
        System.out.println("预算统计:");
        double totalBudget = filteredProjects.stream()
                .filter(project -> project.getIitTotalAmount() != null && !project.getIitTotalAmount().isEmpty())
                .mapToDouble(project -> {
                    try {
                        return Double.parseDouble(project.getIitTotalAmount());
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                })
                .sum();
        System.out.println("  总预算: " + String.format("%.2f", totalBudget) + "元");
        if (filteredProjects.size() > 0) {
            System.out.println("  平均预算: " + String.format("%.2f", totalBudget / filteredProjects.size()) + "元");
        }
    }
    

}
