package com.akesobio.web; // 请确保包名正确

import com.akesobio.common.core.domain.entity.SysUser;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Formatter; // 引入 Formatter

@Slf4j
public class ThirdPartyRequestDemo {


    // --- 配置信息 ---
    private static final String CLIENT_ID = "d09e0abb-9fbf-4b7d-8c7d-964316031804";
    // !! 签名密钥 - 必须与网关 DataAnalysisAuthFilter 中该 Client ID 的配置一致 !!
    private static final String SIGNATURE_SECRET = "J1HO9Ry+PZqPHoPx2EfMw1urf+nHaKwUmrgbQef+yVA=";
    // !! Client Secret - 用于获取 Token，必须与网关 AuthController 中该 Client ID 的配置一致 !!
    private static final String CLIENT_SECRET = "PumQwEYLZIVw2S2GxJvyWQM0Krv1e6MigkW9RmKxAsE="; // 密钥
    private static final String GATEWAY_URL = "https://api.akesobio.com"; // 网关地址
    private static final String AUTH_URL = GATEWAY_URL + "/auth-api/auth/apiAuth"; // 获取 Token 的 URL
    private static final String API_BASE_URL = GATEWAY_URL; // API 请求的基础 URL (网关地址)

    // --- NEW: Configuration for External Token API ---
    private static final String EXTERNAL_API_HOST = "YOUR_EXTERNAL_API_HOST"; // TODO: Replace with actual host
    private static final String TENANT_CODE = "YOUR_TENANT_CODE"; // TODO: Replace with actual tenant code
    private static final String EXTERNAL_TOKEN_URL = EXTERNAL_API_HOST + "/t/" + TENANT_CODE + "/token";
    private static final String EXTERNAL_API_USERNAME = "demo2"; // or "OATB" for production
    private static final String EXTERNAL_API_PASSWORD = "Jcyv2ori#"; // Replace with actual password

    private static final OkHttpClient client = new OkHttpClient.Builder().build(); // OkHttp 客户端实例

    public static void main(String[] args) {
        try {
            // 1. 获取 JWT Token (已包含 Client Secret)
            log.info("=== Step 1: Getting JWT Token ===");
            String jwtToken = getToken();
            log.info("Successfully obtained JWT Token.");
            System.out.println("JWT Token obtained.");
            System.out.println("====================================\n");

            // --- 调用测试接口 ---

            // 2. 调用 GET /test/get 接口
            log.info("=== Step 2: Calling GET /data-analysis-api/test/get ===");
            String testGetEndpoint = "/data-analysis-api/prod-api/test/get"; // 注意包含路由前缀
            String testGetResponse = sendRequest(testGetEndpoint, "GET", null, jwtToken);
            log.info("Response from GET /test/get: {}", testGetResponse);
            System.out.println("GET /test/get Response:\n" + testGetResponse);
            System.out.println("====================================\n");


            // 3. 调用 POST /test/post 接口
            log.info("=== Step 3: Calling POST /data-analysis-api/test/post ===");
            String testPostEndpoint = "/data-analysis-api/prod-api/test/post"; // 注意包含路由前缀
            // 准备一个简单的 JSON Body
            Map<String, Object> postBodyMap = new HashMap<>();
            postBodyMap.put("message", "Hello from client");
            postBodyMap.put("timestamp", System.currentTimeMillis());
            String testPostBody = JSONObject.toJSONString(postBodyMap); // 使用 fastjson2 转换
            log.info("Request Body for POST /test/post: {}", testPostBody);

            String testPostResponse = sendRequest(testPostEndpoint, "POST", testPostBody, jwtToken);
            log.info("Response from POST /test/post: {}", testPostResponse);
            System.out.println("POST /test/post Response:\n" + testPostResponse);
            System.out.println("====================================\n");


            // 4.  调用 GET /test/query 接口 (带查询参数)
            log.info("=== Step 4: Calling GET /data-analysis-api/test/query with parameters ===");
            String testQueryEndpoint = "/data-analysis-api/prod-api/test/query?name=Alice&id=101&status=active"; // 注意包含路由前缀和参数
            String testQueryResponse = sendRequest(testQueryEndpoint, "GET", null, jwtToken);
            log.info("Response from GET /test/query: {}", testQueryResponse);
            System.out.println("GET /test/query Response:\n" + testQueryResponse);
            System.out.println("====================================\n");

            // --- 新增：调用 POST /test/addUser 接口 ---
            log.info("=== Step 5: Calling POST /data-analysis-api/test/addUser ===");
            String addUserEndpoint = "/data-analysis-api/prod-api/test/addUser"; // 网关路径

            // 创建一个 SysUser 对象并填充数据
            // !!! 重要: 确保 SysUser 类及其依赖在当前项目中可用 !!!
            // 如果 ThirdPartyRequestDemo 不在包含 SysUser 的项目中，
            // 你需要将 SysUser 类复制过来，或者创建一个简单的 DTO 类来模拟。
            // 这里假设 SysUser 类可以直接使用。
            Map<String, Object> addUserMap = new HashMap<>();
            addUserMap.put("userName", "testuser_from_demo"); // 必须设置 @NotBlank 字段
            addUserMap.put("nickName", "Demo User");
            addUserMap.put("email", "<EMAIL>"); // 设置 Email
            addUserMap.put("phonenumber", "13800138000"); // 设置手机号
            addUserMap.put("sex", "0"); // "0"=男, "1"=女, "2"=未知
            addUserMap.put("password", "password123"); // 实际接口可能不需要传密码，或只在创建时传
            addUserMap.put("remark", "User created from ThirdPartyRequestDemo");
            // BaseEntity 的字段通常由服务器端自动填充，客户端不需要设置 createTime, updateTime 等
            // newUser.setCreateBy("demo_client"); // 可以设置 CreateBy

            // 将 SysUser 对象转换为 JSON 字符串
            String addUserBody = JSONObject.toJSONString(addUserMap);
            log.info("Request Body for POST /test/addUser: {}", addUserBody);

            // 发送请求 (sendRequest 方法会自动处理 Body 哈希和签名)
            String addUserResponse = sendRequest(addUserEndpoint, "POST", addUserBody, jwtToken);
            log.info("Response from POST /test/addUser: {}", addUserResponse);
            System.out.println("POST /test/addUser Response:\n" + addUserResponse);
            System.out.println("====================================\n");
//            // --- 新增：调用 POST /mdm/institutionalMasterData/addOrEditByInstitutionCode 接口 ---
//            log.info("=== Step 6: Calling POST /data-analysis-api/prod-api/mdm/institutionalMasterData/addOrEditByInstitutionCode ===");
//            testAddOrEditInstitutionalMasterData(jwtToken);
            // --- 新增：调用 POST /logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill 接口 ---
            log.info("=== Step 7: Calling POST /logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill ===");
            String queryWayBillEndpoint = "/logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill"; // 注意包含路由前缀
            // 准备请求体
            Map<String, Object> queryWayBillBodyMap = new HashMap<>();
            queryWayBillBodyMap.put("logisticsCompanyNo", "cimc");
            queryWayBillBodyMap.put("outTradeNo", "XS02-20240819236");
            queryWayBillBodyMap.put("entrustNoOrOrderNo", "368874");
            String queryWayBillBody = JSONObject.toJSONString(queryWayBillBodyMap); // 使用 fastjson2 转换
            log.info("Request Body for POST /logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill: {}", queryWayBillBody);

            String queryWayBillResponse = sendRequest(queryWayBillEndpoint, "POST", queryWayBillBody, jwtToken);
            log.info("Response from POST /logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill: {}", queryWayBillResponse);
            System.out.println("POST /logistics-api/service-YjLogisticsApi/YjLogisticsApiController/queryWayBill Response:\n" + queryWayBillResponse);
            System.out.println("====================================\n");

            log.info("=== Step 8: Calling POST /service-LogisticsApi/LogisticsApiController/rejectOrder ===");
            String rejectOrderEndpoint = "/logistics-api/service-LogisticsApi/LogisticsApiController/rejectOrder";
            Map<String, Object> rejectOrderBodyMap = new HashMap<>();
            rejectOrderBodyMap.put("orderCode", "ORDER123456");
            rejectOrderBodyMap.put("reason", "客户拒收");
            String rejectOrderBody = JSONObject.toJSONString(rejectOrderBodyMap);
            log.info("Request Body for POST /rejectOrder: {}", rejectOrderBody);

            String rejectOrderResponse = sendRequest(rejectOrderEndpoint, "POST", rejectOrderBody, jwtToken);
            log.info("Response from POST /rejectOrder: {}", rejectOrderResponse);
            System.out.println("POST /rejectOrder Response:\n" + rejectOrderResponse);
            System.out.println("====================================\n");
            log.info("=== Step 9: Calling POST /service-LogisticsApi/LogisticsApiController/dispatchCallback ===");
            String dispatchCallbackEndpoint = "/logistics-api/service-LogisticsApi/LogisticsApiController/dispatchCallback";
            Map<String, Object> dispatchCallbackBodyMap = new HashMap<>();
            dispatchCallbackBodyMap.put("dispatchCode", "DISPATCH7890");
            dispatchCallbackBodyMap.put("status", "已调度");
            String dispatchCallbackBody = JSONObject.toJSONString(dispatchCallbackBodyMap);
            log.info("Request Body for POST /dispatchCallback: {}", dispatchCallbackBody);

            String dispatchCallbackResponse = sendRequest(dispatchCallbackEndpoint, "POST", dispatchCallbackBody, jwtToken);
            log.info("Response from POST /dispatchCallback: {}", dispatchCallbackResponse);
            System.out.println("POST /dispatchCallback Response:\n" + dispatchCallbackResponse);
            System.out.println("====================================\n");
            log.info("=== Step 10: Calling POST /service-LogisticsApi/LogisticsApiController/logisticsNodeReport ===");
            String logisticsNodeReportEndpoint = "/logistics-api/service-LogisticsApi/LogisticsApiController/logisticsNodeReport";
            Map<String, Object> logisticsNodeReportBodyMap = new HashMap<>();
            logisticsNodeReportBodyMap.put("nodeCode", "NODE001");
            logisticsNodeReportBodyMap.put("description", "货物已到达分拣中心");
            String logisticsNodeReportBody = JSONObject.toJSONString(logisticsNodeReportBodyMap);
            log.info("Request Body for POST /logisticsNodeReport: {}", logisticsNodeReportBody);

            String logisticsNodeReportResponse = sendRequest(logisticsNodeReportEndpoint, "POST", logisticsNodeReportBody, jwtToken);
            log.info("Response from POST /logisticsNodeReport: {}", logisticsNodeReportResponse);
            System.out.println("POST /logisticsNodeReport Response:\n" + logisticsNodeReportResponse);
            System.out.println("====================================\n");
            log.info("=== Step 11: Calling POST /service-LogisticsApi/LogisticsApiController/uploadEReceipt ===");
            String uploadEReceiptEndpoint = "/logistics-api/service-LogisticsApi/LogisticsApiController/uploadEReceipt";
            Map<String, Object> uploadEReceiptBodyMap = new HashMap<>();
            uploadEReceiptBodyMap.put("receiptCode", "RECEIPT999");
            uploadEReceiptBodyMap.put("fileUrl", "http://example.com/receipts/RECEIPT999.pdf");
            String uploadEReceiptBody = JSONObject.toJSONString(uploadEReceiptBodyMap);
            log.info("Request Body for POST /uploadEReceipt: {}", uploadEReceiptBody);

            String uploadEReceiptResponse = sendRequest(uploadEReceiptEndpoint, "POST", uploadEReceiptBody, jwtToken);
            log.info("Response from POST /uploadEReceipt: {}", uploadEReceiptResponse);
            System.out.println("POST /uploadEReceipt Response:\n" + uploadEReceiptResponse);
            System.out.println("====================================\n");
            log.info("=== Step 12: Calling POST /service-LogisticsApi/LogisticsApiController/tempDeviceBind ===");
            String tempDeviceBindEndpoint = "/logistics-api/service-LogisticsApi/LogisticsApiController/tempDeviceBind";
            Map<String, Object> tempDeviceBindBodyMap = new HashMap<>();
            tempDeviceBindBodyMap.put("deviceCode", "DEVICE001");
            tempDeviceBindBodyMap.put("temperature", "25.5°C");
            String tempDeviceBindBody = JSONObject.toJSONString(tempDeviceBindBodyMap);
            log.info("Request Body for POST /tempDeviceBind: {}", tempDeviceBindBody);

            String tempDeviceBindResponse = sendRequest(tempDeviceBindEndpoint, "POST", tempDeviceBindBody, jwtToken);
            log.info("Response from POST /tempDeviceBind: {}", tempDeviceBindResponse);
            System.out.println("POST /tempDeviceBind Response:\n" + tempDeviceBindResponse);
            System.out.println("====================================\n");

            // --- NEW: Step to get External Auth Token ---
            log.info("=== Step 13: Getting External Auth Token ===");
            try {
                String externalAuthToken = getExternalAuthToken();
                log.info("Successfully obtained External Auth Token: {}", externalAuthToken); // Avoid logging the full token in production if sensitive
                System.out.println("External Auth Token obtained.");
                // System.out.println("External Auth Token: " + externalAuthToken); // Uncomment to print the token
            } catch (Exception e) {
                log.error("Failed to get external auth token: {}", e.getMessage(), e);
                System.err.println("Failed to get external auth token: " + e.getMessage());
            }
            System.out.println("====================================\n");

        } catch (Exception e) {
            log.error("An error occurred in the main execution flow: {}", e.getMessage(), e);
            e.printStackTrace();
        }
    }



    /**
     * 测试调用 POST /mdm/institutionalMasterData/addOrEditByInstitutionCode 接口
     * @param jwtToken JWT Token
     * @throws Exception
     */
//    private static void testAddOrEditInstitutionalMasterData(String jwtToken) throws Exception {
//        log.info("=== Step 6: Calling POST /mdm/institutionalMasterData/addOrEditByInstitutionCode ===");
//        String endpoint = "/data-analysis-api/prod-api/mdm/institutionalMasterData/addOrEditByInstitutionCode";
//
//        // 创建一个 InstitutionalMasterData 对象并填充数据
//        Map<String, Object> institutionalMasterData = new HashMap<>();
//        institutionalMasterData.put("institutionName", "测试机构1");
//        institutionalMasterData.put("institutionCode", "INST002");
//        institutionalMasterData.put("customerType", "医院");
//        institutionalMasterData.put("province", "广东省");
//        institutionalMasterData.put("city", "深圳市");
//        institutionalMasterData.put("district", "南山区");
//        institutionalMasterData.put("address", "深圳市南山区科技园");
//        institutionalMasterData.put("primaryAttribute", "属性1");
//        institutionalMasterData.put("secondaryAttribute", "属性2");
//        institutionalMasterData.put("thirdLevelAttribute", "属性3");
//        institutionalMasterData.put("creditCode", "12345678901234567890");
//        institutionalMasterData.put("hospitalLevel", "三级甲等");
//        institutionalMasterData.put("grade", "A");
//        institutionalMasterData.put("iongitude", "113.9416");
//        institutionalMasterData.put("state", "启用");
//
//        // 将 InstitutionalMasterData 对象转换为 JSON 字符串
//        String jsonBody = JSONObject.toJSONString(institutionalMasterData);
//        log.info("Request Body for POST /mdm/institutionalMasterData/addOrEditByInstitutionCode: {}", jsonBody);
//
//        // 发送请求
//        String response = sendRequest(endpoint, "POST", jsonBody, jwtToken);
//        log.info("Response from POST /mdm/institutionalMasterData/addOrEditByInstitutionCode: {}", response);
//        System.out.println("POST /mdm/institutionalMasterData/addOrEditByInstitutionCode Response:\n" + response);
//        System.out.println("====================================\n");
//    }

    /**
     * 获取 JWT Token (现在包含 Client Secret)
     * @return JWT Token
     * @throws Exception
     */
    public static String getToken() throws Exception {
        log.info("Attempting to get JWT token with Client Secret...");
        String jsonBody = String.format("{\"clientId\":\"%s\", \"clientSecret\":\"%s\"}", CLIENT_ID, CLIENT_SECRET);
        log.debug("Token request body: {}", jsonBody); // 日志中不要记录 Secret

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, jsonBody); // 修正参数顺序

        Request request = new Request.Builder()
                .url(AUTH_URL)
                .post(body)
                .build();

        log.debug("Requesting token from URL: {}", AUTH_URL);
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            if (!response.isSuccessful()) {
                log.error("Failed to get token: Status={}, Body={}", response.code(), responseBody);
                throw new Exception("Failed to get token: " + response.code() + " " + responseBody);
            }
            // ... (后续 Token 解析逻辑不变) ...
            if (responseBody == null) throw new Exception("Failed to get token: Empty response body.");
            Map<String, Object> responseMap = JSONObject.parseObject(responseBody);
            String token = (String) responseMap.get("token");
            if (token == null || token.isEmpty()) throw new Exception("Failed to get token: 'token' field not found in response.");
            log.debug("Token received successfully.");
            return token;
        }
    }

    /**
     * 发送 API 请求到网关 (签名包含 Body Hash)
     * @param endpointWithQuery 包含**路由前缀**和查询参数的端点 (e.g., "/data-analysis-api/test/get?p=1")
     * @param method HTTP 方法 (e.g., "GET", "POST")
     * @param jsonBody 请求体 (对于 POST/PUT)，GET 请求时为 null
     * @param jwtToken 从 getToken() 获取的 JWT
     * @return 响应体字符串
     * @throws Exception
     */
    public static String sendRequest(String endpointWithQuery, String method, String jsonBody, String jwtToken) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUID.randomUUID().toString();

        // --- 准备签名所需信息 ---
        URI fullUri = new URI(API_BASE_URL + endpointWithQuery); // 完整请求 URL
        String pathForSignature = fullUri.getPath(); // 使用包含路由前缀的完整路径进行签名
        String queryForSignature = fullUri.getQuery(); // 获取原始查询字符串

        log.debug("Path used for signature (client-side): {}", pathForSignature);
        log.debug("Query used for signature (client-side): {}", queryForSignature);


        // --- 计算 Body 哈希 ---
        String bodyHash;
        boolean shouldHashBody = method.equalsIgnoreCase("POST")
                || method.equalsIgnoreCase("PUT")
                || method.equalsIgnoreCase("PATCH");

        if (shouldHashBody && jsonBody != null && !jsonBody.isEmpty()) {
            bodyHash = calculateSha256Hex(jsonBody); // 计算实际 Body 哈希
            log.debug("Calculated Body Hash (SHA-256 Hex): {}", bodyHash);
        } else {
            bodyHash = calculateSha256Hex(new byte[0]); // 计算空 Body 哈希
            log.debug("Calculated Empty Body Hash (SHA-256 Hex): {}", bodyHash);
        }
        // --- 结束 Body 哈希计算 ---

        // --- 生成签名 ---
        String signature = generateSignature(method.toUpperCase(), pathForSignature, queryForSignature, timestamp, nonce, CLIENT_ID, bodyHash);
        log.debug("Generated Signature: {}", signature);

        // --- 构建请求 ---
        Request.Builder requestBuilder = new Request.Builder()
                .url(fullUri.toString()) // 使用完整 URL
                .header("Authorization", "Bearer " + jwtToken)
                .header("X-Timestamp", timestamp)
                .header("X-Nonce", nonce)
                .header("X-Signature", signature)
                .header("X-Client-Id", CLIENT_ID);

        RequestBody body = null;
        if (jsonBody != null) {
            // 修正参数顺序
            body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonBody);
        }
        requestBuilder.method(method.toUpperCase(), body);

        log.info("Sending request: {} {}", method.toUpperCase(), fullUri);
        log.debug("Headers: X-Timestamp={}, X-Nonce={}, X-Client-Id={}, Authorization=Bearer ***", timestamp, nonce, CLIENT_ID);

        // --- 发送请求 ---
        try (Response response = client.newCall(requestBuilder.build()).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            if (!response.isSuccessful()) {
                log.error("Request failed: Status={}, Body={}", response.code(), responseBody);
                throw new Exception("Request failed: " + response.code() + " " + responseBody);
            }
            log.debug("Request successful: Status={}", response.code());
            return responseBody != null ? responseBody : "";
        }
    }

    /**
     * 生成包含 Body 哈希的签名
     */
    private static String generateSignature(String method, String path, String query, String timestamp, String nonce, String clientId, String bodyHash) throws Exception {
        // 签名原文格式: HTTP_METHOD + "\n" + RequestPath + "\n" + CanonicalizedQueryString + "\n" + TimestampHeader + "\n" + NonceHeader + "\n" + ClientIdHeader + "\n" + BodyHash

        // 1. 规范化查询字符串 (代码不变)
        String canonicalQueryString = "";
        if (query != null && !query.isEmpty()) {
            canonicalQueryString = Arrays.stream(query.split("&"))
                    .filter(p -> p.contains("="))
                    .sorted()
                    .collect(Collectors.joining("&"));
        }
        log.trace("Canonical Query String: {}", canonicalQueryString);

        // 2. 构建签名原文 (包含 bodyHash)
        StringBuilder sb = new StringBuilder();
        sb.append(method).append("\n");
        sb.append(path).append("\n");
        sb.append(canonicalQueryString).append("\n");
        sb.append(timestamp).append("\n");
        sb.append(nonce).append("\n");
        sb.append(clientId).append("\n"); // ClientId 后加换行
        sb.append(bodyHash); // 追加 Body 哈希

        String signatureBase = sb.toString();
        log.trace("Signature Base String (Client): [{}]", signatureBase.replace("\n", "|"));

        // 3. 计算 HMAC-SHA256 签名 (代码不变)
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(SIGNATURE_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        hmacSha256.init(secretKey);
        byte[] hash = hmacSha256.doFinal(signatureBase.getBytes(StandardCharsets.UTF_8));

        // 4. Base64 编码 (代码不变)
        return Base64.getEncoder().encodeToString(hash);
    }


    // --- SHA-256 工具方法 ---
    private static String calculateSha256Hex(String input) throws NoSuchAlgorithmException {
        return calculateSha256Hex(input != null ? input.getBytes(StandardCharsets.UTF_8) : new byte[0]);
    }

    private static String calculateSha256Hex(byte[] inputBytes) throws NoSuchAlgorithmException {
        if (inputBytes == null) inputBytes = new byte[0];
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(inputBytes);
        return bytesToHex(hash);
    }

    private static String bytesToHex(byte[] bytes) {
        if (bytes == null) return "";
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        try (Formatter formatter = new Formatter(sb)) {
            for (byte b : bytes) {
                formatter.format("%02x", b);
            }
        }
        return sb.toString();
    }

    // --- NEW: Method to get External Auth Token ---
    /**
     * 获取外部 OpenAPI 调用凭证 (AuthToken)
     * @return Access Token 字符串
     * @throws Exception 如果请求失败或无法解析 Token
     */
    public static String getExternalAuthToken() throws Exception {
        log.info("Attempting to get external AuthToken...");

        Map<String, String> requestBodyMap = new HashMap<>();
        requestBodyMap.put("grant_type", "password");
        requestBodyMap.put("username", EXTERNAL_API_USERNAME);
        requestBodyMap.put("password", EXTERNAL_API_PASSWORD);

        String jsonRequestBody = JSONObject.toJSONString(requestBodyMap);
        log.debug("External token request body: {}", jsonRequestBody); // Be cautious logging username/password

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, jsonRequestBody);

        Request request = new Request.Builder()
                .url(EXTERNAL_TOKEN_URL)
                .post(body)
                .header("Accept", "application/json")
                .header("Content-Type", "application/json")
                .build();

        log.debug("Requesting external token from URL: {}", EXTERNAL_TOKEN_URL);
        try (Response response = client.newCall(request).execute()) {
            String responseBodyString = response.body() != null ? response.body().string() : null;
            if (!response.isSuccessful()) {
                log.error("Failed to get external token: Status={}, Body={}", response.code(), responseBodyString);
                throw new Exception("Failed to get external token: " + response.code() + " " + responseBodyString);
            }

            if (responseBodyString == null || responseBodyString.isEmpty()) {
                throw new Exception("Failed to get external token: Empty response body.");
            }

            Map<String, Object> responseMap = JSONObject.parseObject(responseBodyString);
            String accessToken = (String) responseMap.get("access_token");

            if (accessToken == null || accessToken.isEmpty()) {
                log.error("Failed to parse access_token from response: {}", responseBodyString);
                throw new Exception("Failed to get external token: 'access_token' field not found or empty in response.");
            }
            log.info("External AuthToken received successfully.");
            return accessToken;
        } catch (Exception e) {
            log.error("Error while requesting external token: {}", e.getMessage(), e);
            throw e; // Re-throw the exception
        }
    }

}