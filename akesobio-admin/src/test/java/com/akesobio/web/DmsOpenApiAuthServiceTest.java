package com.akesobio.web;

import com.akesobio.ReportApplication;
import com.akesobio.report.dms.service.DmsOpenApiAuthService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
@Slf4j
public class DmsOpenApiAuthServiceTest {



    @Resource
    private DmsOpenApiAuthService dmsOpenApiAuthService;

    @org.junit.Test
    public void testDmsOpenApiAuthService() {
        try {
            String authToken = dmsOpenApiAuthService.getDmsAuthToken();
            log.info("DMS Auth Token: {}", authToken);
        } catch (Exception e) {
            log.error("Failed to get DMS Auth Token: {}", e.getMessage(), e);
        }
    }




} 