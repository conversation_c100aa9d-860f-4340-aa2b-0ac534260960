package com.akesobio.web;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class CustomHeadMergeStrategy implements CellWriteHandler {

    private final List<ExcelGenerator.CustomHead> customHeads;

    public CustomHeadMergeStrategy(List<ExcelGenerator.CustomHead> customHeads) {
        this.customHeads = customHeads;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 无需实现
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 无需实现
    }

    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 无需实现
    }

    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<com.alibaba.excel.metadata.data.WriteCellData<?>> cellDataList, Cell cell, Head head,
                                 Integer relativeRowIndex, Boolean isHead) {
        if (isHead && relativeRowIndex == 0) {
            Sheet sheet = writeSheetHolder.getSheet();
            for (ExcelGenerator.CustomHead customHead : customHeads) {
                // 定义合并区域
                CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, customHead.getStartColumn(), customHead.getEndColumn());

                // 检查是否已经存在相同的合并区域
                boolean isMergedRegionExist = false;
                for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                    CellRangeAddress existingRegion = sheet.getMergedRegion(i);
                    if (existingRegion.intersects(cellRangeAddress)) {
                        isMergedRegionExist = true;
                        break;
                    }
                }

                // 如果不存在相同的合并区域，则添加
                if (!isMergedRegionExist) {
                    sheet.addMergedRegion(cellRangeAddress);
                }

                // 设置合并单元格的内容和样式
                Row row = sheet.getRow(0);
                if (row == null) {
                    row = sheet.createRow(0);
                }
                Cell mergeCell = row.getCell(customHead.getStartColumn());
                if (mergeCell == null) {
                    mergeCell = row.createCell(customHead.getStartColumn());
                }
                mergeCell.setCellValue(customHead.getTitle());
                mergeCell.setCellStyle(getCenterStyle(sheet.getWorkbook()));
            }
        }
    }


    private CellStyle getCenterStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
