package com.akesobio.web.CostControlTest;



import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.Getter;



/**
 * 部门及成本中心主数据
 */
@Data
public class CostCenterDataTest {
    /**
     * 编码 (type B表示公司编码, C成本中心编码, D部门编码)
     */

    @Getter
    @ExcelProperty("成本中心代码")
    private String code;
    /**
     * 父级公司/成本中心/部门编码
     */

    private String parent_code;
    /**
     * 组织名称
     */
    @ExcelProperty("成本中心名称")
    private String department_name;
    /**
     * 主管员工号
     */
    private String supervisor;
    /**
     * 同步的类型(B-公司 C-成本中心 D-部门)
     */
    private String type;
    /**
     * 当前条数据在此批次中所对应的序号
     */
    private String sequence_num;
    /**
     * 成本中心标志(Y/N,默认为N) 默认Y
     */
    private String cost_center_flag;
    /**
     * 是否启用标志(Y/N,默认为N)
     */
    private String enabled_flag;
    /**
     * 部门对应SAP成本中心
     */
    private String column1;

    /**
     * 成本中心大区
     */
    private String column2;

    /**
     * 是否末级部门
     */
    private String column3;

}
