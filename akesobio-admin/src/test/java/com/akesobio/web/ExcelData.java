package com.akesobio.web;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


@Data
public  class ExcelData {
    @ExcelProperty("物料代码")
    private String material;

    @ExcelProperty("物料描述")
    private String materialDesc;

    @ExcelProperty("厂家")
    private String manufacturers;

    @ExcelProperty("货号")
    private String articleNumber;

    @ExcelProperty("包装规格")
    private String packingSpecifications;

    @ExcelProperty("基本计量单位")
    private String basicUnit;

    // 每月需求量和缺口数据
    @ExcelProperty("1月需求量")
    private Double janRequirement;

    @ExcelProperty("1月缺口")
    private Double janGap;

    @ExcelProperty("2月需求量")
    private Double febRequirement;

    @ExcelProperty("2月缺口")
    private Double febGap;

    @ExcelProperty("3月需求量")
    private Double marRequirement;

    @ExcelProperty("3月缺口")
    private Double marGap;

    @ExcelProperty("4月需求量")
    private Double aprRequirement;

    @ExcelProperty("4月缺口")
    private Double aprGap;

    @ExcelProperty("5月需求量")
    private Double mayRequirement;

    @ExcelProperty("5月缺口")
    private Double mayGap;

    @ExcelProperty("6月需求量")
    private Double junRequirement;

    @ExcelProperty("6月缺口")
    private Double junGap;

    @ExcelProperty("7月需求量")
    private Double julRequirement;

    @ExcelProperty("7月缺口")
    private Double julGap;

    @ExcelProperty("8月需求量")
    private Double augRequirement;

    @ExcelProperty("8月缺口")
    private Double augGap;

    @ExcelProperty("9月需求量")
    private Double sepRequirement;

    @ExcelProperty("9月缺口")
    private Double sepGap;

    @ExcelProperty("10月需求量")
    private Double octRequirement;

    @ExcelProperty("10月缺口")
    private Double octGap;

    @ExcelProperty("11月需求量")
    private Double novRequirement;

    @ExcelProperty("11月缺口")
    private Double novGap;

    @ExcelProperty("12月需求量")
    private Double decRequirement;

    @ExcelProperty("12月缺口")
    private Double decGap;





}
