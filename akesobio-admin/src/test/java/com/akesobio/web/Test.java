package com.akesobio.web;
import com.akesobio.common.utils.MessagePushUtils;
import com.akesobio.framework.web.service.MinioSysFileServiceImpl;
import com.akesobio.report.costControl.domain.ExpClaimHeaderDTO;
import com.akesobio.report.costControl.domain.WorkflowUpdate;
import com.akesobio.report.costControl.mapper.ExpClaimHeaderMapper;
import com.akesobio.report.costControl.service.CloudpenseApiUtils;
import com.akesobio.report.costControl.service.OaFormUploadService;
//import com.akesobio.report.costControlApi.timeTask.CostControlTask;
import com.akesobio.report.costControlApi.timeTask.CostControlTask;
import com.akesobio.report.ekp.domain.dto.ApprovalRecord;
import com.akesobio.report.ekp.mapper.ApprovalRecordMapper;
import com.akesobio.report.ekp.service.EkpFlwhtspService;
import com.akesobio.report.mainData.service.ICostCenterService;
import com.akesobio.report.mainData.domain.CostCenter;
import com.akesobio.report.costControl.domain.CostCenterDepartmentData;
import com.akesobio.report.costControl.domain.DepartmentCostInfo;
import com.akesobio.report.costControl.domain.CostCenterDepartmentSyncResponse;
import com.alibaba.fastjson2.JSONArray;
import com.taobao.api.ApiException;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.FileEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.File;
import java.io.IOException;

import com.akesobio.ReportApplication;

import com.akesobio.report.autoacct.mapper.*;
import com.akesobio.report.autoacct.service.*;
//import com.akesobio.report.costControl.service.CloudpenseApiUtils;
//import com.akesobio.report.costControl.service.OaFormUploadService;
//import com.akesobio.report.costControlApi.timeTask.CostControlTask;
import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.HttpEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
@Slf4j
public class Test {

    @Resource
    private MinioSysFileServiceImpl minioSysFileService;
    @Resource
    private ApprovalRecordMapper approvalRecordMapper;
    @Resource
    private OaFormUploadService oaFormUploadService;


    @Resource
    private ExpClaimHeaderMapper expClaimHeaderMapper;
//    @Resource
//    private IAutoMaterialAcctInventoryService autoMaterialAcctInventoryService;
//    @Resource
//    private IAutoMaterialAcctMaterialPlanService autoMaterialAcctMaterialPlanService;
//    @Resource
//    private IInventoryOccupationDetailService inventoryOccupationDetailService;
//    @Resource
//    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;
//    @Resource
//    private AutoMaterialAcctInventoryMapper autoMaterialAcctInventoryMapper;
//
//    @Resource
//    private AutoMaterialAcctPurchaseNotreturnMapper autoMaterialAcctPurchaseNotreturnMapper;
//
//    @Resource
//    private AutoMaterialAcctBomMaterialRequireMapper autoMaterialAcctBomMaterialRequireMapper;
//
//    @Resource
//    private AutoMaterialAcctProdPlanMapper autoMaterialAcctProdPlanMapper;
//
//    @Resource
//    private PurchaseNotreturnOcMapper purchaseNotreturnOcMapper;
//
//    @Resource
//    private MaterialMDMapper materialMDMapper;
//
//    @Resource
//    private MaterialMDService materialMDService;
//
//    @Resource
//    private EkpMapper ekpMapper;



//    private static final String appName = "待办待阅移动端访问受限测试";
//    private static final String modelName = "待办待阅移动端访问受限测试";
//    private static final String modelId = System.currentTimeMillis() + "";
//    private static final String subject = "待办待阅移动端访问受限测试";
//    private static final String link = "https://oa.akesobio.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=193b84ca9c997ccae2170ea4eb299767";
//    private static final String mobileLink = "https://oa.akesobio.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=193b84ca9c997ccae2170ea4eb299767";
//    private static final String padLink = "/";
//    private static final Integer type = 1;
//    private static final String url = "https://oa.akesobio.com/api/sys-notify/sysNotifyTodoRestService/sendTodo";

    public static void main(String[] args) throws ApiException {

        String accessToken = MessagePushUtils.getAccessToken("dingygaltsw8i5nkxgqy","jbDi0SmZll-RfSRX7K1Ka5ZldGBtw5ig_hvMRN80XApcCSEaZvvbNbYqzoKjJiya");
        String fileUrl = "https://ts.akesobio.com/minioProxy/cloudpense/%E7%BA%B8%E5%8D%95%E6%8A%95%E9%80%92%E5%AE%9A%E6%9C%9F%E6%8F%90%E9%86%9220250424.zip?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=DMTO0D94OHOOUNAJOQ6E%2F20250424%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250424T095519Z&X-Amz-Expires=604800&X-Amz-Security-Token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NLZXkiOiJETVRPMEQ5NE9IT09VTkFKT1E2RSIsImV4cCI6MTc0NTQ5MjA1OSwicGFyZW50IjoiYWRtaW4ifQ.XaPH04RDj5a9t_L4i2nASU5SyGa5O3disRF6XHLgTjkjB2stnTX2s-tSc7m85Ln6gwHws90S7Kdab_q60wAmVA&X-Amz-SignedHeaders=host&versionId=null&X-Amz-Signature=f42936b7f7c68d3d14390affd9ede3c51fe346ccc283d284f6c305c420754000";
        List<String> userLists = new ArrayList<>();
        userLists.add("A02025");
        MessagePushUtils.pushFileDownloadMessage(userLists,"纸单投递定期提醒",
                "纸单投递定期提醒20250424","纸单投递定期提醒20250424.zip",fileUrl,accessToken);

    }




//    @Resource
//    private IAutoAccReceivedInventoryService autoAccReceivedInventoryService;
//    @Resource
//    private IAutoAccReceivedMaterialsService autoAccReceivedMaterialsService;
//
//    @Resource
//    private CostControlTask costControlTask;

    @Resource
    private CloudpenseApiUtils cloudpenseApiUtils;

    @Resource
    private CostControlTask costControlTask;

    @Resource
    private EkpFlwhtspService ekpFlwhtspService;
    
    @Resource
    private ICostCenterService costCenterService;




    @org.junit.Test
    public void ekpFlwhtspServiceTest() throws UnknownHostException {
//     String jsonStr = ekpFlwhtspService.getFlwhtspDataAsJson();
//        System.out.println(jsonStr);
        costControlTask.personnelMasterData();
    }






    @org.junit.Test
    public void importHistorySeriesMeeting() throws Exception {
        cloudpenseApiUtils.getToken();

//        oaFormUploadService.importPurchaseContract();
//        oaFormUploadService.importHistorySeriesMeeting();
    }
//
//
    @org.junit.Test
    public void testCostControlTask() throws Exception {


//        String url = "https://ts.akesobio.com/minioProxy/training-system/default/%E6%94%AF%E6%8C%81%E4%BC%9A%E8%AE%AE%E5%8D%8F%E8%AE%AE-3.23.doc";
//        String url = "https://ts.akesobio.com/minioProxy/training-system/default/3%E6%9C%8823%E7%83%9F%E5%8F%B0%E4%BC%9A%E8%AE%AE%E6%97%A5%E7%A8%8B.pptx";

        costControlTask.costCenterMainData();
//        cloudpenseApiUtils.getDocumentDetail("YXHT-***********");
//        costControlTask.employeeAccountMasterData();
//        List<RemoteFileRequest> files = new ArrayList<>();
//        RemoteFileRequest file = new RemoteFileRequest();
//        file.setFile_name("支持会议协议-2.22.doc");
//        file.setFile_url(url);
//        file.setPreview_flag(true);
//        files.add(file);
//        List<String> stringList = cloudpenseApiUtils.uploadRemoteFiles(file.getFile_name(),file.getFile_url());
//
//        System.out.printf(stringList.toString());




    }
//    @org.junit.Test
//    public void tetsAutoAccReceivedInventoryService() throws Exception {
//        List<AutoAccReceivedMaterials> list =  autoAccReceivedMaterialsService.list();
//        List<String> orderNumbers = list.stream().map(AutoAccReceivedMaterials::getOrderNumber).collect(Collectors.toList());
//        autoAccReceivedInventoryService.refreshAutoAccReceivedInventory(orderNumbers,null);
//    }




//    /**
//     * 定时推送滞留节点信息
//     **/
//    @org.junit.Test
//    public void pushChart() throws Exception {
//        String computerName = GetComputerName.getComputerName();
//        System.out.println("计算机名称："+computerName);
////        if (computerName.equals("SERVER22")) {
//            List<String> jobNumberList = new ArrayList<>();
//            jobNumberList.add("B00490");
////            jobNumberList.add("A01710");
//            Map<String, Object> jobNumberMap = new HashMap<>();
//            int num = 0;
//            for (int i = 0; i < jobNumberList.size(); i++) {
//                String creator = "{\"LoginName\":\"admin\"}";//系统管理员
//                String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//                String jobNumber = jobNumberList.get(i);
//                jobNumberMap.put("PersonNo", jobNumber);
//                String targets = JSON.toJSONString(jobNumberMap);
//                // 定义header对象
//                HttpHeaders headers = new HttpHeaders();
//                // 定义请求参数Map
//                Map<String, Object> map = new HashMap<String, Object>();
//                map.put("appName", appName);
//                map.put("modelName", modelName);
//                map.put("modelId", modelId);
//                map.put("subject", subject);
//                map.put("link", link);
//                map.put("mobileLink", mobileLink);
//                map.put("padLink", padLink);
//                map.put("targets", targets);
//                map.put("type", type);
//                map.put("docCreator", creator);
//                map.put("createTime", createTime);
//                map.put("level", 2);
//                log.info("发送OA定时推送直达图表功能通知信息：--" + jobNumber + "----" + map.toString());
//                // 定义http请求实体对象
//                HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(map, headers);
//                //发送请求
//                RestTemplate template = new RestTemplate();
//                ResponseEntity<Map> exchange = template.exchange(url, HttpMethod.POST, entity, Map.class);
//                log.info("OA定时推送直达图表功能通知信息返回：--" + jobNumber + "--" + exchange.getBody());
//                String statusInfo = exchange.getBody().get("returnState").toString();
//                if (statusInfo.equals("2")) {
//                    num += 1;
//                }
//            }
//            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "推送直达图表功能通知为" + num + "条");
////        } else {
////            log.info("当前时间：--" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "--计算机名称：--" + computerName + "--推送直达图表功能通知--不在10.10.2.51服务器执行的定时任务--跳过");
////        }
//    }
//    @org.junit.Test
//    public void test2() throws Exception {
////        inventoryOccupationDetailService.refreshInventoryOccupationDetail();
//        System.out.println(materialMDMapper.selectMaterial(new MaterialMD()));
//    }
//
//    @org.junit.Test
//    public void test() throws Exception {
////        ekpMapper.getAllCG04(new CG04());
//
//
//        Map<String, List<MaterialMD>> materialMDMap = materialMDMapper.getAllMaterials().stream().collect(Collectors.groupingBy(MaterialMD::getMatnr));
//        System.out.println(materialMDMap);
////        autoMaterialAcctInventoryService.refreshInventory();
//
//    }
//    @org.junit.Test
//    public void test1() throws Exception {
//        String filePath = "C:\\Users\\<USER>\\Desktop\\新建文本文档.txt"; // 替换为你的文件路径
//        StringBuilder content = new StringBuilder();
//
//        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
//            String line;
//            while ((line = reader.readLine()) != null) {
//                content.append(line).append(System.lineSeparator());
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        autoMaterialAcctPurchaseNotreturnMapper.truncateTableAcctPurchaseNotreturnCpoy1();
//        JSONObject jsonObject = JSONObject.parseObject(content.toString());
//        if (jsonObject.getInteger("code") == 200) {
//            if (jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("EO_TABLE")) {
//                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("EO_TABLE");
//                for (int i = 0; i < jsonArray.size(); i++) {
//                    JSONObject jsonObject1 = jsonArray.getJSONObject(i);
//                    AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = new AutoMaterialAcctPurchaseNotreturn();
//                    autoMaterialAcctPurchaseNotreturn.sapPurchaseNotreturnConversion(jsonObject1);
//                    autoMaterialAcctPurchaseNotreturnMapper.insertAutoMaterialAcctPurchaseNotreturnCpoy1(autoMaterialAcctPurchaseNotreturn);
//
//                }
//            }
//        }
//
//    }
//
//


    @org.junit.Test
    public void minioSysFileServiceImpl() throws Exception {
       String url = minioSysFileService.uploadFile(new File("C:\\Users\\<USER>\\Desktop\\1726731366888.jpg"));
       log.info("url:"+url);
    }



    @org.junit.Test
    public void getUploadUrl() throws Exception {

        String attachment_url = null;
        // 1. 准备要上传的文件名列表
        List<String> fileNames = new ArrayList<>();
        fileNames.add("1月3会议合同.pdf");
        try {
            // 2. 获取文件上传地址 (v3 API)
            List<Map<String, String>> uploadUrlList = cloudpenseApiUtils.getUploadUrl(fileNames);
            for (Map<String, String> map : uploadUrlList){
                String uploadUrl = map.get("fileURL");           // 上传URL
                attachment_url = map.get("attachmentURL");       // v3 API直接返回的附件URL
                cloudpenseApiUtils.uploadFile(uploadUrl,"C:\\Users\\<USER>\\Downloads\\1月3会议合同.pdf");
                System.out.println("上传成功，附件URL: " + attachment_url);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


//        List<String> attachment_url = cloudpenseApiUtils.uploadRemoteFiles("1月3会议合同.pdf","https://ts.akesobio.com/minioProxy/training-system/default/3%E6%9C%8823%E7%83%9F%E5%8F%B0%E4%BC%9A%E8%AE%AE%E6%97%A5%E7%A8%8B.pptx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3QBBRA7ZAGPQAGK3GA0W%2F20250408%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250408T112951Z&X-Amz-Expires=604800&X-Amz-Security-Token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NLZXkiOiIzUUJCUkE3WkFHUFFBR0szR0EwVyIsImV4cCI6MTc0NDExMzIxNiwicGFyZW50IjoiYWRtaW4ifQ.LNOXl3UPT3fOviu3je-chk8h6kJz01Se9ayMZs9z2Gpz7RkSiBjZmyEMpK25fbh-HPgBIee72jg87MuSszZJSA&X-Amz-SignedHeaders=host&versionId=null&X-Amz-Signature=6cf693aac73d9cf1848df67c441d6ed71fc288ae28ced3eada71a3d3957768e2");
        System.out.println(attachment_url);
                String head = "{\"header\":{\"external_id\":\"YXHT-20241118005\",\"header_type_code\":\"YXHT\",\"submit_date\":\"自动带出\",\"created_by_code\":\"A00659\",\"submit_user_code\":\"A00659\",\"column46\":\"AK104\",\"column45\":\"02\",\"branch_code\":\"1050\",\"total_amount\":\"5000000\",\"column41\":\"AK104\",\"column38\":\"yw01\",\"column17\":\"YW15\",\"column12\":\"Y\",\"column156\":\"执行合同/订单（无框架协议)\",\"column129\":\"\",\"column90\":\"PO1050-0805-202501-0019\",\"supplier_code\":\"0010005825\"}}";


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM-dd");
        String dateString = sdf.format(new Date()) + "/out";



        JSONObject header = JSONObject.parseObject(head);
        JSONArray jsonArray = new JSONArray();

        JSONObject attachments2 = new JSONObject();
        attachments2.put("file_name", "1月3会议合同.pdf");
        attachments2.put("attachment_url", attachment_url);
        jsonArray.add(attachments2);
        header = header.getJSONObject("header");
        header.put("submit_date", new Date().getTime());
        // 单据状态
        header.put("status", "approved");
        header.put("attachments2", jsonArray);

        // 打印 header 对象以验证结果
        JSONObject data = new JSONObject();
        data.put("header", header);
        JSONObject requestParams = new JSONObject();
        requestParams.put("bizId", UUID.randomUUID().toString());
        requestParams.put("timestamp", new Date().getTime());
        requestParams.put("data", data);
        log.info("请求参数：{}", requestParams.toJSONString());
        //上传数据到云简
        String result = cloudpenseApiUtils.uploadDocument(requestParams.toJSONString());






    }


    private void uploadFile(String uploadUrl, String filePath) throws IOException {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
//        String pathName = "xxxx/xxxx/test.pdf";
        URL signedUrl = new URL(uploadUrl);
        try {
            // 获取文件 MIME 类型
            String mimeType = Files.probeContentType(Paths.get(filePath));
            if (mimeType == null) {
                mimeType = "application/octet-stream"; // 设置默认 MIME 类型
            }
            HttpPut put = new HttpPut(signedUrl.toString());
            HttpEntity entity = new FileEntity(new File(filePath));
            put.setEntity(entity);

            // 设置 Content-Type
            put.setHeader("Content-Type", mimeType);
            httpClient = HttpClients.createDefault();
            response = httpClient.execute(put);

            System.out.println("返回上传状态码：" + response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() == 200) {
                System.out.println("上传成功");
            }
            System.out.println(response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            if (httpClient != null) {
                httpClient.close();
            }
        }

    }

//    @org.junit.Test
//    public void batchUpdateWorkflows() throws Exception {
//        // 1. 准备数据  YXHT-***********  FSHA000000063    YXHT-20240828008 FSHA000000064  YXHT-20241118005 FSHA000000062
//
//        List<String> processIds = Arrays.asList( "YXHT-***********", "YXHT-20241118005");
//
//       // YXHT-20241118005 -> FSHA000000019    YXHT-*********** -> FSHA000000020
//
//        Map<String, String> processIdToEmployeeNumberMap = new HashMap<>();
//        processIdToEmployeeNumberMap.put("YXHT-***********", "FSHA000000063");
////        processIdToEmployeeNumberMap.put("YXHT-20241118005", "FSHA000000019");
//
//
//        for (String processId : processIds) {
//            if(processIdToEmployeeNumberMap.containsKey(processId)){
//
//                List<ApprovalRecord> approvalRecords = approvalRecordMapper.findApprovalRecordsByProcessId(processId);
//                approvalRecords = approvalRecords.stream().filter(approvalRecord -> !approvalRecord.getNodeName().contains("分支")).collect(Collectors.toList());
//                WorkflowUpdate nodes1 = oaFormUploadService.convertApprovalRecordsToWorkflowUpdates(approvalRecords,processIdToEmployeeNumberMap.get(processId));
////            nodes1.getNodes().clear();
//                JSONObject response = cloudpenseApiUtils.batchUpdateWorkflows(nodes1, "zh_CN");
//                log.info("response:{}",response);
//            }
//        }
//
//
//
//
//
////        List<ApprovalRecord>  approvalRecords = createTestApprovalRecords();
////        approvalRecords = approvalRecords.stream().filter(approvalRecord -> !approvalRecord.getNodeName().contains("分支")).collect(Collectors.toList());
////
////
////        WorkflowUpdate nodes1 = oaFormUploadService.convertApprovalRecordsToWorkflowUpdates(approvalRecords,"FSHA000000021");
//////        List<WorkflowUpdate> updates = new ArrayList<>();
//////        updates.add(new WorkflowUpdate("FSHA000000021", nodes1));
////
////// 2. 调用接口
//////        WorkflowBatchUpdateService service = new WorkflowBatchUpdateService();
////        JSONObject response = cloudpenseApiUtils.batchUpdateWorkflows(nodes1, "zh_CN");
////
////// 3. 处理响应
////        if (response.getIntValue("resCode") == 200000) {
////            System.out.println("批量更新成功");
////        } else {
////            System.err.println("更新失败: " + response.getString("resMsg"));
////        }
//    }


    /**
     * 创建测试审批记录数据
     */
    /**
     * 创建测试审批记录
     */
    public List<ApprovalRecord> createTestApprovalRecords() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        List<ApprovalRecord> records = new ArrayList<>();

        // 只保留工号，删除姓名
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-06-29 12:03:55.377"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "运营管理部审批", null, "A01676", sdf.parse("2024-06-29 12:16:30.787"), "approved", "同意");
        addRecord(records, "直属审批", null, "A01019", sdf.parse("2024-06-29 12:16:11.933"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "集团高管审批", null, "A00647", sdf.parse("2024-07-01 00:36:53.820"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "财务BP", null, "A02025", sdf.parse("2024-07-21 04:45:10.780"), "approved", "同意");
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-07-14 12:57:07.347"), "approved", "同意");
        addRecord(records, "财务BP", null, "A02025", sdf.parse("2024-07-22 19:49:03.473"), "approved", "同意");
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-08-01 03:45:43.643"), "approved", "同意");
        addRecord(records, "财务BP", null, "A02025", sdf.parse("2024-09-06 01:28:30.523"), "approved", "同意");
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-08-15 15:44:45.227"), "approved", "同意");
        addRecord(records, "财务BP", null, "A02025", sdf.parse("2024-08-24 00:06:14.530"), "approved", "同意");
        addRecord(records, "合规审批", null, "A01685", sdf.parse("2024-08-21 17:25:51.113"), "approved", "同意");
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-08-22 02:53:57.553"), "approved", "同意");
        addRecord(records, "合规审批", null, "A01685", sdf.parse("2024-08-21 18:48:14.917"), "approved", "同意");
        addRecord(records, "起草节点", null, "A02466", sdf.parse("2024-08-21 14:44:33.347"), "approved", "同意");
        addRecord(records, "合规审批", null, "A01685", sdf.parse("2024-08-21 14:57:57.243"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "财务总监审批", null, "A01072", sdf.parse("2024-08-21 19:33:26.527"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "财务分管领导审批", null, "A00077", sdf.parse("2024-08-25 14:18:01.187"), "approved", "同意");
        addRecord(records, "条件分支", null, null, null, null, null);
        addRecord(records, "合同自动编号", null, null, null, null, null);
        addRecord(records, "盖章人审批", null, "A01598", sdf.parse("2024-08-31 17:36:47.490"), "approved", "同意");
        addRecord(records, "启动并行分支", null, null, null, null, null);
        addRecord(records, "上传双方盖章版协议", null, null, null, null, null);
        addRecord(records, "上传双方盖章版协议", null, "A02466", sdf.parse("2024-08-27 23:05:15.080"), "approved", "同意");
        addRecord(records, "结束并行分支", null, null, null, null, null);
        addRecord(records, "归档复核", null, "A00116", sdf.parse("2024-08-28 23:17:31.317"), "approved", "同意");
        addRecord(records, "抄送财务领导", null, null, null, null, null);
        addRecord(records, "总裁办审核", null, "A01598", sdf.parse("2024-08-28 09:35:20.253"), "approved", "同意");
        addRecord(records, "结束节点", null, null, null, null, null);

        return records;
    }
    /**
     * 添加审批记录
     */
    /**
     * 提取的更新单据逻辑测试方法
     * 从 OaFormUploadService.java 第285-330行提取
     */
    @org.junit.Test
    public void testUpdateDocumentHeader() throws Exception {


        // 构建请求体 - 按照指定格式 {"column39":"103020255300001825;;;","note":"","header_id":29900}
        JSONObject requestBody = new JSONObject();
        requestBody.put("column39", "105020255300013102;");
        requestBody.put("note", "");
        requestBody.put("header_id", "");
        log.info("请求参数：{}", requestBody.toJSONString());

        // 直接调用更新接口
        String result = cloudpenseApiUtils.updateExpClaimHeader(requestBody.toJSONString());
        log.info("更新结果：{}", result);
    }

    private void addRecord(List<ApprovalRecord> records, String nodeName, String approverName,
                           String approverCode, Date approveTime, String approveResult, String approveOpinion) {
        ApprovalRecord record = new ApprovalRecord();
        record.setNodeName(nodeName);
        record.setStaffNo(approverCode);
        record.setApproveTime(approveTime);
        record.setCreateTime(approveTime != null ? new Date(approveTime.getTime() - 1000000) : null);
        records.add(record);
    }

    /**
     * 格式化单据编号
     * 将数字格式化为PAY0000000001格式
     *
     * @param num 编号数字
     * @return 格式化后的单据编号
     */
    private String formatDocumentNumber(int num) {
        return String.format("PAY%010d", num);
    }

    /**
     * 生成单据编号列表
     * 根据起始和结束编号生成单据编号列表
     *
     * @param startNum 起始编号
     * @param endNum 结束编号
     * @return 单据编号列表
     */
    private List<String> generateDocumentNumbers(int startNum, int endNum) {
        List<String> documentNumbers = new ArrayList<>();
        for (int i = startNum; i <= endNum; i++) {
            documentNumbers.add(formatDocumentNumber(i));
        }
        return documentNumbers;
    }

    /**
     * 批量更新支付单据的前序单据column39字段测试方法
     */
    @org.junit.Test
    public void testBatchUpdatePaymentDocuments() throws Exception {
        log.info("开始批量更新支付单据的前序单据column39字段");

        // 配置查询编号范围 (可根据需要修改)
        int startNum = 11000;    // 起始编号
        int endNum = 11500;    // 结束编号

        // 1. 生成要查询的单据编号列表
        List<String> documentNumbers = generateDocumentNumbers(startNum, endNum);
        log.info("生成单据编号范围: {} 到 {} (共{}个)",
                formatDocumentNumber(startNum), formatDocumentNumber(endNum), documentNumbers.size());

        // 2. 根据编号列表查询支付单据
        List<ExpClaimHeaderDTO> paymentDocs = expClaimHeaderMapper.selectPaymentDocumentsByNumbers(documentNumbers);
        log.info("查询到匹配的支付单据数量: {}", paymentDocs.size());

        int processedCount = 0;
        int updatedCount = 0;

        // 3. 逐条遍历处理
        for (ExpClaimHeaderDTO payDoc : paymentDocs) {
        Integer linkHeaderId = payDoc.getLinkHeaderId();

            if (linkHeaderId != null) {
                log.info("处理支付单据: {}, 前序单据ID: {}", payDoc.getDocumentNum(), linkHeaderId);

                // 4. 查询相同前序单据的所有支付单据
                List<ExpClaimHeaderDTO> sameLinkDocs = expClaimHeaderMapper.selectByLinkHeaderId(linkHeaderId);
                log.info("找到相同前序单据的支付单据数量: {}", sameLinkDocs.size());

                if (!sameLinkDocs.isEmpty()) {
                    // 5. 拼接journal_num字段
                    List<String> validJournalNums = new ArrayList<>();
                    for (ExpClaimHeaderDTO doc : sameLinkDocs) {
                        if (StringUtils.hasText(doc.getJournalNum())) {
                            validJournalNums.add(doc.getJournalNum());
                        }
                    }

                    String column39Value;
                    if (validJournalNums.size() == 1) {
                        // 只有一个凭证号，不加分号
                        column39Value = validJournalNums.get(0);
                    } else if (validJournalNums.size() > 1) {
                        // 多个凭证号，用分号分隔
                        column39Value = String.join(";", validJournalNums) + ";";
                    } else {
                        column39Value = "";
                    }

                    // 6. 处理submit_date字段，生成column50的值
                    Date latestSubmitDate = sameLinkDocs.stream()
                            .map(ExpClaimHeaderDTO::getSubmitDate)
                            .filter(Objects::nonNull)
                            .max(Date::compareTo)
                            .orElse(null);

                    String column50Value = "";
                    if (latestSubmitDate != null) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        column50Value = sdf.format(latestSubmitDate);
                    }

                    log.info("拼接后的凭证号: {}, 格式化日期: {}", column39Value, column50Value);

                    // 7. 调用更新接口更新前序单据的column39和column50字段
                    if (StringUtils.hasText(column39Value) || StringUtils.hasText(column50Value)) {
                        JSONObject requestBody = new JSONObject();
                        requestBody.put("column39", column39Value);
                        requestBody.put("column50", column50Value);
                        requestBody.put("note", "");
                        requestBody.put("header_id", linkHeaderId);

                        log.info("更新前序单据 header_id: {}, 请求参数: {}", linkHeaderId, requestBody.toJSONString());

                        try {
                            String result = cloudpenseApiUtils.updateExpClaimHeader(requestBody.toJSONString());
                            log.info("更新结果: {}", result);
                            updatedCount++;
                        } catch (Exception e) {
                            log.error("更新前序单据失败, header_id: {}, 错误: {}", linkHeaderId, e.getMessage());
                        }
                    }
                }
                processedCount++;
            } else {
                log.debug("支付单据 {} 没有前序单据", payDoc.getDocumentNum());
            }
        }

        log.info("批量更新完成, 处理总数: {}, 成功更新: {}", processedCount, updatedCount);
    }

    /**
     * 成本中心部门权限同步测试方法
     * 手动触发成本中心部门权限同步到费控系统
     */
    @org.junit.Test
    public void testCostCenterDepartmentSync() throws Exception {
        log.info("开始成本中心部门权限同步测试");
        
        try {
            // 1. 查询所有成本中心数据
            log.info("正在查询成本中心数据...");
            CostCenter queryParam = new CostCenter();
            List<CostCenter> costCenters = costCenterService.selectCostCenterList(queryParam);
            log.info("查询到成本中心数量: {}", costCenters.size());
            
            if (costCenters.isEmpty()) {
                log.warn("未查询到成本中心数据，同步结束");
                return;
            }
            
            // 2. 转换数据结构
            List<CostCenterDepartmentData> syncDataList = new ArrayList<>();
            int processedCount = 0;
            int validDataCount = 0;
            
            for (CostCenter costCenter : costCenters) {
                processedCount++;
                
                // 确定成本中心编码：优先使用code，如果为空则使用id
                String costCenterCode = StringUtils.hasText(costCenter.getCode()) ? 
                    costCenter.getCode() : String.valueOf(costCenter.getId());
                
                log.info("处理成本中心[{}/{}]: ID={}, Code={}, Name={}, ekpIds={}", 
                    processedCount, costCenters.size(),
                    costCenter.getId(), costCenterCode, costCenter.getCostCenterName(), costCenter.getEkpIds());
                
                // 解析ekpIds字段
                if (StringUtils.hasText(costCenter.getEkpIds())) {
                    String[] departmentIds = costCenter.getEkpIds().split(",");
                    List<DepartmentCostInfo> departmentCostList = new ArrayList<>();
                    
                    int sequenceNum = 1;
                    for (String departmentId : departmentIds) {
                        departmentId = departmentId.trim();
                        if (StringUtils.hasText(departmentId)) {
                            DepartmentCostInfo deptInfo = new DepartmentCostInfo();
                            deptInfo.setDepartmentCode(departmentId);
                            deptInfo.setSequenceNum(String.valueOf(sequenceNum++));
                            deptInfo.setChildIncludeFlag("Y"); // 默认包含子部门
                            departmentCostList.add(deptInfo);
                            
                            log.debug("添加部门权限: departmentCode={}, sequenceNum={}, childIncludeFlag=Y", 
                                departmentId, sequenceNum - 1);
                        }
                    }
                    
                    if (!departmentCostList.isEmpty()) {
                        CostCenterDepartmentData syncData = new CostCenterDepartmentData();
                        syncData.setCode(costCenterCode);
                        syncData.setDepartmentCostList(departmentCostList);
                        syncDataList.add(syncData);
                        validDataCount++;
                        
                        log.info("成本中心 {} 生成部门权限数据，包含 {} 个部门", 
                            costCenterCode, departmentCostList.size());
                    } else {
                        log.warn("成本中心 {} 的ekpIds解析后无有效部门ID", costCenterCode);
                    }
                } else {
                    log.warn("成本中心 {} 的ekpIds字段为空，跳过", costCenterCode);
                }
            }
            
            log.info("数据转换完成，共处理 {} 个成本中心，生成有效同步数据 {} 条", processedCount, validDataCount);
            
            // 3. 批量同步到费控系统
            if (!syncDataList.isEmpty()) {
                log.info("开始调用费控系统同步接口，数据量: {}", syncDataList.size());
                
                CostCenterDepartmentSyncResponse response = cloudpenseApiUtils.batchSyncCostCenterDepartments(syncDataList);
                
                if (response != null && response.getResCode() != null && response.getResCode() == 200000) {
                    log.info("成本中心部门权限同步成功！resCode: {}, resMsg: {}, bizId: {}", 
                        response.getResCode(), response.getResMsg(), response.getBizId());
                } else {
                    log.error("成本中心部门权限同步失败！resCode: {}, resMsg: {}", 
                        response != null ? response.getResCode() : "null", 
                        response != null ? response.getResMsg() : "响应为空");
                }
            } else {
                log.warn("没有有效的同步数据，跳过API调用");
            }
            
        } catch (Exception e) {
            log.error("成本中心部门权限同步测试执行异常", e);
            throw e;
        }
        
        log.info("成本中心部门权限同步测试完成");
    }
}
