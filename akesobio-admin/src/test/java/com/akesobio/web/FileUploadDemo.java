package com.akesobio.web;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.FileEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class FileUploadDemo {

    private static final String API_URL = "https://your-api-domain.com/common/files/v3/uploadUrl";
    private static final String LOCALE = "zh_CN"; // 支持zh_CN/en_US/ja_JP/zh_TW
    
    public static void main(String[] args) {
        // 1. 准备要上传的文件名列表
        List<String> fileNames = new ArrayList<>();
        fileNames.add("test1.pdf");
        fileNames.add("test2.jpg");
        
        try {
            // 2. 获取文件上传地址
            List<UploadURL> uploadURLs = getUploadUrls(fileNames);
            
            // 3. 使用获取到的URL上传文件
            for (UploadURL uploadURL : uploadURLs) {
                boolean success = uploadFile(
                    uploadURL.getFileURL(), 
                    "D:/temp/" + uploadURL.getFileName()
                );
                System.out.println("文件" + uploadURL.getFileName() + 
                    (success ? "上传成功" : "上传失败"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    // 获取文件上传地址
    public static List<UploadURL> getUploadUrls(List<String> fileNames) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(API_URL);
        
        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("locale", LOCALE);
        
        // 构建请求体 - v3 API格式
        JSONObject requestBody = new JSONObject();
        requestBody.put("bizId", java.util.UUID.randomUUID().toString());
        requestBody.put("timestamp", System.currentTimeMillis());

        // 构建文件数据数组，包含文件名和content_type
        JSONArray dataArray = new JSONArray();
        for (String fileName : fileNames) {
            JSONObject fileObj = new JSONObject();
            fileObj.put("file_name", fileName);
            fileObj.put("content_type", getContentTypeByFileName(fileName));
            dataArray.add(fileObj);
        }
        requestBody.put("data", dataArray);
        
        httpPost.setEntity(new StringEntity(requestBody.toJSONString()));
        
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject result = JSON.parseObject(responseBody);
            
            if (result.getInteger("resCode") == 200000) {
                JSONArray data = result.getJSONArray("data");
                List<UploadURL> urls = new ArrayList<>();
                
                for (int i = 0; i < data.size(); i++) {
                    JSONObject item = data.getJSONObject(i);
                    urls.add(new UploadURL(
                        item.getString("file_name"),    // v3 API字段名
                        item.getString("upload_url")    // v3 API字段名
                    ));
                }
                return urls;
            } else {
                throw new RuntimeException("获取上传地址失败: " + result.getString("resMsg"));
            }
        }
    }
    
    // 使用URL上传文件
    public static boolean uploadFile(String uploadUrl, String filePath) throws IOException {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            // 获取文件MIME类型
            String mimeType = Files.probeContentType(Paths.get(filePath));
            if (mimeType == null) {
                mimeType = "application/octet-stream";
            }
            
            HttpPut httpPut = new HttpPut(uploadUrl);
            httpPut.setEntity(new FileEntity(new File(filePath)));
            httpPut.setHeader("Content-Type", mimeType);
            
            httpClient = HttpClients.createDefault();
            response = httpClient.execute(httpPut);
            
            return response.getStatusLine().getStatusCode() == 200;
        } finally {
            if (response != null) response.close();
            if (httpClient != null) httpClient.close();
        }
    }
    
    // 上传URL数据对象
    static class UploadURL {
        private String fileName;
        private String fileURL;
        
        public UploadURL(String fileName, String fileURL) {
            this.fileName = fileName;
            this.fileURL = fileURL;
        }
        
        public String getFileName() {
            return fileName;
        }
        
        public String getFileURL() {
            return fileURL;
        }
    }

    // 根据文件名获取Content-Type
    private static String getContentTypeByFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else {
            return "application/octet-stream";
        }
    }
}
