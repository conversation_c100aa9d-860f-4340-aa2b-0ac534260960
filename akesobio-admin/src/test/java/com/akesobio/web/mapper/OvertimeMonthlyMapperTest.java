package com.akesobio.web.mapper;


import com.akesobio.ReportApplication;
import com.akesobio.common.constant.HttpStatus;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.report.hr.domain.entity.OvertimeReportDepartmentMonthly;
import com.akesobio.report.hr.domain.entity.OvertimeReportEmployeeMonthly;
import com.akesobio.report.hr.mapper.OvertimeReportDepartmentMapper;
import com.akesobio.report.hr.mapper.OvertimeReportEmployeeMapper;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class OvertimeMonthlyMapperTest {

    @Autowired
    private OvertimeReportEmployeeMapper empMapper;
    @Autowired
    private OvertimeReportDepartmentMapper deptMapper;

    @Test
    public void testEmpMonthlyList(){
        OvertimeReportEmployeeMonthly query = new OvertimeReportEmployeeMonthly();
        query.setYear(2023);
        List<OvertimeReportEmployeeMonthly> list = empMapper.selectMonthlyList(query);
        System.out.println(list.size());
    }

    @Test
    public void testDeptMonthlyList(){
        OvertimeReportDepartmentMonthly query = new OvertimeReportDepartmentMonthly();
        query.setYear(2023);
        List<OvertimeReportDepartmentMonthly> list = deptMapper.selectMonthlyList(query);
        System.out.println(list.size());
    }


    protected TableDataInfo getDataTable(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

}
