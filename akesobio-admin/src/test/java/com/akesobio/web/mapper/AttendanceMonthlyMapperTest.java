package com.akesobio.web.mapper;


import com.akesobio.ReportApplication;
import com.akesobio.common.constant.HttpStatus;
import com.akesobio.common.core.page.TableDataInfo;
import com.akesobio.report.hr.domain.entity.AttendanceMonthly;
import com.akesobio.report.hr.domain.query.AttendanceMonthlyQuery;
import com.akesobio.report.hr.mapper.AttendanceMonthlyMapper;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class AttendanceMonthlyMapperTest {

    @Autowired
    private AttendanceMonthlyMapper mapper;

    @Test
    public void testSelectByCriteria(){
       /* AttendanceMonthlyQuery query = new AttendanceMonthlyQuery();
        query.setYear(2023);
        query.setJobNumbers(new String[]{"A02444", "A02445"});
        ArrayList<String> monthRange = new ArrayList<>();
        monthRange.add("2023-05");
        monthRange.add("2023-07");
        query.setMonthRange(monthRange);
        List<AttendanceMonthly> list = mapper.selectByQuery(query);
        list.forEach(System.out::println);*/
    }


    protected TableDataInfo getDataTable(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

}
