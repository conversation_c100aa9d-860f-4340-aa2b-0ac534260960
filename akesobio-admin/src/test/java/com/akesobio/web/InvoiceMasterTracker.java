package com.akesobio.web;

import com.akesobio.common.annotation.Excel;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctInventory;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctPurchaseNotreturn;
import com.akesobio.report.autoacct.domain.PurchaseNotreturnOc;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.fasterxml.jackson.core.JsonParser;
import com.google.gson.JsonObject;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ResourceUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class InvoiceMasterTracker {


    public static void main(String[] args) throws Exception {


    }

    /**
     * 获取token
     *     grant_type: client_credentials
     *     client_id: cloudpense2483631363b
     *     client_secret: 44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98
     */
    public static void getToken() {
        String response = "";
        String token_url = "https://taliopenapi.cloudpense.com/common/unAuth/tokens/get";
        String grant_type = "client_credentials";
        String client_id = "cloudpense2483631363b";
        String client_secret = "44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98";
        String url = token_url + "?grant_type=" + grant_type + "&client_id=" + client_id + "&client_secret=" + client_secret;
        try {
            response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);
            String accessToken = jsonObject.getJSONObject("data").getString("access_token");
            System.out.println("Access Token: " + accessToken);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
   }
