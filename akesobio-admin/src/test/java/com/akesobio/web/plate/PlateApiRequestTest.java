package com.akesobio.web.plate;

import com.alibaba.fastjson2.JSONObject;

public class PlateApiRequestTest {
    public static void main(String[] args) {
        JSONObject oaForm = new JSONObject();
        String jobNo = oaForm.getString("job_number");
        String licensePlate = oaForm.getString("license_number");
        String billNo = oaForm.getString("application_subject");
        String operateTypeDec = oaForm.getString("state");
        Integer operateType = null; // 0 for delete, 1 for add, 2 for modify
        if("新增车牌".equals(operateTypeDec)){
            operateType = 1;
        }else if("变更车牌".equals(operateTypeDec)){
            operateType = 2;
        }
        PlateApiRequest.sendApproveRequest(
                jobNo,
                licensePlate,
                billNo,
                operateType // 0 for delete, 1 for add, 2 for modify
        );
    }
}
