package com.akesobio.web.plate;



import java.io.DataOutputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class PlateApiRequest {
    private static final String API_URL = "http://10.0.0.249/v-gateway-licensePlate/licensePlate/open/api/license/plate/approve";
    private static final String APP_ID = "plate";
    private static final String APP_SECRET = "gUv]#~+tt)4E!mXMn.p,";

    public static void sendApproveRequest(String jobNo, String licensePlate, String billNo, int operateType) {
        try {
            URL url = new URL(API_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setDoOutput(true);

            // Generate timestamp and sign
            long timestamp = System.currentTimeMillis();
            String sign = SignUtil.generateSign(APP_ID, APP_SECRET, timestamp);
            System.out.println("Timestamp: " + timestamp);
            conn.setRequestProperty("ts", String.valueOf(timestamp));
            conn.setRequestProperty("appid", APP_ID);
            conn.setRequestProperty("sign", sign);
            System.out.println("Sign: " + sign);
            // Prepare JSON body
            String jsonBody = String.format("{\"jobNo\": \"%s\", \"licensePlate\": \"%s\", \"billNo\": \"%s\", \"operateType\": %d}",
                    jobNo, licensePlate, billNo, operateType);

            try(OutputStream os = conn.getOutputStream()) {
                try(DataOutputStream wr = new DataOutputStream(os)) {
                    wr.writeBytes(jsonBody);
                    wr.flush();
                }
            }

            // Get the response
            int responseCode = conn.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (java.io.BufferedReader in = new java.io.BufferedReader(new java.io.InputStreamReader(conn.getInputStream()))) {
                    String inputLine;
                    StringBuffer response = new StringBuffer();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    System.out.println(response.toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
