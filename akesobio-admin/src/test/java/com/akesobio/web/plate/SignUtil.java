package com.akesobio.web.plate;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class SignUtil {
    public static String generateSign(String appid, String appSecret, long timestamp) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            String toBeSigned = appid + appSecret + timestamp;
            // 对拼接后的字符串进行MD5加密
            md.update(toBeSigned.getBytes());
            // 获取加密后的字节数组
            byte[] digest = md.digest();
            // 将字节数组转换成32位的十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            // 返回MD5签名
            return sb.toString();
//            byte[] encodedhash = md.digest(toBeSigned.getBytes("UTF-8"));
//            return Base64.getEncoder().encodeToString(encodedhash);
        } catch (NoSuchAlgorithmException | IllegalArgumentException e) {
            e.printStackTrace();
            return null;
        }
    }
}
