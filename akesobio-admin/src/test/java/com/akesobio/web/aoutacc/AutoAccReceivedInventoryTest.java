package com.akesobio.web.aoutacc;


import com.akesobio.ReportApplication;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.autoacct.domain.AutoAccReceivedInventory;
import com.akesobio.report.autoacct.domain.AutoAccReceivedMaterials;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctInventory;
import com.akesobio.report.autoacct.service.IAutoAccReceivedInventoryService;
import com.akesobio.report.autoacct.service.IAutoAccReceivedMaterialsService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
@Slf4j
public class AutoAccReceivedInventoryTest {
    @Resource
    private IAutoAccReceivedInventoryService autoAccReceivedInventoryService;

    @Resource
    private IAutoAccReceivedMaterialsService autoAccReceivedMaterialsService;

    @Test
    public void test() throws Exception {
       List<AutoAccReceivedMaterials> list =  autoAccReceivedMaterialsService.list();

       List<String> orderNumbers = list.stream().map(AutoAccReceivedMaterials::getOrderNumber).collect(Collectors.toList());

       autoAccReceivedInventoryService.refreshAutoAccReceivedInventory(orderNumbers,null);
    }

    public static void main(String[] args) throws Exception {

        JSONObject jsonObject2 = JSONObject.parseObject("{\n" +
                "    \"factory_code\": \"1000\"\n" +
                "}");
        jsonObject2.put("factory_code", "1000");
        String str =  HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZRP_MM_003",jsonObject2.toJSONString());
        JSONObject jsonObject = JSONObject.parseObject(str);
        JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("ET_DATA");
        List<AutoMaterialAcctInventory> list = jsonArray.stream().filter(o -> {
            JSONObject jsonObject1 = (JSONObject) o;
            return jsonObject1.getString("MATNR").charAt(0) == '1' &&!jsonObject1.getString("LGOBE").contains("不合格");
        }).map(o -> {
            JSONObject jsonObject1 = (JSONObject) o;
            AutoMaterialAcctInventory autoMaterialAcctInventory = new AutoMaterialAcctInventory();
            autoMaterialAcctInventory.sapInventoryDataConversion(jsonObject1);
            return autoMaterialAcctInventory;
        }).collect(Collectors.toList());
    }
}
