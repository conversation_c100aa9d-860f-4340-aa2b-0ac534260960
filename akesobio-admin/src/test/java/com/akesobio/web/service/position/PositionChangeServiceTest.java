package com.akesobio.web.service.position;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.controller.employee.position.vo.PositionChangeVO;
import com.akesobio.report.hr.convert.personnel.position.PositionChangeConvert;
import com.akesobio.report.hr.dal.dataobject.employee.position.PositionChange;
import com.akesobio.report.hr.service.position.PositionChangeService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * {@link PositionChangeService} 测试类
 *
 * @author: shenglin.qin
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class PositionChangeServiceTest {

    @Autowired
    private PositionChangeService positionChangeService;

    @Test
    public void testList() throws Exception {
        List<PositionChange> doList = positionChangeService.list(null);
        List<PositionChangeVO> list = PositionChangeConvert.INSTANCE.convert(doList);
        Assert.assertFalse(list.isEmpty());
    }


}
