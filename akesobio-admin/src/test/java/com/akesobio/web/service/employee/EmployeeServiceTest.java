package com.akesobio.web.service.employee;

import com.akesobio.ReportApplication;
import com.akesobio.common.utils.collection.CollectionUtils;
import com.akesobio.report.hr.common.constant.PersonnelConstant;
import com.akesobio.report.hr.controller.employee.vo.AnnualReportQuery;
import com.akesobio.report.hr.controller.employee.vo.ManpowerStructureQuery;
import com.akesobio.report.hr.dal.dataobject.employee.file.Employee;
import com.akesobio.report.hr.domain.query.overtime.EmployeeReportQuery;
import com.akesobio.report.hr.service.employee.IEmployeeService;
import com.akesobio.report.hr.service.employee.bo.Headcount;
import com.akesobio.report.hr.service.employee.bo.ManpowerStructure;
import com.akesobio.report.hr.service.employee.bo.PersonnelReport;
import com.akesobio.report.hr.service.employee.bo.structureCount.Age;
import com.akesobio.report.hr.service.employee.bo.structureCount.CurrentSeniority;
import com.akesobio.report.hr.service.employee.bo.structureCount.Education;
import com.akesobio.report.hr.service.employee.bo.structureCount.Gender;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 员工Service测试类
 *
 * @author: shenglin.qin
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class EmployeeServiceTest {

    @Autowired
    private IEmployeeService service;

    @Test
    public void testPersonnelReport() {
        AnnualReportQuery query = new AnnualReportQuery();
        query.setMainBody(PersonnelConstant.ReportMainBody.COMPANY.getValue());
        query.setYear(2023);
//        query.setCompany("康方药业");
//        query.setDepartment("药物警戒部");
        List<PersonnelReport> boList = service.annualPersonnelReport(query);
        PersonnelReport total = boList.stream().filter(item -> {
            return "总数".equals(item.getCompany()) || "总数".equals(item.getDepartment());
        }).collect(Collectors.toList()).get(0);
        // 过滤整个集团的总数
        boList = boList.stream().filter(item -> {
            return !"总数".equals(item.getCompany()) && !"总数".equals(item.getDepartment());
        }).collect(Collectors.toList());
        Map<String, List<PersonnelReport>> listMap;
        if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
            listMap = CollectionUtils.convertMultiMap(boList, PersonnelReport::getCompany);
        } else {
            listMap = CollectionUtils.convertMultiMap(boList, PersonnelReport::getDepartment);
        }
        AtomicInteger realtimeTotalControl = new AtomicInteger();
        Headcount totalControl = new Headcount();
        totalControl.setLastTime(0);
        totalControl.setIncrease(0);
        totalControl.setDecrease(0);
        totalControl.setRealtime(0);


        listMap.forEach((mainBody, mainBodyReportList) -> {
            System.out.println(mainBody);
            Optional<PersonnelReport> totalOptional = mainBodyReportList.stream().filter(vo -> {
                return "汇总".equals(vo.getDepartment()) || "汇总".equals(vo.getCompany());
            }).findFirst();
            mainBodyReportList = mainBodyReportList.stream().filter(vo -> {
                return !"汇总".equals(vo.getDepartment()) && !"汇总".equals(vo.getCompany());
            }).collect(Collectors.toList());

            Map<Integer, Headcount> orgControl = new HashMap<>();
            mainBodyReportList.forEach(report -> {
                System.out.println();
                if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
                    System.out.println(report.getDepartment());
                } else {
                    System.out.println(report.getCompany());
                }
                Headcount deptAnnual = report.getMonthly().get(0);
                // 部门对照组
                Headcount deptControl = new Headcount();
                deptControl.setLastTime(0);
                deptControl.setIncrease(0);
                deptControl.setDecrease(0);
                deptControl.setRealtime(0);

                for (int i = 1; i < report.getMonthly().size(); i++) {
                    Headcount monthly = report.getMonthly().get(i);
                    Headcount headcount;
                    if (orgControl.containsKey(i)) {
                        headcount = orgControl.get(i);
                        headcount.setLastTime(headcount.getLastTime() + monthly.getLastTime());
                        headcount.setIncrease(headcount.getIncrease() + monthly.getIncrease());
                        headcount.setDecrease(headcount.getDecrease() + monthly.getDecrease());
                        headcount.setRealtime(headcount.getRealtime() + monthly.getRealtime());
                    } else {
                        headcount = new Headcount();
                        headcount.setLastTime(monthly.getLastTime());
                        headcount.setIncrease(monthly.getIncrease());
                        headcount.setDecrease(monthly.getDecrease());
                        headcount.setRealtime(monthly.getRealtime());
                    }
                    orgControl.put(i, headcount);
                    int monthlyTotal = monthly.getLastTime() + monthly.getIncrease() - monthly.getDecrease();
                    deptControl.setIncrease(deptControl.getIncrease() + monthly.getIncrease());
                    deptControl.setDecrease(deptControl.getDecrease() + monthly.getDecrease());
                    System.out.println(i + "月: " + monthly.getLastTime() + "+" + monthly.getIncrease() + "-" + monthly.getDecrease() + "=" + monthly.getRealtime() + "==" + monthlyTotal + "? " + (monthlyTotal == monthly.getRealtime()));
                    Assert.assertEquals(monthlyTotal, (int) monthly.getRealtime());
                }
                Headcount headcount;
                if (orgControl.containsKey(0)) {
                    headcount = orgControl.get(0);
                    headcount.setLastTime(headcount.getLastTime() + deptAnnual.getLastTime());
                    headcount.setIncrease(headcount.getIncrease() + deptAnnual.getIncrease());
                    headcount.setDecrease(headcount.getDecrease() + deptAnnual.getDecrease());
                    headcount.setRealtime(headcount.getRealtime() + deptAnnual.getRealtime());
                } else {
                    headcount = new Headcount();
                    headcount.setLastTime(deptAnnual.getLastTime());
                    headcount.setIncrease(deptAnnual.getIncrease());
                    headcount.setDecrease(deptAnnual.getDecrease());
                    headcount.setRealtime(deptAnnual.getRealtime());
                }
                orgControl.put(0, headcount);
                int deptAnnualTotal = deptAnnual.getLastTime() + deptAnnual.getIncrease() - deptAnnual.getDecrease();
                System.out.println("年度: " + deptAnnual.getLastTime() + "+" + deptAnnual.getIncrease() + "-" + deptAnnual.getDecrease() + "=" + deptAnnual.getRealtime() + "==" + deptAnnualTotal + "? " + (deptAnnualTotal == deptAnnual.getRealtime()));
                Assert.assertEquals(deptAnnualTotal, (int) deptAnnual.getRealtime());

                System.out.println("年度新增汇总: " + deptControl.getIncrease() + "==" + deptAnnual.getIncrease() + "? " + (Objects.equals(deptControl.getIncrease(), deptAnnual.getIncrease())));
                Assert.assertEquals(deptControl.getIncrease(), deptAnnual.getIncrease());
                System.out.println("年度减少汇总: " + deptControl.getDecrease() + "==" + deptAnnual.getDecrease() + "? " + (Objects.equals(deptControl.getDecrease(), deptAnnual.getDecrease())));
                Assert.assertEquals(deptControl.getDecrease(), deptAnnual.getDecrease());
            });
            System.out.println();
            if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
                System.out.println("公司汇总");
            } else {
                System.out.println("部门汇总");
            }
            if (totalOptional.isPresent()) {
                Map<Integer, Headcount> reportMonthly = totalOptional.get().getMonthly();
                Headcount orgMonthlyAnnual = reportMonthly.get(0);
                totalControl.setLastTime(totalControl.getLastTime() + orgMonthlyAnnual.getLastTime());
                totalControl.setIncrease(totalControl.getIncrease() + orgMonthlyAnnual.getIncrease());
                totalControl.setDecrease(totalControl.getDecrease() + orgMonthlyAnnual.getDecrease());
                totalControl.setRealtime(totalControl.getRealtime() + orgMonthlyAnnual.getRealtime());
                realtimeTotalControl.addAndGet(orgMonthlyAnnual.getRealtime());
                Headcount orgMonthlyControl = new Headcount();
                orgMonthlyControl.setLastTime(0);
                orgMonthlyControl.setIncrease(0);
                orgMonthlyControl.setDecrease(0);
                orgMonthlyControl.setRealtime(0);
                List<Headcount> monthlyList = new ArrayList<>(reportMonthly.values());
                for (int i = 1; i < monthlyList.size(); i++) {
                    Headcount monthly = monthlyList.get(i);
                    int monthlyTotal = monthly.getLastTime() + monthly.getIncrease() - monthly.getDecrease();
                    orgMonthlyControl.setIncrease(orgMonthlyControl.getIncrease() + monthly.getIncrease());
                    orgMonthlyControl.setDecrease(orgMonthlyControl.getDecrease() + monthly.getDecrease());
                    System.out.println(i + "月: " + monthly.getLastTime() + "+" + monthly.getIncrease() + "-" + monthly.getDecrease() + "=" + monthly.getRealtime() + "==" + monthlyTotal + "? " + (monthlyTotal == monthly.getRealtime()));
                    Assert.assertEquals(monthlyTotal, (int) monthly.getRealtime());
                    System.out.println("部门月汇总对照组: " + orgControl.get(i).getLastTime() + "+" + orgControl.get(i).getIncrease() + "-" + orgControl.get(i).getDecrease() + "=" + orgControl.get(i).getRealtime() + "==" + monthlyTotal + "? " + (monthlyTotal == orgControl.get(i).getRealtime()));
                    Assert.assertEquals(monthlyTotal, (int) orgControl.get(i).getRealtime());
                }
                int deptAnnualTotal = orgMonthlyAnnual.getLastTime() + orgMonthlyAnnual.getIncrease() - orgMonthlyAnnual.getDecrease();
                System.out.println("年度: " + orgMonthlyAnnual.getLastTime() + "+" + orgMonthlyAnnual.getIncrease() + "-" + orgMonthlyAnnual.getDecrease() + "=" + orgMonthlyAnnual.getRealtime() + "==" + deptAnnualTotal + "? " + (deptAnnualTotal == orgMonthlyAnnual.getRealtime()));
                Assert.assertEquals(deptAnnualTotal, (int) orgMonthlyAnnual.getRealtime());
                System.out.println("年度新增汇总: " + orgMonthlyControl.getIncrease() + "==" + orgMonthlyAnnual.getIncrease() + "? " + (Objects.equals(orgMonthlyControl.getIncrease(), orgMonthlyAnnual.getIncrease())));
                Assert.assertEquals(orgMonthlyControl.getIncrease(), orgMonthlyAnnual.getIncrease());
                System.out.println("年度减少汇总: " + orgMonthlyControl.getDecrease() + "==" + orgMonthlyAnnual.getDecrease() + "? " + (Objects.equals(orgMonthlyControl.getDecrease(), orgMonthlyAnnual.getDecrease())));
                Assert.assertEquals(orgMonthlyControl.getDecrease(), orgMonthlyAnnual.getDecrease());
                System.out.println("公司年度与部门年度汇总对比: ");
                Headcount headcount = orgControl.get(0);
                System.out.println(orgMonthlyAnnual.getLastTime() + "==" + headcount.getLastTime() + "? " + (Objects.equals(orgMonthlyAnnual.getLastTime(), headcount.getLastTime())));
                System.out.println(orgMonthlyAnnual.getIncrease() + "==" + headcount.getIncrease() + "? " + (Objects.equals(orgMonthlyAnnual.getIncrease(), headcount.getIncrease())));
                System.out.println(orgMonthlyAnnual.getDecrease() + "==" + headcount.getDecrease() + "? " + (Objects.equals(orgMonthlyAnnual.getDecrease(), headcount.getDecrease())));
                System.out.println(orgMonthlyAnnual.getRealtime() + "==" + headcount.getRealtime() + "? " + (Objects.equals(orgMonthlyAnnual.getRealtime(), headcount.getRealtime())));
                Assert.assertEquals(orgMonthlyAnnual.getLastTime(), headcount.getLastTime());
                Assert.assertEquals(orgMonthlyAnnual.getIncrease(), headcount.getIncrease());
                Assert.assertEquals(orgMonthlyAnnual.getDecrease(), headcount.getDecrease());
                Assert.assertEquals(orgMonthlyAnnual.getRealtime(), headcount.getRealtime());
                System.out.println();
            }
            System.out.println("-----------------------------------------------------------------------");
            System.out.println();
            System.out.println(total.getMonthly().get(0));
            System.out.println(totalControl);
            System.out.println(total.getMonthly().get(0).equals(totalControl));
            System.out.println("实时总人数: " + realtimeTotalControl.get());
        });
    }

    @Test
    public void testManpowerStructure() {
        ManpowerStructureQuery query = new ManpowerStructureQuery();
        query.setMainBody(PersonnelConstant.ReportMainBody.COMPANY.getValue());
        List<ManpowerStructure> boList = service.manpowerStructure(query);
        // 总数是整个集团的总数，汇总是各分公司/各部门的总数
        Optional<ManpowerStructure> totalOptional = boList.stream().filter(item -> {
            return "总数".equals(item.getCompany()) || "总数".equals(item.getDepartment());
        }).findFirst();
        // 过滤整个集团的总数
        boList = boList.stream().filter(item -> {
            return !"总数".equals(item.getCompany()) && !"总数".equals(item.getDepartment());
        }).collect(Collectors.toList());
        Map<String, List<ManpowerStructure>> listMap;
        if (query.getMainBody() == PersonnelConstant.ReportMainBody.COMPANY.getValue()) {
            listMap = CollectionUtils.convertMultiMap(boList, ManpowerStructure::getCompany);
        } else {
            listMap = CollectionUtils.convertMultiMap(boList, ManpowerStructure::getDepartment);
        }
        ManpowerStructure orgControl = getInitControlManpowerStructure();
        // 遍历集团所有公司(/部门)
        listMap.forEach((mainBody, mainBodyReportList) -> {
            Optional<ManpowerStructure> mainBodyTotalOptional = mainBodyReportList.stream().filter(vo -> {
                return "汇总".equals(vo.getDepartment()) || "汇总".equals(vo.getCompany());
            }).findFirst();
            mainBodyReportList = mainBodyReportList.stream().filter(vo -> {
                return !"汇总".equals(vo.getDepartment()) && !"汇总".equals(vo.getCompany());
            }).collect(Collectors.toList());
            // 主体对照组（每个归属部门/分公司相加是否等于汇总）
            ManpowerStructure mainBodyControl = getInitControlManpowerStructure();

            // 遍历公司(/部门)所有部门(/公司)
            mainBodyReportList.forEach(report -> {
                Headcount headcountControl = mainBodyControl.getHeadcount();
                Headcount headcount = report.getHeadcount();
                Integer realtime = headcount.getRealtime();
                sumSubBody(headcountControl, headcount);
                mainBodyControl.setHeadcount(headcountControl);

                Education educationControl = mainBodyControl.getEducation();
                Education education = report.getEducation();
                sumSubBodyAndValid(educationControl, education, realtime);
                mainBodyControl.setEducation(educationControl);

                Age ageControl = mainBodyControl.getAge();
                Age age = report.getAge();
                sumSubBodyAndValid(ageControl, age, realtime);
                mainBodyControl.setAge(ageControl);

                Gender genderControl = mainBodyControl.getGender();
                Gender gender = report.getGender();
                sumSubBodyAndValid(genderControl, gender, realtime);
                mainBodyControl.setGender(genderControl);

                CurrentSeniority seniorityControl = mainBodyControl.getSeniority();
                CurrentSeniority seniority = report.getSeniority();
                sumSubBodyAndValid(seniorityControl, seniority, realtime);
                mainBodyControl.setSeniority(seniorityControl);
            });
            if (mainBodyTotalOptional.isPresent()) {
                ManpowerStructure mainBodyTotal = mainBodyTotalOptional.get();
                Headcount headcount = mainBodyTotal.getHeadcount();
                Education education = mainBodyTotal.getEducation();
                Age age = mainBodyTotal.getAge();
                CurrentSeniority seniority = mainBodyTotal.getSeniority();
                Gender gender = mainBodyTotal.getGender();
                Headcount headcountControl = mainBodyControl.getHeadcount();
                Education educationControl = mainBodyControl.getEducation();
                Age ageControl = mainBodyControl.getAge();
                CurrentSeniority seniorityControl = mainBodyControl.getSeniority();
                Gender genderControl = mainBodyControl.getGender();
                Assert.assertEquals(headcountControl, headcount);
                Assert.assertEquals(educationControl, education);
                Assert.assertEquals(ageControl, age);
                Assert.assertEquals(seniorityControl, seniority);
                Assert.assertEquals(genderControl, gender);

                Headcount orgControlHeadcount = orgControl.getHeadcount();
                Headcount mainBodyTotalHeadcount = mainBodyTotal.getHeadcount();
                Integer realtime = mainBodyTotalHeadcount.getRealtime();
                sumSubBody(orgControlHeadcount, mainBodyTotalHeadcount);
                orgControl.setHeadcount(orgControlHeadcount);

                Education orgControlEducation = orgControl.getEducation();
                Education mainBodyTotalEducation = mainBodyTotal.getEducation();
                sumSubBodyAndValid(orgControlEducation, mainBodyTotalEducation, realtime);
                orgControl.setEducation(orgControlEducation);

                Age orgControlAge = orgControl.getAge();
                Age mainBodyTotalAge = mainBodyTotal.getAge();
                sumSubBodyAndValid(orgControlAge, mainBodyTotalAge, realtime);
                orgControl.setAge(orgControlAge);

                Gender orgControlGender = orgControl.getGender();
                Gender mainBodyTotalGender = mainBodyTotal.getGender();
                sumSubBodyAndValid(orgControlGender, mainBodyTotalGender, realtime);
                orgControl.setGender(orgControlGender);

                CurrentSeniority orgControlSeniority = orgControl.getSeniority();
                CurrentSeniority mainBodyTotalSeniority = mainBodyTotal.getSeniority();
                sumSubBodyAndValid(orgControlSeniority, mainBodyTotalSeniority, realtime);
                orgControl.setSeniority(orgControlSeniority);
            }
        });
        if (totalOptional.isPresent()) {
            ManpowerStructure total = totalOptional.get();
            Headcount headcount = total.getHeadcount();
            Education education = total.getEducation();
            Age age = total.getAge();
            CurrentSeniority seniority = total.getSeniority();
            Gender gender = total.getGender();
            Headcount headcountControl = orgControl.getHeadcount();
            Education educationControl = orgControl.getEducation();
            Age ageControl = orgControl.getAge();
            CurrentSeniority seniorityControl = orgControl.getSeniority();
            Gender genderControl = orgControl.getGender();
            Assert.assertEquals(headcountControl, headcount);
            Assert.assertEquals(educationControl, education);
            Assert.assertEquals(ageControl, age);
            Assert.assertEquals(seniorityControl, seniority);
            Assert.assertEquals(genderControl, gender);
        }
    }

    private void sumSubBodyAndValid(CurrentSeniority control, CurrentSeniority subBody, Integer realtime) {
        Integer below1 = subBody.getBelow1();
        Integer year1To3 = subBody.getYear1To3();
        Integer year3To5 = subBody.getYear3To5();
        Integer year5To8 = subBody.getYear5To8();
        Integer year8To15 = subBody.getYear8To15();
        control.setBelow1(below1 + control.getBelow1());
        control.setYear1To3(year1To3 + control.getYear1To3());
        control.setYear3To5(year3To5 + control.getYear3To5());
        control.setYear5To8(year5To8 + control.getYear5To8());
        control.setYear8To15(year8To15 + control.getYear8To15());
        Assert.assertEquals((int) realtime, below1 + year1To3 + year3To5 + year5To8 + year8To15);
    }

    private void sumSubBodyAndValid(Gender control, Gender subBody, Integer realtime) {
        Integer male = subBody.getMale();
        Integer female = subBody.getFemale();
        control.setMale(male + control.getMale());
        control.setFemale(female + control.getFemale());
        Assert.assertEquals((int) realtime, male + female);
    }

    private void sumSubBodyAndValid(Age control, Age subBody, Integer realtime) {
        Integer under25 = subBody.getUnder25();
        Integer age25To35 = subBody.getAge25To35();
        Integer age35To45 = subBody.getAge35To45();
        Integer age45To55 = subBody.getAge45To55();
        Integer over55 = subBody.getOver55();
        control.setUnder25(under25 + control.getUnder25());
        control.setAge25To35(age25To35 + control.getAge25To35());
        control.setAge35To45(age35To45 + control.getAge35To45());
        control.setAge45To55(age45To55 + control.getAge45To55());
        control.setOver55(over55 + control.getOver55());
        Assert.assertEquals((int) realtime, under25 + age25To35 + age35To45 + age45To55 + over55);
    }

    private void sumSubBodyAndValid(Education control, Education subBody, Integer realtime) {
        Integer doctor = subBody.getDoctor();
        Integer master = subBody.getMaster();
        Integer bachelor = subBody.getBachelor();
        Integer associateDegree = subBody.getAssociateDegree();
        Integer other = subBody.getOther();
        control.setDoctor(doctor + control.getDoctor());
        control.setMaster(master + control.getMaster());
        control.setBachelor(bachelor + control.getBachelor());
        control.setAssociateDegree(associateDegree + control.getAssociateDegree());
        control.setOther(other + control.getOther());
        // 所有学历结构人数加起来等于实时人数
        Assert.assertEquals((int) realtime, doctor + master + bachelor + associateDegree + other);
    }

    private void sumSubBody(Headcount control, Headcount subBody) {
        Integer lastUnit = subBody.getLastTime();
        Integer increase = subBody.getIncrease();
        Integer decrease = subBody.getDecrease();
        Integer realtime = subBody.getRealtime();
        Integer intern = subBody.getIntern();
        control.setLastTime(control.getLastTime() + lastUnit);
        control.setIncrease(control.getIncrease() + increase);
        control.setDecrease(control.getDecrease() + decrease);
        control.setRealtime(control.getRealtime() + realtime);
        control.setIntern(control.getIntern() + intern);
    }

    private static ManpowerStructure getInitControlManpowerStructure() {
        ManpowerStructure control = new ManpowerStructure();
        Headcount headcount = new Headcount();
        headcount.setLastTime(0);
        headcount.setIncrease(0);
        headcount.setDecrease(0);
        headcount.setRealtime(0);
        headcount.setIntern(0);

        control.setHeadcount(headcount);
        Education education = new Education();
        education.setDoctor(0);
        education.setMaster(0);
        education.setBachelor(0);
        education.setAssociateDegree(0);
        education.setOther(0);

        control.setEducation(education);
        Age age = new Age();
        age.setUnder25(0);
        age.setAge25To35(0);
        age.setAge35To45(0);
        age.setAge45To55(0);
        age.setOver55(0);

        control.setAge(age);
        CurrentSeniority seniority = new CurrentSeniority();
        seniority.setBelow1(0);
        seniority.setYear1To3(0);
        seniority.setYear3To5(0);
        seniority.setYear5To8(0);
        seniority.setYear8To15(0);

        control.setSeniority(seniority);
        Gender gender = new Gender();
        gender.setMale(0);
        gender.setFemale(0);

        control.setGender(gender);
        return control;
    }

    @Test
    public void testSelectEmployeeByYear() {
//        startPage();
        EmployeeReportQuery query = new EmployeeReportQuery();
        query.setYear(2023);
        List<Employee> employeeList = service.listFilterNoAskForLeaveByYear(query, false);
//        employeeList.forEach(System.out::println);
        System.out.println(employeeList.size());
        query.setYear(2022);
        employeeList = service.listFilterNoAskForLeaveByYear(query, false);
        System.out.println(employeeList.size());
    }
}
