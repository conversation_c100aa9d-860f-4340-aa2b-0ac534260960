package com.akesobio.web.service;

import com.akesobio.ReportApplication;
import com.akesobio.report.administration.controller.vo.MealConsumeQuery;
import com.akesobio.report.administration.domain.MealConsume;
import com.akesobio.report.administration.enums.MealTypeEnum;
import com.akesobio.report.administration.service.IMealService;
import com.akesobio.report.groupFinance.service.CostAllocationService;
import com.akesobio.report.groupFinance.vo.MealCountVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * 绩效考核相关service测试类
 *
 * @name: EmployeeServiceTest
 * @author: shenglin.qin
 * @date: 2023-07-06 11:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class MealServiceTest {

    @Autowired
    private IMealService mealService;

    @Autowired
    private CostAllocationService costAllocationService;

    @Test
    public void testSelectMealConsumeList() throws Exception {
        MealConsumeQuery query = new MealConsumeQuery();
        query.setMealType(MealTypeEnum.DINNER);
        query.setCompany("生物");
        List<MealConsume> list = mealService.listMealConsume(query);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void testSelectMealConsumeJobNumberList() throws Exception {
        MealConsumeQuery query = new MealConsumeQuery();
        query.setMealType(MealTypeEnum.DINNER);
        List<String> list = mealService.listRealConsumeJobNumber(query);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void mealCostAllocation(){
        MealConsumeQuery query = new MealConsumeQuery();
        query.setJobNumber("A02444");
        List<MealCountVO> list = costAllocationService.listMealCount(query);
        list.forEach(System.out::println);
        Assert.assertTrue(list.size() > 0);
    }


}
