package com.akesobio.web.service;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.controller.kpi.vo.kpi.qualification.KpiQualificationQuery;
import com.akesobio.report.hr.controller.kpi.vo.kpi.qualification.KpiQualificationVO;
import com.akesobio.report.hr.controller.kpi.vo.kpi.result.KpiQuery;
import com.akesobio.report.hr.controller.kpi.vo.kpi.result.KpiVO;
import com.akesobio.report.hr.service.kpi.KpiService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.akesobio.common.utils.PageUtils.startPage;

/**
 * 绩效考核相关service测试类
 *
 * @name: EmployeeServiceTest
 * @author: shenglin.qin
 * @date: 2023-07-06 11:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class KpiServiceTest {

    @Autowired
    private KpiService service;

    @Test
    public void testSelectKpiList() throws Exception {
        KpiQuery query = new KpiQuery();
        query.setYear(2022);
        startPage();
        List<KpiVO> voList = service.selectKpiVoList(query);
        Assert.assertTrue(voList.size() > 0);
    }

    @Test
    public void testKpiQualification(){
        KpiQualificationQuery query = new KpiQualificationQuery();
        query.setYear(2023);
        query.setName("门晓凡");
        List<KpiQualificationVO> list = service.listQualification(query);
        list.forEach(System.out::println);
    }


}
