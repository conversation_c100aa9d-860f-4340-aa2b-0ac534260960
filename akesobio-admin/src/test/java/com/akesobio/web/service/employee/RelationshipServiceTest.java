package com.akesobio.web.service.employee;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.controller.employee.relationship.vo.RelationshipQuery;
import com.akesobio.report.hr.controller.employee.relationship.vo.RelationshipVO;
import com.akesobio.report.hr.service.relationship.RelationshipService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * 员工关系 Service 测试类
 *
 * @author: shenglin.qin
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class RelationshipServiceTest {

    @Autowired
    private RelationshipService service;


    @Test
    public void testList() {
        RelationshipQuery query = new RelationshipQuery();
        List<RelationshipVO> list = service.list(query);
        assertNotNull(list);
    }
}
