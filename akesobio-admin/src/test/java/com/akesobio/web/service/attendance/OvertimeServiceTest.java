package com.akesobio.web.service.attendance;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.controller.attendance.vo.OvertimeDetailQuery;
import com.akesobio.report.hr.domain.entity.OvertimeDetail;
import com.akesobio.report.hr.service.IOvertimeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * 加班相关 service 测试类
 *
 * @author: shenglin.qin
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class OvertimeServiceTest {

    @Autowired
    private IOvertimeService overtimeService;

    @Test
    public void testList() {
        OvertimeDetailQuery query = new OvertimeDetailQuery();
        query.setJobNumber("A01123");
        List<OvertimeDetail> list = overtimeService.listDetails(query);
        assertNotNull(list);
    }
}
