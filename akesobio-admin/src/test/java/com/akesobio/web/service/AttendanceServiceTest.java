package com.akesobio.web.service;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.controller.attendance.vo.DepartmentHourQuery;
import com.akesobio.report.hr.controller.attendance.vo.toFinance.HourVO;
import com.akesobio.report.hr.service.IAttendanceService;
import com.alibaba.fastjson2.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * 假期相关service测试类
 *
 * @name: EmployeeServiceTest
 * @author: shenglin.qin
 * @date: 2023-07-06 11:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class AttendanceServiceTest {

    @Autowired
    private IAttendanceService attendanceService;

    @Test
    public void testListHourToFinance(){
        DepartmentHourQuery query = new DepartmentHourQuery();
        query.setYear(2023);
        List<HourVO> list = attendanceService.listHourToFinance(query);
        String string = JSON.toJSONString(list);
        System.out.println(string);
    }
}
