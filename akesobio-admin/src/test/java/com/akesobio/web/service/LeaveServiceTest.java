package com.akesobio.web.service;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.domain.query.LeaveReportQuery;
import com.akesobio.report.hr.controller.attendance.vo.leave.report.LeaveReportVO;
import com.akesobio.report.hr.service.ILeaveService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.akesobio.common.utils.PageUtils.startPage;

/**
 * 假期相关service测试类
 *
 * @name: EmployeeServiceTest
 * @author: shenglin.qin
 * @date: 2023-07-06 11:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class LeaveServiceTest {

    @Autowired
    private ILeaveService service;

    @Test
    public void testLeaveReport() throws Exception {
        startPage();
        LeaveReportQuery query = new LeaveReportQuery();
        query.setYear(2023);
        List<LeaveReportVO> list = service.listReport(query);
        list.forEach(System.out::println);
    }




}
