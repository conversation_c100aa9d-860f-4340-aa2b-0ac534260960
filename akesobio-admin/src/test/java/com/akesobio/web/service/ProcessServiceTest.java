package com.akesobio.web.service;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.service.IProcessService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 流程相关service测试类
 *
 * @name: ProcessServiceTest
 * @author: shenglin.qin
 * @date: 2023-07-06 11:30
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class ProcessServiceTest {

    @Autowired
    private IProcessService service;

    @Test
    public void testGetRunningProcess() throws Exception {
    }




}
