package com.akesobio.web.service.employee;

import com.akesobio.ReportApplication;
import com.akesobio.report.hr.dal.dataobject.employee.file.HrStaffPersonFamily;
import com.akesobio.report.hr.domain.entity.HrStaffPersonInfo;
import com.akesobio.report.hr.service.employee.EmployeeFileService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * 员工档案 Service 测试类
 *
 * @author: shenglin.qin
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class EmployeeFileServiceTest {

    @Autowired
    private EmployeeFileService service;

    @Test
    public void testInfoList(){
        List<String> ids = new ArrayList<>();
        ids.add("180646f4c3ede81629593b24012bd555");
        ids.add("189e22fcc0a818815972899463580128");
        List<HrStaffPersonInfo> list = service.listInfo(ids);
        assertNotNull(list);
    }

    @Test
    public void testFamilyList(){
        List<String> ids = new ArrayList<>();
        ids.add("180646f4c3ede81629593b24012bd555");
        ids.add("189e22fcc0a818815972899463580128");
        List<HrStaffPersonFamily> list = service.listFamily(ids);
        assertNotNull(list);
        list.forEach(System.out::println);
    }
}
