package com.akesobio.web.excel;

import com.akesobio.common.utils.bean.BeanUtils;
import com.akesobio.report.autoacct.domain.vo.InventoryOccupationDetailVo;
import com.akesobio.report.autoacct.domain.vo.MonthlyShipmentPlanVo;
import com.akesobio.report.autoacct.service.impl.InventoryOccupationDetailServiceImpl;
import com.alibaba.excel.EasyExcel;
import org.apache.poi.openxml4j.util.ZipSecureFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

public class ExcelWriter {

    public static void main(String[] args) {


        List<InventoryOccupationDetailVo> list = new ArrayList<>();
        Random random = new Random();
        for (int i = 10; i < 100; i++) {
            InventoryOccupationDetailVo vo = new InventoryOccupationDetailVo();
            vo.setProductMonth(new Date());
            // 随机选择一个前缀 13, 11, 12, 14
            String[] prefixes = {"13", "11", "12", "14"};
            String prefix = prefixes[random.nextInt(prefixes.length)];


            if (i % 2 == 0) {
                vo.setCategory("缺口");
                vo.setGap(0-i+"");
            }else {
                vo.setRequiredQuantity(i+"" );
            }


            vo.setMaterial(prefix + i);
            vo.setMpmCode("MPM" + i);
            vo.setMpmName("MPM名称" + i);
            vo.setFactoryCode("工厂" + i);
            vo.setProductCode("产品" + i);
            vo.setProductName("产品名称" + i);
            vo.setProductLine("生产线" + i);
            vo.setMaterialDesc("物料描述" + i);
            vo.setBasicUnit("基本单位" + i);
            vo.setProductMonthStr("2025/01/01");
            vo.setProductMonth(new Date());
            vo.setManufacturers("厂家" + i);



            list.add(vo); // 将对象添加到列表
        }


        // 生成测试数据
        InventoryOccupationDetailServiceImpl inventoryOccupationDetailService = new InventoryOccupationDetailServiceImpl();

        List<MonthlyShipmentPlanVo> data = inventoryOccupationDetailService.generateMonthlyShipmentPlan(list);

        EasyExcel.write("C:\\Users\\<USER>\\Downloads\\output.xlsx", MonthlyShipmentPlanVo.class)
                .sheet("Sheet1")
                .doWrite(data);
    }

    private static List<MaterialData> generateSampleData(int count) {
        List<MaterialData> data = new ArrayList<>();
        Random random = new Random();

        for (int i = 1; i <= count; i++) {
            String materialCode = "M00" + i;
            String materialDescription = "物料描述" + i;
            String manufacturer = "厂家" + i;
            String partNumber = "货号" + i;
            String packagingSpecification = "包装规格" + i;
            String basicUnit = "基本单位" + i;
            int inventoryQuantity = random.nextInt(500); // 随机库存数量
            int unshippedQuantity = random.nextInt(100); // 随机未交货数量
            int purchaseQuantity = random.nextInt(100); // 随机申购数量
            String conversionUnit = "换算单位" + i;
            String supplier = "供应商" + i;
            int deliveryPeriod = random.nextInt(60); // 随机货期
            String validityPeriod = "2024-12-31";

            // 每月需求量动态生成
            BigDecimal[] monthlyDemands = new BigDecimal[12];
            for (int month = 0; month < 12; month++) {
                monthlyDemands[month] = BigDecimal.valueOf(random.nextDouble() * 100).setScale(2, BigDecimal.ROUND_HALF_UP);
            }

            // 每月未交货、审批中、缺口数量动态生成
            BigDecimal[] monthlyUnshipped = new BigDecimal[12];
            for (int month = 0; month < 12; month++) {
                monthlyUnshipped[month] = BigDecimal.valueOf(random.nextDouble() * 50).setScale(2, BigDecimal.ROUND_HALF_UP);
            }

            // 每月发货需求（数值化）
            BigDecimal[] monthlyDeliveries = new BigDecimal[12];
            for (int month = 0; month < 12; month++) {
                monthlyDeliveries[month] = BigDecimal.valueOf(random.nextDouble() * 50).setScale(2, BigDecimal.ROUND_HALF_UP);
            }


            data.add(new MaterialData(
                    materialCode, materialDescription, manufacturer, partNumber, packagingSpecification,
                    basicUnit, inventoryQuantity, unshippedQuantity, purchaseQuantity, conversionUnit,
                    supplier, deliveryPeriod, validityPeriod,
                    monthlyDemands[0], monthlyDemands[1], monthlyDemands[2], monthlyDemands[3],
                    monthlyDemands[4], monthlyDemands[5], monthlyDemands[6], monthlyDemands[7],
                    monthlyDemands[8], monthlyDemands[9], monthlyDemands[10], monthlyDemands[11],
                    monthlyUnshipped[0], monthlyUnshipped[1], monthlyUnshipped[2], monthlyUnshipped[3],
                    monthlyUnshipped[4], monthlyUnshipped[5], monthlyUnshipped[6], monthlyUnshipped[7],
                    monthlyUnshipped[8], monthlyUnshipped[9], monthlyUnshipped[10], monthlyUnshipped[11],
                    monthlyDeliveries[0], monthlyDeliveries[1], monthlyDeliveries[2], monthlyDeliveries[3],
                    monthlyDeliveries[4], monthlyDeliveries[5], monthlyDeliveries[6], monthlyDeliveries[7],
                    monthlyDeliveries[8], monthlyDeliveries[9], monthlyDeliveries[10], monthlyDeliveries[11]
            ));
        }
        return data;
    }

    public static void writeExcel(List<MaterialData> data) {
        EasyExcel.write("C:\\Users\\<USER>\\Downloads\\output.xlsx", MaterialData.class)
                .sheet("Sheet1")
                .doWrite(data);
    }
}
