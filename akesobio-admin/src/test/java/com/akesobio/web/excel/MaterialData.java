package com.akesobio.web.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
public class MaterialData {
    @ExcelProperty(value = "物料代码", index = 0)
    private String materialCode;
    
    @ExcelProperty(value = "物料描述", index = 1)
    private String materialDescription;
    
    @ExcelProperty(value = "厂家", index = 2)
    private String manufacturer;
    
    @ExcelProperty(value = "货号", index = 3)
    private String partNumber;
    
    @ExcelProperty(value = "包装规格", index = 4)
    private String packagingSpecification;
    
    @ExcelProperty(value = "基本计量单位", index = 5)
    private String basicUnit;
    
    @ExcelProperty(value = "赛诺库存数量", index = 6)
    private Integer inventoryQuantity;
    
    @ExcelProperty(value = "赛诺未交货数量", index = 7)
    private Integer unshippedQuantity;
    
    @ExcelProperty(value = "赛诺申购中数量（对接OA系统）", index = 8)
    private Integer purchaseQuantity;
    
    @ExcelProperty(value = "换算单位", index = 9)
    private String conversionUnit;
    
    @ExcelProperty(value = "供应商", index = 10)
    private String supplier;
    
    @ExcelProperty(value = "货期（天）", index = 11)
    private Integer deliveryPeriod;
    
    @ExcelProperty(value = "效期", index = 12)
    private String validityPeriod;
    /**
     * 每月需求数量
     * 1月	2月	3月	4月	5月	6月	7月	8月	9月	10月	11月	12月
     */
    @ExcelProperty(value = {"每月需求数量", "1月"}, index = 13)
    private BigDecimal januaryDemand;
    @ExcelProperty(value = {"每月需求数量", "2月"}, index = 14)
    private BigDecimal februaryDemand;
    @ExcelProperty(value = {"每月需求数量", "3月"}, index = 15)
    private BigDecimal marchDemand;
    @ExcelProperty(value = {"每月需求数量", "4月"}, index = 16)
    private BigDecimal aprilDemand;
    @ExcelProperty(value = {"每月需求数量", "5月"}, index = 17)
    private BigDecimal mayDemand;
    @ExcelProperty(value = {"每月需求数量", "6月"}, index = 18)
    private BigDecimal juneDemand;
    @ExcelProperty(value = {"每月需求数量", "7月"}, index = 19)
    private BigDecimal julyDemand;
    @ExcelProperty(value = {"每月需求数量", "8月"}, index = 20)
    private BigDecimal augustDemand;
    @ExcelProperty(value = {"每月需求数量", "9月"}, index = 21)
    private BigDecimal septemberDemand;
    @ExcelProperty(value = {"每月需求数量", "10月"}, index = 22)
    private BigDecimal octoberDemand;
    @ExcelProperty(value = {"每月需求数量", "11月"}, index = 23)
    private BigDecimal novemberDemand;
    @ExcelProperty(value = {"每月需求数量", "12月"}, index = 24)
    private BigDecimal decemberDemand;

    /**
     * 每月未交货、审批中、缺口数量
     * 1月	2月	3月	4月	5月	6月	7月	8月	9月	10月	11月	12月
     */
    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "1月"}, index = 25)
    private BigDecimal januaryUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "2月"}, index = 26)
    private BigDecimal februaryUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "3月"}, index = 27)
    private BigDecimal marchUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "4月"}, index = 28)
    private BigDecimal aprilUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "5月"}, index = 29)
    private BigDecimal mayUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "6月"}, index = 30)
    private BigDecimal juneUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "7月"}, index = 31)
    private BigDecimal julyUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "8月"}, index = 32)
    private BigDecimal augustUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "9月"}, index = 33)
    private BigDecimal septemberUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "10月"}, index = 34)
    private BigDecimal octoberUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "11月"}, index = 35)
    private BigDecimal novemberUnshipped;

    @ExcelProperty(value = {"每月未交货、审批中、缺口数量", "12月"}, index = 36)
    private BigDecimal decemberUnshipped;

    /**
     * 每月发货需求（数值化）
     * 25年1月发货	25年2月发货	25年3月发货	25年4月发货	25年5月发货	25年6月发货	25年7月发货	25年8月发货	25年9月发货	25年10月发货	25年11月发货	25年12月发货
     */
    @ExcelProperty(value = {"每月发货需求（数值化）", "25年1月发货"}, index = 37)
    private BigDecimal januaryShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年2月发货"}, index = 38)
    private BigDecimal februaryShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年3月发货"}, index = 39)
    private BigDecimal marchShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年4月发货"}, index = 40)
    private BigDecimal aprilShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年5月发货"}, index = 41)
    private BigDecimal mayShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年6月发货"}, index = 42)
    private BigDecimal juneShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年7月发货"}, index = 43)
    private BigDecimal julyShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年8月发货"}, index = 44)
    private BigDecimal augustShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年9月发货"}, index = 45)
    private BigDecimal septemberShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年10月发货"}, index = 46)
    private BigDecimal octoberShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年11月发货"}, index = 47)
    private BigDecimal novemberShipment;

    @ExcelProperty(value = {"每月发货需求（数值化）", "25年12月发货"}, index = 48)
    private BigDecimal decemberShipment;


    // Getters and Setters
}
