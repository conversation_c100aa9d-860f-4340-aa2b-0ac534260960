package com.akesobio.web.excel;

import com.akesobio.common.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
@Getter
@Setter
@EqualsAndHashCode
@Data
public class InventoryOccupationDetailVoTemp {

    @ExcelProperty("生产日期")
    private String productMonthStr;

    /** MPM代码 */
    @ExcelProperty("MPM编码")
    @Excel(name = "MPM编码")
    private String mpmCode;

    /** MPM名称 */
    @ExcelProperty("MPM名称")
    @Excel(name = "MPM名称")
    private String mpmName;

    /** 工厂代码 */
    @ExcelProperty("工厂代码")
    @Excel(name = "工厂代码")
    private String factoryCode;

    @ExcelProperty("产品代码")
    @Excel(name = "产品代码")
    private String productCode; // 产品代码

    @ExcelProperty("产品名称")
    @Excel(name = "产品名称")
    private String productName;  // 产品名称

    @ExcelProperty("生产线")
    @Excel(name = "生产线")
    private String productLine;

    /** 生产月份 */
    @Excel(name = "生产日期", width = 30)
    private Date productMonth;

    @ExcelProperty("物料")
    @Excel(name = "物料")
    private String material;

    @ExcelProperty("物料描述")
    @Excel(name = "物料")
    private String materialDesc;

    /** 基本计量单位 */
    @ExcelProperty("基本计量单位")
    @Excel(name = "基本计量单位")
    private String basicUnit;

    /** 厂家 */
    @ExcelProperty("厂家")
    @Excel(name = "厂家")
    private String manufacturers;

    /** 货号 */
    @ExcelProperty("货号")
    @Excel(name = "货号")
    private String articleNumber;

    @ExcelProperty("包装规格")
    @Excel(name = "包装规格")
    private String packingSpecifications;

    /** 需求量 */
    @ExcelProperty("需求量")
    @Excel(name = "需求量")
    private String requiredQuantity;

    /** 供应物料代码 */
    @ExcelProperty("供应物料代码")
    @Excel(name = "供应物料代码")
    private String supplyMaterialCode;

    /** 分类 */
    @ExcelProperty("分类")
    @Excel(name = "分类")
    private String category;

    /** 批号/采购未回/缺口 */
    @ExcelProperty("批号/采购未回/缺口")
    @Excel(name = "批号/采购未回/缺口")
    private String batchNumberOrPurchaseBacklogOrGap;

    /** 供应量 */
    @ExcelProperty("供应量")
    @Excel(name = "供应量")
    private String supplyQuantity;

    /** 批量剩余 */
    @ExcelProperty("批量剩余")
    @Excel(name = "批量剩余（被优先占用的批号扣除供应量后剩余数量）")
    private String batchRemaining;

    /** 库存剩余 */
    @ExcelProperty("库存剩余")
    @Excel(name = "库存剩余（总库存包括所有的合格和待验）")
    private String inventoryRemaining;

    /** 合格剩余 */
    @ExcelProperty("合格剩余")
    @Excel(name = "合格剩余（优先占用近效期库存）")
    private String qualifiedRemaining;

    /** 待验剩余 */
    @ExcelProperty("待验剩余")
    @Excel(name = "待验剩余")
    private String pendingInspectionRemaining;

    /** 采购未回剩余 */
    @ExcelProperty("采购未回剩余")
    @Excel(name = "采购未回剩余")
    private String purchaseBacklogRemaining;

    /** 缺口 */
    @ExcelProperty("缺口")
    @Excel(name = "缺口")
    private String gap;

    /** 有效期/到货日期 */
    @ExcelProperty("有效期/到货日期")
    @Excel(name = "有效期/到货日期（占用哪一批号显示哪一批号有效期/到货日期）")
    private Date expirationDateOrArrivalDate;
}
