package com.akesobio.web.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;


import java.util.ArrayList;
import java.util.List;

public class ListStringConverter implements Converter<List<String>> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return List.class; // 支持的目标类型
    }

    @Override
    public List<String> convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        // 将 Excel 单元格中的字符串转换为 List<String>
        String value = cellData.getStringValue();
        if (value == null || value.isEmpty()) {
            return new ArrayList<>();
        }
        String[] items = value.split(","); // 假设使用逗号分隔
        List<String> result = new ArrayList<>();
        for (String item : items) {
            result.add(item.trim());
        }
        return result;
    }
}
