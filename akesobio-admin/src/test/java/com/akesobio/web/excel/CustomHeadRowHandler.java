package com.akesobio.web.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

public class CustomHeadRowHandler implements RowWriteHandler {

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        if (isHead && relativeRowIndex == 0) {
            // 获取 Sheet 对象
            Sheet sheet = writeSheetHolder.getSheet();

            // 合并单元格逻辑
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4)); // 物料信息区域 (第0行，第0列到第4列)
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 5, 10)); // 库存及采购信息区域 (第0行，第5列到第10列)
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 12)); // 货期及效期区域 (第0行，第11列到第12列)

        }
    }
}