package com.akesobio.web.excel;

import com.akesobio.report.autoacct.domain.vo.InventoryOccupationDetailVo;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.List;

public class IndexOrNameDataListener extends AnalysisEventListener<InventoryOccupationDetailVo> {
    private final List<InventoryOccupationDetailVo> dataList = new ArrayList<>();

    @Override
    public void invoke(InventoryOccupationDetailVo data, AnalysisContext context) {
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据解析完成后的操作
    }

    public List<InventoryOccupationDetailVo> getDataList() {
        return dataList;
    }
}
