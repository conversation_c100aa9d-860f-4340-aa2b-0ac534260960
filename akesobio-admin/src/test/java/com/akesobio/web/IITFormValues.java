package com.akesobio.web;

import com.fasterxml.jackson.annotation.*;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

/**
 * IIT立项申请表单数据 POJO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class IITFormValues {
    
    // --- 主表字段 (Serialized directly by <PERSON>) ---

    // fd_3d8f9550810cd6 String 否 单据类型
    @JsonProperty("fd_3d8f9550810cd6")
    private String documentType;

    // fd_3d8f9815e3aa1a String 否 费控系统单据编号
    @JsonProperty("fd_3d8f9815e3aa1a")
    private String costControlDocumentNumber;

    // fd_3d8f986d504674 String 否 oa系统单据编号
    @JsonProperty("fd_3d8f986d504674")
    private String oaSystemDocumentNumber;

    // fd_3d8f9552174718 String 否 所属公司
    @JsonProperty("fd_3d8f9552174718")
    private String belongingCompany;

    // fd_3d8f973d569874 String 否 所属公司Code
    @JsonProperty("fd_3d8f973d569874")
    private String belongingCompanyCode;
    
    // fd_3d8f95564e581a String 否 提交人
    @JsonProperty("fd_3d8f95564e581a")
    @ExcelProperty("姓名")
    private String submitter;
    
    // fd_3d8f95f948f7cc String 否 提交人Code
    @JsonProperty("fd_3d8f95f948f7cc")
    private String submitterCode;
    
    // fd_3d8f95a8992dbc String 否 提交部门
    @JsonProperty("fd_3d8f95a8992dbc")
    private String submittingDepartment;
    
    // fd_3d8f973eb373a2 String 否 提交部门Code
    @JsonProperty("fd_3d8f973eb373a2")
    private String submittingDepartmentCode;
    
    // fd_3d8f95574170f8 String 否 申请人
    @ExcelProperty("姓名")
    @JsonProperty("fd_3d8f95574170f8")
    private String applicant;
    
    // fd_3d8f95fab6cd0e String 否 申请人Code
    @JsonProperty("fd_3d8f95fab6cd0e")
    private String applicantCode;
    
    // fd_3d8f95a9e7c1dc String 否 IIT申请总金额
    @JsonProperty("fd_3d8f95a9e7c1dc")
    @ExcelProperty("预计费用预算")
    private String iitTotalAmount;
    
    // fd_3d8f957dd741fe String 否 预算期间
    @JsonProperty("fd_3d8f957dd741fe")
    private String budgetPeriod;
    
    // fd_3d8f9580845ede String 否 业务类型
    @JsonProperty("fd_3d8f9580845ede")
    private String businessType;
    
    // fd_3d8f973a4d21ce String 否 业务类型Code
    @JsonProperty("fd_3d8f973a4d21ce")
    private String businessTypeCode;
    
    // fd_3d8f959a64d236 String 否 部门所属片区
    @JsonProperty("fd_3d8f959a64d236")
    @ExcelProperty("片区")
    private String departmentRegion;
    
    // fd_3d8f96601c1714 String 否 部门所属片区Code
    @JsonProperty("fd_3d8f96601c1714")
    private String departmentRegionCode;
    
    // fd_3d8f939119e4f6 String 否 产品管线
    @JsonProperty("fd_3d8f939119e4f6")
    private String productPipeline;
    
    // fd_3d8f968367955e String 否 产品管线Code
    @JsonProperty("fd_3d8f968367955e")
    private String productPipelineCode;
    
    // fd_3d8f959baf9d86 String 否 IIT项目编号
    @JsonProperty("fd_3d8f959baf9d86")
    @ExcelProperty("项目号（整理）")
    private String iitProjectNumber;
    
    // fd_3d8f959ced1418 String 否 IIT分类
    @JsonProperty("fd_3d8f959ced1418")
    private String iitCategory;
    
    // fd_3d8f9675e7c136 String 否 IIT分类Code
    @JsonProperty("fd_3d8f9675e7c136")
    private String iitCategoryCode;
    
    // fd_3d8f9712c0a7be String 否 项目名称（多行输入框）
    @JsonProperty("fd_3d8f9712c0a7be")
    private String projectName;
    
    // fd_3d8f95ad14ee98 String 否 项目开始时间
    @JsonProperty("fd_3d8f95ad14ee98")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @ExcelProperty("申请日期")
    private Date projectStartDate;
    
    // fd_3d8f95af0f4680 String 否 项目结束时间
    @JsonProperty("fd_3d8f95af0f4680")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date projectEndDate;
    
    // fd_3d8f95b0860f3a String 否 预估入组病例数
    @JsonProperty("fd_3d8f95b0860f3a")
    private String estimatedPatientCount;
    
    // fd_3d8f95b1c70f2a String 否 主要研究者
    @JsonProperty("fd_3d8f95b1c70f2a")
    private String principalInvestigator;
    
    // fd_3d8f95b3418a10 String 否 备注（多行输入框）
    @JsonProperty("fd_3d8f95b3418a10")
    private String remarks;
    
  
    
    // --- 明细表字段 (Handled by @JsonAnyGetter, so ignore direct serialization) ---
    
    // 如果有明细表，可以在这里添加，例如：
    // fd_iit_detail_table Array 否 明细表-IIT详细信息 (List of IITDetailItem)
//    @JsonIgnore
//    private List<IITDetailItem> iitDetailItems;
    
    // 默认构造函数
//    public IITFormValues() {
//        this.iitDetailItems = new ArrayList<>();
//    }
    
    // --- Getters and Setters for main fields ---
    
    public String getDocumentType() { return documentType; }
    public void setDocumentType(String documentType) { this.documentType = documentType; }
    
    public String getCostControlDocumentNumber() { return costControlDocumentNumber; }
    public void setCostControlDocumentNumber(String costControlDocumentNumber) { this.costControlDocumentNumber = costControlDocumentNumber; }
    
    public String getOaSystemDocumentNumber() { return oaSystemDocumentNumber; }
    public void setOaSystemDocumentNumber(String oaSystemDocumentNumber) { this.oaSystemDocumentNumber = oaSystemDocumentNumber; }
    
    public String getBelongingCompany() { return belongingCompany; }
    public void setBelongingCompany(String belongingCompany) { this.belongingCompany = belongingCompany; }
    
    public String getBelongingCompanyCode() { return belongingCompanyCode; }
    public void setBelongingCompanyCode(String belongingCompanyCode) { this.belongingCompanyCode = belongingCompanyCode; }
    
    public String getSubmitter() { return submitter; }
    public void setSubmitter(String submitter) { this.submitter = submitter; }
    
    public String getSubmitterCode() { return submitterCode; }
    public void setSubmitterCode(String submitterCode) { this.submitterCode = submitterCode; }
    
    public String getSubmittingDepartment() { return submittingDepartment; }
    public void setSubmittingDepartment(String submittingDepartment) { this.submittingDepartment = submittingDepartment; }
    
    public String getSubmittingDepartmentCode() { return submittingDepartmentCode; }
    public void setSubmittingDepartmentCode(String submittingDepartmentCode) { this.submittingDepartmentCode = submittingDepartmentCode; }
    
    public String getApplicant() { return applicant; }
    public void setApplicant(String applicant) { this.applicant = applicant; }
    
    public String getApplicantCode() { return applicantCode; }
    public void setApplicantCode(String applicantCode) { this.applicantCode = applicantCode; }
    
    public String getIitTotalAmount() { return iitTotalAmount; }
    public void setIitTotalAmount(String iitTotalAmount) { this.iitTotalAmount = iitTotalAmount; }
    
    public String getBudgetPeriod() { return budgetPeriod; }
    public void setBudgetPeriod(String budgetPeriod) { this.budgetPeriod = budgetPeriod; }
    
    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }
    
    public String getBusinessTypeCode() { return businessTypeCode; }
    public void setBusinessTypeCode(String businessTypeCode) { this.businessTypeCode = businessTypeCode; }
    
    public String getDepartmentRegion() { return departmentRegion; }
    public void setDepartmentRegion(String departmentRegion) { this.departmentRegion = departmentRegion; }
    
    public String getDepartmentRegionCode() { return departmentRegionCode; }
    public void setDepartmentRegionCode(String departmentRegionCode) { this.departmentRegionCode = departmentRegionCode; }
    
    public String getProductPipeline() { return productPipeline; }
    public void setProductPipeline(String productPipeline) { this.productPipeline = productPipeline; }
    
    public String getProductPipelineCode() { return productPipelineCode; }
    public void setProductPipelineCode(String productPipelineCode) { this.productPipelineCode = productPipelineCode; }
    
    public String getIitProjectNumber() { return iitProjectNumber; }
    public void setIitProjectNumber(String iitProjectNumber) { this.iitProjectNumber = iitProjectNumber; }
    
    public String getIitCategory() { return iitCategory; }
    public void setIitCategory(String iitCategory) { this.iitCategory = iitCategory; }
    
    public String getIitCategoryCode() { return iitCategoryCode; }
    public void setIitCategoryCode(String iitCategoryCode) { this.iitCategoryCode = iitCategoryCode; }
    
    public String getProjectName() { return projectName; }
    public void setProjectName(String projectName) { this.projectName = projectName; }
    
    public Date getProjectStartDate() { return projectStartDate; }
    public void setProjectStartDate(Date projectStartDate) { this.projectStartDate = projectStartDate; }
    
    public Date getProjectEndDate() { return projectEndDate; }
    public void setProjectEndDate(Date projectEndDate) { this.projectEndDate = projectEndDate; }
    
    public String getEstimatedPatientCount() { return estimatedPatientCount; }
    public void setEstimatedPatientCount(String estimatedPatientCount) { this.estimatedPatientCount = estimatedPatientCount; }
    
    public String getPrincipalInvestigator() { return principalInvestigator; }
    public void setPrincipalInvestigator(String principalInvestigator) { this.principalInvestigator = principalInvestigator; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    

    
    // --- Getters and Setters for detail item lists ---
    
//    public List<IITDetailItem> getIitDetailItems() { return iitDetailItems; }
//    public void setIitDetailItems(List<IITDetailItem> iitDetailItems) { this.iitDetailItems = iitDetailItems; }
//
    /**
     * 处理明细表字段的序列化
     * 如果有明细表数据，通过此方法进行JSON序列化
     */
    @JsonAnyGetter
    public Map<String, Object> getDetailTableFieldsAsMap() {
        Map<String, Object> detailFields = new HashMap<>();
        
        // 处理IIT详细信息明细表
//        if (iitDetailItems != null && !iitDetailItems.isEmpty()) {
//            // 这里需要根据实际的明细表字段ID进行映射
//            // 示例：假设明细表有字段 fd_iit_detail_table.fd_detail_field1
//            // detailFields.put("fd_iit_detail_table.fd_detail_field1",
//            //     iitDetailItems.stream().map(IITDetailItem::getField1).collect(Collectors.toList()));
//        }
        
        return detailFields;
    }
} 