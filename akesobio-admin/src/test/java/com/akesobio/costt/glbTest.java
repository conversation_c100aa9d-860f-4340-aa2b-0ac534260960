package com.akesobio.costt;

import com.akesobio.ReportApplication;
import com.akesobio.report.costControl.mapper.GlBudgetValidateRuleMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Todo
 *
 * <AUTHOR>
 * @since 2025/7/7  14:54
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReportApplication.class)
public class glbTest {

    @Autowired
    GlBudgetValidateRuleMapper glbBudgetValidateRuleMapper;

    @org.junit.Test
    public void testOne() {
        System.out.println(glbBudgetValidateRuleMapper.one());
    }
}
